package com.byzaneo.generix.rtemachine.builtinfunction;

import static java.util.Optional.ofNullable;

import java.util.List;
import java.util.regex.Pattern;

import com.byzaneo.generix.rtemachine.RteRuntime;
import com.byzaneo.generix.rtemachine.bean.FormattedString;
import com.byzaneo.generix.rtemachine.exception.*;

/**
 * <AUTHOR> Aboulaye <<EMAIL>>
 * @company Generix group
 * @date 4 mars 2016
 */
public class CompareFunction implements BuiltInFunction {

  @Override
  public Object call(RteRuntime runtime, List<Object> args, int statementInputFileLine)
      throws RteException, RteInternalException {
    // assertion
    ofNullable(args)
        .filter(list -> list.size() == 2)
        .orElseThrow(RteException::new);
    // execution
    final String firstArgument = args.get(0) instanceof FormattedString ? ((FormattedString) args.get(0)).getValue() : (String) args.get(0);
    final String secondArgument = args.get(1) instanceof FormattedString ? ((FormattedString) args.get(1)).getValue() : (String) args.get(1);

    String regex = (secondArgument)
        .replace("\\\\", "\\")
        .replace("*", ".*")
        .replace("?", ".")
        .replace("\\.*", "\\*")
        .replace("\\.", "\\?");
    return Pattern.matches(regex, firstArgument);
  }

}
