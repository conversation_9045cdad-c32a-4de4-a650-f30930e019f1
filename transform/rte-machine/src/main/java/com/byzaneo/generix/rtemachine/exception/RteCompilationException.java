package com.byzaneo.generix.rtemachine.exception;

import java.util.Collection;

/**
 * Exceptions of this type denotes an invalid source file that cannot be executed.
 * 
 * <AUTHOR>
 */
public class RteCompilationException extends Exception {

  private static final long serialVersionUID = 3246352671883450858L;

  private final Collection<String> messages;

  public RteCompilationException(Collection<String> messages) {
    this.messages = messages;
  }

  @Override
  public String getMessage() {
    return String.join("\n", messages);
  }
}
