package com.byzaneo.generix.rtemachine.builtinfunction;

import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.Party.BILLTOPARTY;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.Party.BUYER;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.Party.BUYERAGENT;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.Party.FACTOR;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.Party.PAYER;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.Party.REMITTOPARTY;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.Party.SELLER;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.Party.SELLERAGENT;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.STATUS_ACCEPTED_FOR_APPROVED_B2G_ON_SELLERAGENT;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.STATUS_ACCEPTED_FOR_APPROVED_ON_BUYER;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.STATUS_ACCEPTED_FOR_APPROVED_ON_SELLERAGENT;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.STATUS_ACCEPTED_FOR_APPROVED_PARTIALLY_ON_BUYER;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.STATUS_ACCEPTED_FOR_DISPUTED_ON_BUYER;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.STATUS_ACCEPTED_FOR_OPENED_ON_BUYER;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.STATUS_ACCEPTED_FOR_PAYMENT_RECEIVED_ON_REMITTOPARTY;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.STATUS_ACCEPTED_FOR_PAYMENT_RECEIVED_ON_SELLER;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.STATUS_ACCEPTED_FOR_PAYMENT_SENT_ON_BUYER;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.STATUS_ACCEPTED_FOR_PAYMENT_SENT_ON_PAYER;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.STATUS_ACCEPTED_FOR_REFUSED_ON_BUYER;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.STATUS_ACCEPTED_FOR_REFUSED_ON_SELLER;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.STATUS_ACCEPTED_FOR_REFUSED_ON_SELLERAGENT;
import static com.byzaneo.generix.rtemachine.util.LifeCycleStatusFunctionHelper.STATUS_ACCEPTED_FOR_SUSPENDED_ON_BUYER;
import static com.byzaneo.xtrade.api.DocumentStage.APPROVED;
import static com.byzaneo.xtrade.api.DocumentStage.APPROVED_B2G;
import static com.byzaneo.xtrade.api.DocumentStage.APPROVED_PARTIALLY;
import static com.byzaneo.xtrade.api.DocumentStage.COMPLETED;
import static com.byzaneo.xtrade.api.DocumentStage.DISPUTED;
import static com.byzaneo.xtrade.api.DocumentStage.OPENED;
import static com.byzaneo.xtrade.api.DocumentStage.PAYMENT_RECEIVED;
import static com.byzaneo.xtrade.api.DocumentStage.PAYMENT_SENT;
import static com.byzaneo.xtrade.api.DocumentStage.REFUSED;
import static com.byzaneo.xtrade.api.DocumentStage.SUSPENDED;
import static java.util.Collections.emptyList;
import static org.apache.commons.collections4.CollectionUtils.size;

import java.util.*;

import org.apache.commons.lang3.EnumUtils;

import com.byzaneo.generix.rtemachine.RteRuntime;
import com.byzaneo.generix.rtemachine.bean.FormattedString;
import com.byzaneo.generix.rtemachine.exception.*;
import com.byzaneo.generix.rtemachine.util.*;
import com.byzaneo.xtrade.api.DocumentStage;

public class GetAllowedStatusFunction implements BuiltInFunction {
  @Override
  public Object call(RteRuntime runtime, List<Object> args, int statementInputFileLine)
      throws RteException, RteInternalException, ExitFunctionException {
    if (size(args) != 1 && size(args) != 2) {
      throw new RteException(
          "Wrong parameter: getAllowedStatus takes one or two arguments: current status and actor." +
              " For exemple: getAllowedStatus(REJECTED) or getAllowedStatus(REJECTED, BUYER)");
    }
    if (!args.stream()
        .allMatch(arg -> arg instanceof FormattedString || arg instanceof String)) {
      throw new RteException("The parameter of getAllowedStatus function must be string");
    }

    String currentStatus = args.get(0) instanceof FormattedString ? ((FormattedString) args.get(0)).getValue() : (String) args.get(0);
    if (!EnumUtils.isValidEnum(DocumentStage.class, currentStatus)) {
      RteErrorHelper.printErrMessage(runtime, statementInputFileLine,
          String.format("The status is not valid : %s", currentStatus));
      return emptyList();
    }
    if (args.size() == 2) {
      String actor = args.get(1) instanceof FormattedString ? ((FormattedString) args.get(1)).getValue() : (String) args.get(1);
      actor = actor.toUpperCase();
      if (!EnumUtils.isValidEnum(LifeCycleStatusFunctionHelper.Party.class, actor)) {
        RteErrorHelper.printErrMessage(runtime, statementInputFileLine,
            String.format("The actor is not valid : %s", actor));
        return emptyList();
      }
      return getAllowedStatus(currentStatus, actor);
    }
    else
      return getAllowedStatus(currentStatus);

  }

  private List<String> getAllowedStatus(String status, String actor) {
    List<String> allowedStatuses = new ArrayList<>();
    DocumentStage stage = DocumentStage.valueOf(status);
    LifeCycleStatusFunctionHelper.Party party = LifeCycleStatusFunctionHelper.Party.valueOf(actor);

    if (SELLER.equals(party)) {
      if (SUSPENDED.equals(stage))
        allowedStatuses.add(COMPLETED.name());
      if (STATUS_ACCEPTED_FOR_PAYMENT_RECEIVED_ON_SELLER.contains(stage))
        allowedStatuses.add(PAYMENT_RECEIVED.name());
      if (STATUS_ACCEPTED_FOR_REFUSED_ON_SELLER.contains(stage))
        allowedStatuses.add(REFUSED.name());
    }
    if (FACTOR.equals(party)) {
      if (SUSPENDED.equals(stage))
        allowedStatuses.add(COMPLETED.name());
    }
    if (REMITTOPARTY.equals(party)) {
      if (STATUS_ACCEPTED_FOR_PAYMENT_RECEIVED_ON_REMITTOPARTY.contains(stage))
        allowedStatuses.add(PAYMENT_RECEIVED.name());
    }
    if (SELLERAGENT.equals(party)) {
      if (STATUS_ACCEPTED_FOR_APPROVED_ON_SELLERAGENT.contains(stage))
        allowedStatuses.add(APPROVED.name());
      if (STATUS_ACCEPTED_FOR_REFUSED_ON_SELLERAGENT.contains(stage))
        allowedStatuses.add(REFUSED.name());
      if (STATUS_ACCEPTED_FOR_APPROVED_B2G_ON_SELLERAGENT.contains(stage))
        allowedStatuses.add(APPROVED_B2G.name());
    }
    if (Arrays.asList(BUYER, BUYERAGENT, BILLTOPARTY)
        .contains(party)) {
      if (STATUS_ACCEPTED_FOR_OPENED_ON_BUYER.contains(stage))
        allowedStatuses.add(OPENED.name());
      if (STATUS_ACCEPTED_FOR_SUSPENDED_ON_BUYER.contains(stage))
        allowedStatuses.add(SUSPENDED.name());
      if (STATUS_ACCEPTED_FOR_DISPUTED_ON_BUYER.contains(stage))
        allowedStatuses.add(DISPUTED.name());
      if (STATUS_ACCEPTED_FOR_APPROVED_ON_BUYER.contains(stage))
        allowedStatuses.add(APPROVED.name());
      if (STATUS_ACCEPTED_FOR_APPROVED_PARTIALLY_ON_BUYER.contains(stage))
        allowedStatuses.add(APPROVED_PARTIALLY.name());
      if (STATUS_ACCEPTED_FOR_PAYMENT_SENT_ON_BUYER.contains(stage))
        allowedStatuses.add(PAYMENT_SENT.name());
      if (STATUS_ACCEPTED_FOR_REFUSED_ON_BUYER.contains(stage))
        allowedStatuses.add(REFUSED.name());
    }
    if (PAYER.equals(party)) {
      if (STATUS_ACCEPTED_FOR_PAYMENT_SENT_ON_PAYER.contains(stage))
        allowedStatuses.add(PAYMENT_SENT.name());
    }
    return allowedStatuses;
  }

  private List<String> getAllowedStatus(String currentStatus) {
    if (DocumentStage.REJECTED.name()
        .equals(currentStatus))
      return emptyList();
    List<String> statusesList = new ArrayList<>();
    //PAS DE FACTURE
    if (DocumentStage.UNDEFINED.name()
        .equals(currentStatus)) {
      statusesList = Arrays.asList(DocumentStage.UPLOADED.name());
      return statusesList;
    }
    //DEPOSEE
    if (DocumentStage.UPLOADED.name()
        .equals(currentStatus)) {
      statusesList = Arrays.asList(DocumentStage.RECEIVED.name(), DocumentStage.REJECTED.name(), DocumentStage.AVAILABLE.name(),
          DocumentStage.PAYMENT_RECEIVED.name(), DocumentStage.REFUSED.name(), DocumentStage.APPROVED_B2G.name(), DocumentStage.NOT_APPROVED_B2G.name(),
          DocumentStage.OPENED.name(), DocumentStage.SUSPENDED.name(), DocumentStage.DISPUTED.name(), DocumentStage.APPROVED.name(),
          DocumentStage.APPROVED_PARTIALLY.name(), DocumentStage.PAYMENT_SENT.name(), DocumentStage.SENT.name());
      return statusesList;
    }
    //EMISE PAR LA PLATEFORME
    if (DocumentStage.SENT.name()
        .equals(currentStatus)) {
      statusesList = Arrays.asList(DocumentStage.RECEIVED.name(), DocumentStage.REJECTED.name(), DocumentStage.AVAILABLE.name(),
          DocumentStage.PAYMENT_RECEIVED.name(), DocumentStage.REFUSED.name(), DocumentStage.APPROVED_B2G.name(), DocumentStage.NOT_APPROVED_B2G.name(),
          DocumentStage.OPENED.name(), DocumentStage.SUSPENDED.name(), DocumentStage.DISPUTED.name(), DocumentStage.APPROVED.name(),
          DocumentStage.APPROVED_PARTIALLY.name(), DocumentStage.PAYMENT_SENT.name());
      return statusesList;
    }
    //RECUE PAR LA PLATEFORME
    if (DocumentStage.RECEIVED.name()
        .equals(currentStatus)) {
      statusesList = Arrays.asList(DocumentStage.AVAILABLE.name(), DocumentStage.PAYMENT_RECEIVED.name(),
          DocumentStage.REFUSED.name(), DocumentStage.OPENED.name(), DocumentStage.SUSPENDED.name(), DocumentStage.DISPUTED.name(),
          DocumentStage.APPROVED.name(), DocumentStage.APPROVED_PARTIALLY.name(), DocumentStage.PAYMENT_SENT.name());
      return statusesList;
    }
    //MISE A DISPOSITION
    if (DocumentStage.AVAILABLE.name()
        .equals(currentStatus)) {
      statusesList = Arrays.asList(DocumentStage.PAYMENT_RECEIVED.name(), DocumentStage.REFUSED.name(), DocumentStage.OPENED.name(),
          DocumentStage.SUSPENDED.name(), DocumentStage.DISPUTED.name(), DocumentStage.APPROVED.name(),
          DocumentStage.APPROVED_PARTIALLY.name(), DocumentStage.PAYMENT_SENT.name());
      return statusesList;
    }
    //PRISE EN CHARGE
    if (DocumentStage.OPENED.name()
        .equals(currentStatus)) {
      statusesList = Arrays.asList(DocumentStage.PAYMENT_RECEIVED.name(), DocumentStage.REFUSED.name(),
          DocumentStage.SUSPENDED.name(), DocumentStage.DISPUTED.name(), DocumentStage.APPROVED.name(),
          DocumentStage.APPROVED_PARTIALLY.name(), DocumentStage.PAYMENT_SENT.name());
      return statusesList;
    }
    //SUSPENDUE
    if (DocumentStage.SUSPENDED.name()
        .equals(currentStatus)) {
      statusesList = Arrays.asList(DocumentStage.COMPLETED.name(), DocumentStage.PAYMENT_RECEIVED.name(),
          DocumentStage.REFUSED.name(), DocumentStage.DISPUTED.name(), DocumentStage.APPROVED.name(),
          DocumentStage.APPROVED_PARTIALLY.name(),
          DocumentStage.PAYMENT_SENT.name());
      return statusesList;
    }
    //COMPLETEE
    if (DocumentStage.COMPLETED.name()
        .equals(currentStatus)) {
      statusesList = Arrays.asList(DocumentStage.PAYMENT_RECEIVED.name(), DocumentStage.REFUSED.name(),
          DocumentStage.APPROVED_B2G.name(), DocumentStage.NOT_APPROVED_B2G.name(), DocumentStage.OPENED.name(),
          DocumentStage.SUSPENDED.name(), DocumentStage.DISPUTED.name(), DocumentStage.APPROVED.name(),
          DocumentStage.APPROVED_PARTIALLY.name(), DocumentStage.PAYMENT_SENT.name());
      return statusesList;
    }
    //EN LITIGE
    if (DocumentStage.DISPUTED.name()
        .equals(currentStatus)) {
      statusesList = Arrays.asList(DocumentStage.PAYMENT_RECEIVED.name(), DocumentStage.REFUSED.name(), DocumentStage.APPROVED.name(),
          DocumentStage.APPROVED_PARTIALLY.name(), DocumentStage.PAYMENT_SENT.name());
      return statusesList;
    }
    //APPROUVEE
    if (DocumentStage.APPROVED.name()
        .equals(currentStatus)) {
      statusesList = Arrays.asList(DocumentStage.PAYMENT_RECEIVED.name(), DocumentStage.REFUSED.name(), DocumentStage.DISPUTED.name(),
          DocumentStage.APPROVED_PARTIALLY.name(), DocumentStage.PAYMENT_SENT.name());
      return statusesList;
    }
    //APPROUVEE PARTIELLEMENT
    if (DocumentStage.APPROVED_PARTIALLY.name()
        .equals(currentStatus)) {
      statusesList = Arrays.asList(DocumentStage.PAYMENT_RECEIVED.name(), DocumentStage.REFUSED.name(), DocumentStage.DISPUTED.name(),
          DocumentStage.APPROVED.name(),
          DocumentStage.PAYMENT_SENT.name());
      return statusesList;
    }
    //REFUSEE
    if (DocumentStage.REFUSED.name()
        .equals(currentStatus)) {
      return emptyList();
    }
    //PAIEMENT TRANSMIS
    if (DocumentStage.PAYMENT_SENT.name()
        .equals(currentStatus)) {
      statusesList = Arrays.asList(DocumentStage.PAYMENT_RECEIVED.name(), DocumentStage.REFUSED.name(), DocumentStage.PAYMENT_SENT.name());
      return statusesList;
    }
    //ENCAISSEE
    if (DocumentStage.PAYMENT_RECEIVED.name()
        .equals(currentStatus)) {
      statusesList = Arrays.asList(DocumentStage.RECEIVED.name(), DocumentStage.AVAILABLE.name(),
          DocumentStage.PAYMENT_RECEIVED.name(), DocumentStage.REFUSED.name(), DocumentStage.OPENED.name(),
          DocumentStage.SUSPENDED.name(), DocumentStage.DISPUTED.name(), DocumentStage.APPROVED.name(),
          DocumentStage.APPROVED_PARTIALLY.name(), DocumentStage.PAYMENT_SENT.name(), DocumentStage.SENT.name(),
          DocumentStage.UPLOADED.name(), DocumentStage.PAYMENT_SENT.name());
      return statusesList;
    }
    //VISE OR NON-VISEE
    if (DocumentStage.APPROVED_B2G.name()
        .equals(currentStatus) || DocumentStage.NOT_APPROVED_B2G.name()
        .equals(currentStatus)) {
      statusesList = Arrays.asList(DocumentStage.RECEIVED.name(), DocumentStage.REJECTED.name(),
          DocumentStage.AVAILABLE.name(), DocumentStage.PAYMENT_RECEIVED.name(), DocumentStage.REFUSED.name(),
          DocumentStage.OPENED.name(), DocumentStage.SUSPENDED.name(), DocumentStage.DISPUTED.name(),
          DocumentStage.APPROVED.name(), DocumentStage.APPROVED_PARTIALLY.name(), DocumentStage.PAYMENT_SENT.name());
      return statusesList;
    }
    return emptyList();
  }
}
