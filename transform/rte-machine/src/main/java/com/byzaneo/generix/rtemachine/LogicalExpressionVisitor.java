package com.byzaneo.generix.rtemachine;

import com.byzaneo.generix.rtemachine.exception.ExitFunctionException;
import com.byzaneo.generix.rtemachine.exception.RteException;
import com.byzaneo.generix.rtemachine.exception.RteInternalException;
import com.byzaneo.generix.rtemachine.exception.RteInterpreterException;
import com.byzaneo.generix.rtemachine.rte.RteParser;
import org.antlr.v4.runtime.tree.AbstractParseTreeVisitor;
import org.antlr.v4.runtime.tree.RuleNode;
import org.antlr.v4.runtime.tree.TerminalNode;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;

/**
 * This visitor evaluates a logic expression.
 * <p>
 * <strong>Implementation note:<br/>
 * </strong> For relational operations such as 'equality', 'less than' ou 'greater than', this implementation compares Comparable operands.
 * In the case of an expression such as:
 *
 * <pre>
 * 42 > "blue"
 * </pre>
 *
 * Where we compare a number with a string, the operand would be both Comparables, but none would be comparable <em>with the other</em>. In
 * such case, we can't avoid (without overcomplicated construction) ClassCastException. However, whereas this implementation is not
 * protected against such expression, these whould be forbidden by the grammar.
 * </p>
 *
 * <AUTHOR>
 */
class LogicalExpressionVisitor extends AbstractParseTreeVisitor<Boolean> {

  private final static Collection<String> BOOLEAN_OPERATORS = Arrays.asList("or", "and");
  private final static String BOOLEAN_NEGATION = "not";

  private final RteInterpreter interpreter;
  private final Deque<Comparable<?>> operandStack = new LinkedList<>();
  private final Deque<Operator> operatorStack = new LinkedList<>();

  private enum Operator {
    EQUAL("=",
        x -> x == 0),
    NOT_EQUAL("<>",
        x -> x != 0),
    LESS_THAN("<",
        x -> x < 0),
    GREATER_THAN(">",
        x -> x > 0),
    LESS_THAN_OR_EQUAL("<=",
        x -> x <= 0),
    GREATER_THAN_OR_EQUAL(">=",
        x -> x >= 0);

    private static Operator fromSymbol(String text) {
      for (Operator value : values()) {
        if (value.symbol.equals(text)) {
          return value;
        }
      }
      return null;
    }

    private final String symbol;
    private final Function<Integer, Boolean> comparator;

    Operator(String symbol, Function<Integer, Boolean> comparator) {
      this.symbol = symbol;
      this.comparator = comparator;
    }

    @SuppressWarnings("squid:S1226")
    public <T extends Comparable<T>> Boolean aggregate(T leftOperand, T rightOperand) {
      if (leftOperand == null ^ rightOperand == null) {
        return Boolean.FALSE;
      }
      leftOperand = convertEmptyString(leftOperand);
      rightOperand = convertEmptyString(rightOperand);

      return comparator.apply(leftOperand.compareTo(rightOperand));
    }

    @SuppressWarnings("unchecked")
    private <T> T convertEmptyString(T str) {
      if (str instanceof String) {
        String result = StringUtils.trim((String) str)
            .isEmpty() ? StringUtils.EMPTY : (String) str;
        return (T) result;
      }
      return str;
    }
  }

  LogicalExpressionVisitor(RteInterpreter interpreter) {
    this.interpreter = interpreter;
  }

  @Override
  @SuppressWarnings({ "rawtypes", "unchecked" })
  public Boolean visitChildren(RuleNode node) {
    try {
      int ruleIndex = node.getRuleContext()
          .getRuleIndex();
      switch (ruleIndex) {
      case RteParser.RULE_booleanTerminalExpression:
        Boolean booleanValue = interpreter.evaluateBooleanExpression((RteParser.BooleanTerminalExpressionContext) node.getRuleContext());
        operandStack.push(booleanValue);
        return booleanValue;
      case RteParser.RULE_textTerminalExpression:
        operandStack.push(interpreter.evaluateFormattedTextExpression((RteParser.TextTerminalExpressionContext) node.getRuleContext())
            .getValue());
        return defaultResult();
      case RteParser.RULE_additiveExpression:
        operandStack.push(node.accept(new AdditiveExpressionVisitor(interpreter)));
        return defaultResult();
      default:
        break;
      }

      super.visitChildren(node);
      if (operandStack.size() >= 2 && operatorStack.size() >= 1) {
        Comparable rightOperand = operandStack.pop();
        Comparable leftOperand = operandStack.pop();
        if (leftOperand == null) {
          leftOperand = "";
        }
        Boolean operationResult = operatorStack.pop()
            .aggregate(leftOperand, rightOperand);
        operandStack.push(operationResult);
      }
      return (Boolean) operandStack.getFirst();
    }
    catch (RteInterpreterException | RteException | RteInternalException | ExitFunctionException ex) {
      throw new RuntimeException(ex);
    }
  }

  /**
   * Calculates if tree walking is still necessary.
   * <ul>
   * <li>If the result is still undecided, continue evaluation.</li>
   * <li>If we are evaluating a AND expression and the result is already false, do not continue evaluation, the final result is necessarily
   * false.</li>
   * <li>If we are evaluating an OR expression and the result is already true, do not continue evaluation, the final result is necessarily
   * true.</li>
   * </ul>
   *
   * @param node
   * @param currentResult
   * @return
   */
  @Override
  protected boolean shouldVisitNextChild(RuleNode node, Boolean currentResult) {
    if (Objects.equals(currentResult, defaultResult())) {
      return true;
    }
    int currentRule = node.getRuleContext()
        .getRuleIndex();
    return !(currentRule == RteParser.RULE_logicalAndExpression && Boolean.FALSE.equals(currentResult)) &&
        !(currentRule == RteParser.RULE_logicalOrExpression && Boolean.TRUE.equals(currentResult));
  }

  @Override
  public Boolean visitTerminal(TerminalNode node) {
    String symbol = node.getText();
    Operator op = Operator.fromSymbol(symbol);
    if (op != null) {
      operatorStack.push(op);
    }
    else if (BOOLEAN_OPERATORS.contains(symbol)) {
      operandStack.pop();
    }
    else if (BOOLEAN_NEGATION.equals(symbol)) {
      operandStack.push(Boolean.FALSE);
      operatorStack.push(Operator.EQUAL);
    }
    return defaultResult();
  }
}
