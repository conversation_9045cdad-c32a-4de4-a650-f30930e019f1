package com.byzaneo.generix.rtemachine.builtinfunction;



import static com.byzaneo.query.builder.Clauses.equal;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.InvalidPathException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Precision;

import com.byzaneo.commons.util.SpringContextHelper;
import com.byzaneo.generix.rtemachine.RteRuntime;
import com.byzaneo.generix.rtemachine.bean.FormattedString;
import com.byzaneo.generix.rtemachine.exception.RteException;
import com.byzaneo.generix.rtemachine.exception.RteInternalException;
import com.byzaneo.generix.rtemachine.util.RteErrorHelper;
import com.byzaneo.query.builder.QueryBuilder;
import com.byzaneo.xtrade.bean.PeriodicReport;
import com.byzaneo.xtrade.dao.mongo.PeriodicReportDAO;

public class AddPeriodicReportFunction implements BuiltInFunction {

  private static final String ERROR_MESSAGE = "Wrong arguments: addPeriodicReport(tReportName, tPeriod, tSIREN, fReportPath)";
  private static final String ERROR_MESSAGE_WHEN_COPY = "Error copying the file ";
  private static final String ERROR_MESSAGE_NEUTRAL = "An error has occured";
  private static final String PERIODIC_REPORTS_FOLDER = "periodicreport";
  private static final int ERROR_EXIT_CODE = 1;

  private PeriodicReportDAO periodicReportDAO;

  private String errorMessage;

  @Override
  public Object call(RteRuntime runtime, List<Object> args, int statementInputFileLine)
      throws RteException, RteInternalException {

    if (!addPeriodicReport(runtime, args)) {
      RteErrorHelper.printErrMessage(runtime, statementInputFileLine, this.errorMessage);
      runtime.updateExitCode(ERROR_EXIT_CODE);
    }
    return null;
  }

  private boolean addPeriodicReport(RteRuntime runtime, List<Object> args) throws RteException {

    List<String> params = this.checkParameters(args);
    if (params == null) {
      return false;
    }

    String tReportName = params.get(0);
    String tPeriod = params.get(1);
    String tSIREN = params.get(2);
    String fReportPath = params.get(3);
    Double size;

    String code = runtime.getProperty("instance_code");
    String owner = runtime.getProperty("instance_company_identifier");
    if (StringUtils.isBlank(code) || StringUtils.isBlank(owner)) {
      this.errorMessage = ERROR_MESSAGE_NEUTRAL;
      return false;
    }

    String copyPath = runtime.getProperty("output_dir_string") + File.separator + code + File.separator + PERIODIC_REPORTS_FOLDER +
        File.separator + tSIREN;
    String fullPath;
    try {
      Path original = Paths.get(fReportPath);
      fullPath = copyPath + File.separator + tReportName;
      Path copiedFolder = Paths.get(copyPath);
      if (!Files.exists(copiedFolder)) {
        Files.createDirectories(copiedFolder);
      }
      Path copied = Paths.get(fullPath);
      Files.copy(original, copied, StandardCopyOption.REPLACE_EXISTING);
      size = Precision.round((double) Files.size(copied) / 1024d, 3);
    }
    catch (InvalidPathException | IOException | SecurityException e) {
      this.errorMessage = ERROR_MESSAGE_WHEN_COPY + fReportPath;
      return false;
    }

    // checks to see this is an override
    PeriodicReport periodicReport;

    QueryBuilder qb = QueryBuilder.createBuilder()
        .and(equal("reportName", tReportName),
            equal("entity", tSIREN));
    periodicReport = getPeriodicReportDAO().findOneByQuery(qb.query());

    if (periodicReport != null) {
      // updateData
      periodicReport.setReportName(tReportName);
      periodicReport.setDate(new Date());
      periodicReport.setPeriod(tPeriod);
      periodicReport.setEntity(tSIREN);
      periodicReport.setSize(size);
      periodicReport.setFilePath(fullPath);
      periodicReport.setOwner(owner);
    }
    else {
      periodicReport = new PeriodicReport(tReportName, new Date(), tPeriod, tSIREN, size, fullPath, owner);
    }

    getPeriodicReportDAO().save(periodicReport);

    return true;
  }

  private List<String> checkParameters(List<Object> args) {

    if (CollectionUtils.size(args) != 4 || args.get(0) == null || args.get(1) == null || args.get(2) == null ||
        args.get(3) == null) {
      this.errorMessage = ERROR_MESSAGE;
      return null;
    }

    if (args.get(0) instanceof FormattedString && args.get(1) instanceof FormattedString && args.get(2) instanceof FormattedString &&
        args.get(3) instanceof FormattedString) {
      String tReportName = ((FormattedString) args.get(0)).getValue();
      String tPeriod = ((FormattedString) args.get(1)).getValue();
      String tSIREN = ((FormattedString) args.get(2)).getValue();
      String fReportPath = ((FormattedString) args.get(3)).getValue();
      if (tReportName == null || tPeriod == null || tSIREN == null ||
          fReportPath == null) {
        this.errorMessage = ERROR_MESSAGE;
        return null;
      }
      return Arrays.asList(tReportName, tPeriod, tSIREN, fReportPath);
    }
    else if (args.get(0) instanceof String && args.get(1) instanceof String && args.get(2) instanceof String &&
        args.get(3) instanceof String) {
          return Arrays.asList((String) args.get(0), (String) args.get(1), (String) args.get(2), (String) args.get(3));
        }
    else {
      this.errorMessage = ERROR_MESSAGE;
      return null;
    }
  }

  private PeriodicReportDAO getPeriodicReportDAO() {
    return periodicReportDAO == null ? periodicReportDAO = SpringContextHelper.getBean(PeriodicReportDAO.class, PeriodicReportDAO.DAO_NAME)
        : periodicReportDAO;
  }

}
