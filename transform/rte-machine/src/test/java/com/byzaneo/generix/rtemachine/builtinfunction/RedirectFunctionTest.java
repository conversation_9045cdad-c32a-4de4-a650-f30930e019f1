package com.byzaneo.generix.rtemachine.builtinfunction;

import static com.byzaneo.commons.util.FileHelper.deleteFile;
import static com.byzaneo.generix.rtemachine.RteCompiler.runtime;
import static com.byzaneo.generix.rtemachine.RteRuntime.RTE_FILE_PATH;
import static com.byzaneo.generix.rtemachine.RteRuntime.RteEnv.EDIHOME;
import static com.byzaneo.generix.rtemachine.RteRuntime.RteEnv.HOME;
import static com.byzaneo.generix.rtemachine.util.OperationsHelper.initMongoAfterCleanUp;
import static java.lang.System.currentTimeMillis;
import static java.nio.charset.StandardCharsets.UTF_8;
import static java.nio.file.Paths.get;
import static java.util.Arrays.stream;
import static java.util.stream.Collectors.joining;
import static org.apache.commons.io.FileUtils.readFileToString;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.slf4j.LoggerFactory.getLogger;

import java.io.CharArrayWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.io.UncheckedIOException;
import java.net.UnknownHostException;
import java.util.Map.Entry;
import java.util.Properties;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.springframework.dao.DataAccessException;

import com.byzaneo.generix.rtemachine.RteRuntime;
import com.byzaneo.generix.rtemachine.exception.RteCompilationException;
import com.byzaneo.generix.rtemachine.exception.RteException;
import com.byzaneo.generix.rtemachine.exception.RteInternalException;
import com.byzaneo.generix.rtemachine.exception.RteInterpreterException;

class RedirectFunctionTest {

  private static final Logger LOGGER = getLogger(RedirectFunctionTest.class);

  @Test
  void redirect() {
    long start = System.currentTimeMillis();
    File rte = new File("./src/test/resources/redirect/AIO-5637-redirectfunction.rte");
    LOGGER.info("{}", rte.getName());

    // properties
    Properties properties = new Properties();
    String rtePath = get("./src/test/resources/redirect").toString();
    properties.setProperty(EDIHOME.name(), rtePath);
    properties.setProperty(HOME.name(), rtePath);
    properties.setProperty(RTE_FILE_PATH, rtePath);
    LOGGER.info("\t > props: \n\t\t . {}", properties.entrySet()
        .stream()
        .map(Entry::toString)
        .collect(joining("\n\t\t . ")));
    CharArrayWriter stderr = new CharArrayWriter();
    try (Reader source = new InputStreamReader(new FileInputStream(rte), UTF_8);
        InputStream stdin = new FileInputStream("./src/test/resources/edierror/input/ORDERS_ERR_1.txt");
        RteRuntime runtime = runtime(source, stderr, properties)) {

      // i/o
      runtime.setStdin(stdin);
      CharArrayWriter stdout = new CharArrayWriter();
      runtime.setStdout(stdout);

      // executes
      runtime.execute();

      // results
      LOGGER.info("STDOUT:\n{}", stdout);
      LOGGER.error("STDERR:\n{}", stderr);

      // standards
      assertEquals("I/O will be redirected", stdout.toString());
      // assertTrue(stderr.toString().isEmpty());

      // redirects
      stream(new File("./src/test/resources/redirect")
          .listFiles(file -> file.getName()
              .endsWith(".log")))
                  .forEach(expected ->
      {
                    File actual = new File("./target/redirect", expected.getName());
                    try {
                      String expectedString = readFileToString(expected, UTF_8).replace("\r", "");
                      String acutualString = readFileToString(actual, UTF_8).replace("\r", "");
                      // LOGGER.info("{}:\n{}", actual.getName(), acutualString);
                      assertEquals(expectedString, acutualString);
                    }
                    catch (IOException e) {
                      throw new UncheckedIOException(e);
                    }
                  });

    }
    catch (IOException e) {
      throw new UncheckedIOException(e);
    }
    catch (RteCompilationException | RteException | RteInterpreterException | RteInternalException e) {
      LOGGER.error("\t > end on error : " + e.getMessage(), e);
    }
    finally {
      LOGGER.info("\t > end with : {}ms", currentTimeMillis() - start);
    }

  }

  @Test
  void redirectQAL() throws DataAccessException, UnknownHostException {
    deleteFile(new File("target/QAL"));

    long start = System.currentTimeMillis();
    File rte = new File("./src/test/resources/redirect/QAL-AIO-5637-redirect.rte");
    LOGGER.info("{}", rte.getName());

    // properties
    Properties properties = new Properties();
    String rtePath = get("./src/test/resources/redirect").toString();
    properties.setProperty(EDIHOME.name(), get("./src/test/resources").toString());
    properties.setProperty(HOME.name(), rtePath);
    properties.setProperty(RTE_FILE_PATH, rtePath);
    properties.setProperty("mongo", "ci-mongo:27017/ci");
    LOGGER.info("\t > props: \n\t\t . {}", properties.entrySet()
        .stream()
        .map(Entry::toString)
        .collect(joining("\n\t\t . ")));

    CharArrayWriter stderr = new CharArrayWriter();
    try (Reader source = new InputStreamReader(new FileInputStream(rte), UTF_8);
        InputStream stdin = new FileInputStream("./src/test/resources/edierror/input/ORDERS_ERR_1.txt");
        RteRuntime runtime = runtime(source, stderr, properties)) {

      // i/o
      runtime.setStdin(stdin);
      CharArrayWriter stdout = new CharArrayWriter();
      runtime.setStdout(stdout);

      // executes
      runtime.execute();

      // results
      LOGGER.info("STDOUT:\n{}", stdout);
      LOGGER.error("STDERR:\n{}", stderr);

    }
    catch (IOException e) {
      throw new UncheckedIOException(e);
    }
    catch (RteCompilationException | RteException | RteInterpreterException | RteInternalException e) {
      LOGGER.error("\t > end on error : " + e.getMessage(), e);
    }
    finally {
      LOGGER.info("\t > end with : {}ms", currentTimeMillis() - start);
    }
  }
}
