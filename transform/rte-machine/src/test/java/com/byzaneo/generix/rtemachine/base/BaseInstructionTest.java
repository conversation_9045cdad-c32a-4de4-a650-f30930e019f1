package com.byzaneo.generix.rtemachine.base;

import static java.nio.charset.StandardCharsets.UTF_8;
import static org.junit.jupiter.api.Assertions.*;

import java.io.ByteArrayInputStream;
import java.io.CharArrayWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.util.Properties;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import com.byzaneo.generix.rtemachine.RteCompiler;
import com.byzaneo.generix.rtemachine.RteRuntime;
import com.byzaneo.generix.rtemachine.base.configclassparameters.BaseOptions;
import com.byzaneo.generix.rtemachine.context.BaseVariable;
import com.byzaneo.generix.rtemachine.context.Memory;
import com.byzaneo.generix.rtemachine.exception.RteCompilationException;
import com.byzaneo.generix.rtemachine.exception.RteRuntimeException;
import com.byzaneo.generix.rtemachine.util.RteBaseExtensionFileHelper;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@Disabled
class BaseInstructionTest {

  private final String cfgFile = " \"/testBase/config/completeCfg.cfg\" ";
  private final String EDIHOME = "src/main/resources/RTE_HOME/PUBLIC";
  private final String HOME = "src/main/resources/RTE_HOME/CUSTOMERS/ENV/SOC";
  private final String RTE_FILE = "src/main/resources/RTE_HOME/CUSTOMERS/ENV/SOC/rte/fake.rte";

  private Properties properties;

  @BeforeEach
  void initProperties() throws IOException {
    properties = new Properties();
    properties = addProperties("r=" + RTE_FILE);
  }

  @Test
  void absolutePathShouldBeOK() throws Exception {
    RteRuntime runtime = new RteRuntime(properties);
    String cfgUrl = RteBaseExtensionFileHelper.getRightCfgFile("/testBase/config/completeCfg.cfg", runtime);
    assertEquals(Paths.get(cfgUrl), Paths.get(HOME, "rte", "testBase", "config", "completeCfg.cfg"));
  }

  @Test
  void relatifPathStartingWithPointShouldBeOK() throws Exception {
    RteRuntime runtime = new RteRuntime(properties);
    String cfgUrl = RteBaseExtensionFileHelper.getRightCfgFile("./testBase/config/completeCfg.cfg", runtime);
    assertEquals(Paths.get(cfgUrl), Paths.get(HOME, "rte", "testBase", "config", "completeCfg.cfg"));
  }

  @Test
  void relatifPathStartingWithPointAndBackslashShouldBeOK() throws Exception {
    RteRuntime runtime = new RteRuntime(properties);
    String cfgUrl = RteBaseExtensionFileHelper.getRightCfgFile(".\\testBase\\config\\completeCfg.cfg", runtime);
    assertEquals(Paths.get(cfgUrl), Paths.get(HOME, "rte", "testBase", "config", "completeCfg.cfg"));
  }

  @Test
  void relatifPathShouldBeOK() throws Exception {
    RteRuntime runtime = new RteRuntime(properties);
    String cfgUrl = RteBaseExtensionFileHelper.getRightCfgFile("testBase/config/completeCfg.cfg", runtime);
    assertEquals(Paths.get(cfgUrl), Paths.get(HOME, "rte", "testBase", "config", "completeCfg.cfg"));

  }

  @Test
  void relatifPathWithBackslashShouldBeOK() throws Exception {
    RteRuntime runtime = new RteRuntime(properties);
    String cfgUrl = RteBaseExtensionFileHelper.getRightCfgFile("testBase\\config\\completeCfg.cfg", runtime);
    assertEquals(Paths.get(cfgUrl), Paths.get(HOME, "rte", "testBase", "config", "completeCfg.cfg"));

  }

  @Test
  void cfgToFindInHOME() throws Exception {
    properties = addProperties("HOME=" + HOME);
    RteRuntime runtime = new RteRuntime(properties);
    String cfgUrl = RteBaseExtensionFileHelper.getRightCfgFile("\"example.cfg\"", runtime);
    assertEquals(Paths.get(cfgUrl), Paths.get(HOME, "database", "example.cfg"));

  }

  @Test
  void cfgToFindInEDIHOME() throws Exception {
    properties = addProperties("EDIHOME=" + EDIHOME);
    RteRuntime runtime = new RteRuntime(properties);
    String cfgUrl = RteBaseExtensionFileHelper.getRightCfgFile("\"publicExample.cfg\"", runtime);
    assertEquals(Paths.get(cfgUrl), Paths.get(EDIHOME, "database", "publicExample.cfg"));
  }

  @Test
  void shouldTakeTheEdiHomeConfigFile() throws Exception {
    properties = addProperties("EDIHOME=" + EDIHOME, "HOME=" + HOME);
    RteRuntime runtime = new RteRuntime(properties);
    String cfgUrl = RteBaseExtensionFileHelper.getRightCfgFile("\"publicExample.cfg\"", runtime);
    assertEquals(Paths.get(cfgUrl), Paths.get(EDIHOME, "database", "publicExample.cfg"));
  }

  @Test
  void shouldTakeTheHomeConfigFile() throws Exception {
    properties = addProperties("EDIHOME=" + EDIHOME, "HOME=" + HOME);
    RteRuntime runtime = new RteRuntime(properties);
    String cfgUrl = RteBaseExtensionFileHelper.getRightCfgFile("\"example.cfg\"", runtime);
    assertEquals(Paths.get(cfgUrl), Paths.get(HOME, "database", "example.cfg"));
  }

  @Test
  void shouldReturnNull() throws Exception {
    RteRuntime runtime = new RteRuntime(properties);
    String cfgUrlHome = RteBaseExtensionFileHelper.getRightCfgFile("\"example.cfg\"", runtime);
    String cfgUrlEdiHome = RteBaseExtensionFileHelper.getRightCfgFile("\"publicExample.cfg\"", runtime);
    assertNull(cfgUrlHome);
    assertNull(cfgUrlEdiHome);
  }

  @Test
  void shouldReturnNullFileNotFoundInEdiHome() throws Exception {
    properties = addProperties("EDIHOME=" + EDIHOME);
    RteRuntime runtime = new RteRuntime(properties);
    String cfgUrl = RteBaseExtensionFileHelper.getRightCfgFile("\"doNotExists.cfg\"", runtime);
    assertNull(cfgUrl);
  }

  @Test
  void shouldReturnNullFileNotFoundInHome() throws Exception {
    properties = addProperties("HOME=" + HOME);
    RteRuntime runtime = new RteRuntime(properties);
    String cfgUrl = RteBaseExtensionFileHelper.getRightCfgFile("\"doNotExists.cfg\"", runtime);
    assertNull(cfgUrl);
  }

  // ####### One Entry - Synthaxe tests

  @Test
  void triUpperCaseEntry() throws Exception {
    // base "completeCfg.cfg" ABCDEFG
    File temp = File.createTempFile("basicCommand", ".rte");
    String baseInstruction = "base" + cfgFile + "ABC";
    FileUtils.writeStringToFile(temp, baseInstruction, StandardCharsets.UTF_8);

    BaseVariable baseVariable = getBaseVariableFromMemory(process(temp), "ABC");

    assertTrue(baseVariable != null);
    assertTrue(isBaseCreated(baseVariable));
    assertTrue(areOptionDefault(baseVariable));

  }

  @Test
  void upperCaseEntry() throws Exception {
    // base "completeCfg.cfg" ABCDEFG
    File temp = File.createTempFile("basicCommand", ".rte");
    String baseInstruction = "base" + cfgFile + "ABCDEFG ";
    FileUtils.writeStringToFile(temp, baseInstruction, StandardCharsets.UTF_8);

    BaseVariable baseVariable = getBaseVariableFromMemory(process(temp), "ABCDEFG");

    assertTrue(baseVariable != null);
    assertTrue(isBaseCreated(baseVariable));
    assertTrue(areOptionDefault(baseVariable));

  }

  @Test
  void entryNameStartingWithAnSShouldCompil() throws Exception {
    // base "completeCfg.cfg" ABCDEFG
    File temp = File.createTempFile("basicCommand", ".rte");
    String baseInstruction = "base" + cfgFile + "SABCD ";
    FileUtils.writeStringToFile(temp, baseInstruction, StandardCharsets.UTF_8);

    BaseVariable baseVariable = getBaseVariableFromMemory(process(temp), "SABCD");

    assertTrue(baseVariable != null);
    assertTrue(isBaseCreated(baseVariable));
    assertTrue(areOptionDefault(baseVariable));

  }

  @Test
  void lowerCaseEntry() throws Exception {
    // base "completeCfg.cfg" ABCDEFG
    File temp = File.createTempFile("basicCommand", ".rte");
    String baseInstruction = "base" + cfgFile + "abcd ";
    FileUtils.writeStringToFile(temp, baseInstruction, StandardCharsets.UTF_8);

    BaseVariable baseVariable = getBaseVariableFromMemory(process(temp), "abcd");

    assertTrue(baseVariable != null);
    assertTrue(isBaseCreated(baseVariable));
    assertTrue(areOptionDefault(baseVariable));

  }

  @Test
  void upperAndLowerCaseEntry() throws Exception {
    // base "completeCfg.cfg" ABcdeFG
    File temp = File.createTempFile("basicCommand", ".rte");
    String baseInstruction = "base" + cfgFile + "ABcdeFG ";
    FileUtils.writeStringToFile(temp, baseInstruction, StandardCharsets.UTF_8);

    BaseVariable baseVariable = getBaseVariableFromMemory(process(temp), "ABcdeFG");

    assertTrue(baseVariable != null);
    assertTrue(isBaseCreated(baseVariable));
    assertTrue(areOptionDefault(baseVariable));

  }

  @Test
  void lowerAndUpperCaseEntry() throws Exception {
    // base "completeCfg.cfg" abCd
    File temp = File.createTempFile("basicCommand", ".rte");
    String baseInstruction = "base" + cfgFile + "abCd ";
    FileUtils.writeStringToFile(temp, baseInstruction, StandardCharsets.UTF_8);

    BaseVariable baseVariable = getBaseVariableFromMemory(process(temp), "abCd");

    assertTrue(baseVariable != null);
    assertTrue(isBaseCreated(baseVariable));
    assertTrue(areOptionDefault(baseVariable));

  }

  @Test
  void underscoreEntry() throws Exception {
    // base "completeCfg.cfg" _a_B_c_
    File temp = File.createTempFile("basicCommand", ".rte");
    String baseInstruction = "base" + cfgFile + "_a_B_c_ ";
    FileUtils.writeStringToFile(temp, baseInstruction, StandardCharsets.UTF_8);

    BaseVariable baseVariable = getBaseVariableFromMemory(process(temp), "_a_B_c_");

    assertTrue(baseVariable != null);
    assertTrue(isBaseCreated(baseVariable));
    assertTrue(areOptionDefault(baseVariable));

  }

  // ####### Solo Entry - single options tests

  @Test
  void autoCommitOption() throws Exception {
    // base "completeCfg.cfg" ABCDEFG autocommit on
    File temp = File.createTempFile("basicCommand", ".rte");
    String baseInstruction = "base" + cfgFile + "ABCDEFG autocommit on";
    FileUtils.writeStringToFile(temp, baseInstruction, StandardCharsets.UTF_8);

    BaseVariable baseVariable = getBaseVariableFromMemory(process(temp), "ABCDEFG");

    assertTrue(baseVariable != null);
    assertTrue(isBaseCreated(baseVariable));
    assertTrue(isAutoCommitOn(baseVariable));

  }

  @Test
  void autoCommitOptionWithoutOnOff() throws IOException {
    File temp = File.createTempFile("basicCommand", ".rte");
    String baseInstruction = "base" + cfgFile + "ABCDEFG autocommit";
    FileUtils.writeStringToFile(temp, baseInstruction, StandardCharsets.UTF_8);
    assertThrows(RteCompilationException.class, () ->
      process(temp));

  }

  @Test
  void autoFlushOption() throws Exception {
    // base "mydatabase.cfg" ABCDEFG autoflush off
    File temp = File.createTempFile("basicCommand", ".rte");
    String baseInstruction = "base" + cfgFile + "ABCDEFG autoflush off";
    FileUtils.writeStringToFile(temp, baseInstruction, StandardCharsets.UTF_8);

    BaseVariable baseVariable = getBaseVariableFromMemory(process(temp), "ABCDEFG");

    assertTrue(baseVariable != null);
    assertTrue(isBaseCreated(baseVariable));
    assertFalse(isAutoFlushOn(baseVariable));
  }

  @Test
  void autoFlushOptionWithoutOnOff() throws IOException {
    File temp = File.createTempFile("basicCommand", ".rte");
    String baseInstruction = "base" + cfgFile + "ABCDEFG autoflush";
    FileUtils.writeStringToFile(temp, baseInstruction, StandardCharsets.UTF_8);
    assertThrows(RteCompilationException.class, () ->
      process(temp));
  }

  @Test
  void readOnlyOption() throws Exception {
    // base "mydatabase.cfg" ABCDEFG readonly
    File temp = File.createTempFile("basicCommand", ".rte");
    String baseInstruction = "base" + cfgFile + "ABCDEFG readonly";
    FileUtils.writeStringToFile(temp, baseInstruction, StandardCharsets.UTF_8);

    BaseVariable baseVariable = getBaseVariableFromMemory(process(temp), "ABCDEFG");

    assertTrue(baseVariable != null);
    assertTrue(isBaseCreated(baseVariable));
    assertTrue(isReadyOn(baseVariable));
  }

  @Test
  void readOnlyOptionWithOn() throws IOException {
    File temp = File.createTempFile("basicCommand", ".rte");
    String baseInstruction = "base" + cfgFile + "ABCDEFG readonly on";
    FileUtils.writeStringToFile(temp, baseInstruction, StandardCharsets.UTF_8);
    assertThrows(RteCompilationException.class, () ->
      process(temp));
  }

  // ####### Multiple Entry - Synthaxe tests

  @Test
  void twoBaseWithThreeUpperDeclaration() throws Exception {
    // base "mydatabase.cfg" ABC DEF
    File temp = File.createTempFile("basicCommand", ".rte");
    String baseInstruction = "base" + cfgFile + "ABC DEF";
    FileUtils.writeStringToFile(temp, baseInstruction, StandardCharsets.UTF_8);

    BaseVariable baseVariable = getBaseVariableFromMemory(process(temp), "ABC");

    assertTrue(baseVariable != null);
    assertTrue(isBaseCreated(baseVariable));
    assertTrue(areOptionDefault(baseVariable));

    BaseVariable baseVariable2 = getBaseVariableFromMemory(process(temp), "DEF");

    assertTrue(baseVariable2 != null);
    assertTrue(isBaseCreated(baseVariable2));
    assertTrue(areOptionDefault(baseVariable2));

  }

  @Test
  void multipleWithTwoBaseDeclarationAndOption() throws Exception {
    // base "mydatabase.cfg" AB_C DaeEF
    File temp = File.createTempFile("basicCommand", ".rte");
    String baseInstruction = "base" + cfgFile + "AB_C DaeEF";
    FileUtils.writeStringToFile(temp, baseInstruction, StandardCharsets.UTF_8);

    BaseVariable baseVariable = getBaseVariableFromMemory(process(temp), "AB_C");

    assertTrue(baseVariable != null);
    assertTrue(isBaseCreated(baseVariable));
    assertTrue(areOptionDefault(baseVariable));

    BaseVariable baseVariable2 = getBaseVariableFromMemory(process(temp), "DaeEF");

    assertTrue(baseVariable2 != null);
    assertTrue(isBaseCreated(baseVariable2));
    assertTrue(areOptionDefault(baseVariable2));

  }

  // ####### Multiple Entry - Synthaxe tests

  @Test
  void multipleWithTwoBaseDeclarationAndMultipleOption() throws Exception {
    // base "mydatabase.cfg" ABC DEF autocommit on readonly autoflush off
    File temp = File.createTempFile("basicCommand", ".rte");
    String baseInstruction = "base" + cfgFile + "aedde TOTO autocommit on readonly autoflush off";
    FileUtils.writeStringToFile(temp, baseInstruction, StandardCharsets.UTF_8);

    BaseVariable baseVariable = getBaseVariableFromMemory(process(temp), "aedde");

    assertTrue(baseVariable != null);
    assertTrue(isBaseCreated(baseVariable));
    assertFalse(isAutoFlushOn(baseVariable));
    assertTrue(isReadyOn(baseVariable));
    assertTrue(isAutoCommitOn(baseVariable));

    BaseVariable baseVariable2 = getBaseVariableFromMemory(process(temp), "TOTO");

    assertTrue(baseVariable2 != null);
    assertTrue(isBaseCreated(baseVariable2));
    assertFalse(isAutoFlushOn(baseVariable2));
    assertTrue(isReadyOn(baseVariable2));
    assertTrue(isAutoCommitOn(baseVariable2));

  }

  @Test
  void TestWhiteSpace() throws Exception {
    // base ABCDEFG autoflush off
    File temp = File.createTempFile("basicCommand", ".rte");
    String baseInstruction = "   base " + cfgFile + "  ABCDEFG   autoflush    off  ";
    FileUtils.writeStringToFile(temp, baseInstruction, StandardCharsets.UTF_8);

    BaseVariable baseVariable = getBaseVariableFromMemory(process(temp), "ABCDEFG");
    assertTrue(baseVariable != null);
    assertTrue(isBaseCreated(baseVariable));
    assertFalse(isAutoFlushOn(baseVariable));
  }

  private Memory process(File rte) throws Exception {
    final String rteName = FilenameUtils.getBaseName(rte.getName());

    try (Reader source = new InputStreamReader(new FileInputStream(rte), UTF_8);
        InputStream stdin = getInputStream(rteName)) {
      RteCompiler poc = new RteCompiler();
      Properties properties = new Properties();
      properties.setProperty(RteRuntime.INCLUDE_FOLDER_PATH,
          Paths.get("./src/test/resources/include")
              .toString());

      InputStream propertiesStream = getPropertiesStream(rteName);

      if (propertiesStream != null) {
        properties.load(propertiesStream);
      }

      InputStream rteProperties = new ByteArrayInputStream(("r=" + RTE_FILE).getBytes());
      properties.load(rteProperties);

      RteRuntime runtime = poc.compile(source, properties);

      runtime.setStdin(stdin);

      CharArrayWriter stdout = new CharArrayWriter();
      runtime.setStdout(stdout);

      CharArrayWriter stderr = new CharArrayWriter();
      runtime.setStderr(stderr);

      runtime.execute();

      return runtime.getMemory();
    }
  }

  private InputStream getPropertiesStream(final String filename) throws FileNotFoundException {
    File file = new File("./src/test/resources/config/" + filename + ".properties");
    return file.exists() ? new FileInputStream(file) : null;
  }

  private InputStream getInputStream(final String filename) throws FileNotFoundException {
    File file = new File("./src/test/resources/input/" + filename);
    return file.exists() ? new FileInputStream(file) : null;
  }

  private BaseVariable getBaseVariableFromMemory(Memory memory, String identifier) throws RteRuntimeException {
    return memory.getBase(identifier);
  }

  private boolean areOptionDefault(BaseVariable baseVariable) throws RteRuntimeException {
    BaseOptions options = baseVariable.getBase()
        .getBeanDescriptor()
        .getOptions();
    return (options.isReadonlyOption() == BaseOptions.READONLYOPTION_DEFAULT) &&
        (options.isAutoflushOption() == BaseOptions.AUTOFLUSHOPTION_DEFAULT) &&
        (options.isAutoCommitOption() == BaseOptions.AUTOCOMMITOPTION_DEFAULT);
  }

  private boolean isAutoFlushOn(BaseVariable baseVariable) {
    return baseVariable.getBase()
        .getBeanDescriptor()
        .getOptions()
        .isAutoflushOption();
  }

  private boolean isReadyOn(BaseVariable baseVariable) {
    return baseVariable.getBase()
        .getBeanDescriptor()
        .getOptions()
        .isReadonlyOption();
  }

  private boolean isAutoCommitOn(BaseVariable baseVariable) {
    return baseVariable.getBase()
        .getBeanDescriptor()
        .getOptions()
        .isAutoCommitOption();
  }

  private boolean isBaseCreated(BaseVariable baseVariable) throws RteRuntimeException {
    return baseVariable.getBase() != null;
  }

  // private File createTemporaryFileWithBaseInstruction(String RteFileName, String cfgUrl) throws IOException{
  // File temp = File.createTempFile("RteFileName", ".rte");
  // String baseInstruction = "base" + cfgUrl + "ENTRY";
  // FileUtils.writeStringToFile(temp, baseInstruction, StandardCharsets.UTF_8);
  // return temp;
  // }

  private Properties addProperties(String... args) throws IOException {
    for (String arg : args) {
      InputStream is = new ByteArrayInputStream(arg.getBytes());
      properties.load(is);
    }
    return properties;
  }

}
