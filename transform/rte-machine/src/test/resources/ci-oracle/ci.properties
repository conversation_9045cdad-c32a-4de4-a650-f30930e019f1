# -- D A T A B A S E --
database.flyway.disable = true
database.schema=GNX
database.type=oracle
database.driver=oracle.jdbc.OracleDriver
database.url=***********************************
database.username=GNX
database.password=GNXORACLE
database.target=com.byzaneo.commons.dao.hibernate.support.Oracle11gDialect
database.datasource=pooledDataSource

database.view.user=GNX
database.view.password=GNXORACLE
database.view.timeout=300

# - DDL -
# validate | update | create | create-drop
database.generateDdl		= true
database.generateDdl.mode	= create-drop
database.process.generateDdl = drop-create
database.generateDdl.imports=/META-INF/data/security.${database.type}.sql,/META-INF/data/gnx.${database.type}.sql,/META-INF/data/gnx-test.${database.type}.sql

# -- I N D E X A T I O N --
# MONGODB

index.mongo.uri=mongodb://ci:ci@ci-mongo/ci
