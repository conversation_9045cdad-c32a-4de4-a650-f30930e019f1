#====================================================================
#
#                              UN/EDIFACT
#
#                UNITED NATIONS STANDARD MESSAGE (UNSM)
#
#                  Waste disposal information message
#
#
#
#
#====================================================================
MESSAGE=WASDIS
VERSION=D
RELEASE=01B
AGENCY=UN
COMMENT
ENDCOMMENT

#====================================================================
SEGMENTS segs
#====================================================================
UNH M 1 Message header                            
BGM M 1 Beginning of message                      
DTM C 9 Date/time/period                          
group 1 C 9
  RFF M 1 Reference                                 
  DTM C 9 Date/time/period                          
endgroup 1
group 2 M 1
  LOC M 1 Place/location identification             
  DTM M 1 Date/time/period                          
  GOR M 1 Governmental requirements                 
endgroup 2
group 3 M 1
  TDT M 1 Details of transport                      
  DTM M 1 Date/time/period                          
  LOC M 2 Place/location identification             
  RFF C 2 Reference                                 
  MEA C 1 Measurements                              
endgroup 3
group 4 C 1
  NAD M 1 Name and address                          
  group 5 C 1
    CTA M 1 Contact information                       
    COM C 3 Communication contact                     
  endgroup 5
endgroup 4
group 6 C 99
  DGS M 1 Dangerous goods                           
  MEA C 1 Measurements                              
  SGP C 999 Split goods placement                     
endgroup 6
UNT M 1 Message trailer                           

#====================================================================
USAGE
#====================================================================
![UNH]
UNH, Message header
A service segment starting and uniquely identifying a message.
The message type code for the Waste disposal information
message is WASDIS.
Note: Waste disposal information messages conforming to this
document must contain the following data in segment UNH,
composite S009:
Data element  0065 WASDIS
              0052 D
              0054 01B
              0051 UN
![BGM]
BGM, Beginning of message
A segment to indicate the beginning of a message and to
transmit the identifying number.
![DTM]
DTM, Date/time/period
A segment to provide the date and time of the message.
![g1]
Segment group 1:  RFF-DTM
A group of segments to specify a reference applying to the
whole message.
![RFF 1]
RFF, Reference
A segment to specify a reference, e.g. reference to previous
message.
![DTM 1]
DTM, Date/time/period
A segment to indicate a date and time related to the
reference.
![g2]
Segment group 2:  LOC-DTM-GOR
A group of segments to give information about the inspection of
the means of transport and the associated results.
![LOC 2]
LOC, Place/location identification
A segment to specify the place where the means of transport
has been inspected.
![DTM 2]
DTM, Date/time/period
A segment to indicate the date and time of the inspection.
![GOR 2]
GOR, Governmental requirements
A segment to indicate the government agency involved (e.g.
Maritime pollution inspection agency), the government
involvement (e.g. an indication that a certain procedure is
completed or required) and the government procedure (e.g. to
indicate that charge of waste is required).
![g3]
Segment group 3:  TDT-DTM-LOC-RFF-MEA
A group of segments to indicate the details of the means of
transport, with related locations, arrival dates and times,
measurement, etc.
![TDT 3]
TDT, Details of transport
A segment to identify the means of transport (e.g. by
Lloyd's Register number of a vessel, or by the license plate
number of a truck) and the type of the means of transport.
![DTM 3]
DTM, Date/time/period
A segment to indicate the date and time of departure in the
place or port of call.
![LOC 3]
LOC, Place/location identification
A segment to indicate a location relating to the means of
transport, such as: - place or port of departure - next
place or port of call.
![RFF 3]
RFF, Reference
A segment to specify a reference as an additional
identification of the means of transport, e.g. radio call
sign of a vessel.
![MEA 3]
MEA, Measurements
A segment to specify a measurement of the means of
transport, e.g. gross tonnage (of a vessel).
![g4]
Segment group 4:  NAD-SG5
A group of segments to identify a party which could give
further information on the conveyance of the means of
transport.
![NAD 4]
NAD, Name and address
A segment to identify a party's name, address and function
from which further information can be obtained (e.g. port
authority, harbour master).
![g5]
Segment group 5:  CTA-COM
A group of segments to indicate a contact within the party.
![CTA 5]
CTA, Contact information
A segment to identify a person or department within the
party.
![COM 5]
COM, Communication contact
A segment to identify a communication number of the
contact to whom communication should be directed.
![g6]
Segment group 6:  DGS-MEA-SGP
A group of segments to specify the result of the inspection of
the means of transport, in terms of marine pollutant waste on
board.
![DGS 6]
DGS, Dangerous goods
A segment to specify the type of marine pollutant and its
subdivision according to the MARPOL regulation (i.e.
Maritime Pollution Regulation), as found on board during
inspection. Data element 8273 is to be used to indicate the
MARPOL regulation, regarding the prevention of maritime
pollution by ocean-going vessels issued by the International
Maritime Organization (IMO). Data element 8351 is to be used
to indicate MARPOL type (e.g. I or IV), which identifies the
hazard code. Data element 8078 is to be used to indicate the
MARPOL type subdivision, which identifies an additional
hazard code classification.
![MEA 6]
MEA, Measurements
A segment to specify the net weight of waste to be
discharged, for the MARPOL type and subdivision of goods as
indicated in DGS.
![SGP 6]
SGP, Split goods placement
A segment to indicate the equipment in which the dangerous
goods are loaded.
![UNT]
UNT, Message trailer
A service segment ending a message, giving the total number of
segments in the message (including the UNH & UNT) and the
control reference number of the message.
#====================================================================
