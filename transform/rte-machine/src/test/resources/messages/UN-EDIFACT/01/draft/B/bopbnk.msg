#====================================================================
#
#                              UN/EDIFACT
#
#                UNITED NATIONS STANDARD MESSAGE (UNSM)
#
#      Bank transactions and portfolio transactions report message
#
#
#
#
#====================================================================
MESSAGE=BOPBNK
VERSION=D
RELEASE=01B
AGENCY=UN
COMMENT
ENDCOMMENT

#====================================================================
SEGMENTS segs
#====================================================================
UNH M 1 Message header                            
BGM M 1 Beginning of message                      
DTM M 9 Date/time/period                          
group 1 C 9
  RFF M 1 Reference                                 
  DTM C 1 Date/time/period                          
endgroup 1
group 2 M 9
  NAD M 1 Name and address                          
  CTA C 1 Contact information                       
  COM C 9 Communication contact                     
  FTX C 99 Free text                                 
endgroup 2
group 3 M 999
  RFF M 1 Reference                                 
  CUX C 1 Currencies                                
  MOA C 9 Monetary amount                           
  LOC C 1 Place/location identification             
  group 4 M 999
    RCS M 1 Requirements and conditions               
    FTX C 1 Free text                                 
    group 5 M 9999
      MOA M 1 Monetary amount                           
      ATT C 1 Attribute                                 
      NAD C 1 Name and address                          
      group 6 C 1
        GIR M 1 Related identification numbers            
        QTY C 1 Quantity                                  
        PRI C 1 Price details                             
      endgroup 6
      group 7 C 1
        RFF M 1 Reference                                 
        DTM C 1 Date/time/period                          
      endgroup 7
      LOC M 9 Place/location identification             
    endgroup 5
  endgroup 4
endgroup 3
CNT C 9 Control total                             
UNT M 1 Message trailer                           

#====================================================================
USAGE
#====================================================================
![UNH]
UNH, Message header
A service segment starting and uniquely identifying a message.
The message type code for the Bank transactions and portfolio
transactions report message is BOPBNK.
Note: Bank transactions and portfolio transactions report
messages conforming to this document must contain the following
data in segment UNH, composite S009:
Data element  0065 BOPBNK
              0052 D
              0054 01B
              0051 UN
![BGM]
BGM, Beginning of message
A segment to indicate the type and function of the message and
to transmit its identifying number.
![DTM]
DTM, Date/time/period
A segment to specify the date and, when required, the time of
the creation of the message and optionally to specify other
process dates which apply to the whole message.
![g1]
Segment group 1:  RFF-DTM
A group of segments to specify references which apply to the
whole message and optionally to specify related dates.
![RFF 1]
RFF, Reference
A segment to specify a reference for the message.
![DTM 1]
DTM, Date/time/period
A segment to specify dates related to the reference.
![g2]
Segment group 2:  NAD-CTA-COM-FTX
A group of segments to identify the parties associated with the
message.
![NAD 2]
NAD, Name and address
A segment to identify the resident bank.
![CTA 2]
CTA, Contact information
A segment to identify a person or a department for the party
to whom communication should be directed.
![COM 2]
COM, Communication contact
A segment to specify a communication number for the party,
such as phone or fax number.
![FTX 2]
FTX, Free text
A segment to specify additional free text information
related to the party.
![g3]
Segment group 3:  RFF-CUX-MOA-LOC-SG4
A group of segments to specify the details of the reported
account and the details of the reported individual
transactions.
![RFF 3]
RFF, Reference
A segment to specify the reference of the reported account.
![CUX 3]
CUX, Currencies
A segment to specify the currency of the reported account.
![MOA 3]
MOA, Monetary amount
A segment to specify the opening balance and the closing
balance of the reported account.
![LOC 3]
LOC, Place/location identification
A segment to specify the relevant country related to the
reported account.
![g4]
Segment group 4:  RCS-FTX-SG5
A group of segments to specify information relating to a
transaction or position.
![RCS 4]
RCS, Requirements and conditions
A segment to specify the reason for the transaction or
the type of position.
![FTX 4]
FTX, Free text
A segment to specify information in clear and free form
to provide explanations about the reason for the
transaction or the position.
![g5]
Segment group 5:  MOA-ATT-NAD-SG6-SG7-LOC
A group of segments to specify the amount and details
associated with each different reason of transaction or
position.
![MOA 5]
MOA, Monetary amount
A segment to specify the amount, and if necessary the
currency, of the transaction or of the position.
![ATT 5]
ATT, Attribute
A segment to specify the type of the reported amount.
![NAD 5]
NAD, Name and address
A segment to specify the identification of additional
related parties such as the Payor, the Payee or a
third party).
![g6]
Segment group 6:  GIR-QTY-PRI
A group of segments to specify the details related to
transactions on financial securities.
![GIR 6]
GIR, Related identification numbers
A segment to identify the type of securities
(shares, bonds, etc).
![QTY 6]
QTY, Quantity
A segment to specify the quantity of the security.
![PRI 6]
PRI, Price details
A segment to specify the face value of the
security.
![g7]
Segment group 7:  RFF-DTM
A group of segments to specify the references and
dates of the transaction.
![RFF 7]
RFF, Reference
A segment to specify the serial number of the
transaction.
![DTM 7]
DTM, Date/time/period
A segment to specify the date of the transaction.
![LOC 5]
LOC, Place/location identification
A segment to identify countries involved in the
transaction, such country of origin or destination of
the goods, direct investment country, donation acting
country, payment transaction country (creditor or
debtor) or the country in which the construction work
is done.
![CNT]
CNT, Control total
A segment to specify total values for control purposes.
![UNT]
UNT, Message trailer
A service segment ending a message, giving the total number of
segments in the message (including the UNH & UNT) and the
control reference number of the message.
#====================================================================
