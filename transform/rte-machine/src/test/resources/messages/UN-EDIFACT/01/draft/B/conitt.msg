#====================================================================
#
#                              UN/EDIFACT
#
#                UNITED NATIONS STANDARD MESSAGE (UNSM)
#
#                     Invitation to tender message
#
#
#
#
#====================================================================
MESSAGE=CONITT
VERSION=D
RELEASE=01B
AGENCY=UN
COMMENT
ENDCOMMENT

#====================================================================
SEGMENTS segs
#====================================================================
UNH M 1 Message header                            
BGM M 1 Beginning of message                      
RFF M 1 Reference                                 
DTM M 1 Date/time/period                          
AUT C 2 Authentication result                     
AGR C 2 Agreement identification                  
group 1 C 1000
  IND M 1 Index details                             
  RCS M 1 Requirements and conditions               
  group 2 M 1
    GIS M 1 General indicator                         
    group 3 M 1
      BII M 1 Structure identification                  
      IMD M 9 Item description                          
    endgroup 3
  endgroup 2
endgroup 1
group 4 C 1000
  BII M 1 Structure identification                  
  RCS M 1 Requirements and conditions               
  GIS M 10 General indicator                         
  NAD C 1 Name and address                          
  LOC C 1 Place/location identification             
  APR C 1 Additional price information              
  ALI C 2 Additional information                    
  QTY C 5 Quantity                                  
  DTM C 10 Date/time/period                          
  RTE C 9 Rate details                              
  group 5 C 10
    RFF M 1 Reference                                 
    DTM C 2 Date/time/period                          
    GIS C 5 General indicator                         
    FTX C 5 Free text                                 
  endgroup 5
  group 6 C 5
    CUX M 1 Currencies                                
    DTM C 5 Date/time/period                          
    FTX C 1 Free text                                 
  endgroup 6
  group 7 C 2
    ALC M 1 Allowance or charge                       
    RFF C 1 Reference                                 
    DTM C 1 Date/time/period                          
    RNG C 1 Range details                             
    FTX C 10 Free text                                 
    group 8 C 20
      PCD M 1 Percentage details                        
      RFF C 1 Reference                                 
    endgroup 8
  endgroup 7
  group 9 C 100
    RCS M 1 Requirements and conditions               
    BII M 1 Structure identification                  
    FTX C 9 Free text                                 
  endgroup 9
  group 10 M 100
    ARD M 1 Monetary amount function                  
    MOA M 6 Monetary amount                           
    FTX C 10 Free text                                 
    group 11 C 5
      TAX M 1 Duty/tax/fee details                      
      MOA C 1 Monetary amount                           
      LOC C 5 Place/location identification             
    endgroup 11
    group 12 C 15
      ALC M 1 Allowance or charge                       
      ALI C 5 Additional information                    
      group 13 C 1
        QTY M 1 Quantity                                  
        RNG C 1 Range details                             
      endgroup 13
      group 14 C 1
        PCD M 1 Percentage details                        
        RNG C 1 Range details                             
      endgroup 14
      group 15 C 1
        MOA M 1 Monetary amount                           
        RNG C 1 Range details                             
      endgroup 15
      group 16 C 1
        RTE M 1 Rate details                              
        RNG C 1 Range details                             
      endgroup 16
      group 17 C 5
        TAX M 1 Duty/tax/fee details                      
        MOA C 1 Monetary amount                           
      endgroup 17
    endgroup 12
  endgroup 10
  group 18 C 20
    NAD M 1 Name and address                          
    LOC C 25 Place/location identification             
    FII C 9 Financial institution information         
    FTX C 5 Free text                                 
    group 19 C 5
      RFF M 1 Reference                                 
      DTM C 5 Date/time/period                          
    endgroup 19
    group 20 C 5
      DOC M 1 Document/message details                  
      DTM C 5 Date/time/period                          
    endgroup 20
    group 21 C 5
      CTA M 1 Contact information                       
      COM C 5 Communication contact                     
    endgroup 21
  endgroup 18
endgroup 4
UNS M 1 Section control                           
group 22 M 1000
  BII M 1 Structure identification                  
  RCS M 1 Requirements and conditions               
  GIS M 10 General indicator                         
  group 23 C 10
    RFF M 1 Reference                                 
    DTM C 1 Date/time/period                          
  endgroup 23
  group 24 C 100
    DIM M 1 Dimensions                                
    APR C 1 Additional price information              
    FTX C 1 Free text                                 
  endgroup 24
  group 25 C 100
    LIN M 1 Line item                                 
    group 26 C 1000
      IMD M 1 Item description                          
      RFF C 5 Reference                                 
      GIS C 5 General indicator                         
    endgroup 26
  endgroup 25
  group 27 M 1000
    QTY M 1 Quantity                                  
    GIS C 3 General indicator                         
    APR C 1 Additional price information              
    group 28 C 3
      PRI M 1 Price details                             
      GIS M 3 General indicator                         
      group 29 C 2
        ARD M 1 Monetary amount function                  
        MOA C 1 Monetary amount                           
      endgroup 29
    endgroup 28
  endgroup 27
  group 30 C 5
    TAX M 1 Duty/tax/fee details                      
    MOA C 1 Monetary amount                           
    LOC C 5 Place/location identification             
  endgroup 30
  group 31 C 5
    RCS M 1 Requirements and conditions               
    BII M 1 Structure identification                  
    GIS M 1 General indicator                         
  endgroup 31
  group 32 C 2
    ALC M 1 Allowance or charge                       
    RFF C 1 Reference                                 
    DTM C 1 Date/time/period                          
    RNG C 1 Range details                             
    FTX C 10 Free text                                 
    group 33 C 20
      PCD M 1 Percentage details                        
      RFF C 1 Reference                                 
    endgroup 33
  endgroup 32
  group 34 C 9
    IMD M 1 Item description                          
    QTY C 1 Quantity                                  
    MOA C 1 Monetary amount                           
    PRI C 1 Price details                             
  endgroup 34
endgroup 22
CNT C 10 Control total                             
UNT M 1 Message trailer                           

#====================================================================
USAGE
#====================================================================
![UNH]
UNH, Message header
A service segment starting and uniquely identifying a message.
The message type code for the Invitation to tender message is
CONITT.
Note: Invitation to tender messages conforming to this document
must contain the following data in segment UNH, composite S009:
Data element  0065 CONITT
              0052 D
              0054 01B
              0051 UN
![BGM]
BGM, Beginning of message
A segment which identifies the document name and the message
function code.
![RFF]
RFF, Reference
This segment is used to identify the unique project reference
number.
![DTM]
DTM, Date/time/period
The document date will be recorded using this DTM segment.
![AUT]
AUT, Authentication result
A segment used to authenticate the message by exchanging a
password or some other form of identification agreed between
the trading partners.
![AGR]
AGR, Agreement identification
This segment will be used to identify the "type of contract and
form of tender" that apply to this contract.
![g1]
Segment group 1:  IND-RCS-SG2
A group of segments to record both the structure and contents
of the indexing used for the BoQ document.
![IND 1]
IND, Index details
A segment to qualify whether the index applies to the whole
project, part of a project (a group of items), or whether
the index provides an alternative view (analysis) of the
work items. The segment also indicates in the index
qualifier whether it contains index structure information or
describes the index contents.
![RCS 1]
RCS, Requirements and conditions
A segment used to indicate the action request (add, change
etc.) to be applied to the index data.
![g2]
Segment group 2:  GIS-SG3
A group of segments to specify the position and detailed
component parts of the index information.
![GIS 2]
GIS, General indicator
This indicator segment specifies one of six positions
within the index structure to which the detailed
information applies.
![g3]
Segment group 3:  BII-IMD
A group of segments to specify the detailed components of
the index information.
![BII 3]
BII, Structure identification
A segment used to specify a structured index number
that is to be associated with the descriptions that
follow in segment IMD.
![IMD 3]
IMD, Item description
A segment to specify the descriptions in both full
text and abbreviated form . These descriptions to be
associated with the contents of segment BII.
![g4]
Segment group 4:  BII-RCS-GIS-NAD-LOC-APR-ALI-QTY-DTM-RTE-SG5-
                  SG6-SG7-SG9-SG10-SG18
This information is subordinate to the data provided within the
Header section and relates to groups of work items as described
in section 1.3.1 of this document. A segment group recording
all the details that apply to a group of items. There can be
many "Groups of items" within a project and all are to be
recorded within this part of the message.
![BII 4]
BII, Structure identification
A segment used to convey a structured index number that
uniquely identifies each group of items recorded for the
project.
![RCS 4]
RCS, Requirements and conditions
The action to be applied to segment group 4 data is recorded
here.
![GIS 4]
GIS, General indicator
This indicator segment identifies what category of item
grouping.
![NAD 4]
NAD, Name and address
The recording of the name and address information applying
to this group of items.
![LOC 4]
LOC, Place/location identification
In addition to the name and address details above this is
the identification of a location description and an
abbreviated or short name by which the grouping is to be
known.
![APR 4]
APR, Additional price information
A segment to record a multiplication factor called
"Timesing" that applies to the entire project or a specific
grouping of items.
![ALI 4]
ALI, Additional information
This segment would be used to denote, in a coded form, what
rules were adopted for the rounding of all financial values
within this group of items.
![QTY 4]
QTY, Quantity
This segment enables grouping control quantities to be
recorded. These are not to be confused with message control
totals that appear later and use the CNT segment.
![DTM 4]
DTM, Date/time/period
Any date details required for the group of items will be
recorded using the DTM segment. There are a considerable
number of different date qualifiers that could apply.
![RTE 4]
RTE, Rate details
This segment is used to record a cost rate that may apply to
this grouping.
![g5]
Segment group 5:  RFF-DTM-GIS-FTX
Some base data about how the information for this group of
items was compiled. It will identify the standard method
conventions adopted, the author of the data and it will also
refer to any "request for quotation" messages that are to be
read in conjunction .
![RFF 5]
RFF, Reference
This segment will be used to specify a series of
references, including the reference to a "Request for
Quotation" message, standard method identity, the
identity of the author of the data and the identity, if
appropriate any software tool used during the preparation
process.
![DTM 5]
DTM, Date/time/period
The date of the Request for Quotation message that is to
be processed in conjunction with this BoQ message.
![GIS 5]
GIS, General indicator
An entry in this segment will acknowledge or note the
existence of variations to the standard method rules
referenced in the associated RFF.
![FTX 5]
FTX, Free text
In the event of variations to the standard method rules
this segment will provide any necessary narrative to
explain the changes.
![g6]
Segment group 6:  CUX-DTM-FTX
Details of which currency rules apply to this group of
items.
![CUX 6]
CUX, Currencies
Recording details of the currency that applies to the
group of items being defined.
![DTM 6]
DTM, Date/time/period
This segment will be used to recorded any dates that are
applicable to qualify the currency details recorded in
CUX.
![FTX 6]
FTX, Free text
Any narrative necessary to explain or qualify the
currency information.
![g7]
Segment group 7:  ALC-RFF-DTM-RNG-FTX-SG8
A group of segments used to convey details of the price
fluctuation calculations to be applied.
![ALC 7]
ALC, Allowance or charge
A segment to identity whether the data recorded in this
segment group represents an actualization or a revision
figure.
![RFF 7]
RFF, Reference
A segment providing a reference to the (usually standard)
formula rules to be applied.
![DTM 7]
DTM, Date/time/period
A segment to record the contract base or control date to
be used in the formula.
![RNG 7]
RNG, Range details
Where the formula makes reference to data within a range
the details of the range will be identified in this
segment.
![FTX 7]
FTX, Free text
A segment to convey the actual formula and narrative
relating to the formula. This will be used only in the
exceptional case of a non-standard formula being used.
![g8]
Segment group 8:  PCD-RFF
A group of segments to specify the remaining components
of the price fluctuation calculation.
![PCD 8]
PCD, Percentage details
A segment to specify a percentage value to be used as
a coefficient in conjunction with the reference given
in the following RFF.
![RFF 8]
RFF, Reference
A segment to specify the reference of a work section
applicable to a set of calculation components.
![g9]
Segment group 9:  RCS-BII-FTX
This segment group is used to record, in textual form, any
terms and conditions that are deemed to apply to part or all
of the group of items.
![RCS 9]
RCS, Requirements and conditions
The identity of the particular conditions that are to
apply and a coded reference to denote whether this is to
be added or changed etc.
![BII 9]
BII, Structure identification
A structured index reference to identify to what range
within the group of items the conditions apply.
![FTX 9]
FTX, Free text
The narrative description of the condition or term
identified by this segment group.
![g10]
Segment group 10: ARD-MOA-FTX-SG11-SG12
This segment group is used to record any financial values
that apply for a group of items. There are considerable
number of different values and qualifiers that will apply.
![ARD 10]
ARD, Monetary amount function
A segment to record the qualifier to the financial value.
This qualifier is in addition to the qualifier used
within the following MOA segment.
![MOA 10]
MOA, Monetary amount
A segment to record the value of the monetary amount
being defined.
![FTX 10]
FTX, Free text
Any narrative qualification necessary to complete the
details about a financial value will be recorded in this
segment.
![g11]
Segment group 11: TAX-MOA-LOC
Where specific tax rules apply to the entire project or a
grouping within the project they will be recorded in this
segment group.
![TAX 11]
TAX, Duty/tax/fee details
Where the financial value being described within
segment group 10 is a taxation or duty value, details
of the tax or duty will be recorded here.
![MOA 11]
MOA, Monetary amount
A segment to specify the actual monetary amount of the
tax or duty.
![LOC 11]
LOC, Place/location identification
A segment to specify any location details used to
qualify the tax details.
![g12]
Segment group 12: ALC-ALI-SG13-SG14-SG15-SG16-SG17
Where it is necessary to express the components that
contribute to a monetary value this segment group will be
used.
![ALC 12]
ALC, Allowance or charge
A segment identifying the charge or allowance and
where necessary it's calculation sequence.
![ALI 12]
ALI, Additional information
A segment indicating that the allowance or charge
specified is subject to special conditions owing to
origin, customs preference or commercial factors.
![g13]
Segment group 13: QTY-RNG
Components of a financial value expressed in terms of
quantities are recorded in this segment group.
![QTY 13]
QTY, Quantity
Any quantity details used to define or make precise
the monetary value are recorded in this segment.
![RNG 13]
RNG, Range details
Details of the range that applies to the quantity
components are expressed in this segment.
![g14]
Segment group 14: PCD-RNG
Components of a financial value expressed in terms of
a percentage are recorded in this segment group.
![PCD 14]
PCD, Percentage details
This segment is used where the financial
information is expressed as a percentage or a
percentage is required for further qualification.
![RNG 14]
RNG, Range details
Details of the range that applies to the percentage
components are expressed in this segment.
![g15]
Segment group 15: MOA-RNG
Any components of a monetary amount that are
themselves expressed as monetary amounts are recorded
in this segment group.
![MOA 15]
MOA, Monetary amount
The monetary amount component is recorded in this
segment.
![RNG 15]
RNG, Range details
Details of the range that applies to the monetary
amount components are expressed in this segment.
![g16]
Segment group 16: RTE-RNG
Components of a financial value expressed in terms of
a rate are recorded in this segment group.
![RTE 16]
RTE, Rate details
Any rate details used to define or make precise the
monetary value are recorded in this segment.
![RNG 16]
RNG, Range details
Details of the range that applies to the rate
components are expressed in this segment.
![g17]
Segment group 17: TAX-MOA
Components of a financial value expressed in terms of
a tax or duty are recorded in this segment group.
![TAX 17]
TAX, Duty/tax/fee details
Any tax details used to define or make precise the
monetary value are recorded in this segment.
![MOA 17]
MOA, Monetary amount
A segment to specify the actual monetary amount of
the tax or duty component.
![g18]
Segment group 18: NAD-LOC-FII-FTX-SG19-SG20-SG21
Name and address information relating to Parties, Banks and
Contacts are recorded in this segment group.
![NAD 18]
NAD, Name and address
Company name and address that relate to this group of
items and that have not been previously notified are
recorded in this segment. Each name and address recorded
is qualified by the use of the party qualifier 3035.
![LOC 18]
LOC, Place/location identification
A segment used to record more specific location
information of the party specified in the NAD segment,
e.g. a location within a construction site.
![FII 18]
FII, Financial institution information
Bank details associated with these parties recorded in
the preceding NAD segment are given here.
![FTX 18]
FTX, Free text
This segment allows for any narrative that may need to
accompany the name and address information about a
company.
![g19]
Segment group 19: RFF-DTM
This group of segments will record unique reference
numbers by which the party will be known and referenced.
This will ensure that each party is uniquely identified
even if there is more than one party with a similar
qualifier.
![RFF 19]
RFF, Reference
Details of any unique referencing used within the
project to identify the party. Not to be confused with
the party qualifier in the NAD.
![DTM 19]
DTM, Date/time/period
Date and time details that are used to qualify the
party reference number are recorded in this segment.
![g20]
Segment group 20: DOC-DTM
This segment group is used to identify documentation
related specifically to the party being defined.
![DOC 20]
DOC, Document/message details
The identity of the documentation is recorded in this
segment.
![DTM 20]
DTM, Date/time/period
Date details used to qualify the documentation are
recorded in this segment.
![g21]
Segment group 21: CTA-COM
The information about who to contact within each of the
parties detailed in this segment group.
![CTA 21]
CTA, Contact information
This segment will contain the identity of the
individual, within a company, who is the contact
point.
![COM 21]
COM, Communication contact
Here is recorded the telephone number etc. that the
contact can be reached via.
![UNS]
UNS, Section control
A service segment placed at the beginning of the detail section
to avoid collision.
![g22]
Segment group 22: BII-RCS-GIS-SG23-SG24-SG25-SG27-SG30-SG31-
                  SG32-SG34
A group of segments recording all the details that apply to an
item. There can be many items within a project, details of all
items, regardless of where they appear in the BoQ will be
recorded in this segment group.
![BII 22]
BII, Structure identification
A segment used to convey a structured index number that
uniquely identifies each item recorded for the project.
![RCS 22]
RCS, Requirements and conditions
The action to be applied to this segment group data is
recorded here.
![GIS 22]
GIS, General indicator
Through a list of coded qualifiers this segment is used to
define precisely various features about this item e.g.: - it
will distinguish between standard, work or sub item types. -
identify whether this item is of an agreed, disputed or
provisional status. - a qualification of the type of rate
that has been associated with this item. - identifying to
what category this item resides.
![g23]
Segment group 23: RFF-DTM
A group of segments recording additional reference
information against an item of work.
![RFF 23]
RFF, Reference
Where project documentation has been prepared in
accordance with a Standard Methodology the item's
reference to that Standard Method is recorded here. Also
recorded here is the unique reference number assigned to
a sub item. This reference would be used in conjunction
with the item index no. in the BII of this segment group.
![DTM 23]
DTM, Date/time/period
A segment to record a date for the Standard Method of
Measurement reference.
![g24]
Segment group 24: DIM-APR-FTX
A group of segments which may be used where Segment Group 23
refers to a sub-item number. It would contain sets of
dimension details to further describe the quantities.
![DIM 24]
DIM, Dimensions
A segment used to record where applicable, the length,
width and height for portions of work measured.
![APR 24]
APR, Additional price information
A segment to record a multiplication factor called
"Timesing" that applies to a set of dimensions.
![FTX 24]
FTX, Free text
A segment to record an annotation for each set of
dimensions.
![g25]
Segment group 25: LIN-SG26
A group of segments to record descriptive text for items and
headings for groupings of items.
![LIN 25]
LIN, Line item
A segment to record a qualifier as to whether it is a
description for an item or a heading for a group of
items. This would also include a number identifying the
level the description applies to, within a set of
levelled headings.
![g26]
Segment group 26: IMD-RFF-GIS
A group of segments to record the description line
details together with text reference numbers and change
action codes used to identify who is authorised to apply
changes.
![IMD 26]
IMD, Item description
This segment is used to convey any descriptive
material to be recorded for the item. This descriptive
material can apply to the item description and item
heading information. The distinction is explained in
section 1.3 of this paper.
![RFF 26]
RFF, Reference
A segment used to record up to five text reference
numbers per item description line.
![GIS 26]
GIS, General indicator
A segment used to record up to five text change action
codes per item description line.
![g27]
Segment group 27: QTY-GIS-APR-SG28
This segment group is used to record the complex range of
quantity values and prices that apply to an item.
![QTY 27]
QTY, Quantity
Many different quantity values can be applied to an item
during it's execution. All of these quantities will be
recorded in this segment.
![GIS 27]
GIS, General indicator
This segment is used to provide the qualifiers that apply
to the various quantities in the previous segment.
![APR 27]
APR, Additional price information
Item quantities are frequently accompanied by some
multiplicands. This APR segment is used to record these.
![g28]
Segment group 28: PRI-GIS-SG29
A group of segments to record a series of prices and
monetary amounts relating to each QTY value.
![PRI 28]
PRI, Price details
A segment to record up to three price values for each
QTY value.
![GIS 28]
GIS, General indicator
A segment to record up to three qualifiers for each
price.
![g29]
Segment group 29: ARD-MOA
A group of segments to record the monetary amounts,
currency, tax details resulting from the
multiplication of QTY and PRI values.
![ARD 29]
ARD, Monetary amount function
A segment to qualify the monetary amount as to it's
purpose in the business exchange.
![MOA 29]
MOA, Monetary amount
A segment to record the value and currency of the
monetary amount.
![g30]
Segment group 30: TAX-MOA-LOC
A group of segments in which is detailed taxation
information, which applies to the item . There are often
several different types of tax which need to be applied.
![TAX 30]
TAX, Duty/tax/fee details
Details of the taxation to be applied is recorded here.
![MOA 30]
MOA, Monetary amount
A segment to record the value and currency of the
monetary amount.
![LOC 30]
LOC, Place/location identification
A segment to specify location codes relating to the tax
details recorded in this segment group.
![g31]
Segment group 31: RCS-BII-GIS
Where it is necessary to record a relationship about the
current item and it's association with another item, this
segment group will be used.
![RCS 31]
RCS, Requirements and conditions
A segment to identify the requirements that applied to
this particular reference.
![BII 31]
BII, Structure identification
Record here the structured index number of the other item
to which the current item relates.
![GIS 31]
GIS, General indicator
This is the segment where a coded reference describing
the item relationship is recorded.
![g32]
Segment group 32: ALC-RFF-DTM-RNG-FTX-SG33
A group of segments used to convey details of the price
fluctuation calculations to be applied to the item.
![ALC 32]
ALC, Allowance or charge
A segment to identity whether the data recorded in this
segment group represents an actualization or a revision
figure.
![RFF 32]
RFF, Reference
A segment providing a reference to the (usually standard)
formula rules to be applied.
![DTM 32]
DTM, Date/time/period
A segment to record the contract base or control date to
be used in the formula.
![RNG 32]
RNG, Range details
Where the formula makes reference to data within a range
the details of the range will be identified in this
segment.
![FTX 32]
FTX, Free text
A segment to convey the actual formula and narrative
relating to the formula . This will be used only in the
exceptional case of a non- standard formula being used.
![g33]
Segment group 33: PCD-RFF
A group of segments to specify the remaining components
of the price fluctuation calculation.
![PCD 33]
PCD, Percentage details
A segment to specify a percentage value to be used as
a coefficient in conjunction with the reference given
in the following RFF.
![RFF 33]
RFF, Reference
A segment to specify the reference of a work section
applicable to a set of calculation components.
![g34]
Segment group 34: IMD-QTY-MOA-PRI
A segment group to record where necessary alternative index
information for an item. The validity of an alternative
index would be defined in the Segment Group 3 IMD.
![IMD 34]
IMD, Item description
A segment used to record an alternative index description
(previously defined in Segment Group 3 IMD), that relates
to this item.
![QTY 34]
QTY, Quantity
A segment to record the quantity and measurement unit
that relates to the coded entry for the item.
![MOA 34]
MOA, Monetary amount
A segment to record the currency and value associated
with the item.
![PRI 34]
PRI, Price details
A segment to record the monetary rate of the code
associated with the item.
![CNT]
CNT, Control total
Various control total checks may be taken and the resultant
values would be recorded in this segment. Typically these
checks will be counts of quantity and monetary values that
apply to the entire message.
![UNT]
UNT, Message trailer
A service segment ending a message, giving the total number of
segments in the message (including the UNH & UNT) and the
control reference number of the message.
#====================================================================
