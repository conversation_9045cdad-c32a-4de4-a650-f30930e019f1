#====================================================================
#
#                              UN/EDIFACT
#
#                UNITED NATIONS STANDARD MESSAGE (UNSM)
#
#                      Work grant decision message
#
#
#
#
#====================================================================
MESSAGE=WKGRDC
VERSION=D
RELEASE=01B
AGENCY=UN
COMMENT
ENDCOMMENT

#====================================================================
SEGMENTS segs
#====================================================================
UNH M 1 Message header                            
BGM M 1 Beginning of message                      
DTM M 2 Date/time/period                          
group 1 M 3
  PNA M 1 Party identification                      
  ADR C 1 Address                                   
  LOC C 5 Place/location identification             
  DTM C 5 Date/time/period                          
  group 2 C 5
    CTA M 1 Contact information                       
    COM C 5 Communication contact                     
  endgroup 2
endgroup 1
group 3 C 5
  RFF M 1 Reference                                 
  DTM C 5 Date/time/period                          
endgroup 3
group 4 C 5
  GIS M 1 General indicator                         
  FTX C 1 Free text                                 
endgroup 4
UNS M 1 Section control                           
group 5 M 200
  RFF M 1 Reference                                 
  DTM C 5 Date/time/period                          
  group 6 C 1
    PNA M 1 Party identification                      
    ADR C 1 Address                                   
    DTM C 1 Date/time/period                          
    LOC C 1 Place/location identification             
    NAT C 1 Nationality                               
    PDI C 1 Person demographic information            
    DOC C 9 Document/message details                  
  endgroup 6
  FTX M 99 Free text                                 
  group 7 C 5
    GIS M 1 General indicator                         
    FTX C 1 Free text                                 
  endgroup 7
  group 8 C 5
    RFF M 1 Reference                                 
    DTM C 5 Date/time/period                          
  endgroup 8
  group 9 C 10
    RCS M 1 Requirements and conditions               
    RFF C 5 Reference                                 
    DTM C 5 Date/time/period                          
    FTX C 5 Free text                                 
  endgroup 9
  group 10 M 1
    EMP M 1 Employment details                        
    LOC C 10 Place/location identification             
    group 11 C 20
      ATT M 1 Attribute                                 
      FTX C 1 Free text                                 
    endgroup 11
  endgroup 10
  group 12 C 1
    PNA M 1 Party identification                      
    ADR C 1 Address                                   
    LOC C 2 Place/location identification             
    DTM C 5 Date/time/period                          
    FTX C 1 Free text                                 
    group 13 C 5
      CTA M 1 Contact information                       
      COM C 5 Communication contact                     
    endgroup 13
  endgroup 12
endgroup 5
UNT M 1 Message trailer                           

#====================================================================
USAGE
#====================================================================
![UNH]
UNH, Message header
A service segment starting and uniquely identifying a message.
The message type code for the Work grant decision message is
WKGRDC.
Note: Work grant decision messages conforming to this document
must contain the following data in segment UNH, composite S009:
Data element  0065 WKGRDC
              0052 D
              0054 01B
              0051 UN
![BGM]
BGM, Beginning of message
A segment by which the sender must uniquely identify the work
grant decision by means of its type and number and, when
necessary, its function.
![DTM]
DTM, Date/time/period
A segment specifying general dates and, when relevant, times
related to the whole message. The segment must be specified at
least once to identify the document date.
The Date/time/period segment within other Segment groups should
be used whenever the date or time or period requires to be
logically related to another specified data item.
![g1]
Segment group 1:  PNA-ADR-LOC-DTM-SG2
A group of segments identifying the parties with associated
information.
![PNA 1]
PNA, Party identification
A segment identifying names of the parties, in coded or
clear form, and their functions relevant to the Work Grant
Decision Message. Identification of the enterprise and the
job administration parties is mandatory for the Work Grant
Decision Message. 
It is recommended that, where possible, only the coded form
of the party identification should be specified, e.g. where
the enterprise and the job administration are known to each
other, thus only the coded identification is required.
![ADR 1]
ADR, Address
A segment indicating the address of the party identified in
the previous PNA segment.
![LOC 1]
LOC, Place/location identification
A segment giving more specific location information of the
party specified in the ADR segment, e.g. internal site or
building number.
![DTM 1]
DTM, Date/time/period
A segment specifying the relevant date or time.
![g2]
Segment group 2:  CTA-COM
A group of segments giving contact details of the specific
person or department within the party identified in the PNA
segment.
![CTA 2]
CTA, Contact information
A segment to identify a person or department and their
function, to whom communications should be directed.
![COM 2]
COM, Communication contact
A segment to identify a communications type and number
for the contact specified in the CTA segment, e.g.
telephone-number, fax- number, telex-number.
![g3]
Segment group 3:  RFF-DTM
A group of segments for giving references and, where necessary,
their dates, relating to the whole message.
![RFF 3]
RFF, Reference
A segment identifying the reference by its number related to
the whole message.
![DTM 3]
DTM, Date/time/period
A segment specifying the date or time related to the
reference.
![g4]
Segment group 4:  GIS-FTX
A group of segments specifying general processing indicators
and, when necessary, additional textual information, relevant
for the whole message.
![GIS 4]
GIS, General indicator
A segment specifying general indicators for the whole
message.
![FTX 4]
FTX, Free text
A segment with free text information, in coded or clear
form, used when additional information is needed but cannot
be accommodated within other segments. In computer to
computer exchanges such text will normally require the
receiver to process this segment manually.
![UNS]
UNS, Section control
A mandatory service segment placed before the first user
segment in the detail section to avoid segment collision.
![g5]
Segment group 5:  RFF-DTM-SG6-FTX-SG7-SG8-SG9-SG10-SG12
A group of segments providing details of the decision of the
work grant request of a foreigner. There must be at least one
occurrence of Segment group 5 within a Work Grant Decision
Message. This Segment group may be repeated to give information
about the decision of other work grant requests. The Segment
group 5 gives information about the name and the address of the
foreigner, the decision itself and the text of the decision.
The Segment group may also contain the reasons, in case of the
rejection of the work grant request. In case of the positive
grant, information is given for the validity dates of the work
grant and the locations, where the job could be performed.
![RFF 5]
RFF, Reference
A segment identifying the administrative operation of the
work grant request by a number given by the governmental
agency, e.g. work grant number.
![DTM 5]
DTM, Date/time/period
A segment specifying date and, when relevant, time relating
to the work grant decision.
Examples of the use of this DTM segment are: validity dates
of the work grant, when the decision is positive.
![g6]
Segment group 6:  PNA-ADR-DTM-LOC-NAT-PDI-DOC
A group of segments specifying identity and related
information regarding the person expected to be employed.
![PNA 6]
PNA, Party identification
A segment giving the identity, in coded or clear form, of
the person expected to be employed.
![ADR 6]
ADR, Address
A segment identifying the address of a person and its
function relevant to the Work Grant Request Message.
![DTM 6]
DTM, Date/time/period
A segment indicating the date of birth of the person
requested for an job.
![LOC 6]
LOC, Place/location identification
A segment giving more specific location information of
the party specified in the ADR segment.
![NAT 6]
NAT, Nationality
A segment indicating authorized nationality for a person
expected to be employed.
![PDI 6]
PDI, Person demographic information
A segment providing information gender or marital status
of the requested person.
![DOC 6]
DOC, Document/message details
A segment indicating list of documents the person must
have in his possession.
![FTX 5]
FTX, Free text
A segment with free text information for the work grant
decision, in coded or clear form. In computer to computer
exchanges such text will normally require the receiver to
process this segment manually.
![g7]
Segment group 7:  GIS-FTX
A group of segments specifying general processing indicators
and, when necessary, additional textual information,
relevant for the decision.
![GIS 7]
GIS, General indicator
A segment specifying general indicators related to the
decision, e.g. positive decision of the work grant
request or rejection of the work grant request.
![FTX 7]
FTX, Free text
A segment with free text information, in coded or clear
form, used when additional information is needed but
cannot be accommodated within other segments. In computer
to computer exchanges such text will normally require the
receiver to process this segment manually.
![g8]
Segment group 8:  RFF-DTM
A group of segments for giving references of the request
and, other references where and when necessary, their dates,
relevant to the work grant.
![RFF 8]
RFF, Reference
A segment for specifying references relevant to the work
grant, e.g. social insurance number of the foreigner.
![DTM 8]
DTM, Date/time/period
A segment specifying the date or time related to the
reference.
![g9]
Segment group 9:  RCS-RFF-DTM-FTX
A group of segments identifying requirements or conditions
of the rejection of a work grant request.
![RCS 9]
RCS, Requirements and conditions
A segment to give, in coded form, conditions and reasons
for the rejection of a work grant request.
![RFF 9]
RFF, Reference
A segment identifying the referenced document by its
number and, where appropriate, a line number.
![DTM 9]
DTM, Date/time/period
A segment indicating the date or time details relating to
the references.
![FTX 9]
FTX, Free text
A segment with free text information for the reasons of
the rejection of a work grant request, in coded or clear
form, used when additional information is needed but
cannot be accommodated within other segments, e.g.
advertisement of the job. In computer to computer
exchanges such text will normally require the receiver to
process this segment manually.
![g10]
Segment group 10: EMP-LOC-SG11
A group of segments identifying the qualification of a
person and attributes, such as education and experience.
![EMP 10]
EMP, Employment details
A segment to identify the related area or sector of a
qualification and the qualification, in coded or clear
form related to the work grant request.
![LOC 10]
LOC, Place/location identification
A segment indicating the location, where the person is
allowed to perform the job (limitation of the work
locations).
![g11]
Segment group 11: ATT-FTX
A group of segments giving coded information about
attributes of a job regarding educational qualifications
and experience and, when necessary, additional textual
information.
![ATT 11]
ATT, Attribute
A segment identifying attributes of a job. The codes
are related to education, job experience, special
knowledge and required qualifications.
![FTX 11]
FTX, Free text
A segment with free text information for special
knowledge, in coded or clear form, used when
additional information is needed but cannot be
accommodated within other segments. In computer to
computer exchanges such text will normally require the
receiver to process this segment manually.
![g12]
Segment group 12: PNA-ADR-LOC-DTM-FTX-SG13
A group of segments identifying the parties involved with
the work grant request with associated information.
![PNA 12]
PNA, Party identification
A segment identifying names of the parties, in coded or
clear form, and their functions relevant to the work
grant request, e.g. related job center. It is recommended
that, where possible, only the coded form of the party
identification should be specified.
![ADR 12]
ADR, Address
A segment indicating the address of the relevant party.
![LOC 12]
LOC, Place/location identification
A segment giving more specific location information of
the party specified in the ADR segment, e.g. internal
site or building number.
![DTM 12]
DTM, Date/time/period
A segment specifying the date or time related to the
address given in the ADR segment.
![FTX 12]
FTX, Free text
A segment with free text information, in coded or clear
form, used when additional information is needed but
cannot be accommodated within other segments. In computer
to computer exchanges such text will normally require the
receiver to process this segment manually.
![g13]
Segment group 13: CTA-COM
A group of segments giving contact details of the
specific person or department within the party identified
in the PNA segment, e.g. person in a department in a the
job center office.
![CTA 13]
CTA, Contact information
A segment to identify a person or department and their
function, to whom communications should be directed.
![COM 13]
COM, Communication contact
A segment to identify a communications type and number
for the contact specified in the CTA segment, e.g.
telephone-number, fax-number, telex-number.
![UNT]
UNT, Message trailer
A service segment ending a message, giving the total number of
segments in the message (including the UNH & UNT) and the
control reference number of the message.
#====================================================================
