#====================================================================
#
#                              UN/EDIFACT
#
#                UNITED NATIONS STANDARD MESSAGE (UNSM)
#
#                Classification information set message
#
#
#
#
#====================================================================
MESSAGE=CLASET
VERSION=D
RELEASE=01B
AGENCY=UN
COMMENT
ENDCOMMENT

#====================================================================
SEGMENTS segs
#====================================================================
UNH M 1 Message header                            
BGM M 1 Beginning of message                      
DTM C 1 Date/time/period                          
group 1 C 9
  RFF M 1 Reference                                 
  DTM C 9 Date/time/period                          
endgroup 1
group 2 C 9
  PNA M 1 Party identification                      
  ADR C 1 Address                                   
  group 3 C 9
    CTA M 1 Contact information                       
    COM C 9 Communication contact                     
  endgroup 3
endgroup 2
group 4 C 99
  VLI M 1 Value list identification                 
  DTM C 9 Date/time/period                          
  EQN C 1 Number of units                           
  group 5 C 9
    PNA M 1 Party identification                      
    LAN C 1 Language                                  
    group 6 C 9
      CTA M 1 Contact information                       
      ADR C 1 Address                                   
      COM C 9 Communication contact                     
    endgroup 6
  endgroup 5
  group 7 C 99
    STS M 1 Status                                    
    DTM C 9 Date/time/period                          
  endgroup 7
  group 8 C 99
    ATT M 1 Attribute                                 
    DTM C 9 Date/time/period                          
    ELM C 1 Simple data element details               
    group 9 C 9
      CAV M 1 Characteristic value                      
      FTX C 99 Free text                                 
    endgroup 9
    group 10 C 99
      STS M 1 Status                                    
      DTM C 9 Date/time/period                          
    endgroup 10
  endgroup 8
  group 11 C 999999
    SCD M 1 Structure component definition            
    DTM C 9 Date/time/period                          
    EQN C 1 Number of units                           
    group 12 C 99
      STS M 1 Status                                    
      DTM C 9 Date/time/period                          
    endgroup 12
    group 13 C 99
      ATT M 1 Attribute                                 
      DTM C 9 Date/time/period                          
      ELM C 1 Simple data element details               
      group 14 C 99
        CAV M 1 Characteristic value                      
        FTX C 99 Free text                                 
      endgroup 14
      group 15 C 99
        STS M 1 Status                                    
        DTM C 9 Date/time/period                          
      endgroup 15
    endgroup 13
    group 16 C 99
      IDE M 1 Identity                                  
      DTM C 9 Date/time/period                          
      EQN C 1 Number of units                           
      group 17 C 99
        STS M 1 Status                                    
        DTM C 9 Date/time/period                          
      endgroup 17
      group 18 C 99
        ATT M 1 Attribute                                 
        DTM C 2 Date/time/period                          
        ELM C 1 Simple data element details               
        group 19 C 99
          CAV M 1 Characteristic value                      
          FTX C 99 Free text                                 
        endgroup 19
        group 20 C 99
          STS M 1 Status                                    
          DTM C 9 Date/time/period                          
        endgroup 20
      endgroup 18
    endgroup 16
  endgroup 11
endgroup 4
UNT M 1 Message trailer                           

#====================================================================
USAGE
#====================================================================
![UNH]
UNH, Message header
A service segment starting and uniquely identifying a message.
The message type code for the Classification information set
message is CLASET.
Note: Classification information set messages conforming to
this document must contain the following data in segment UNH,
composite S009:
Data element  0065 CLASET
              0052 D
              0054 01B
              0051 UN
![BGM]
BGM, Beginning of message
A segment specifying the functional use (e.g. request) of the
Classification information set message.
![DTM]
DTM, Date/time/period
A segment identifying the message preparation date/time.
![g1]
Segment group 1:  RFF-DTM
A group of segments to specify references such as the request
message to which the current message relates, and related date
and time.
![RFF 1]
RFF, Reference
A segment to specify the reference number.
![DTM 1]
DTM, Date/time/period
A segment to specify the date and time of the reference.
![g2]
Segment group 2:  PNA-ADR-SG3
A group of segments identifying the sender, the requester (if
the message function is a response to a request) and other
relevant parties, their reference and the communication contact
inside the corresponding organisations.
![PNA 2]
PNA, Party identification
A segment identifying a party.
![ADR 2]
ADR, Address
A segment identifying the address of the party.
![g3]
Segment group 3:  CTA-COM
A group of segments giving contact information.
![CTA 3]
CTA, Contact information
A segment identifying a person or department for the
party identified in the PNA segment and to whom
communication should be directed.
![COM 3]
COM, Communication contact
A segment containing communication information for the
contact identified in the CTA segment.
![g4]
Segment group 4:  VLI-DTM-EQN-SG5-SG7-SG8-SG11
A group of segments identifying a classification or a table of
links, and defining the items and, if the list is hierarchic,
the levels in the classification or table of links.
![VLI 4]
VLI, Value list identification
A segment identifying a classification or a table of links.
![DTM 4]
DTM, Date/time/period
A segment defining the interval of validity and other
relevant dates for the classification, or the table of
links.
![EQN 4]
EQN, Number of units
A segment defining the number of objects (e.g. levels or
items) in the classification or the table of links.
![g5]
Segment group 5:  PNA-LAN-SG6
A group of segments identifying a responsible agency for the
classification or the table of links and its role (e.g.
compilation, ongoing maintenance, dissemination).
![PNA 5]
PNA, Party identification
A segment identifying an agency and its role.
![LAN 5]
LAN, Language
A segment identifying the language of the agency.
![g6]
Segment group 6:  CTA-ADR-COM
A group of segments giving contact information.
![CTA 6]
CTA, Contact information
A segment identifying a person or a department for the
agency and to whom communication should be directed.
![ADR 6]
ADR, Address
A segment defining address details for the contact
person or department.
![COM 6]
COM, Communication contact
A segment containing communication details for the
person or the department identified in the CTA
segment.
![g7]
Segment group 7:  STS-DTM
A group of segments defining a status event for the
classification or the table of links such as created,
updated or deleted and the relevant dates of the status
event.
![STS 7]
STS, Status
A segment identifying a status event for the
classification or the table of links.
![DTM 7]
DTM, Date/time/period
A segment defining relevant dates for the status event.
![g8]
Segment group 8:  ATT-DTM-ELM-SG9-SG10
A group of segments identifying a property for the
classification, or table of links.
![ATT 8]
ATT, Attribute
A segment identifying a property function and a property
type.
![DTM 8]
DTM, Date/time/period
A segment defining an interval of validity and other
relevant dates for the property.
![ELM 8]
ELM, Simple data element details
A segment identifying the data type and format of the
property value.
![g9]
Segment group 9:  CAV-FTX
A group of segments defining a textual property value.
![CAV 9]
CAV, Characteristic value
A segment defining a characteristic of a textual
property value.
![FTX 9]
FTX, Free text
A segment defining a textual property value and the
language of the text.
![g10]
Segment group 10: STS-DTM
A group of segments defining a status event for the
property and the relevant dates of the status event.
![STS 10]
STS, Status
A segment identifying a status event for the property.
![DTM 10]
DTM, Date/time/period
A segment defining relevant dates for the status
event.
![g11]
Segment group 11: SCD-DTM-EQN-SG12-SG13-SG16
A group of segments identifying a structure component in a
classification, or a table of links.
![SCD 11]
SCD, Structure component definition
A segment identifying the type of structure component
(e.g. level, item or link) and the component itself.
![DTM 11]
DTM, Date/time/period
A segment defining the interval of validity and other
relevant dates for the structure component.
![EQN 11]
EQN, Number of units
A segment defining the number of structure components in
the identified level of hierarchy structure, such as the
number of items in the level.
![g12]
Segment group 12: STS-DTM
A group of segments defining a status event for the
structure component and the relevant dates of the status
event.
![STS 12]
STS, Status
A segment identifying a status event for the structure
component.
![DTM 12]
DTM, Date/time/period
A segment defining relevant dates for the status
event.
![g13]
Segment group 13: ATT-DTM-ELM-SG14-SG15
A group of segments identifying a property of the
structure component.
![ATT 13]
ATT, Attribute
A segment identifying a property function and a
property type.
![DTM 13]
DTM, Date/time/period
A segment defining an interval of validity and other
relevant dates for the property.
![ELM 13]
ELM, Simple data element details
A segment identifying the data type and format of the
property value.
![g14]
Segment group 14: CAV-FTX
A group of segments defining a textual property value.
![CAV 14]
CAV, Characteristic value
A segment defining a characteristic of a textual
property value.
![FTX 14]
FTX, Free text
A segment defining a textual property value and the
language of the text.
![g15]
Segment group 15: STS-DTM
A group of segments defining a status event for the
property and the relevant dates of the status event.
![STS 15]
STS, Status
A segment defining a status event for the property.
![DTM 15]
DTM, Date/time/period
A segment defining relevant dates for the status
event.
![g16]
Segment group 16: IDE-DTM-EQN-SG17-SG18
A group of segments identifying a source or a target
related structure component (e.g. item or level) of a
link and defining the status and properties of this
component.
![IDE 16]
IDE, Identity
A segment identifying the related structure component
such as a level or an item in a link.
![DTM 16]
DTM, Date/time/period
A segment defining an interval of validity and other
relevant dates for the related structure component.
![EQN 16]
EQN, Number of units
A segment defining the number of structure components
in the identified level of hierarchy structure, such
as the number of items in the level.
![g17]
Segment group 17: STS-DTM
A group of segments defining a status event for the
related structure component and the relevant dates of
the status event.
![STS 17]
STS, Status
A segment identifying a status event for the
related structure component.
![DTM 17]
DTM, Date/time/period
A segment defining relevant dates for the status
event.
![g18]
Segment group 18: ATT-DTM-ELM-SG19-SG20
A group of segments defining a property for the
related structure component.
![ATT 18]
ATT, Attribute
A segment identifying the property function and the
property type.
![DTM 18]
DTM, Date/time/period
A segment defining an interval of validity and
other relevant dates of the property.
![ELM 18]
ELM, Simple data element details
A segment defining the data type and format of the
property.
![g19]
Segment group 19: CAV-FTX
A group of segments identifying a textual property
value.
![CAV 19]
CAV, Characteristic value
A segment identifying a characteristic of the
textual property value.
![FTX 19]
FTX, Free text
A segment defining a textual property value and
the language of the text.
![g20]
Segment group 20: STS-DTM
A group of segments defining a status event for the
property and relevant dates of the status event.
![STS 20]
STS, Status
A segment identifying a status event for the
property.
![DTM 20]
DTM, Date/time/period
A segment defining relevant dates for the status
event.
![UNT]
UNT, Message trailer
A service segment ending a message, giving the total number of
segments in the message (including the UNH & UNT) and the
control reference number of the message.
#====================================================================
