#====================================================================
#
#                              UN/EDIFACT
#
#                UNITED NATIONS STANDARD MESSAGE (UNSM)
#
#                            Balance message
#
#
#
#
#====================================================================
MESSAGE=BALANC
VERSION=D
RELEASE=01B
AGENCY=UN
COMMENT
ENDCOMMENT

#====================================================================
SEGMENTS segs
#====================================================================
UNH M 1 Message header                            
BGM M 1 Beginning of message                      
DTM M 99 Date/time/period                          
RFF C 99 Reference                                 
CUX C 99 Currencies                                
FTX C 1 Free text                                 
group 1 M 99
  NAD M 1 Name and address                          
  RFF C 9 Reference                                 
  group 2 C 99
    CTA M 1 Contact information                       
    COM C 9 Communication contact                     
  endgroup 2
endgroup 1
group 3 C 99
  CCI M 1 Characteristic/class id                   
  CAV C 1 Characteristic value                      
endgroup 3
group 4 M 99999
  LIN M 1 Line item                                 
  MOA M 999 Monetary amount                           
  DTM C 1 Date/time/period                          
  RFF C 1 Reference                                 
  QTY C 10 Quantity                                  
  group 5 M 9
    CPT M 1 Account identification                    
    group 6 C 99
      CCI M 1 Characteristic/class id                   
      CAV C 1 Characteristic value                      
    endgroup 6
  endgroup 5
endgroup 4
group 7 M 1
  EQN M 1 Number of units                           
  MOA M 9 Monetary amount                           
endgroup 7
group 8 C 1
  AUT M 1 Authentication result                     
  DTM C 1 Date/time/period                          
endgroup 8
UNT M 1 Message trailer                           

#====================================================================
USAGE
#====================================================================
![UNH]
UNH, Message header
A service segment starting and uniquely identifying a message.
The message type code for the Balance message is BALANC.
Note: Balance messages conforming to this document must contain
the following data in segment UNH, composite S009:
Data element  0065 BALANC
              0052 D
              0054 01B
              0051 UN
![BGM]
BGM, Beginning of message
A segment identifying the balance message.
![DTM]
DTM, Date/time/period
A segment containing balance opening and closing dates and its
preparation date.
![RFF]
RFF, Reference
A segment containing the number of the balance linked to the
message and its version number.
![CUX]
CUX, Currencies
A segment providing currencies which are specific to the
balance message.
![FTX]
FTX, Free text
A segment containing free text information, in coded or clear
form, giving additional clarifications solely concerning the
summary section. In computer-to-computer exchanges such text
information will normally require the receiver to process this
segment manually.
![g1]
Segment group 1:  NAD-RFF-SG2
A segment group giving information on sender, recipient and
intermediary.
![NAD 1]
NAD, Name and address
A segment to identify a party.
![RFF 1]
RFF, Reference
A segment to provide references which are specific to the
concerned party.
![g2]
Segment group 2:  CTA-COM
A segment group to identify contacts and related
communication numbers.
![CTA 2]
CTA, Contact information
A segment to provide contacts related to the party.
![COM 2]
COM, Communication contact
A segment to provide communication numbers of the
contact.
![g3]
Segment group 3:  CCI-CAV
A group of segments to provide accounting, fiscal, social or
legal characteristics of the enterprise accounting balance.
![CCI 3]
CCI, Characteristic/class id
A segment to identify a characteristic.
![CAV 3]
CAV, Characteristic value
A segment to provide the value of the characteristic.
![g4]
Segment group 4:  LIN-MOA-DTM-RFF-QTY-SG5
A segment group providing the following relevant information
for a given period of the balance : account number and its
identification, period, quantity, stated value and record
number.
![LIN 4]
LIN, Line item
A segment indicating the trial balance line number within
the message.
![MOA 4]
MOA, Monetary amount
A segment containing detailed information about debit or
credit trend of the amount, the amount itself in local and
original currency and the currency code relating to these
amounts.
![DTM 4]
DTM, Date/time/period
A segment giving detailed information on the opening and
closing dates of account.
![RFF 4]
RFF, Reference
A segment containing a reference number which may be a
journal page number (folio) and sequence number within the
folio (line) as a single reference number or a serial number
assigned at bookkeeping entry level (record number) for a
given period.
![QTY 4]
QTY, Quantity
A segment specifying the quantity and the measure unit
relative to the amount of the accounting trial balance.
![g5]
Segment group 5:  CPT-SG6
A segment group containing detailed information identifying
an account.
![CPT 5]
CPT, Account identification
A segment containing detailed information identifying the
treated main account number and its subsidiary account
numbers.
![g6]
Segment group 6:  CCI-CAV
A group of segments to provide accounting, fiscal, social
or legal characteristics of the account.
![CCI 6]
CCI, Characteristic/class id
A segment to identify a characteristic.
![CAV 6]
CAV, Characteristic value
A segment to provide the value of the characteristic.
![g7]
Segment group 7:  EQN-MOA
A segment group providing information on trial balance amounts
and quantities. This group provides totals for verification
purposes.
![EQN 7]
EQN, Number of units
A segment specifying the number of balance lines for a given
period relative to the balance message.
![MOA 7]
MOA, Monetary amount
A segment adding up the total amounts in local and all
foreign currencies in the balance message.
![g8]
Segment group 8:  AUT-DTM
A segment group to provide authentication information.
![AUT 8]
AUT, Authentication result
A segment to provide authentication results.
![DTM 8]
DTM, Date/time/period
A segment to provide the date of authentication.
![UNT]
UNT, Message trailer
A service segment ending a message, giving the total number of
segments in the message (including the UNH & UNT) and the
control reference number of the message.
#====================================================================
