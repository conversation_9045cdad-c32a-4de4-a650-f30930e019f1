#====================================================================
#
#                              UN/EDIFACT
#
#                UNITED NATIONS STANDARD MESSAGE (UNSM)
#
#                    Life reinsurance claims message
#
#
#
#
#====================================================================
MESSAGE=LRECLM
VERSION=D
RELEASE=01B
AGENCY=UN
COMMENT
ENDCOMMENT

#====================================================================
SEGMENTS segs
#====================================================================
UNH M 1 Message header                            
BGM M 1 Beginning of message                      
DTM M 1 Date/time/period                          
CUX C 1 Currencies                                
CTA C 9 Contact information                       
FTX C 1 Free text                                 
group 1 M 999999
  IMD M 1 Item description                          
  ATT M 99 Attribute                                 
  DTM C 9 Date/time/period                          
  LOC C 1 Place/location identification             
  PCD C 9 Percentage details                        
  RFF C 9 Reference                                 
  CUX C 1 Currencies                                
  FTX C 1 Free text                                 
  group 2 C 9
    MOA M 1 Monetary amount                           
    ARD M 1 Monetary amount function                  
  endgroup 2
  group 3 M 1
    PNA M 1 Party identification                      
    DTM M 9 Date/time/period                          
    LOC C 1 Place/location identification             
    EMP C 1 Employment details                        
    group 4 C 9
      MOA M 1 Monetary amount                           
      ARD M 1 Monetary amount function                  
    endgroup 4
  endgroup 3
  group 5 M 99
    GIS M 1 General indicator                         
    RFF M 9 Reference                                 
    PNA C 1 Party identification                      
    DTM C 99 Date/time/period                          
    group 6 C 9
      MOA M 1 Monetary amount                           
      ARD M 1 Monetary amount function                  
    endgroup 6
    group 7 C 99
      IDE M 1 Identity                                  
      PNA M 1 Party identification                      
      DTM M 1 Date/time/period                          
      group 8 C 99
        ICD M 1 Insurance cover description               
        DTM C 99 Date/time/period                          
        RFF C 9 Reference                                 
        ATT C 9 Attribute                                 
        PCD C 1 Percentage details                        
        AGR C 1 Agreement identification                  
        group 9 C 9
          MOA M 1 Monetary amount                           
          ARD M 1 Monetary amount function                  
        endgroup 9
      endgroup 8
    endgroup 7
  endgroup 5
endgroup 1
UNT M 1 Message trailer                           

#====================================================================
USAGE
#====================================================================
![UNH]
UNH, Message header
A service segment starting and uniquely identifying a message.
The message type code for the Life reinsurance claims message
is LRECLM.
Note: Life reinsurance claims messages conforming to this
document must contain the following data in segment UNH,
composite S009:
Data element  0065 LRECLM
              0052 D
              0054 01B
              0051 UN
![BGM]
BGM, Beginning of message
A segment specifying the type of message contained in the
transmission.
![DTM]
DTM, Date/time/period
A segment specifying the reporting period ending date.
![CUX]
CUX, Currencies
A segment identifying the default currency and its scaling
factor for monetary amounts in this message.
![CTA]
CTA, Contact information
A segment identifying a person(s) or department(s), and their
function(s), to whom communications should be directed.
![FTX]
FTX, Free text
A segment with free text information, in coded or clear form,
used when additional information is needed but cannot be
accommodated within other segments.
![g1]
Segment group 1:  IMD-ATT-DTM-LOC-PCD-RFF-CUX-FTX-SG2-SG3-SG5
A group of segments in which is detailed claim information.
![IMD 1]
IMD, Item description
A segment defining the type of claim.
![ATT 1]
ATT, Attribute
A segment providing attributes of the claim.
![DTM 1]
DTM, Date/time/period
A segment specifying general dates or durations related to
the claim.
![LOC 1]
LOC, Place/location identification
A segment giving location information for the place of death
or location where the onset of the disability occurred.
![PCD 1]
PCD, Percentage details
A segment specifying the extent of loss and the interest
rate to be used in calculating the claim payment.
![RFF 1]
RFF, Reference
A segment specifying the claim numbers as assigned by the
ceding company and the reinsurer.
![CUX 1]
CUX, Currencies
A segment identifying the default currency and its scaling
factor for monetary amounts for the claim.
![FTX 1]
FTX, Free text
A segment with free text information, in coded or clear
form, used when additional information is needed regarding
the claim.
![g2]
Segment group 2:  MOA-ARD
A group of segments in which is detailed monetary amounts
and their purpose for the claim.
![MOA 2]
MOA, Monetary amount
A segment specifying a monetary amount.
![ARD 2]
ARD, Monetary amount function
A segment specifying the purpose of the monetary amount.
![g3]
Segment group 3:  PNA-DTM-LOC-EMP-SG4
A group of segments in which is detailed information
regarding the person for which this claim is being
submitted.
![PNA 3]
PNA, Party identification
A segment identifying the name of the person on claim.
![DTM 3]
DTM, Date/time/period
A segment specifying general date and durations
associated with the person on claim.
![LOC 3]
LOC, Place/location identification
A segment giving location information for the place of
residence of the person on claim at the time of loss.
![EMP 3]
EMP, Employment details
A segment providing the occupation class of the person on
claim at time of loss for purposes of this claim as
defined by the issuing company.
![g4]
Segment group 4:  MOA-ARD
A group of segments in which is detailed monetary amounts
and their purpose for this person on claim.
![MOA 4]
MOA, Monetary amount
A segment specifying a monetary amount.
![ARD 4]
ARD, Monetary amount function
A segment specifying the purpose of the monetary
amount.
![g5]
Segment group 5:  GIS-RFF-PNA-DTM-SG6-SG7
A group of segments in which is detailed information
regarding the policy on claim and other policies related to
the claim.
![GIS 5]
GIS, General indicator
An indicator specifying whether this policy is the policy
on claim or an associated policy.
![RFF 5]
RFF, Reference
A segment specifying the policy number as assigned by the
ceding company.
![PNA 5]
PNA, Party identification
A segment identifying the original issuer of the policy,
in coded or clear form.
![DTM 5]
DTM, Date/time/period
A segment specifying general dates and durations
associated with the policy on claim or related policy.
![g6]
Segment group 6:  MOA-ARD
A group of segments in which is detailed monetary amounts
and their purpose for this policy.
![MOA 6]
MOA, Monetary amount
A segment specifying a monetary amount.
![ARD 6]
ARD, Monetary amount function
A segment specifying the purpose of the monetary
amount.
![g7]
Segment group 7:  IDE-PNA-DTM-SG8
A group of segments in which is detailed information
regarding the insureds on this policy.
![IDE 7]
IDE, Identity
A segment identifying the party as the insured of
record.
![PNA 7]
PNA, Party identification
A segment identifying the insured's name and universal
identifier.
![DTM 7]
DTM, Date/time/period
A segment specifying the date of birth of the insured.
![g8]
Segment group 8:  ICD-DTM-RFF-ATT-PCD-AGR-SG9
A group of segments in which is detailed coverage
information regarding the insured.
![ICD 8]
ICD, Insurance cover description
A segment giving the insurance cover type
associated with this coverage.
![DTM 8]
DTM, Date/time/period
A segment specifying general dates or durations
related to this coverage.
![RFF 8]
RFF, Reference
A segment specifying general references for this
coverage.
![ATT 8]
ATT, Attribute
A segment providing attributes of the coverage.
![PCD 8]
PCD, Percentage details
A segment specifying the percentages associated
with the claim.
![AGR 8]
AGR, Agreement identification
A segment identifying the treaty as assigned by the
ceding company.
![g9]
Segment group 9:  MOA-ARD
A group of segments in which is detailed monetary
amounts and their purpose for the coverage.
![MOA 9]
MOA, Monetary amount
A segment specifying a monetary amount.
![ARD 9]
ARD, Monetary amount function
A segment specifying the purpose of the monetary
amount.
![UNT]
UNT, Message trailer
A service segment ending a message, giving the total number of
segments in the message (including the UNH & UNT) and the
control reference number of the message.
#====================================================================
