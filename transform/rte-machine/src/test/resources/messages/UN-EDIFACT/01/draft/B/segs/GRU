SEGMENT GROUP USAGE DETAILS
#Release:
To specify the usage of a segment group within a message type structure and its maintenance operation.
#--------------------------------------------------------
ELEMENTS
#--------------------------------------------------------
9164 <USER> <GROUP>..4 GROUP IDENTIFIER                          
7299 C an..3 REQUIREMENT DESIGNATOR CODE               
6176 C n..7 OCCURRENCES MAXIMUM NUMBER                
4513 C an..3 MAINTENANCE OPERATION CODE                
1050 C an..10 SEQUENCE POSITION IDENTIFIER              
