#====================================================================
#
#                              UN/EDIFACT
#
#                UNITED NATIONS STANDARD MESSAGE (UNSM)
#
#                        Payment order message
#
#
#
#
#====================================================================
MESSAGE=PAYORD
VERSION=D
RELEASE=01B
AGENCY=UN
COMMENT
ENDCOMMENT

#====================================================================
SEGMENTS segs
#====================================================================
UNH M 1 Message header                            
BGM M 1 Beginning of message                      
BUS C 1 Business function                         
PAI C 1 Payment instructions                      
FCA C 1 Financial charges allocation              
DTM M 4 Date/time/period                          
FTX C 5 Free text                                 
group 1 C 5
  RFF M 1 Reference                                 
  DTM C 1 Date/time/period                          
endgroup 1
group 2 M 1
  MOA M 1 Monetary amount                           
  CUX C 1 Currencies                                
  DTM C 2 Date/time/period                          
  RFF C 1 Reference                                 
endgroup 2
group 3 C 4
  FII M 1 Financial institution information         
  CTA C 1 Contact information                       
  COM C 5 Communication contact                     
endgroup 3
group 4 C 6
  NAD M 1 Name and address                          
  CTA C 1 Contact information                       
  COM C 5 Communication contact                     
endgroup 4
group 5 C 4
  INP M 1 Parties and instruction                   
  FTX C 1 Free text                                 
  DTM C 2 Date/time/period                          
endgroup 5
group 6 C 10
  GIS M 1 General indicator                         
  MOA C 1 Monetary amount                           
  LOC C 2 Place/location identification             
  NAD C 1 Name and address                          
  RCS C 1 Requirements and conditions               
  FTX C 10 Free text                                 
endgroup 6
group 7 C 10
  DOC M 1 Document/message details                  
  DTM C 1 Date/time/period                          
endgroup 7
group 8 C 5
  AUT M 1 Authentication result                     
  DTM C 1 Date/time/period                          
endgroup 8
UNT M 1 Message trailer                           

#====================================================================
USAGE
#====================================================================
![UNH]
UNH, Message header
A service segment starting and uniquely identifying a message.
The message type code for the Payment order message is PAYORD.
Note: Payment order messages conforming to this document must
contain the following data in segment UNH, composite S009:
Data element  0065 PAYORD
              0052 D
              0054 01B
              0051 UN
![BGM]
BGM, Beginning of message
A segment for unique identification of the Payment Order, the
type of Payment Order and its function. The requirement for a
response, e.g., related debit advice, may be indicated.

Note: The identification will be passed back to the Ordering
Customer for reconciliation purposes; it would be used in the
case of a cancellation or confirmation of a Payment Order.
![BUS]
BUS, Business function
A segment identifying certain characteristics of the Payment
Order, such as its business function. In so doing, it provides
information about the message that may be used, for instance,
for routing within an institution, for tariffing, or for the
provision of some statistical information.
![PAI]
PAI, Payment instructions
A segment specifying the conditions, guarantee, method and
channel of payment for the Payment Order.
![FCA]
FCA, Financial charges allocation
A segment specifying the method for allocation of charges and
allowances (e.g., charges borne by the Ordering Customer, the
Beneficiary or both), and identifying the Ordering Customer's
account to which such charges or allowances should be directed
where it is different from the principal account.
![DTM]
DTM, Date/time/period
A segment identifying the date/time of creation of the message,
the requested execution date and/or due date at the
beneficiary's side as well as other dates and times relevant to
the financial transaction.
![FTX]
FTX, Free text
This segment contains the details of payment, which have to be
passed from the ordered bank to the beneficiary through the
banking chain together with the remittance details.
![g1]
Segment group 1:  RFF-DTM
A group of segments identifying a previously-sent message.
![RFF 1]
RFF, Reference
A segment specifying the reference number of the previously-
sent message.
![DTM 1]
DTM, Date/time/period
A segment identifying the creation date of the referenced
message.
![g2]
Segment group 2:  MOA-CUX-DTM-RFF
A group of segments identifying the monetary amount and, if
necessary, the currencies, exchange rate and date for the
payment.
![MOA 2]
MOA, Monetary amount
A segment giving the amount of the payment.
![CUX 2]
CUX, Currencies
A segment identifying the source currency and the target
currency of the transaction when they are different. The
rate of exchange is solely used when previously agreed
between the Ordering Customer and the Ordered Bank.
![DTM 2]
DTM, Date/time/period
A segment identifying the effective date and/or time the
rate of exchange was fixed. The other occurrence identifies
the reference date.
![RFF 2]
RFF, Reference
A segment identifying other transactions to which funds
associated with the Payment Order are related, such as a
separate foreign exchange deal.
![g3]
Segment group 3:  FII-CTA-COM
A group of segments providing information about the financial
institutions and accounts related to the Payment Order,
together with details of any parties to be contacted in
relation with the transaction. This group may also contain a
single customer for the debiting and/or crediting side.
![FII 3]
FII, Financial institution information
A segment identifying the financial institution (e.g., bank)
and relevant account number and currency for each party
involved in the transaction. The Ordering Customer may
indicate the previously agreed choice of financial
institution for payment.
![CTA 3]
CTA, Contact information
A segment identifying a person or a department for the
financial institution specified in the FII segment and to
whom communication should be directed.
![COM 3]
COM, Communication contact
A segment providing a communication number for the party
identified in the FII segment and optionally for the contact
identified in the associated CTA segment.
![g4]
Segment group 4:  NAD-CTA-COM
A group of segments identifying the name and address of the
non-financial institutions involved in the transaction and
their contacts.
![NAD 4]
NAD, Name and address
A segment identifying the name and address of the non-
financial parties associated with the Payment Order and
their functions.
![CTA 4]
CTA, Contact information
A segment identifying a person or a department for the party
specified in the NAD segment and to whom communication
should be directed.
![COM 4]
COM, Communication contact
A segment providing a communication number for the party
identified in the NAD segment and optionally for the contact
identified in the associated CTA segment.
![g5]
Segment group 5:  INP-FTX-DTM
A group of segments containing instructions from the Ordering
Customer relating to parties identified in the NAD and FII
segments. It specifies action to be taken by the identified
parties, and the date (and optionally time) by which such
action needs to be taken.
![INP 5]
INP, Parties and instruction
A segment identifying the party to enact the instruction and
the parties to be contacted at or by the associated
financial institution on matters concerning the execution of
the payment. It specifies where appropriate the instruction
in coded form.
![FTX 5]
FTX, Free text
A segment providing free text instruction relating to the
associated INP segment.
![DTM 5]
DTM, Date/time/period
A segment specifying the earliest and the latest dates and
times by which the instruction specified in the INP and/or
FTX segments needs to be carried out.
![g6]
Segment group 6:  GIS-MOA-LOC-NAD-RCS-FTX
A group of segments providing information for subsequent use by
regulatory authorities requiring statistical and other types of
data. It also identifies the regulatory authority for which the
information is intended followed by the information itself.
![GIS 6]
GIS, General indicator
A segment identifying what processing should be completed
for regulatory authorities.
![MOA 6]
MOA, Monetary amount
A segment giving the amount and the currency of each
transaction to be reported.
![LOC 6]
LOC, Place/location identification
A segment giving the different origins/destinations (places)
of goods/investment/services.
![NAD 6]
NAD, Name and address
A segment identifying the recipient of the associated
informative text.
![RCS 6]
RCS, Requirements and conditions
A segment giving the nature (e.g. goods, transport services)
and direction of each transaction to be recorded in coded
form.
![FTX 6]
FTX, Free text
A segment giving information, in coded or clear form, to
provide information relevant to regulatory authorities
requirements.
![g7]
Segment group 7:  DOC-DTM
A group of segments providing brief structured cross references
to transactions to which the Payment Order relates.
![DOC 7]
DOC, Document/message details
A segment identifying the document(s) relating to the
payment order.
![DTM 7]
DTM, Date/time/period
A segment identifying the date of issue of the document(s)
described in the preceding DOC segment.
![g8]
Segment group 8:  AUT-DTM
A group of segments specifying the details of authentication.
![AUT 8]
AUT, Authentication result
A segment specifying the details of any authentication
(validation) procedure applied to the Payment Order message.
![DTM 8]
DTM, Date/time/period
A segment identifying the date and where necessary, the time
of validation.
![UNT]
UNT, Message trailer
A service segment ending a message, giving the total number of
segments in the message (including the UNH & UNT) and the
control reference number of the message.
#====================================================================
