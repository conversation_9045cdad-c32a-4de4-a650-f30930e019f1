#====================================================================
#
#                              UN/EDIFACT
#
#                UNITED NATIONS STANDARD MESSAGE (UNSM)
#
#                Reinsurance technical account message
#
#
#
#
#====================================================================
MESSAGE=RETACC
VERSION=D
RELEASE=01B
AGENCY=UN
COMMENT
ENDCOMMENT

#====================================================================
SEGMENTS segs
#====================================================================
UNH M 1 Message header                            
GIS M 6 General indicator                         
group 1 M 9
  NAD M 1 Name and address                          
  CTA C 1 Contact information                       
  COM C 5 Communication contact                     
  RFF C 9 Reference                                 
endgroup 1
DTM M 4 Date/time/period                          
FTX C 5 Free text                                 
group 2 M 99
  CUX M 1 Currencies                                
  RFF M 5 Reference                                 
  FTX C 1 Free text                                 
  group 3 M 999
    MOA M 1 Monetary amount                           
    DTM M 10 Date/time/period                          
    PCD C 2 Percentage details                        
    GIS C 10 General indicator                         
    LOC C 9 Place/location identification             
    FTX C 3 Free text                                 
    group 4 C 9
      RFF M 1 Reference                                 
      DTM C 1 Date/time/period                          
      COM C 1 Communication contact                     
      FTX C 1 Free text                                 
    endgroup 4
  endgroup 3
endgroup 2
UNS M 1 Section control                           
group 5 C 99
  MOA M 1 Monetary amount                           
  GIS C 1 General indicator                         
  group 6 C 999
    NAD M 1 Name and address                          
    PCD C 1 Percentage details                        
    RFF C 2 Reference                                 
    GIS C 1 General indicator                         
    MOA C 1 Monetary amount                           
  endgroup 6
  PAI M 1 Payment instructions                      
  RFF C 9 Reference                                 
  DTM C 3 Date/time/period                          
endgroup 5
UNT M 1 Message trailer                           

#====================================================================
USAGE
#====================================================================
![UNH]
UNH, Message header
A service segment starting and uniquely identifying a message.
The message type code for the Reinsurance technical account
message is RETACC.
Note: Reinsurance technical account messages conforming to this
document must contain the following data in segment UNH,
composite S009:
Data element  0065 RETACC
              0052 D
              0054 01B
              0051 UN
![GIS]
GIS, General indicator
A segment to give a processing indicator relating to the whole
message.
![g1]
Segment group 1:  NAD-CTA-COM-RFF
A group of segments to specify relevant parties for the whole
transaction.
![NAD 1]
NAD, Name and address
A segment to specify the name and address and the related
function of the party involved.
![CTA 1]
CTA, Contact information
A segment to identify a person or department to whom
communication should be directed.
![COM 1]
COM, Communication contact
A segment to identify a communication number of a department
or a person to whom communication should be directed.
![RFF 1]
RFF, Reference
A segment to specify the references of the party identified
in the leading NAD.
![DTM]
DTM, Date/time/period
A segment to specify the message transaction date, accounting
period, accounting year, accounting periodicity.
![FTX]
FTX, Free text
A segment to provide general message information, and/or
narrative descriptions of references or account period given.
![g2]
Segment group 2:  CUX-RFF-FTX-SG3
A group of segments to specify accounting breakdowns within the
contract.
![CUX 2]
CUX, Currencies
A segment to specify the account rendering currency
(statement currency) of the sub-account, and if required the
settlement currency and the exchange rate used to convert
from original account currency into settlement currency.
![RFF 2]
RFF, Reference
A segment used to tie the sub-account to a specific
reference and to additionally identify the layer or surplus
number of the contract for which data are rendered.
![FTX 2]
FTX, Free text
A segment used to provide free text information regarding
the contract section reported in the sub-account
(particularly for facultative contracts).
![g3]
Segment group 3:  MOA-DTM-PCD-GIS-LOC-FTX-SG4
A group of segments to specify accounting entries for the
sub-account, and if required the sub-account balance(s).
![MOA 3]
MOA, Monetary amount
A segment to specify the accounting entries or the sub-
account balance(s).
![DTM 3]
DTM, Date/time/period
A segment to specify pertinent dates and periods relating
to the entry.
![PCD 3]
PCD, Percentage details
A segment to specify the percentage used to calculate the
entry, and/or the receiver's share percentage.
![GIS 3]
GIS, General indicator
A segment to give an indicator to specify the way to
process the entry.
![LOC 3]
LOC, Place/location identification
A segment to identify the geographical scope to which the
entry pertains or - for facultative accounts - the
location of the risk.
![FTX 3]
FTX, Free text
A segment used to provide free text information regarding
the entry. More particularly, the segment can be used to
give loss details or additional information concerning
bucket entry codes.
![g4]
Segment group 4:  RFF-DTM-COM-FTX
A group of segments to refer to supporting messages or
documents. The segment may equally be used in case of
loss amount reporting to specify the event reference.
![RFF 4]
RFF, Reference
A segment to specify a reference to a supporting
document or message and/or (in case of losses
reported), the event reference.
![DTM 4]
DTM, Date/time/period
A segment to specify the date of transaction of the
supporting document or message.
![COM 4]
COM, Communication contact
A segment to identify the means by which the
referenced document or message was transmitted.
![FTX 4]
FTX, Free text
A segment used to provide free text information
regarding the document or message that is referenced.
![UNS]
UNS, Section control
A segment used to prevent collisions between the detail and the
summary sections of the message.
![g5]
Segment group 5:  MOA-GIS-SG6-PAI-RFF-DTM
A group of segments that may be used to specify account totals
and subdivide them per type of settlement. At the same type
this group can be used to break down the account into sub-
balances using various criteria, such as collectable or non-
collectable, and the embedded group 6 can be used to make
breakdowns per party in the underlying market.
![MOA 5]
MOA, Monetary amount
A segment to specify the total account balances per
settlement currency, or the possible breakdowns e.g. the
amounts due in instalments, or per payment means or
combined.
![GIS 5]
GIS, General indicator
A segment to give a processing indicator relating to the
settlement.
![g6]
Segment group 6:  NAD-PCD-RFF-GIS-MOA
A group of segments used to make breakdowns of the amount
reported, per party in the underlying market.
![NAD 6]
NAD, Name and address
A segment to identify the bank where the letter of credit
was issued, the payer's and beneficiary's bank, the
assuming company, the multiple cedants involved.
![PCD 6]
PCD, Percentage details
A segment used to specify the share of the party in the
broker's order or in the 100% of the contract, expressed
as a percentage.
![RFF 6]
RFF, Reference
A segment to specify the references of the party named in
the leading NAD.
![GIS 6]
GIS, General indicator
A segment to give a processing indicator relating to the
settlement by specific parties involved in the
transaction.
![MOA 6]
MOA, Monetary amount
A segment to specify the share amount of the party
specified.
![PAI 5]
PAI, Payment instructions
A segment to specify the means of settlement.
![RFF 5]
RFF, Reference
A segment to specify references related to the settlement of
the account balance.
![DTM 5]
DTM, Date/time/period
A segment to specify pertinent dates and periods relating to
the settlement of the RETACC.
![UNT]
UNT, Message trailer
A service segment ending a message, giving the total number of
segments in the message (including the UNH & UNT) and the
control reference number of the message.
#====================================================================
