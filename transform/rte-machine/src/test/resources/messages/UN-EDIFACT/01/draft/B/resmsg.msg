#====================================================================
#
#                              UN/EDIFACT
#
#                UNITED NATIONS STANDARD MESSAGE (UNSM)
#
#                          Reservation message
#
#
#
#
#====================================================================
MESSAGE=RESMSG
VERSION=D
RELEASE=01B
AGENCY=UN
COMMENT
ENDCOMMENT

#====================================================================
SEGMENTS segs
#====================================================================
UNH M 1 Message header                            
BGM M 1 Beginning of message                      
LAN C 1 Language                                  
PCD C 1 Percentage details                        
PAI C 1 Payment instructions                      
DTM C 5 Date/time/period                          
FTX C 5 Free text                                 
LOC C 2 Place/location identification             
MOA C 5 Monetary amount                           
PAT C 10 Payment terms basis                       
FII C 5 Financial institution information         
group 1 C 5
  RFF M 1 Reference                                 
  DTM C 1 Date/time/period                          
endgroup 1
group 2 C 5
  NAD M 1 Name and address                          
  RFF C 5 Reference                                 
  COM C 5 Communication contact                     
  LAN C 1 Language                                  
  group 3 C 5
    CTA M 1 Contact information                       
    COM C 5 Communication contact                     
    LAN C 1 Language                                  
  endgroup 3
endgroup 2
group 4 C 99
  SEQ M 1 Sequence details                          
  group 5 C 99
    IMD M 1 Item description                          
    QTY C 1 Quantity                                  
    LAN C 1 Language                                  
    DTM C 20 Date/time/period                          
    NAD C 1 Name and address                          
    RFF C 99 Reference                                 
    PCD C 1 Percentage details                        
    PAI C 1 Payment instructions                      
    PAT C 5 Payment terms basis                       
    FII C 5 Financial institution information         
    DIM C 2 Dimensions                                
    FTX C 5 Free text                                 
    group 6 C 10
      LOC M 1 Place/location identification             
      NAD C 1 Name and address                          
      DTM C 10 Date/time/period                          
    endgroup 6
    group 7 C 99
      MOA M 1 Monetary amount                           
      QTY C 1 Quantity                                  
    endgroup 7
    group 8 C 10
      RCS M 1 Requirements and conditions               
      FII C 5 Financial institution information         
      MOA C 2 Monetary amount                           
      RFF C 1 Reference                                 
      FTX C 1 Free text                                 
    endgroup 8
  endgroup 5
  group 9 C 99
    MEM M 1 Membership details                        
    group 10 C 99
      NAD M 1 Name and address                          
      ATT C 99 Attribute                                 
      RFF C 99 Reference                                 
      LAN C 1 Language                                  
      FTX C 5 Free text                                 
      COM C 5 Communication contact                     
      group 11 C 1
        CTA M 1 Contact information                       
        COM C 5 Communication contact                     
      endgroup 11
      group 12 C 5
        DOC M 1 Document/message details                  
        LOC C 1 Place/location identification             
        DTM C 10 Date/time/period                          
      endgroup 12
      group 13 C 10
        PAI M 1 Payment instructions                      
        PAT C 1 Payment terms basis                       
        FII C 1 Financial institution information         
        MOA C 1 Monetary amount                           
        RFF C 9 Reference                                 
      endgroup 13
      group 14 C 10
        RCS M 1 Requirements and conditions               
        MOA C 2 Monetary amount                           
        RFF C 2 Reference                                 
        FTX C 1 Free text                                 
      endgroup 14
    endgroup 10
  endgroup 9
endgroup 4
UNT M 1 Message trailer                           

#====================================================================
USAGE
#====================================================================
![UNH]
UNH, Message header
A service segment starting and uniquely identifying a message.
The message type code for the Reservation message is RESMSG.
Note: Reservation messages conforming to this document must
contain the following data in segment UNH, composite S009:
Data element  0065 RESMSG
              0052 D
              0054 01B
              0051 UN
![BGM]
BGM, Beginning of message
A segment identifying the Reservation message.
![LAN]
LAN, Language
A segment identifying the language used in a transaction.
![PCD]
PCD, Percentage details
A segment containing a percentage factor which relates to the
services or products requested in the whole message.
![PAI]
PAI, Payment instructions
A segment specifying a method of payment relating to the
services or products requested in the whole message.
![DTM]
DTM, Date/time/period
A segment specifying a date that applies to the message, (e.g.
date of this message, date until such message is valid).
![FTX]
FTX, Free text
A segment for information in coded or clear form to provide any
other information related to the whole message.
![LOC]
LOC, Place/location identification
A segment containing location information pertaining to the
services or products requested in the whole message.
![MOA]
MOA, Monetary amount
A segment containing the monetary amounts to be paid by the
sender for all beneficiaries, services or products requested in
the whole message.
![PAT]
PAT, Payment terms basis
A segment specifying payment terms relating to the services or
products requested in the whole message.
![FII]
FII, Financial institution information
A segment containing financial institution information relating
to the services or products requested in the whole message.
![g1]
Segment group 1:  RFF-DTM
A group of segments indicating references relating to the
message.
![RFF 1]
RFF, Reference
A segment containing references relating to the whole
message.
![DTM 1]
DTM, Date/time/period
A segment specifying the date that applies to the previous
reference.
![g2]
Segment group 2:  NAD-RFF-COM-LAN-SG3
A group of segments identifying the sender and the receiver and
all other parties involved in the transaction and how to
communicate information to each of them.
![NAD 2]
NAD, Name and address
A segment providing name and address of the parties related
to the message (sender, receiver, airline, agent).
![RFF 2]
RFF, Reference
A segment indicating any additional reference for the party
identified in the preceding NAD segment.
![COM 2]
COM, Communication contact
A segment containing communication information for the party
identified in the preceding NAD segment.
![LAN 2]
LAN, Language
A segment indicating the language to be used by the party
identified in the preceding NAD segment.
![g3]
Segment group 3:  CTA-COM-LAN
A group of segments identifying a person or a department
within a party specified in the preceding NAD segment and
how to communicate with them.
![CTA 3]
CTA, Contact information
A segment identifying a person or a department for the
party specified in the preceding NAD segment, and to whom
the communication should be directed.
![COM 3]
COM, Communication contact
A segment providing communication information for the
person or department identified in the preceding CTA
segment.
![LAN 3]
LAN, Language
A segment indicating the language to communicate with the
person or department identified in the preceding CTA
segment.
![g4]
Segment group 4:  SEQ-SG5-SG9
A group of segments relating to the requested services and to
the beneficiaries.
![SEQ 4]
SEQ, Sequence details
A segment which provides a sequence number for later
reference to a related service, product or beneficiary.
![g5]
Segment group 5:  IMD-QTY-LAN-DTM-NAD-RFF-PCD-PAI-PAT-FII-
                  DIM-FTX-SG6-SG7-SG8
A group of segments identifying a particular service or
product requested.
![IMD 5]
IMD, Item description
A segment identifying a particular product or service.
![QTY 5]
QTY, Quantity
A segment specifying a quantity related to the service or
product identified in the preceding IMD segment.
![LAN 5]
LAN, Language
A segment indicating the language to be used for the
requested service or product identified in the preceding
IMD segment.
![DTM 5]
DTM, Date/time/period
A segment to indicate any date and/or time pertaining to
the service or product identified in the preceding IMD
segment.
![NAD 5]
NAD, Name and address
A segment specifying name and address information related
to the service or product identified in the preceding IMD
segment.
![RFF 5]
RFF, Reference
A segment containing reference relating to the service or
product identified in the preceding IMD segment.
![PCD 5]
PCD, Percentage details
A segment containing percentage factors which relate to
the service or product identified in the preceding IMD
segment.
![PAI 5]
PAI, Payment instructions
A segment specifying methods of payment relating to the
service or product identified in the preceding IMD
segment.
![PAT 5]
PAT, Payment terms basis
A segment specifying payment terms for the service or
product identified in an associated IMD segment.
![FII 5]
FII, Financial institution information
A segment containing financial institution information
relating to the service or product identified in the
preceding IMD segment.
![DIM 5]
DIM, Dimensions
A segment providing dimensions which may be required for
the provision of the service or product identified in the
preceding IMD segment.
![FTX 5]
FTX, Free text
A segment for information in coded or clear form to
provide any other information related to the service or
product identified in the preceding IMD segment.
![g6]
Segment group 6:  LOC-NAD-DTM
A group of segments identifying location and if necessary
the name and address of a specific place connected with
this location.
![LOC 6]
LOC, Place/location identification
A segment containing location information pertaining
to the service or product identified in the preceding
IMD segment.
![NAD 6]
NAD, Name and address
A segment providing name and address information
relating to the information specified in the preceding
LOC segment.
![DTM 6]
DTM, Date/time/period
A segment providing date and/or time information
relating to the information specified in the preceding
LOC segment.
![g7]
Segment group 7:  MOA-QTY
A group of segments containing the monetary amount to be
paid related to the quantity of the service or product
identified in the preceding IMD segment.
![MOA 7]
MOA, Monetary amount
A segment containing the monetary amount to be paid by
the sender for the service or product identified in
the preceding IMD segment.
![QTY 7]
QTY, Quantity
A segment specifying a quantity related to the service
or product identified in the IMD segment.
![g8]
Segment group 8:  RCS-FII-MOA-RFF-FTX
A group of segments providing special conditions or
additional requirements related to the service or product
identified in the preceding IMD segment.
![RCS 8]
RCS, Requirements and conditions
A segment to specify types of requirements attached to
the provision of a service.
![FII 8]
FII, Financial institution information
A segment containing financial institution information
relating to the special conditions or requirements
identified in the preceding RCS segment.
![MOA 8]
MOA, Monetary amount
A segment containing the monetary amount to be paid by
the sender for the special conditions or requirements
identified in the preceding RCS segment.
![RFF 8]
RFF, Reference
A segment containing reference information relating to
the special conditions or requirements identified in
the preceding RCS segment.
![FTX 8]
FTX, Free text
A segment for information in coded or clear form to
provide any other information related to the preceding
RCS segment.
![g9]
Segment group 9:  MEM-SG10
A group of segments containing information pertaining to the
beneficiaries of the services or products requested in the
message.
![MEM 9]
MEM, Membership details
A segment specifying relationship between persons within
a group.
![g10]
Segment group 10: NAD-ATT-RFF-LAN-FTX-COM-SG11-SG12-SG13-
                  SG14
A group of segments providing information related to a
group or an individual beneficiary.
![NAD 10]
NAD, Name and address
A segment specifying name and address information
related to the preceding MEM segment.
![ATT 10]
ATT, Attribute
A segment specifying an attribute related to the
preceding NAD segment.
![RFF 10]
RFF, Reference
A segment providing reference information related to
the preceding NAD segment.
![LAN 10]
LAN, Language
A segment providing language information which relates
to the preceding NAD segment.
![FTX 10]
FTX, Free text
A segment for information in coded or clear form to
provide any other information related to the preceding
NAD segment.
![COM 10]
COM, Communication contact
A segment providing communication information related
to the preceding NAD segment.
![g11]
Segment group 11: CTA-COM
A group of segments providing contact information
related to the NAD segment.
![CTA 11]
CTA, Contact information
A segment providing contact information related to
the preceding NAD segment.
![COM 11]
COM, Communication contact
A segment providing communication information
related to the preceding CTA segment.
![g12]
Segment group 12: DOC-LOC-DTM
A group of segments providing documentation
information related to the preceding NAD segment.
![DOC 12]
DOC, Document/message details
A segment indicating a type of document (e.g.
passport, visa, driving licence, vaccination
certificate, etc...) related to the preceding NAD
segment.
![LOC 12]
LOC, Place/location identification
A segment identifying the place of issue of the
document referenced in the preceding DOC segment.
![DTM 12]
DTM, Date/time/period
A segment indicating the date of issue of the
document referenced in the preceding DOC segment.
![g13]
Segment group 13: PAI-PAT-FII-MOA-RFF
A group of segments providing payment information,
payment terms, financial institution, monetary amount
and references related to the preceding NAD segment.
![PAI 13]
PAI, Payment instructions
A segment specifying a method of payment relating
to the preceding NAD segment.
![PAT 13]
PAT, Payment terms basis
A segment providing payment terms related to the
preceding PAI segment.
![FII 13]
FII, Financial institution information
A segment providing financial institution
information relating to preceding PAI segment.
![MOA 13]
MOA, Monetary amount
A segment providing monetary information (e.g. a
rate request, maximum price, etc...) related to the
preceding PAI segment.
![RFF 13]
RFF, Reference
A segment providing reference information related
to the preceding PAI segment.
![g14]
Segment group 14: RCS-MOA-RFF-FTX
A group of segments providing information of special
requirements or conditions related to the preceding
NAD segment.
![RCS 14]
RCS, Requirements and conditions
A segment to specify a type of requirement (e.g.
special diet needs, handicapped facilities, etc...)
related to the preceding NAD segment.
![MOA 14]
MOA, Monetary amount
A segment providing a monetary amount related to
the preceding RCS segment.
![RFF 14]
RFF, Reference
A segment containing reference information relating
to the preceding RCS segment.
![FTX 14]
FTX, Free text
A segment specifying information in coded or clear
form to provide any other information related to
the preceding RCS segment.
![UNT]
UNT, Message trailer
A service segment ending a message, giving the total number of
segments in the message (including the UNH & UNT) and the
control reference number of the message.
#====================================================================
