STRUCTURE COMPONENT DEFINITION
#Release:
To specify a component of a data structure (e.g. an array or table).
#--------------------------------------------------------
ELEMENTS
#--------------------------------------------------------

7497 <USER> <GROUP>..3 COMPONENT FUNCTION QUALIFIER
C786 C  STRUCTURE COMPONENT IDENTIFICATION
  7512 M an..35 Structure component identifier
  7405 C an..3 Identity number qualifier
C082 C  PARTY IDENTIFICATION DETAILS
  3039 M an..35 Party id. identification
  1131 C an..3 Code list qualifier
  3055 C an..3 Code list responsible agency, coded
4405 C an..3 STATUS, CODED
1222 C n..2 CONFIGURATION LEVEL
C778 C  POSITION IDENTIFICATION
  7164 C an..12 Hierarchical id. number
  1050 C an..6 Sequence number
C240 C  PRODUCT CHARACTERISTIC
  7037 M an..17 Characteristic identification
  1131 C an..3 Code list qualifier
  3055 C an..3 Code list responsible agency, coded
  7036 C an..35 Characteristic
  7036 C an..35 Characteristic