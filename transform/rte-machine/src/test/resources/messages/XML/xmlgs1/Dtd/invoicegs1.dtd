<?xml version="1.0" encoding="UTF-8"?>
<!--DTD generated by XMLSpy v2017 rel. 3 sp1 (http://www.altova.com)-->
<!--the entity declarations may be overridden in the internal subset-->
<!--namespace prefixes-->
<!ENTITY % eanucc_prefix "eanucc">
<!ENTITY % pay_prefix "pay">
<!ENTITY % ds_prefix "ds">
<!--namespace prefix to namespace uri mappings-->
<!ENTITY % eanucc_prefix.. "%eanucc_prefix;:">
<!ENTITY % pay_prefix.. "%pay_prefix;:">
<!ENTITY % ds_prefix.. "%ds_prefix;:">
<!--namespaces attributes for root element-->
<!ENTITY % documentElementAttributes " xmlns:%eanucc_prefix; CDATA #FIXED 'urn:ean.ucc:2' xmlns:%pay_prefix; CDATA #FIXED 'urn:ean.ucc:pay:2' xmlns:%ds_prefix; CDATA #FIXED 'http://www.w3.org/2000/09/xmldsig#'">
<!--the declarations below should not be modified-->
<!--element name mappings-->
<!ENTITY % eanucc..document "%eanucc_prefix..;document">
<!ENTITY % eanucc..response "%eanucc_prefix..;response">
<!ENTITY % pay..invoice "%pay_prefix..;invoice">
<!ENTITY % ds..Signature "%ds_prefix..;Signature">
<!ENTITY % ds..SignatureValue "%ds_prefix..;SignatureValue">
<!ENTITY % ds..SignedInfo "%ds_prefix..;SignedInfo">
<!ENTITY % ds..CanonicalizationMethod "%ds_prefix..;CanonicalizationMethod">
<!ENTITY % ds..SignatureMethod "%ds_prefix..;SignatureMethod">
<!ENTITY % ds..HMACOutputLength "%ds_prefix..;HMACOutputLength">
<!ENTITY % ds..Reference "%ds_prefix..;Reference">
<!ENTITY % ds..Transforms "%ds_prefix..;Transforms">
<!ENTITY % ds..Transform "%ds_prefix..;Transform">
<!ENTITY % ds..XPath "%ds_prefix..;XPath">
<!ENTITY % ds..DigestMethod "%ds_prefix..;DigestMethod">
<!ENTITY % ds..DigestValue "%ds_prefix..;DigestValue">
<!ENTITY % ds..KeyInfo "%ds_prefix..;KeyInfo">
<!ENTITY % ds..KeyName "%ds_prefix..;KeyName">
<!ENTITY % ds..MgmtData "%ds_prefix..;MgmtData">
<!ENTITY % ds..KeyValue "%ds_prefix..;KeyValue">
<!ENTITY % ds..RetrievalMethod "%ds_prefix..;RetrievalMethod">
<!ENTITY % ds..X509Data "%ds_prefix..;X509Data">
<!ENTITY % ds..X509IssuerSerial "%ds_prefix..;X509IssuerSerial">
<!ENTITY % ds..X509SKI "%ds_prefix..;X509SKI">
<!ENTITY % ds..X509SubjectName "%ds_prefix..;X509SubjectName">
<!ENTITY % ds..X509Certificate "%ds_prefix..;X509Certificate">
<!ENTITY % ds..X509CRL "%ds_prefix..;X509CRL">
<!ENTITY % ds..X509IssuerName "%ds_prefix..;X509IssuerName">
<!ENTITY % ds..X509SerialNumber "%ds_prefix..;X509SerialNumber">
<!ENTITY % ds..PGPData "%ds_prefix..;PGPData">
<!ENTITY % ds..PGPKeyID "%ds_prefix..;PGPKeyID">
<!ENTITY % ds..PGPKeyPacket "%ds_prefix..;PGPKeyPacket">
<!ENTITY % ds..SPKIData "%ds_prefix..;SPKIData">
<!ENTITY % ds..SPKISexp "%ds_prefix..;SPKISexp">
<!ENTITY % ds..Object "%ds_prefix..;Object">
<!ENTITY % ds..Manifest "%ds_prefix..;Manifest">
<!ENTITY % ds..SignatureProperties "%ds_prefix..;SignatureProperties">
<!ENTITY % ds..SignatureProperty "%ds_prefix..;SignatureProperty">
<!ENTITY % ds..DSAKeyValue "%ds_prefix..;DSAKeyValue">
<!ENTITY % ds..P "%ds_prefix..;P">
<!ENTITY % ds..Q "%ds_prefix..;Q">
<!ENTITY % ds..G "%ds_prefix..;G">
<!ENTITY % ds..Y "%ds_prefix..;Y">
<!ENTITY % ds..J "%ds_prefix..;J">
<!ENTITY % ds..Seed "%ds_prefix..;Seed">
<!ENTITY % ds..PgenCounter "%ds_prefix..;PgenCounter">
<!ENTITY % ds..RSAKeyValue "%ds_prefix..;RSAKeyValue">
<!ENTITY % ds..Modulus "%ds_prefix..;Modulus">
<!ENTITY % ds..Exponent "%ds_prefix..;Exponent">
<!--wildcards-->
<!ENTITY % wildcard_absent "OVERWRITE_IN_INTERNAL_SUBSET">
<!ENTITY % wildcard_prefix_eanucc "%eanucc..document; | %eanucc..response;">
<!ENTITY % wildcard_prefix_pay "%pay..invoice;">
<!ENTITY % wildcard_prefix_ds "%ds..Signature; | %ds..SignatureValue; | %ds..SignedInfo; | %ds..CanonicalizationMethod; | %ds..SignatureMethod; | %ds..Reference; | %ds..Transforms; | %ds..Transform; | %ds..DigestMethod; | %ds..DigestValue; | %ds..KeyInfo; | %ds..KeyName; | %ds..MgmtData; | %ds..KeyValue; | %ds..RetrievalMethod; | %ds..X509Data; | %ds..PGPData; | %ds..SPKIData; | %ds..Object; | %ds..Manifest; | %ds..SignatureProperties; | %ds..SignatureProperty; | %ds..DSAKeyValue; | %ds..RSAKeyValue;">
<!ENTITY % wildcard_other "other:OVERWRITE_IN_INTERNAL_SUBSET">
<!--element and attribute declarations-->
<!ELEMENT invoiceAllowanceChargeAmount (#PCDATA)>
<!ELEMENT invoiceAllowanceChargeType (#PCDATA)>
<!ELEMENT invoiceAllowanceOrChargeType (#PCDATA)>
<!ELEMENT invoiceLineTaxInformation (dutyTaxFeeType, taxableAmount, taxAmount?, extension?, taxPercentage)>
<!ATTLIST invoiceLineTaxInformation
	dutyTaxFeeDescription CDATA #IMPLIED
	%documentElementAttributes;
>
<!ELEMENT amountExclusiveAllowancesCharges (#PCDATA)>
<!ELEMENT amountInclusiveAllowancesCharges (#PCDATA)>
<!ELEMENT tradeItemIdentification (gtin?, additionalTradeItemIdentification*)>
<!ELEMENT invoicedQuantity (value, unitOfMeasure?)>
<!ELEMENT itemDescription (text)>
<!ELEMENT itemPriceBaseQuantity (value, unitOfMeasure?)>
<!ELEMENT itemPriceExclusiveAllowancesCharges (#PCDATA)>
<!ELEMENT itemPriceInclusiveAllowancesCharges (#PCDATA)>
<!ELEMENT invoiceLineAllowanceCharge (invoiceAllowanceChargeAmount?, invoiceAllowanceChargeType?, invoiceAllowanceOrChargeType?, invoiceLineTaxInformation?)>
<!ELEMENT extension ((%wildcard_any;)*)>
<!ELEMENT totalInvoiceAmount (#PCDATA)>
<!ELEMENT totalLineAmountInclusiveAllowancesCharges (#PCDATA)>
<!ELEMENT totalTaxAmount (#PCDATA)>
<!ELEMENT taxSubTotal (dutyTaxFeeType, taxableAmount, taxAmount, extension?, taxPercentage)>
<!ATTLIST taxSubTotal
	dutyTaxFeeDescription CDATA #IMPLIED
	%documentElementAttributes;
>
<!ELEMENT totalInvoiceAmountPayable (#PCDATA)>
<!ELEMENT invoiceIdentification (uniqueCreatorIdentification)>
<!ELEMENT invoiceCurrency (currencyISOCode)>
<!ELEMENT invoiceType (#PCDATA)>
<!ELEMENT buyer (partyIdentification, nameAndAddress?, companyRegistrationNumber?, extension?)>
<!ELEMENT seller (partyIdentification, nameAndAddress?, companyRegistrationNumber?, extension?)>
<!ELEMENT orderedBy (partyIdentification, nameAndAddress?, companyRegistrationNumber?, extension?)>
<!ELEMENT invoicingPeriod EMPTY>
<!ATTLIST invoicingPeriod
	beginDate NMTOKEN #REQUIRED
	endDate NMTOKEN #REQUIRED
	%documentElementAttributes;
>
<!ELEMENT invoiceLineItem (tradeItemIdentification, invoicedQuantity, itemDescription, itemPriceBaseQuantity?, itemPriceExclusiveAllowancesCharges?, itemPriceInclusiveAllowancesCharges?, invoiceLineTaxInformation*, invoiceLineAllowanceCharge*, extension?)>
<!ATTLIST invoiceLineItem
	number NMTOKEN #REQUIRED
	%documentElementAttributes;
>
<!ELEMENT invoiceTotals (totalInvoiceAmount, totalLineAmountInclusiveAllowancesCharges, totalTaxAmount, taxSubTotal+, totalInvoiceAmountPayable?)>
<!ATTLIST invoiceTotals
	totalVATAmount NMTOKEN #IMPLIED
	prepaidAmount NMTOKEN #IMPLIED
	prepaidAmountDate NMTOKEN #IMPLIED
	baseAmount NMTOKEN #IMPLIED
	%documentElementAttributes;
>
<!ELEMENT invoiceAllowanceCharge (invoiceAllowanceChargeAmount?, invoiceAllowanceChargeType?, invoiceAllowanceOrChargeType?, invoiceLineTaxInformation?)>
<!ELEMENT paymentTerms (netPayment)>
<!ATTLIST paymentTerms
	paymentTermsType (FIXED_DATE) #REQUIRED
	paymentTermsEvent (AFTER_DATE_OF_DELIVERY | ANTICIPATED_DELIVERY_DATE | DATE_INVOICE_RECEIVED | DATE_OF_DELIVERY_TO_SITE | DATE_OF_INVOICE | DATE_OF_SHIPMENT_AS_EVIDENCED_BY_TRANSPORT_DOCUMENTS | EFFECTIVE_DATE | INVOICE_TRANSMISSION_DATE | PRIOR_TO_DATE_OF_DELIVERY | RECEIPT_OF_GOODS) #REQUIRED
	%documentElementAttributes;
>
<!ELEMENT invoice (uniqueCreatorIdentification)>
<!ATTLIST invoice
	creationDateTime NMTOKEN #IMPLIED
	%documentElementAttributes;
>
<!ELEMENT orderIdentification (uniqueCreatorIdentification)>
<!ATTLIST orderIdentification
	creationDateTime NMTOKEN #IMPLIED
	%documentElementAttributes;
>
<!ELEMENT deliveryNote (uniqueCreatorIdentification)>
<!ATTLIST deliveryNote
	creationDateTime NMTOKEN #IMPLIED
	%documentElementAttributes;
>
<!ELEMENT despatchAdvice (uniqueCreatorIdentification)>
<!ATTLIST despatchAdvice
	creationDateTime NMTOKEN #IMPLIED
	%documentElementAttributes;
>
<!ELEMENT communicationChannel EMPTY>
<!ATTLIST communicationChannel
	communicationChannelCode (EMAIL | TELEFAX | TELEPHONE | WEBSITE) #REQUIRED
	communicationNumber CDATA #REQUIRED
	%documentElementAttributes;
>
<!ELEMENT personOrDepartmentName (description+)>
<!ELEMENT countryISOCode (#PCDATA)>
<!ELEMENT countrySubDivisionISOCode (#PCDATA)>
<!ELEMENT reference (referenceDateTime, referenceIdentification)>
<!ELEMENT referenceDateTime (#PCDATA)>
<!ELEMENT referenceIdentification (#PCDATA)>
<!ELEMENT responseIdentification (uniqueCreatorIdentification)>
<!ELEMENT versionIdentification (#PCDATA)>
<!ELEMENT value (#PCDATA)>
<!ELEMENT unitOfMeasure (measurementUnitCodeValue)>
<!ELEMENT partyIdentification (gln?, additionalPartyIdentification*)>
<!ELEMENT nameAndAddress (city, countryCode, name, postalCode?, streetAddressOne?, streetAddressTwo?)>
<!ELEMENT companyRegistrationNumber (#PCDATA)>
<!ELEMENT language (languageISOCode)>
<!ELEMENT text (#PCDATA)>
<!ELEMENT languageISOCode (#PCDATA)>
<!ELEMENT longText (#PCDATA)>
<!ELEMENT longDescription (language, longText)>
<!ELEMENT description ANY>
<!ELEMENT discountDescription (description+)>
<!ELEMENT paymentTimePeriod (timePeriodDue | dayOfMonthDue | date)>
<!ELEMENT discountAmount (currencyCode, monetaryAmount)>
<!ELEMENT discountPercent (#PCDATA)>
<!ELEMENT percentOfPaymentDue (#PCDATA)>
<!ELEMENT netPayment (paymentTimePeriod)>
<!ELEMENT timePeriodDue (value)>
<!ATTLIST timePeriodDue
	timePeriod (DAYS | MONTHS | WEEKS) #REQUIRED
	%documentElementAttributes;
>
<!ELEMENT dayOfMonthDue (#PCDATA)>
<!ELEMENT date (#PCDATA)>
<!ELEMENT automatedClearingHousePaymentFormat (#PCDATA)>
<!ELEMENT paymentMethodType (#PCDATA)>
<!ELEMENT currencyCode (currencyISOCode)>
<!ELEMENT monetaryAmount (#PCDATA)>
<!ELEMENT currencyISOCode (#PCDATA)>
<!ELEMENT amount (currencyCode, monetaryAmount)>
<!ELEMENT latitude (#PCDATA)>
<!ELEMENT longitude (#PCDATA)>
<!ELEMENT city (#PCDATA)>
<!ELEMENT countryCode (countryISOCode)>
<!ELEMENT name (#PCDATA)>
<!ELEMENT postalCode (#PCDATA)>
<!ELEMENT streetAddressOne (#PCDATA)>
<!ELEMENT streetAddressTwo (#PCDATA)>
<!ELEMENT issuedCapital (currencyCode, monetaryAmount)>
<!ELEMENT legalStructure (text)>
<!ELEMENT dutyTaxFeeType (#PCDATA)>
<!ELEMENT taxableAmount (#PCDATA)>
<!ELEMENT taxAmount (#PCDATA)>
<!ELEMENT taxPercentage (#PCDATA)>
<!ELEMENT measurementUnitCodeValue (#PCDATA)>
<!ELEMENT additionalPartyIdentificationValue (#PCDATA)>
<!ELEMENT additionalPartyIdentificationType (#PCDATA)>
<!ELEMENT gln (#PCDATA)>
<!ELEMENT additionalPartyIdentification (additionalPartyIdentificationValue, additionalPartyIdentificationType)>
<!ELEMENT uniqueCreatorIdentification (#PCDATA)>
<!ELEMENT entityIdentification (uniqueCreatorIdentification)>
<!ELEMENT documentReference (uniqueCreatorIdentification)>
<!ATTLIST documentReference
	creationDateTime NMTOKEN #IMPLIED
	%documentElementAttributes;
>
<!ELEMENT additionalTradeItemIdentificationValue (#PCDATA)>
<!ELEMENT additionalTradeItemIdentificationType (#PCDATA)>
<!ELEMENT gtin (#PCDATA)>
<!ELEMENT additionalTradeItemIdentification (additionalTradeItemIdentificationValue, additionalTradeItemIdentificationType)>
<!ELEMENT earliestDate (#PCDATA)>
<!ELEMENT latestDate (#PCDATA)>
<!ELEMENT earliestTime (#PCDATA)>
<!ELEMENT latestTime (#PCDATA)>
<!ELEMENT time (#PCDATA)>
<!ELEMENT requestedDeliveryDateAtUltimateConsignee (date, time?)>
<!ELEMENT requestedDeliveryDate (date, time?)>
<!ELEMENT requestedShipDate (date, time?)>
<!ELEMENT requestedPickUpDate (date, time?)>
<!ELEMENT requestedDeliveryDateRangeAtUltimateConsignee (earliestDate, latestDate, earliestTime?, latestTime?)>
<!ELEMENT requestedShipDateRange (earliestDate, latestDate, earliestTime?, latestTime?)>
<!ELEMENT requestedDeliveryDateRange (earliestDate, latestDate, earliestTime?, latestTime?)>
<!ELEMENT shipToLogistics ((shipTo | shipToNonCommercial), shipFrom?, inventoryLocation?, ultimateConsignee?, shipmentTransportationInformation?)>
<!ELEMENT shipFromLogistics (shipFrom, shipTo?)>
<!ELEMENT orderLogisticalDateGroup (requestedDeliveryDateAtUltimateConsignee | requestedDeliveryDate | requestedShipDate | requestedPickUpDate | requestedDeliveryDateRangeAtUltimateConsignee | requestedShipDateRange | requestedDeliveryDateRange)>
<!ELEMENT shipFrom (gln?, additionalPartyIdentification*)>
<!ELEMENT shipTo (gln?, additionalPartyIdentification*)>
<!ELEMENT shipToNonCommercial (nameAndAddress, contact?)>
<!ELEMENT inventoryLocation (gln?, additionalPartyIdentification*)>
<!ELEMENT ultimateConsignee (gln?, additionalPartyIdentification*)>
<!ELEMENT shipmentTransportationInformation (carrier?, serviceLevelCode?, shipmentSpecialHandlingCode*)>
<!ATTLIST shipmentTransportationInformation
	transportationMethodType (AIR | AIR_CHARTER | AIR_EXPRESS | AIR_FREIGHT | BEST_WAY_SHIPPERS_OPTION | BOOK_POSTAL | BUS | CAB | CONSOLIDATION | CONTRACT_CARRIER | CUSTOMER_PICKUP | CUSTOMER_PICKUP_OR_CUSTOMERS_EXPENSE | EXPEDITED_TRUCK | GEOGRAPHIC_RECEIVING | GEOGRAPHIC_RECEIVING_SHIPPING | GEOGRAPHIC_SHIPPING | INLAND_WATERWAY | INTERMODAL_PIGGYBACK | LESS_THAN_TRUCK_LOAD | MOTOR | MOTOR_COMMON_CARRIER | MOTOR_FLATBED | MOTOR_PACKAGE_CARRIER | MOTOR_TRUCKLOAD | MOTOR_VAN | MUTUALLY_DEFINED | OCEAN | PARCEL_POST | PIPELINE | POOL_TO_POOL | POOLED_AIR | POOLED_PIGGYBACK | POOLED_PIGGYPACK | POOLED_RAIL | POOLED_TRUCK | PRIVATE_CARRIER | PRIVATE_PARCEL_SERVICE | PRIVATE_VESSEL | RAIL | ROADRAILER | SEA_AIR | STEAM_SHIP | SUPPLIER_TRUCK) #IMPLIED
	%documentElementAttributes;
>
<!ELEMENT contact (communicationChannel*, personOrDepartmentName)>
<!ELEMENT currencyConversionFrom (currencyISOCode)>
<!ELEMENT currencyConversionTo (currencyISOCode)>
<!ELEMENT exchangeRate (#PCDATA)>
<!ELEMENT exchangeRateDate (#PCDATA)>
<!ELEMENT documentLineReference (documentReference?)>
<!ATTLIST documentLineReference
	number NMTOKEN #REQUIRED
	%documentElementAttributes;
>
<!ELEMENT number (#PCDATA)>
<!ELEMENT accountNumberType (#PCDATA)>
<!ELEMENT accountName (#PCDATA)>
<!ELEMENT accountNumber (number, accountNumberType)>
<!ELEMENT routingNumber (number, routingNumberType)>
<!ELEMENT financialInsitutionNameAndAddress (city, countryCode, name, postalCode?, streetAddressOne?, streetAddressTwo?)>
<!ELEMENT branch (#PCDATA)>
<!ELEMENT additionalFinancialInformation (description+)>
<!ELEMENT routingNumberType (#PCDATA)>
<!ELEMENT carrier (gln?, additionalPartyIdentification*)>
<!ELEMENT serviceLevelCode (#PCDATA)>
<!ELEMENT shipmentSpecialHandlingCode (#PCDATA)>
<!ELEMENT %eanucc..document; EMPTY>
<!ATTLIST %eanucc..document;
	creationDateTime NMTOKEN #REQUIRED
	documentStatus (ADDITIONAL_TRANSMISSION) #REQUIRED
	%documentElementAttributes;
>
<!ELEMENT %eanucc..response; (responseIdentification)>
<!ATTLIST %eanucc..response;
	responseStatus (ACCEPTED | MODIFIED | REJECTED) #REQUIRED
	%documentElementAttributes;
>
<!ELEMENT %pay..invoice; (invoiceIdentification, invoiceCurrency, invoiceType, buyer, seller, orderedBy, invoicingPeriod?, invoiceLineItem*, invoiceTotals, invoiceAllowanceCharge*, paymentTerms+, invoice?, orderIdentification, deliveryNote?, despatchAdvice?, extension?)>
<!ATTLIST %pay..invoice;
	creationDateTime NMTOKEN #REQUIRED
	documentStatus (ADDITIONAL_TRANSMISSION) #REQUIRED
	%documentElementAttributes;
>
<!ELEMENT %ds..Signature; (%ds..SignedInfo;, %ds..SignatureValue;, (%ds..KeyInfo;)?, (%ds..Object;)*)>
<!ATTLIST %ds..Signature;
	Id ID #IMPLIED
	%documentElementAttributes;
>
<!ELEMENT %ds..SignatureValue; (#PCDATA)>
<!ATTLIST %ds..SignatureValue;
	Id ID #IMPLIED
	%documentElementAttributes;
>
<!ELEMENT %ds..SignedInfo; (%ds..CanonicalizationMethod;, %ds..SignatureMethod;, (%ds..Reference;)+)>
<!ATTLIST %ds..SignedInfo;
	Id ID #IMPLIED
	%documentElementAttributes;
>
<!ELEMENT %ds..CanonicalizationMethod; ANY>
<!ATTLIST %ds..CanonicalizationMethod;
	Algorithm CDATA #REQUIRED
	%documentElementAttributes;
>
<!ELEMENT %ds..SignatureMethod; (#PCDATA | %ds..HMACOutputLength; | %wildcard_other; | %wildcard_prefix_eanucc; | %wildcard_prefix_pay;)*>
<!ATTLIST %ds..SignatureMethod;
	Algorithm CDATA #REQUIRED
	%documentElementAttributes;
>
<!ELEMENT %ds..HMACOutputLength; (#PCDATA)>
<!ELEMENT %ds..Reference; ((%ds..Transforms;)?, %ds..DigestMethod;, %ds..DigestValue;)>
<!ATTLIST %ds..Reference;
	Id ID #IMPLIED
	URI CDATA #IMPLIED
	Type CDATA #IMPLIED
	%documentElementAttributes;
>
<!ELEMENT %ds..Transforms; ((%ds..Transform;)+)>
<!ATTLIST %ds..Transforms;
	%documentElementAttributes;
>
<!ELEMENT %ds..Transform; (#PCDATA | %ds..XPath; | %wildcard_other; | %wildcard_prefix_eanucc; | %wildcard_prefix_pay;)*>
<!ATTLIST %ds..Transform;
	Algorithm CDATA #REQUIRED
	%documentElementAttributes;
>
<!ELEMENT %ds..XPath; (#PCDATA)>
<!ELEMENT %ds..DigestMethod; (#PCDATA | %wildcard_other; | %wildcard_prefix_eanucc; | %wildcard_prefix_pay;)*>
<!ATTLIST %ds..DigestMethod;
	Algorithm CDATA #REQUIRED
	%documentElementAttributes;
>
<!ELEMENT %ds..DigestValue; (#PCDATA)>
<!ATTLIST %ds..DigestValue;
	%documentElementAttributes;
>
<!ELEMENT %ds..KeyInfo; (#PCDATA | %ds..KeyName; | %ds..KeyValue; | %ds..RetrievalMethod; | %ds..X509Data; | %ds..PGPData; | %ds..SPKIData; | %ds..MgmtData; | %wildcard_other; | %wildcard_prefix_eanucc; | %wildcard_prefix_pay;)*>
<!ATTLIST %ds..KeyInfo;
	Id ID #IMPLIED
	%documentElementAttributes;
>
<!ELEMENT %ds..KeyName; (#PCDATA)>
<!ATTLIST %ds..KeyName;
	%documentElementAttributes;
>
<!ELEMENT %ds..MgmtData; (#PCDATA)>
<!ATTLIST %ds..MgmtData;
	%documentElementAttributes;
>
<!ELEMENT %ds..KeyValue; (#PCDATA | %ds..DSAKeyValue; | %ds..RSAKeyValue; | %wildcard_other; | %wildcard_prefix_eanucc; | %wildcard_prefix_pay;)*>
<!ATTLIST %ds..KeyValue;
	%documentElementAttributes;
>
<!ELEMENT %ds..RetrievalMethod; ((%ds..Transforms;)?)>
<!ATTLIST %ds..RetrievalMethod;
	URI CDATA #IMPLIED
	Type CDATA #IMPLIED
	%documentElementAttributes;
>
<!ELEMENT %ds..X509Data; ((%ds..X509IssuerSerial; | %ds..X509SKI; | %ds..X509SubjectName; | %ds..X509Certificate; | %ds..X509CRL; | (%wildcard_other; | %wildcard_prefix_eanucc; | %wildcard_prefix_pay;)))+>
<!ATTLIST %ds..X509Data;
	%documentElementAttributes;
>
<!ELEMENT %ds..X509IssuerSerial; (%ds..X509IssuerName;, %ds..X509SerialNumber;)>
<!ELEMENT %ds..X509SKI; (#PCDATA)>
<!ELEMENT %ds..X509SubjectName; (#PCDATA)>
<!ELEMENT %ds..X509Certificate; (#PCDATA)>
<!ELEMENT %ds..X509CRL; (#PCDATA)>
<!ELEMENT %ds..X509IssuerName; (#PCDATA)>
<!ELEMENT %ds..X509SerialNumber; (#PCDATA)>
<!ELEMENT %ds..PGPData; ((%ds..PGPKeyID;, (%ds..PGPKeyPacket;)?, (%wildcard_other; | %wildcard_prefix_eanucc; | %wildcard_prefix_pay;)*) | (%ds..PGPKeyPacket;, (%wildcard_other; | %wildcard_prefix_eanucc; | %wildcard_prefix_pay;)*))>
<!ATTLIST %ds..PGPData;
	%documentElementAttributes;
>
<!ELEMENT %ds..PGPKeyID; (#PCDATA)>
<!ELEMENT %ds..PGPKeyPacket; (#PCDATA)>
<!ELEMENT %ds..SPKIData; (%ds..SPKISexp;, (%wildcard_other; | %wildcard_prefix_eanucc; | %wildcard_prefix_pay;)?)+>
<!ATTLIST %ds..SPKIData;
	%documentElementAttributes;
>
<!ELEMENT %ds..SPKISexp; (#PCDATA)>
<!ELEMENT %ds..Object; ANY>
<!ATTLIST %ds..Object;
	Id ID #IMPLIED
	MimeType CDATA #IMPLIED
	Encoding CDATA #IMPLIED
	%documentElementAttributes;
>
<!ELEMENT %ds..Manifest; ((%ds..Reference;)+)>
<!ATTLIST %ds..Manifest;
	Id ID #IMPLIED
	%documentElementAttributes;
>
<!ELEMENT %ds..SignatureProperties; ((%ds..SignatureProperty;)+)>
<!ATTLIST %ds..SignatureProperties;
	Id ID #IMPLIED
	%documentElementAttributes;
>
<!ELEMENT %ds..SignatureProperty; (#PCDATA | %wildcard_other; | %wildcard_prefix_eanucc; | %wildcard_prefix_pay;)*>
<!ATTLIST %ds..SignatureProperty;
	Target CDATA #REQUIRED
	Id ID #IMPLIED
	%documentElementAttributes;
>
<!ELEMENT %ds..DSAKeyValue; ((%ds..P;, %ds..Q;)?, (%ds..G;)?, %ds..Y;, (%ds..J;)?, (%ds..Seed;, %ds..PgenCounter;)?)>
<!ATTLIST %ds..DSAKeyValue;
	%documentElementAttributes;
>
<!ELEMENT %ds..P; (#PCDATA)>
<!ELEMENT %ds..Q; (#PCDATA)>
<!ELEMENT %ds..G; (#PCDATA)>
<!ELEMENT %ds..Y; (#PCDATA)>
<!ELEMENT %ds..J; (#PCDATA)>
<!ELEMENT %ds..Seed; (#PCDATA)>
<!ELEMENT %ds..PgenCounter; (#PCDATA)>
<!ELEMENT %ds..RSAKeyValue; (%ds..Modulus;, %ds..Exponent;)>
<!ATTLIST %ds..RSAKeyValue;
	%documentElementAttributes;
>
<!ELEMENT %ds..Modulus; (#PCDATA)>
<!ELEMENT %ds..Exponent; (#PCDATA)>
