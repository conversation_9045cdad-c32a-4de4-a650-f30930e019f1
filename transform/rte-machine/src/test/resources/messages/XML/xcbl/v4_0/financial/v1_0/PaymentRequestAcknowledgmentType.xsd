<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd" xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns:dgs="http://www.w3.org/2000/09/xmldsig#" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd" elementFormDefault="qualified">


    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType name="PaymentRequestAcknowledgmentType">
        <xsd:annotation>
            <xsd:documentation> acknowledges the receipt of the
        <!--code-->PaymentRequest<!--/code-->.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="PaymentRequestAcknHeader" type="PaymentRequestAcknHeaderType">
                <xsd:annotation>
                    <xsd:documentation>contains information to acknowledge receipt of a <!--code-->PaymentRequest<!--/code-->
        document.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="ListOfPaymentRequestAcknDetail" type="ListOfPaymentRequestAcknDetailType">
                <xsd:annotation>
                    <xsd:documentation>contains a list of one or more payment document details for which the
        <!--code-->PaymentRequestAcknowledgement<!--/code--> is referencing from the original <!--code-->PaymentRequest<!--/code-->
        document.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="PaymentRequestAcknSummary" type="PaymentRequestSummaryType">
                <xsd:annotation>
                    <xsd:documentation>contains summary information for the <!--code-->PaymentRequestAcknowledgment<!--/code-->
        document.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
