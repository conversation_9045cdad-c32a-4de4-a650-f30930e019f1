<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" elementFormDefault="qualified">
    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType name="UnitOfMeasurementType">
        <xsd:annotation>
            <xsd:documentation>specifies the units which relate to the measurement or quantity</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="UOMCoded" type="UOMCodeType">
                <xsd:annotation>
                    <xsd:documentation>is used to define the unit of measure.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="UOMCodedOther" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>is used to provide a non-standard <!--code-->UOMCode<!--/code-->.
        This element is mandatory if the value of <!--code-->UOMCoded<!--/code--> is
        'Other'. These codes should not contain white space unless absolutely
        necessary.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
