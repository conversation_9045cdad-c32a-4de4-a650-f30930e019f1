<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" elementFormDefault="qualified">
    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleType name="TransportModeCodeType">
        <xsd:annotation>
            <xsd:documentation>This code identifies the mode of transportation.  This code list is derived from EDIFACT Recommendation 19 (Mode of Transport) and X12 91 (Transportation Method/Type Code)</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:NMTOKEN">
            <xsd:enumeration value="Other">
                <xsd:annotation>
                    <xsd:documentation>Custom Code</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Air">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AirCharter">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AirExpress">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AirFreight">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AirFrieghtForwarder">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AirTaxi">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="AirTransport">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ArmedForcesCourierService">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Backhaul">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Barge">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BestWay-ShippersOption">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="BookPostal">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Bus">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CabOrTaxi">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CommonIrregularCarrier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Consolidation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContainerizedOcean">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ContractCarrier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ConventionalOcean">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomerPickup">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="CustomerPickupOrCustomersExpense">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Driveaway">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="DriveawayService">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="EuropeanOrPacificDistributionSystem">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ExpeditedTruck">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="FixedTransportInstallation">
                <xsd:annotation>
                    <xsd:documentation>This code applies to installations for continuous transport such as pipelines, ropeways and electric power lines</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Flyaway">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GeographicReceiving">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GeographicReceivingOrShipping">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GeographicShipping">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InlandWaterTransport">
                <xsd:annotation>
                    <xsd:documentation>This code is used only in such cases where the application of waterborne transport is reported separately with in the framework of a one-digit code</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="InlandWaterway">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Intermodal-Piggyback">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="LessThanTrailerLoad">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Logair">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Mail">
                <xsd:annotation>
                    <xsd:documentation>(Active mode of transport unknown) This code is provided for practical reasons, despite the fact that mail is not a genuine mode of transport. In many countries, the value of merchandise exported and imported by mail is considerable, but the exporter or importer concerned would be unable to state by which mode postal items had passed the national border.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MaritimeTransport">
                <xsd:annotation>
                    <xsd:documentation>Provision is made for separate reporting of inland water transport under code 8, assuming that appropriate definitions and rules of application will be given, particularly when transport covers sea as well as inland waterways.</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MilitaryIntratheaterAirliftService">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MilitaryOfficialMail">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ModeUnknown">
                <xsd:annotation>
                    <xsd:documentation>This code can be used when the mode is not known or when information on it is not available at the time of issuing the document concerned</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Motor">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Motor-BulkCarrier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Motor-CommonCarrier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Motor-Flatbed">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Motor-PackageCarrier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Motor-Van">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="MutimodalTransport">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Ocean">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OceanConferenceCarrier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="OceanNon-ConferenceCarrier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ParcelPost">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Pipeline">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PooledAir">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PooledPiggyback">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PooledRail">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PooledTruck">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PoolToPool">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrivateCarrier">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrivateParcelService">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="PrivateVessel">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Quicktrans">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Rail">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RailTransport">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Roadrailer">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="RoadTransport">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SeaOrAir">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipperAgent">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipperAgent-Truck">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ShipperAssociation">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="StackTrain">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Steamship">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="SupplierTruck">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="TowawayService">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="Vessel">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="WaterOrPipelineIntermodalMovement">
                <xsd:annotation>
                    <xsd:documentation/>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>
</xsd:schema>
