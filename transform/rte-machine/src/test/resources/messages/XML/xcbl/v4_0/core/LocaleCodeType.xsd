<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" elementFormDefault="qualified">
    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleType name="LocaleCodeType">
        <xsd:annotation>
            <xsd:documentation>This code identifies the location where the language is spoken.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:NMTOKEN">
            <xsd:enumeration value="Other">
                <xsd:annotation>
                    <xsd:documentation>Custom code</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ae">
                <xsd:annotation>
                    <xsd:documentation>United Arab Emirates</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="af">
                <xsd:annotation>
                    <xsd:documentation>Afghanistan</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ag">
                <xsd:annotation>
                    <xsd:documentation>Antigua and Barbuda</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ai">
                <xsd:annotation>
                    <xsd:documentation>Anguilla</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="al">
                <xsd:annotation>
                    <xsd:documentation>Albania</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="am">
                <xsd:annotation>
                    <xsd:documentation>Armenia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="an">
                <xsd:annotation>
                    <xsd:documentation>Netherlands Antilles</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ao">
                <xsd:annotation>
                    <xsd:documentation>Angola</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="aq">
                <xsd:annotation>
                    <xsd:documentation>Antarctica</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ar">
                <xsd:annotation>
                    <xsd:documentation>Argentina</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="as">
                <xsd:annotation>
                    <xsd:documentation>American Samoa</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="at">
                <xsd:annotation>
                    <xsd:documentation>Austria</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="au">
                <xsd:annotation>
                    <xsd:documentation>Australia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="aw">
                <xsd:annotation>
                    <xsd:documentation>Aruba</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="az">
                <xsd:annotation>
                    <xsd:documentation>Azerbaijan</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ba">
                <xsd:annotation>
                    <xsd:documentation>Bosnia-Hercegovina</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="bb">
                <xsd:annotation>
                    <xsd:documentation>Barbados</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="bd">
                <xsd:annotation>
                    <xsd:documentation>Bangladesh</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="be">
                <xsd:annotation>
                    <xsd:documentation>Belgium</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="bf">
                <xsd:annotation>
                    <xsd:documentation>Burkina Faso</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="bg">
                <xsd:annotation>
                    <xsd:documentation>Bulgaria</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="bh">
                <xsd:annotation>
                    <xsd:documentation>Bahrain</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="bi">
                <xsd:annotation>
                    <xsd:documentation>Burundi</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="bj">
                <xsd:annotation>
                    <xsd:documentation>Benin</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="bm">
                <xsd:annotation>
                    <xsd:documentation>Bermuda</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="bn">
                <xsd:annotation>
                    <xsd:documentation>Brunei Darussalam</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="bo">
                <xsd:annotation>
                    <xsd:documentation>Bolivia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="br">
                <xsd:annotation>
                    <xsd:documentation>Brazil</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="bs">
                <xsd:annotation>
                    <xsd:documentation>Bahamas</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="bt">
                <xsd:annotation>
                    <xsd:documentation>Bhutan</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="bu">
                <xsd:annotation>
                    <xsd:documentation>Burma (See MM Myanmar)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="bw">
                <xsd:annotation>
                    <xsd:documentation>Botswana</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="by">
                <xsd:annotation>
                    <xsd:documentation>Belarus</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="bz">
                <xsd:annotation>
                    <xsd:documentation>Belize</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ca">
                <xsd:annotation>
                    <xsd:documentation>Canada</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="cc">
                <xsd:annotation>
                    <xsd:documentation>Cocos (Keeling) Islands</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="cd">
                <xsd:annotation>
                    <xsd:documentation>Congo, Democratic Republic Of</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="cf">
                <xsd:annotation>
                    <xsd:documentation>Central African Republic</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="cg">
                <xsd:annotation>
                    <xsd:documentation>Congo</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ch">
                <xsd:annotation>
                    <xsd:documentation>Switzerland</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ci">
                <xsd:annotation>
                    <xsd:documentation>Cote D'Ivoire</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ck">
                <xsd:annotation>
                    <xsd:documentation>Cook Islands</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="cl">
                <xsd:annotation>
                    <xsd:documentation>Chile</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="cm">
                <xsd:annotation>
                    <xsd:documentation>Cameroon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="cn">
                <xsd:annotation>
                    <xsd:documentation>China</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="co">
                <xsd:annotation>
                    <xsd:documentation>Colombia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="cr">
                <xsd:annotation>
                    <xsd:documentation>Costa Rica</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="cs">
                <xsd:annotation>
                    <xsd:documentation>Former Czechoslovakia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="cu">
                <xsd:annotation>
                    <xsd:documentation>Cuba</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="cv">
                <xsd:annotation>
                    <xsd:documentation>Cape Verde</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="cx">
                <xsd:annotation>
                    <xsd:documentation>Christmas Islands</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="cy">
                <xsd:annotation>
                    <xsd:documentation>Cyprus</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="cz">
                <xsd:annotation>
                    <xsd:documentation>Czech Republic</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="de">
                <xsd:annotation>
                    <xsd:documentation>Germany, Federal Republic Of</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="dj">
                <xsd:annotation>
                    <xsd:documentation>Djibouti</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="dk">
                <xsd:annotation>
                    <xsd:documentation>Denmark</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="dm">
                <xsd:annotation>
                    <xsd:documentation>Dominica</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="do">
                <xsd:annotation>
                    <xsd:documentation>Dominican Republic</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="dz">
                <xsd:annotation>
                    <xsd:documentation>Algeria</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ec">
                <xsd:annotation>
                    <xsd:documentation>Ecuador</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ee">
                <xsd:annotation>
                    <xsd:documentation>Estonia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="eg">
                <xsd:annotation>
                    <xsd:documentation>Egypt</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="eh">
                <xsd:annotation>
                    <xsd:documentation>Western Sahara</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="er">
                <xsd:annotation>
                    <xsd:documentation>Eritrea</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="es">
                <xsd:annotation>
                    <xsd:documentation>Spain</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="et">
                <xsd:annotation>
                    <xsd:documentation>Ethiopia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="fi">
                <xsd:annotation>
                    <xsd:documentation>Finland</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="fj">
                <xsd:annotation>
                    <xsd:documentation>Fiji</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="fk">
                <xsd:annotation>
                    <xsd:documentation>Falkland Islands (Malvinas)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="fm">
                <xsd:annotation>
                    <xsd:documentation>Fed States Of Micronesia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="fo">
                <xsd:annotation>
                    <xsd:documentation>Faeroe Islands</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="fr">
                <xsd:annotation>
                    <xsd:documentation>France</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ga">
                <xsd:annotation>
                    <xsd:documentation>Gabon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="gb">
                <xsd:annotation>
                    <xsd:documentation>United Kingdom</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="gd">
                <xsd:annotation>
                    <xsd:documentation>Grenada</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ge">
                <xsd:annotation>
                    <xsd:documentation>Georgia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="gf">
                <xsd:annotation>
                    <xsd:documentation>French Guiana</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="gh">
                <xsd:annotation>
                    <xsd:documentation>Ghana</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="gi">
                <xsd:annotation>
                    <xsd:documentation>Gibraltar</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="gl">
                <xsd:annotation>
                    <xsd:documentation>Greenland</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="gm">
                <xsd:annotation>
                    <xsd:documentation>Gambia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="gn">
                <xsd:annotation>
                    <xsd:documentation>Guinea</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="gp">
                <xsd:annotation>
                    <xsd:documentation>Guadeloupe</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="gq">
                <xsd:annotation>
                    <xsd:documentation>Equatorial Guinea</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="gr">
                <xsd:annotation>
                    <xsd:documentation>Greece</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="gs">
                <xsd:annotation>
                    <xsd:documentation>S. Georgia and S. Sandwich Islands</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="gt">
                <xsd:annotation>
                    <xsd:documentation>Guatemala</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="gu">
                <xsd:annotation>
                    <xsd:documentation>Guam</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="gw">
                <xsd:annotation>
                    <xsd:documentation>Guinea-Bissau</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="gy">
                <xsd:annotation>
                    <xsd:documentation>Guyana</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="hk">
                <xsd:annotation>
                    <xsd:documentation>Hong Kong</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="hn">
                <xsd:annotation>
                    <xsd:documentation>Honduras</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="hr">
                <xsd:annotation>
                    <xsd:documentation>Croatia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ht">
                <xsd:annotation>
                    <xsd:documentation>Haiti</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="hu">
                <xsd:annotation>
                    <xsd:documentation>Hungary</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="id">
                <xsd:annotation>
                    <xsd:documentation>Indonesia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ie">
                <xsd:annotation>
                    <xsd:documentation>Ireland</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="il">
                <xsd:annotation>
                    <xsd:documentation>Israel</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="in">
                <xsd:annotation>
                    <xsd:documentation>India</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="io">
                <xsd:annotation>
                    <xsd:documentation>Indian Ocean Territory</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="iq">
                <xsd:annotation>
                    <xsd:documentation>Iraq</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ir">
                <xsd:annotation>
                    <xsd:documentation>Iran, Islamic Republic Of</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="is">
                <xsd:annotation>
                    <xsd:documentation>Iceland</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="it">
                <xsd:annotation>
                    <xsd:documentation>Italy</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="jm">
                <xsd:annotation>
                    <xsd:documentation>Jamaica</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="jo">
                <xsd:annotation>
                    <xsd:documentation>Jordan</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="jp">
                <xsd:annotation>
                    <xsd:documentation>Japan</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ke">
                <xsd:annotation>
                    <xsd:documentation>Kenya</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="kg">
                <xsd:annotation>
                    <xsd:documentation>Kyrgyzstan</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="kh">
                <xsd:annotation>
                    <xsd:documentation>Cambodia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ki">
                <xsd:annotation>
                    <xsd:documentation>Kiribati</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="km">
                <xsd:annotation>
                    <xsd:documentation>Comoros</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="kn">
                <xsd:annotation>
                    <xsd:documentation>St Kitts-Nevis</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="kp">
                <xsd:annotation>
                    <xsd:documentation>Korea, Democratic People's Rep</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="kr">
                <xsd:annotation>
                    <xsd:documentation>Korea, Republic Of</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="kw">
                <xsd:annotation>
                    <xsd:documentation>Kuwait</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ky">
                <xsd:annotation>
                    <xsd:documentation>Cayman Islands</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="kz">
                <xsd:annotation>
                    <xsd:documentation>Kazakhstan</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="la">
                <xsd:annotation>
                    <xsd:documentation>Lao, People's Democratic Rep</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="lb">
                <xsd:annotation>
                    <xsd:documentation>Lebanon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="lc">
                <xsd:annotation>
                    <xsd:documentation>Saint Lucia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="li">
                <xsd:annotation>
                    <xsd:documentation>Liechtenstein</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="lk">
                <xsd:annotation>
                    <xsd:documentation>Sri Lanka</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="lr">
                <xsd:annotation>
                    <xsd:documentation>Liberia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ls">
                <xsd:annotation>
                    <xsd:documentation>Lesotho</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="lt">
                <xsd:annotation>
                    <xsd:documentation>Lithuania</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="lu">
                <xsd:annotation>
                    <xsd:documentation>Luxembourg</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="lv">
                <xsd:annotation>
                    <xsd:documentation>Latvia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ly">
                <xsd:annotation>
                    <xsd:documentation>Lybian Arab Jamahiriya</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ma">
                <xsd:annotation>
                    <xsd:documentation>Morocco</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="mc">
                <xsd:annotation>
                    <xsd:documentation>Monaco</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="md">
                <xsd:annotation>
                    <xsd:documentation>Moldova</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="mg">
                <xsd:annotation>
                    <xsd:documentation>Madagascar</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="mh">
                <xsd:annotation>
                    <xsd:documentation>Marshall Islands</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="mk">
                <xsd:annotation>
                    <xsd:documentation>Frmr Yugoslav Rep Of Macedonia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ml">
                <xsd:annotation>
                    <xsd:documentation>Mali</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="mm">
                <xsd:annotation>
                    <xsd:documentation>Myanmar (Former Burma)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="mn">
                <xsd:annotation>
                    <xsd:documentation>Mongolia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="mo">
                <xsd:annotation>
                    <xsd:documentation>Macau</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="mp">
                <xsd:annotation>
                    <xsd:documentation>Northern Marianas</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="mq">
                <xsd:annotation>
                    <xsd:documentation>Martinique</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="mr">
                <xsd:annotation>
                    <xsd:documentation>Mauritania</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ms">
                <xsd:annotation>
                    <xsd:documentation>Montserrat</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="mt">
                <xsd:annotation>
                    <xsd:documentation>Malta</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="mu">
                <xsd:annotation>
                    <xsd:documentation>Mauritius</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="mv">
                <xsd:annotation>
                    <xsd:documentation>Maldives</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="mw">
                <xsd:annotation>
                    <xsd:documentation>Malawi</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="mx">
                <xsd:annotation>
                    <xsd:documentation>Mexico</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="my">
                <xsd:annotation>
                    <xsd:documentation>Malaysia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="mz">
                <xsd:annotation>
                    <xsd:documentation>Mozambique</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="na">
                <xsd:annotation>
                    <xsd:documentation>Namibia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="nc">
                <xsd:annotation>
                    <xsd:documentation>New Caledonia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ne">
                <xsd:annotation>
                    <xsd:documentation>Niger</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="nf">
                <xsd:annotation>
                    <xsd:documentation>Norfolk Island</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ng">
                <xsd:annotation>
                    <xsd:documentation>Nigeria</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ni">
                <xsd:annotation>
                    <xsd:documentation>Nicaragua</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="nl">
                <xsd:annotation>
                    <xsd:documentation>Netherlands</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="no">
                <xsd:annotation>
                    <xsd:documentation>Norway</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="np">
                <xsd:annotation>
                    <xsd:documentation>Nepal</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="nr">
                <xsd:annotation>
                    <xsd:documentation>Nauru</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="nu">
                <xsd:annotation>
                    <xsd:documentation>Niue</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="nz">
                <xsd:annotation>
                    <xsd:documentation>New Zealand</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="om">
                <xsd:annotation>
                    <xsd:documentation>Oman</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="pa">
                <xsd:annotation>
                    <xsd:documentation>Panama</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="pe">
                <xsd:annotation>
                    <xsd:documentation>Peru</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="pf">
                <xsd:annotation>
                    <xsd:documentation>French Polynesia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="pg">
                <xsd:annotation>
                    <xsd:documentation>Papua New Guinea</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ph">
                <xsd:annotation>
                    <xsd:documentation>Philippines</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="pk">
                <xsd:annotation>
                    <xsd:documentation>Pakistan</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="pl">
                <xsd:annotation>
                    <xsd:documentation>Poland</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="pm">
                <xsd:annotation>
                    <xsd:documentation>St Pierre and Miquelon</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="pn">
                <xsd:annotation>
                    <xsd:documentation>Pitcairn</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="pr">
                <xsd:annotation>
                    <xsd:documentation>Puerto Rico</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="pt">
                <xsd:annotation>
                    <xsd:documentation>Portugal</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="pw">
                <xsd:annotation>
                    <xsd:documentation>Palau</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="py">
                <xsd:annotation>
                    <xsd:documentation>Paraguay</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="qa">
                <xsd:annotation>
                    <xsd:documentation>Qatar</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="re">
                <xsd:annotation>
                    <xsd:documentation>Reunion</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ro">
                <xsd:annotation>
                    <xsd:documentation>Romania</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ru">
                <xsd:annotation>
                    <xsd:documentation>Russian Federation</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="rw">
                <xsd:annotation>
                    <xsd:documentation>Rwanda</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="sa">
                <xsd:annotation>
                    <xsd:documentation>Saudi Arabia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="sb">
                <xsd:annotation>
                    <xsd:documentation>Solomon Islands</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="sc">
                <xsd:annotation>
                    <xsd:documentation>Seychelles</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="sd">
                <xsd:annotation>
                    <xsd:documentation>Sudan</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="se">
                <xsd:annotation>
                    <xsd:documentation>Sweden</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="sg">
                <xsd:annotation>
                    <xsd:documentation>Singapore</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="sh">
                <xsd:annotation>
                    <xsd:documentation>St Helena</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="si">
                <xsd:annotation>
                    <xsd:documentation>Slovenia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="sj">
                <xsd:annotation>
                    <xsd:documentation>Svalbard and Jan Mayen</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="sk">
                <xsd:annotation>
                    <xsd:documentation>Slovakia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="sl">
                <xsd:annotation>
                    <xsd:documentation>Sierra Leone</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="sm">
                <xsd:annotation>
                    <xsd:documentation>San Marino</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="sn">
                <xsd:annotation>
                    <xsd:documentation>Senegal</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="so">
                <xsd:annotation>
                    <xsd:documentation>Somalia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="sr">
                <xsd:annotation>
                    <xsd:documentation>Suriname</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="st">
                <xsd:annotation>
                    <xsd:documentation>Sao Tome and Principe</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="sv">
                <xsd:annotation>
                    <xsd:documentation>El Salvador</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="sy">
                <xsd:annotation>
                    <xsd:documentation>Syrian Arab Republic</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="sz">
                <xsd:annotation>
                    <xsd:documentation>Swaziland</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="tc">
                <xsd:annotation>
                    <xsd:documentation>Turks and Caicos Islands</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="td">
                <xsd:annotation>
                    <xsd:documentation>Chad</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="tf">
                <xsd:annotation>
                    <xsd:documentation>French Southern Territories</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="tg">
                <xsd:annotation>
                    <xsd:documentation>Togo</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="th">
                <xsd:annotation>
                    <xsd:documentation>Thailand</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="tj">
                <xsd:annotation>
                    <xsd:documentation>Tajikistan</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="tm">
                <xsd:annotation>
                    <xsd:documentation>Turkmenistan</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="tn">
                <xsd:annotation>
                    <xsd:documentation>Tunisia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="to">
                <xsd:annotation>
                    <xsd:documentation>Tonga</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="tp">
                <xsd:annotation>
                    <xsd:documentation>East Timor</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="tr">
                <xsd:annotation>
                    <xsd:documentation>Turkey</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="tt">
                <xsd:annotation>
                    <xsd:documentation>Trinidad and Tobago</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="tv">
                <xsd:annotation>
                    <xsd:documentation>Tuvalu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="tw">
                <xsd:annotation>
                    <xsd:documentation>Taiwan, Province Of China</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="tz">
                <xsd:annotation>
                    <xsd:documentation>Tanzania, United Republic Of</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ua">
                <xsd:annotation>
                    <xsd:documentation>Ukraine</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ug">
                <xsd:annotation>
                    <xsd:documentation>Uganda</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="um">
                <xsd:annotation>
                    <xsd:documentation>US Minor Outlying Islands</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="us">
                <xsd:annotation>
                    <xsd:documentation>United States</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="uy">
                <xsd:annotation>
                    <xsd:documentation>Uruguay</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="uz">
                <xsd:annotation>
                    <xsd:documentation>Uzbekistan</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="va">
                <xsd:annotation>
                    <xsd:documentation>Vatican City State (Holy See)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="vc">
                <xsd:annotation>
                    <xsd:documentation>St Vincent and Grenadines</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ve">
                <xsd:annotation>
                    <xsd:documentation>Venezuela</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="vg">
                <xsd:annotation>
                    <xsd:documentation>Virgin Islands, British</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="vi">
                <xsd:annotation>
                    <xsd:documentation>United States Virgin Islands</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="vn">
                <xsd:annotation>
                    <xsd:documentation>Viet Nam</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="vu">
                <xsd:annotation>
                    <xsd:documentation>Vanuatu</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="wf">
                <xsd:annotation>
                    <xsd:documentation>Wallis and Futuna Islands</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ws">
                <xsd:annotation>
                    <xsd:documentation>Samoa</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="ye">
                <xsd:annotation>
                    <xsd:documentation>Yemen</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="yt">
                <xsd:annotation>
                    <xsd:documentation>Mayotte</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="yu">
                <xsd:annotation>
                    <xsd:documentation>Yugoslavia (Fed Rep Of)</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="za">
                <xsd:annotation>
                    <xsd:documentation>South Africa</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="zm">
                <xsd:annotation>
                    <xsd:documentation>Zambia</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="zw">
                <xsd:annotation>
                    <xsd:documentation>Zimbabwe</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>
</xsd:schema>
