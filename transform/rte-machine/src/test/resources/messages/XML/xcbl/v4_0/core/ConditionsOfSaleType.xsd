<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" elementFormDefault="qualified">
    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType name="ConditionsOfSaleType">
        <xsd:annotation>
            <xsd:documentation>details the conditions of sale for the item.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element maxOccurs="unbounded" name="SalesRequirement" type="SalesRequirementType">
                <xsd:annotation>
                    <xsd:documentation>outlines any specific sales information relating to the
        item.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="SalesActionCoded" type="SalesActionCodeType">
                <xsd:annotation>
                    <xsd:documentation>code indicating what action should be undertaken in specific
        circumstances.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="SalesActionCodedOther" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>is used to provide a non-standard <!--code-->SalesActionCode<!--/code-->. This element
        is mandatory if the value of <!--code-->SalesActionCoded<!--/code--> is 'Other'. These codes should
        not contain white space unless absolutely necessary.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="SalesActionValue" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>is the value associated with the action code.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
