<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" elementFormDefault="qualified">
    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType name="ReferenceCodedType">
        <xsd:annotation>
            <xsd:documentation>identifies the source of the information using a standard
        codelist.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="ReferenceTypeCoded" type="ReferenceTypeCodeType">
                <xsd:annotation>
                    <xsd:documentation>is used to identify the type of source of information by using a standard codelist.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ReferenceTypeCodedOther" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>is used to provide a non-standard <!--code-->ReferenceTypeCode<!--/code-->.
        This element is mandatory if the value of <!--code-->ReferenceTypeCoded<!--/code--> is
        'Other'. These codes should not contain white space unless absolutely
        necessary.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="PrimaryReference" type="ReferenceType">
                <xsd:annotation>
                    <xsd:documentation>holds the highest level of identification of the information
        source. This is typically used to identify a document.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="SupportingReference" type="ReferenceType">
                <xsd:annotation>
                    <xsd:documentation>holds the second level of identification of the information
        source. This is typically used to identify a line item in the document being
        referenced.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="SupportingSubReference" type="ReferenceType">
                <xsd:annotation>
                    <xsd:documentation>holds the lowest level of identification of the information
        source. This is typically used to reference a sub line item in a
        document.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ReferenceDescription" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>is used to describe a reference using free form text.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="ListOfReferenceCodedType">
        <xsd:annotation>
            <xsd:documentation>contains one or more elements containing information that can be
        used to find further information elsewhere that is not explicitly stated in
        another element.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element maxOccurs="unbounded" name="ReferenceCoded" type="ReferenceCodedType">
                <xsd:annotation>
                    <xsd:documentation>identifies the source of the information using a standard
        codelist.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
