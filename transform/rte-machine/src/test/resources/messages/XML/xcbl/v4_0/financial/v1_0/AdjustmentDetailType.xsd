<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd" xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns:dgs="http://www.w3.org/2000/09/xmldsig#" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd" elementFormDefault="qualified">
<xsd:import namespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" schemaLocation="../../core/core.xsd"/>
<xsd:include schemaLocation="AdjustmentReasonCodeType.xsd" />


    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType name="AdjustmentDetailType">
        <xsd:annotation>
            <xsd:documentation>contains the information relevant to making monetary adjustments.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element minOccurs="0" name="SequenceNum" type="xsd:int">
                <xsd:annotation>
                    <xsd:documentation>uniquely identifies the adjustment detail element.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="AdjustmentReasonCoded" type="AdjustmentReasonCodeType">
                <xsd:annotation>
                    <xsd:documentation>is a reason code for adjustment. </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="AdjustmentReasonCodedOther" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>is used to provide a non-standard <!--code-->AdjustmentReasonCode<!--/code-->.
        			This element is mandatory if the value of <!--code-->AdjustmentReasonCoded<!--/code--> is
        			'Other'. These codes should not contain white space unless absolutely
        			necessary.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="AdjustmentDate" type="xsd:dateTime">
                <xsd:annotation>
                    <xsd:documentation>is the date on which the adjustment was made.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="AdjustmentAmount" type="core:MonetaryValueType">
                <xsd:annotation>
                    <xsd:documentation>contains a monetary amount of adjustment.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="AdjustmentPercent" type="core:PercentType">
                <xsd:annotation>
                    <xsd:documentation>contains a percent of adjustment.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="AdjustmentDetailNote" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>describes any free-form text pertinent to the <!--code-->Adjustment<!--/code-->. This element may contain notes or any other similar information that
    				is not contained explicitly in another structure.  You should not assume that the receiving application
    				is capable of doing more than storing and/or displaying this information.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="ListOfAdjustmentDetailType">
        <xsd:sequence>
            <xsd:element maxOccurs="unbounded" name="AdjustmentDetail" type="AdjustmentDetailType"/>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
