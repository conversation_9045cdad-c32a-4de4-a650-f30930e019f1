Interchange Control Header
To start and identify an interchange of zero or more functional groups and 
interchange-related control segments

ELEMENTS
ISA01 M ID2..2 Authorization Information Qualifier (I01)
ISA02 M AN10..10 Authorization Information (I02)
ISA03 M ID2..2 Security Information Qualifier (I03)
ISA04 M AN10..10 Security Information (I04)
ISA05 M ID2..2 Interchange ID Qualifier (I05)
ISA06 M AN15..15 Interchange Sender ID (I06)
ISA07 M ID2..2 Interchange ID Qualifier (I05)
ISA08 M AN15..15 Interchange Receiver ID (I07)
ISA09 M DT6..6 Interchange Date (I08)
ISA10 M TM4..4 Interchange Time (I09)
ISA11 M ID1..1 Interchange Control Standards Identifier (I10)
ISA12 M ID5..5 Interchange Control Version Number (I11)
ISA13 M N0,9..9 Interchange Control Number (I12)
ISA14 M ID1..1 Acknowledgment Requested (I13)
ISA15 M ID1..1 Usage Indicator (I14)
ISA16 O Component Element Separator (I15)
  ISA00 O ID1..1 Dummy
  ISA00 O ID1..1 Dummy
