MESSAGE=830
MESSAGE_NAME=Planning Schedule with Release Capability
FUNCTIONAL_GROUP=PS
VERSION=002
RELEASE=040
AGENCY=ANSI X12
COMMENT
This standard provides the format and establishes the data contents of 
a planning schedule with release capability transaction set. The 
planning schedule with release capability transaction set provides for 
customary and established business practice relative to the transfer 
of forecasting/material release information between organizations.

The planning schedule transaction may be used in various ways or in a 
combination of ways, such as: (1) a simple forecast; (2) a forecast 
with the buyer's authorization for the seller to commit to resources, 
such as labor or material; (3) a forecast that is also used as an 
order release mechanism, containing such elements as resource 
authorizations, period-to-date cumulative quantities, and specific 
ship/delivery patterns for requirements that have been represented in 
``buckets,'' such as weekly, monthly, or quarterly. The order release 
forecast may also contain all data related to purchase orders, as 
required, because the order release capability eliminates the need for 
discrete generation of purchase orders.
ENDCOMMENT
SEGMENTS segs
ST M 1 Transaction Set Header
BFR M 1 Beginning Segment for Planning Schedule
NTE F 100 Note/Special Instruction
CUR O 1 Currency
REF O 12 Reference Numbers
PER O 3 Administrative Communications Contact
TAX O 3 Sales Tax Reference
FOB O 1 F.O.B. Related Instructions
group 1 O 200
  N1 M 1 Name
  N2 O 2 Additional Name Information
  N3 O 2 Address Information
  N4 O 1 Geographic Location
  REF O 12 Reference Numbers
  PER O 3 Administrative Communications Contact
  FOB O 1 F.O.B. Related Instructions
endgroup 1
CTP O 25 Pricing Information
SSS O 25 Special Services
CSH O 1 Header Sale Condition
ITD O 2 Terms of Sale/Deferred Terms of Sale
DTM O 10 Date/Time Reference
PID O 200 Product/Item Description
MEA O 40 Measurements
PWK O 25 Paperwork
PKG O 25 Marking
TD1 O 2 Carrier Details (Quantity and Weight)
TD5 O 12 Carrier Details (Routing Sequence/Transit Time)
TD3 O 12 Carrier Details (Equipment)
TD4 O 5 Carrier Details (Special Handling/Hazardous Materials)
MAN O 10 Marks and Numbers
group 2 M 10000
  LIN M 1 Item Identification
  UIT M 1 Unit Detail
  CUR O 1 Currency
  group 3 O 100
    SLN M 1 Subline Item Detail
    PID O 1000 Product/Item Description
  endgroup 3
  PO3 O 25 Additional Item Detail
  CTP O 25 Pricing Information
  PID O 1000 Product/Item Description
  MEA O 40 Measurements
  PWK O 25 Paperwork
  PKG O 25 Marking
  PO4 O 1 Item Physical Details
  PRS O 1 Part Release Status
  REF O 12 Reference Numbers
  PER O 3 Administrative Communications Contact
  SSS O 25 Special Services
  ITA O 10 Allowance
  ITD O 2 Terms of Sale/Deferred Terms of Sale
  TAX O 3 Sales Tax Reference
  FOB O 1 F.O.B. Related Instructions
  group 4 O 200
    N1 M 1 Name
    N2 O 2 Additional Name Information
    N3 O 2 Address Information
    N4 O 1 Geographic Location
    REF O 12 Reference Numbers
    PER O 3 Administrative Communications Contact
    FOB O 1 F.O.B. Related Instructions
  endgroup 4
  FST O 260 Forecast Schedule
  group 5 O 260
    SDP M 1 Ship/Delivery Pattern
    FST O 260 Forecast Schedule
  endgroup 5
  SDQ O 50 Destination Quantity
  ATH O 20 Resource Authorization
  group 6 O 25
    SHP M 1 Shipped/Received Information
    REF O 5 Reference Numbers
  endgroup 6
  TD1 O 1 Carrier Details (Quantity and Weight)
  TD5 O 12 Carrier Details (Routing Sequence/Transit Time)
  TD4 O 5 Carrier Details (Special Handling/Hazardous Materials)
  TD3 O 12 Carrier Details (Equipment)
  MAN O 10 Marks and Numbers
endgroup 2
CTT M 1 Transaction Totals
SE M 1 Transaction Set Trailer
USAGE

![ST]
To indicate the start of a transaction set and to assign a control 
number
Comment:
 The transaction set identifier (ST01) is intended for use by the 
translation routines of the interchange partners to select the 
appropriate transaction set definition (e.g., 810 selects the  invoice 
transaction set).

![BFR]
To indicate the beginning of a planning schedule transaction set; 
whether a ship or delivery based forecast; and related forecast 
envelope dates
Comment:
 BFR09 - Date Forecast Updated: The date the forecast was updated  with 
"Net Change" data.  (Used only when element 353 in BFR04  equals "04", 
meaning net change.)

![NTE]
To transmit information in a free-form format, if necessary, for 
comment or special instruction
Comment:
 The NTE segment permits free-form information/data which, under ANSI 
X12 standard implementations, is not machine processable.  The use of 
the ``NTE'' segment should therefore be avoided, if at all possible, 
in an automated environment.

![CUR]
To specify the currency (dollars, pounds, francs, etc.) used in a 
transaction
Comment:
 Monetary values are assumed to be expressed in the currency 
of the country of the transaction originator unless the optional 
CUR segment is used to specify a different currency. The CUR 
segment also permits the transaction originator to indicate 
a specific exchange rate, foreign exchange location and date/time 
as the basis for a currency conversion. Example 1. Assuming 
the currency of the transaction originator is U.S. dollars, 
the following CUR segment, when used in the heading area of 
a transaction, would indicate that all monetary values appearing 
in the transaction are expressed in Canadian Dollars (CAD). 
(In this example the exchange rate is at the discretion of the 
receiver).

CUR*BY*CAD N/L

Example 2. Assuming the currency of the transaction originator is 
U.S. dollars, the following CUR segment, when used in the detail area of a 
transaction, describes a currency conversion for that particular 
item from U.S. dollars to Canadian dollars. It also indicates 
that a specific exchange rate, at a specified foreign exchange 
location on a given date/time be used as the basis for the currency 
conversion. Notes below the diagram describe the meaning of 
the element values.

CUR*BY*USD*1.20*SE*CAD*NY*007*840821*1400 N/L
     1       2     3          4

1. Identifies the buyer's (BY) currency as U.S. dollars (USD).
2. The multiplier (1.20) is the exchange rate factor for the conversion.
3. Identifies the seller's (SE) currency as Canadian dollars (CAD).
4. Indicates the basis for the exchange rate as the New York Foreign Exchange 
(NY) and the effective date/time (007) as August 21, 1984 (840821) 
at 2:00 P.M. (1400).

The value for this item is to be converted to Canadian dollars (CAD) at 
the exchange rate of 1.20, based on the New York Foreign Exchange (NY) 
at 2:00 P.M. (1400) on August 21, 1984. The actual unit price conversion 
for the item would be:

The unit price value 7.50 (U.S. dollars) multiplied 
by the exchange rate (1.20) equals 9.00 Canadian dollars (7.50 
X 1.20 = 9.00) CUR07 through CUR21 provide for five (5) dates/times 
relating to the currency conversion, i.e., effective date, expiration 
date, etc.

![REF]
To specify identifying numbers.

![PER]
To identify a person or office to whom administrative communications 
should be directed

![TAX]
To provide data required for proper notification/determination of 
applicable sales and related taxes applying to the transaction.
Comment:
 TAX01 is required if tax exemption is being claimed.

![FOB]
To specify transportation instructions relating to shipment
Comment:
 FOB08 is the code specifying the point at which the risk of loss 
transfers. This may be different than the location specified in 
FOB02/FOB03 and FOB06/FOB07.

![g1]

![N1 1]
To identify a party by type of organization, name and code
Comment:
 This segment, used alone, provides the most efficient method of 
providing organizational identification.  To obtain this  efficiency 
the "ID Code" (N104) must provide a key to the table  maintained by 
the transaction processing party.

![N2 1]
To specify additional names or those longer than 35 characters in 
length

![N3 1]
To specify the location of the named party

![N4 1]
To specify the geographic place of the named party
Comment:
 N402 is required only if city name (N401) is in the USA or Canada.

![REF 1]
To specify identifying numbers.

![PER 1]
To identify a person or office to whom administrative communications 
should be directed

![FOB 1]
To specify transportation instructions relating to shipment
Comment:
 FOB08 is the code specifying the point at which the risk of loss 
transfers. This may be different than the location specified in 
FOB02/FOB03 and FOB06/FOB07.

![CTP]
To specify pricing information
Comment:
 CTP07 is a multiplier factor to arrive at a final discounted price.  A 
multiplier of 90 would be the factor if a 10% discount is given.

![SSS]
To specify special conditions or services associated with the 
purchased product
Comment:
 SSS07 (Description) is normally used to clarify/expand on the 
services to be provided.

![CSH]
To specify general conditions or requirements of the sale
Comment:
 CSH04 is the account number to which the purchase amount is to be 
charged.

![ITD]
To specify terms of sale.
Comment:
 If the code in ITD01 is 04, then ITD09 is required and either ITD10 or 
ITD11 is required. If the code in ITD01 equals 05, then ITD06 or ITD07 
is required. If the code in ITD01 does not equal 04 or 05, then ITD03 
or ITD08 is required.

![DTM]
To specify pertinent dates and times

![PID]
To describe a product or process in coded or free-form format
Comment:
 Use PID06 when necessary to refer to the product surface or layer 
being described in the segment.

![MEA]
To specify physical measurements, including dimensions, tolerances, 
weights and counts.
Comment:
 When citing dimensional tolerances, any measurement requiring a  sign 
(+ or -), or any measurement where a positive (+) value  cannot be 
assumed use MEA05 as the negative (-) value and MEA06  as the positive 
(+) value.

![PWK]
To specify the type and transmission of paperwork relating to product 
or order.
Comment:
 PWK07 may be used to indicate special information to be shown on  the 
specified report.

![PKG]
To describe marking, packaging, loading and unloading requirements.
Comment:
 Special marking or tagging data can be given in PKG05  (Description).

![TD1]
To specify the transportation details relative to commodity, weight 
and quantity.

![TD5]
To specify the carrier, sequence of routing and to provide transit 
time information
Comment:
 When specifying a routing sequence to be used for the shipment 
movement in lieu of specifying each carrier within the movement:   use 
TD502 to identify the party responsible for defining the  routing 
sequence; use TD503 to identify the actual routing  sequence, 
specified by the party identified in TD502.

![TD3]
To specify transportation details relating to the equipment used by 
the carrier.

![TD4]
To specify transportation special handling requirements and hazardous 
materials information

![MAN]
To indicate identifying marks and numbers for shipping containers

![g2]

![LIN 2]
To specify basic item identification data.
Comment:
 LIN02 through LIN31 provide for fifteen (15) different 
product/service ID's for each item.  For Example: Case, Color, 
Drawing No., UPC No., ISBN No., Model No., SKU.

![UIT 2]
To specify item unit data

![CUR 2]
To specify the currency (dollars, pounds, francs, etc.) used in a 
transaction
Comment:
 Monetary values are assumed to be expressed in the currency 
of the country of the transaction originator unless the optional 
CUR segment is used to specify a different currency. The CUR 
segment also permits the transaction originator to indicate 
a specific exchange rate, foreign exchange location and date/time 
as the basis for a currency conversion. Example 1. Assuming 
the currency of the transaction originator is U.S. dollars, 
the following CUR segment, when used in the heading area of 
a transaction, would indicate that all monetary values appearing 
in the transaction are expressed in Canadian Dollars (CAD). 
(In this example the exchange rate is at the discretion of the 
receiver).

CUR*BY*CAD N/L

Example 2. Assuming the currency of the transaction originator is 
U.S. dollars, the following CUR segment, when used in the detail area of a 
transaction, describes a currency conversion for that particular 
item from U.S. dollars to Canadian dollars. It also indicates 
that a specific exchange rate, at a specified foreign exchange 
location on a given date/time be used as the basis for the currency 
conversion. Notes below the diagram describe the meaning of 
the element values.

CUR*BY*USD*1.20*SE*CAD*NY*007*840821*1400 N/L
     1       2     3          4

1. Identifies the buyer's (BY) currency as U.S. dollars (USD).
2. The multiplier (1.20) is the exchange rate factor for the conversion.
3. Identifies the seller's (SE) currency as Canadian dollars (CAD).
4. Indicates the basis for the exchange rate as the New York Foreign Exchange 
(NY) and the effective date/time (007) as August 21, 1984 (840821) 
at 2:00 P.M. (1400).

The value for this item is to be converted to Canadian dollars (CAD) at 
the exchange rate of 1.20, based on the New York Foreign Exchange (NY) 
at 2:00 P.M. (1400) on August 21, 1984. The actual unit price conversion 
for the item would be:

The unit price value 7.50 (U.S. dollars) multiplied 
by the exchange rate (1.20) equals 9.00 Canadian dollars (7.50 
X 1.20 = 9.00) CUR07 through CUR21 provide for five (5) dates/times 
relating to the currency conversion, i.e., effective date, expiration 
date, etc.

![g3]

![SLN 3]
To specify product subline detail item data
Comment:
 SLN03 through SLN28 provide for eleven (11) different  product/service 
ID's for each item. For example: Case, Color,  Drawing No., UPC No., 
ISBN No., Model No., SKU.

![PID 3]
To describe a product or process in coded or free-form format
Comment:
 Use PID06 when necessary to refer to the product surface or layer 
being described in the segment.

![PO3 3]
To specify additional item related data involving variations in normal 
price/quantity structure.
Comment:
 PO307 defines the unit of measure for PO306.

![CTP 3]
To specify pricing information
Comment:
 CTP07 is a multiplier factor to arrive at a final discounted price.  A 
multiplier of 90 would be the factor if a 10% discount is given.

![PID 3]
To describe a product or process in coded or free-form format
Comment:
 Use PID06 when necessary to refer to the product surface or layer 
being described in the segment.

![MEA 3]
To specify physical measurements, including dimensions, tolerances, 
weights and counts.
Comment:
 When citing dimensional tolerances, any measurement requiring a  sign 
(+ or -), or any measurement where a positive (+) value  cannot be 
assumed use MEA05 as the negative (-) value and MEA06  as the positive 
(+) value.

![PWK 3]
To specify the type and transmission of paperwork relating to product 
or order.
Comment:
 PWK07 may be used to indicate special information to be shown on  the 
specified report.

![PKG 3]
To describe marking, packaging, loading and unloading requirements.
Comment:
 Special marking or tagging data can be given in PKG05  (Description).

![PO4 3]
To specify the physical qualities, packaging, weights and dimensions 
relating to the item.
Comment:
 PO410 defines the unit of measure for PO408, PO409, and PO410.

![PRS 3]
To indicate the status of the part being ordered with respect to this 
material release (only use if the planning schedule is considered to 
be an order/material release).

![REF 3]
To specify identifying numbers.

![PER 3]
To identify a person or office to whom administrative communications 
should be directed

![SSS 3]
To specify special conditions or services associated with the 
purchased product
Comment:
 SSS07 (Description) is normally used to clarify/expand on the 
services to be provided.

![ITA 3]
To specify allowances, charges or services
Comment:
 ITA12 is the quantity of free goods.

![ITD 3]
To specify terms of sale.
Comment:
 If the code in ITD01 is 04, then ITD09 is required and either ITD10 or 
ITD11 is required. If the code in ITD01 equals 05, then ITD06 or ITD07 
is required. If the code in ITD01 does not equal 04 or 05, then ITD03 
or ITD08 is required.

![TAX 3]
To provide data required for proper notification/determination of 
applicable sales and related taxes applying to the transaction.
Comment:
 TAX01 is required if tax exemption is being claimed.

![FOB 3]
To specify transportation instructions relating to shipment
Comment:
 FOB08 is the code specifying the point at which the risk of loss 
transfers. This may be different than the location specified in 
FOB02/FOB03 and FOB06/FOB07.

![g4]

![N1 4]
To identify a party by type of organization, name and code
Comment:
 This segment, used alone, provides the most efficient method of 
providing organizational identification.  To obtain this  efficiency 
the "ID Code" (N104) must provide a key to the table  maintained by 
the transaction processing party.

![N2 4]
To specify additional names or those longer than 35 characters in 
length

![N3 4]
To specify the location of the named party

![N4 4]
To specify the geographic place of the named party
Comment:
 N402 is required only if city name (N401) is in the USA or Canada.

![REF 4]
To specify identifying numbers.

![PER 4]
To identify a person or office to whom administrative communications 
should be directed

![FOB 4]
To specify transportation instructions relating to shipment
Comment:
 FOB08 is the code specifying the point at which the risk of loss 
transfers. This may be different than the location specified in 
FOB02/FOB03 and FOB06/FOB07.

![FST 4]
To specify the forecasted dates and quantities
At least one occurence of segment FST is required, either as a 
stand-alone segment or within the SDP loop.
Comment:
 FST06 - To qualify time in FST07.  The purpose of the FST07  element 
is to express the specific time of day in a 24-hour  clock, to satisfy 
"just-in-time" requirements.  As an  alternative, the ship/delivery 
pattern segment (SDP) may be used  to define an approximate time, such 
as "AM" or "PM".

![g5]

![SDP 5]
To identify specific ship/delivery requirements
Comment:
 The intent of this segment is to define the routine ship or  delivery 
patterns, as required, when order quantities are in  "buckets", such 
as weekly, monthly. Ship/Delivery patterns  eliminate the need to 
transmit discrete quantities and dates for  each required shipment or 
delivery. It is assumed that a  "bucketed" quantity is to be divided 
equally by the ship/delivery  pattern.  For example, a weekly quantity 
of 100 with a delivery  pattern of Monday and Wednesday would result 
in 50 to be  delivered on Monday and 50 to be delivered on Wednesday.

![FST 5]
To specify the forecasted dates and quantities
Comment:
 FST06 - To qualify time in FST07.  The purpose of the FST07  element 
is to express the specific time of day in a 24-hour  clock, to satisfy 
"just-in-time" requirements.  As an  alternative, the ship/delivery 
pattern segment (SDP) may be used  to define an approximate time, such 
as "AM" or "PM".

![SDQ 5]
To specify destination and quantity detail.
Comment:
 SDQ02 is used only if different than previously defined in the 
transaction set.

![ATH 5]
To specify resource authorizations (i.e., finished labor, material, 
etc.) in the planning schedule.
Comment:
 ATH05 - Cumulative Start-Date: The date where the cumulative  quantity 
count starts.  This date might be the start-date of a  contract 
period, a calendar or fiscal year, or other.

![g6]

![SHP 6]
To specify shipment and/or receipt information
Comment:
 SHP06 - The cumulative quantity end date.

![REF 6]
To specify identifying numbers.

![TD1 6]
To specify the transportation details relative to commodity, weight 
and quantity.

![TD5 6]
To specify the carrier, sequence of routing and to provide transit 
time information
Comment:
 When specifying a routing sequence to be used for the shipment 
movement in lieu of specifying each carrier within the movement:   use 
TD502 to identify the party responsible for defining the  routing 
sequence; use TD503 to identify the actual routing  sequence, 
specified by the party identified in TD502.

![TD4 6]
To specify transportation special handling requirements and hazardous 
materials information

![TD3 6]
To specify transportation details relating to the equipment used by 
the carrier.

![MAN 6]
To indicate identifying marks and numbers for shipping containers

![CTT]
To transmit a hash total for a specific element in the transaction set
Number of line items (CTT01) is the accumulation of the number of LIN 
segments. If used, hash total (CTT02) is the sum of the values of the 
quantities (FST01) for each FST segment.
Comment:
 This segment is intended to provide hash totals to validate 
transaction completeness and correctness.

![SE]
To indicate the end of the transaction set and provide the count  of 
the transmitted segments (including the beginning (ST) and  ending 
(SE) segments).
