Loop Header
To indicate that the next segment begins a loop

ELEMENTS
LS01 M ID1..4 Loop Identifier Code (447)
DEPENDENCIES
O LSne LS l LSoo LSp  LSma LSy  LSbe LS n LSes LSte LSd  LS(c LSon LSta LSin LSed LS)  LSwi LSth LSin LS a LSno LSth LSer LS l LSoo LSp, LS p LSro LSvi LSde LSd  LSth LSe  LS
i LSnn LSer LS ( LSne LSst LSed LS)  LSlo LSop LS t LSer LSmi LSna LSte LSs  LSbe LSfo LSre LS t LShe LS o LSut LSer LS l LSoo LSp. LS W LShe LSn  LSsp LSec LSif LSie LSd  LS
b LSy  LSth LSe  LSst LSan LSda LSrd LS s LSet LSti LSng LS b LSod LSy  LSas LS ` LS`m LSan LSda LSto LSry LS'' LS,  LSth LSis LS s LSeg LSme LSnt LS i LSn  LS
c LSom LSbi LSna LSti LSon LS w LSit LSh  LS`` LSLE LS'' LS,  LSmu LSst LS b LSe  LSus LSed LS.  LS I LSt  LSis LS n LSot LS t LSo  LSbe LS u LSse LSd  LSif LS n LSot LS 
 LSsp LSec LSif LSic LSal LSly LS s LSet LS f LSor LSth LS f LSor LS u LSse LS.  LSTh LSe  LSlo LSop LS i LSde LSnt LSif LSie LSr  LSin LS t LShe LS l LSoo LSp  LShe LSad LSer LS 
 LSan LSd  LStr LSai LSle LSr  LSmu LSst LS b LSe  LSid LSen LSti LSca LSl. LS   LSTh LSe  LSpr LSef LSer LSre LSd  LSva LSlu LSe  LSfo LSr  LSth LSe  LSid LSen LSti LSfi LSer LS 
 LSis LS t LShe LS s LSeg LSme LSnt LS I LSD  LSof LS t LShe LS r LSeq LSui LSre LSd  LSlo LSop LS b LSeg LSin LSni LSng LS s LSeg LSme LSnt LS.
