Item Identification
To specify basic item identification data.

ELEMENTS
LIN01 O AN1..6 Assigned Identification (350)
LIN02 M ID2..2 Product/Service ID Qualifier (235)
LIN03 M AN1..30 Product/Service ID (234)
LIN04 O ID2..2 Product/Service ID Qualifier (235)
LIN05 X AN1..30 Product/Service ID (234)
LIN06 O ID2..2 Product/Service ID Qualifier (235)
LIN07 X AN1..30 Product/Service ID (234)
LIN08 O ID2..2 Product/Service ID Qualifier (235)
LIN09 X AN1..30 Product/Service ID (234)
LIN10 O ID2..2 Product/Service ID Qualifier (235)
LIN11 X AN1..30 Product/Service ID (234)
LIN12 O ID2..2 Product/Service ID Qualifier (235)
LIN13 X AN1..30 Product/Service ID (234)
LIN14 O ID2..2 Product/Service ID Qualifier (235)
LIN15 X AN1..30 Product/Service ID (234)
LIN16 O ID2..2 Product/Service ID Qualifier (235)
LIN17 X AN1..30 Product/Service ID (234)
LIN18 O ID2..2 Product/Service ID Qualifier (235)
LIN19 X AN1..30 Product/Service ID (234)
LIN20 O ID2..2 Product/Service ID Qualifier (235)
LIN21 X AN1..30 Product/Service ID (234)
LIN22 O ID2..2 Product/Service ID Qualifier (235)
LIN23 X AN1..30 Product/Service ID (234)
LIN24 O ID2..2 Product/Service ID Qualifier (235)
LIN25 X AN1..30 Product/Service ID (234)
LIN26 O ID2..2 Product/Service ID Qualifier (235)
LIN27 X AN1..30 Product/Service ID (234)
LIN28 O ID2..2 Product/Service ID Qualifier (235)
LIN29 X AN1..30 Product/Service ID (234)
LIN30 O ID2..2 Product/Service ID Qualifier (235)
LIN31 X AN1..30 Product/Service ID (234)
DEPENDENCIES
I LINf  LINLI LINN0 LIN4  LINis LIN p LINre LINse LINnt LIN,  LINth LINen LIN L LININ LIN05 LIN i LINs  LINre LINqu LINir LINed LIN.
I LINf  LINLI LINN0 LIN6  LINis LIN p LINre LINse LINnt LIN,  LINth LINen LIN L LININ LIN07 LIN i LINs  LINre LINqu LINir LINed LIN.
I LINf  LINLI LINN0 LIN8  LINis LIN p LINre LINse LINnt LIN,  LINth LINen LIN L LININ LIN09 LIN i LINs  LINre LINqu LINir LINed LIN.
I LINf  LINLI LINN1 LIN0  LINis LIN p LINre LINse LINnt LIN,  LINth LINen LIN L LININ LIN11 LIN i LINs  LINre LINqu LINir LINed LIN.
I LINf  LINLI LINN1 LIN2  LINis LIN p LINre LINse LINnt LIN,  LINth LINen LIN L LININ LIN13 LIN i LINs  LINre LINqu LINir LINed LIN.
I LINf  LINLI LINN1 LIN4  LINis LIN p LINre LINse LINnt LIN,  LINth LINen LIN L LININ LIN15 LIN i LINs  LINre LINqu LINir LINed LIN.
I LINf  LINLI LINN1 LIN6  LINis LIN p LINre LINse LINnt LIN,  LINth LINen LIN L LININ LIN17 LIN i LINs  LINre LINqu LINir LINed LIN.
I LINf  LINLI LINN1 LIN8  LINis LIN p LINre LINse LINnt LIN,  LINth LINen LIN L LININ LIN19 LIN i LINs  LINre LINqu LINir LINed LIN.
I LINf  LINLI LINN2 LIN0  LINis LIN p LINre LINse LINnt LIN,  LINth LINen LIN L LININ LIN21 LIN i LINs  LINre LINqu LINir LINed LIN.
I LINf  LINLI LINN2 LIN2  LINis LIN p LINre LINse LINnt LIN,  LINth LINen LIN L LININ LIN23 LIN i LINs  LINre LINqu LINir LINed LIN.
I LINf  LINLI LINN2 LIN4  LINis LIN p LINre LINse LINnt LIN,  LINth LINen LIN L LININ LIN25 LIN i LINs  LINre LINqu LINir LINed LIN.
I LINf  LINLI LINN2 LIN6  LINis LIN p LINre LINse LINnt LIN,  LINth LINen LIN L LININ LIN27 LIN i LINs  LINre LINqu LINir LINed LIN.
I LINf  LINLI LINN2 LIN8  LINis LIN p LINre LINse LINnt LIN,  LINth LINen LIN L LININ LIN29 LIN i LINs  LINre LINqu LINir LINed LIN.
I LINf  LINLI LINN3 LIN0  LINis LIN p LINre LINse LINnt LIN,  LINth LINen LIN L LININ LIN31 LIN i LINs  LINre LINqu LINir LINed LIN.
