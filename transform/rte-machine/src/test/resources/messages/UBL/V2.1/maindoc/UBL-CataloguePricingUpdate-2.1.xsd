<?xml version="1.0" encoding="UTF-8"?>
<!--
  Library:           OASIS Universal Business Language (UBL) 2.1 OS
                     http://docs.oasis-open.org/ubl/os-UBL-2.1/
  Release Date:      04 November 2013
  Module:            xsd/maindoc/UBL-CataloguePricingUpdate-2.1.xsd
  Generated on:      2013-10-31 17:17z
  Copyright (c) OASIS Open 2013. All Rights Reserved.
-->
<xsd:schema xmlns="urn:oasis:names:specification:ubl:schema:xsd:CataloguePricingUpdate-2"
            xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
            xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
            xmlns:ext="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2"
            xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            xmlns:ccts="urn:un:unece:uncefact:documentation:2"
            targetNamespace="urn:oasis:names:specification:ubl:schema:xsd:CataloguePricingUpdate-2"
            elementFormDefault="qualified"
            attributeFormDefault="unqualified"
            version="2.1">
   <!-- ===== Imports ===== -->
   <xsd:import namespace="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
               schemaLocation="../common/UBL-CommonAggregateComponents-2.1.xsd"/>
   <xsd:import namespace="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
               schemaLocation="../common/UBL-CommonBasicComponents-2.1.xsd"/>
   <xsd:import namespace="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2"
               schemaLocation="../common/UBL-CommonExtensionComponents-2.1.xsd"/>
   <!-- ===== Element Declarations ===== -->
   <xsd:element name="CataloguePricingUpdate" type="CataloguePricingUpdateType">
      <xsd:annotation>
         <xsd:documentation>This element MUST be conveyed as the root element in any instance document based on this Schema expression</xsd:documentation>
      </xsd:annotation>
   </xsd:element>
   <!-- ===== Type Definitions ===== -->
   <!-- ===== Aggregate Business Information Entity Type Definitions ===== -->
   <xsd:complexType name="CataloguePricingUpdateType">
      <xsd:annotation>
         <xsd:documentation>
            <ccts:Component>
               <ccts:ComponentType>ABIE</ccts:ComponentType>
               <ccts:DictionaryEntryName>Catalogue Pricing Update. Details</ccts:DictionaryEntryName>
               <ccts:Definition>A document used to update information about prices in an existing Catalogue.</ccts:Definition>
               <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
            </ccts:Component>
         </xsd:documentation>
      </xsd:annotation>
      <xsd:sequence>
           <xsd:element ref="ext:UBLExtensions" minOccurs="0" maxOccurs="1">
              <xsd:annotation>
                 <xsd:documentation>A container for all extensions present in the document.</xsd:documentation>
              </xsd:annotation>
           </xsd:element>
          <xsd:element ref="cbc:UBLVersionID" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. UBL Version Identifier. Identifier</ccts:DictionaryEntryName>
                     <ccts:Definition>Identifies the earliest version of the UBL 2 schema for this document type that defines all of the elements that might be encountered in the current instance.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTerm>UBL Version Identifier</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
                     <ccts:DataType>Identifier. Type</ccts:DataType>
                     <ccts:Examples>2.0.5</ccts:Examples>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:CustomizationID" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Customization Identifier. Identifier</ccts:DictionaryEntryName>
                     <ccts:Definition>Identifies a user-defined customization of UBL for a specific use.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTerm>Customization Identifier</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
                     <ccts:DataType>Identifier. Type</ccts:DataType>
                     <ccts:Examples>NES</ccts:Examples>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:ProfileID" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Profile Identifier. Identifier</ccts:DictionaryEntryName>
                     <ccts:Definition>Identifies a user-defined profile of the subset of UBL being used.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTerm>Profile Identifier</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
                     <ccts:DataType>Identifier. Type</ccts:DataType>
                     <ccts:Examples>BasicProcurementProcess</ccts:Examples>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:ProfileExecutionID" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Profile Execution Identifier. Identifier</ccts:DictionaryEntryName>
                     <ccts:Definition>Identifies an instance of executing a profile, to associate all transactions in a collaboration.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTerm>Profile Execution Identifier</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
                     <ccts:DataType>Identifier. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:ID" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Identifier</ccts:DictionaryEntryName>
                     <ccts:Definition>An identifier for this document, assigned by the sender.</ccts:Definition>
                     <ccts:Cardinality>1</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTerm>Identifier</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
                     <ccts:DataType>Identifier. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:UUID" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. UUID. Identifier</ccts:DictionaryEntryName>
                     <ccts:Definition>A universally unique identifier for an instance of this document.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTerm>UUID</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
                     <ccts:DataType>Identifier. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:Name" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Name</ccts:DictionaryEntryName>
                     <ccts:Definition>Text, assigned by the sender, that identifies this document to business users.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTerm>Name</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Name</ccts:RepresentationTerm>
                     <ccts:DataType>Name. Type</ccts:DataType>
                     <ccts:Examples>Seasonal Promotion </ccts:Examples>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:IssueDate" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Issue Date. Date</ccts:DictionaryEntryName>
                     <ccts:Definition>The date, assigned by the sender, on which this document was issued.</ccts:Definition>
                     <ccts:Cardinality>1</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTerm>Issue Date</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Date</ccts:RepresentationTerm>
                     <ccts:DataType>Date. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:IssueTime" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Issue Time. Time</ccts:DictionaryEntryName>
                     <ccts:Definition>The time, assigned by the sender, at which this document was issued.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTerm>Issue Time</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Time</ccts:RepresentationTerm>
                     <ccts:DataType>Time. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:RevisionDate" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Revision Date. Date</ccts:DictionaryEntryName>
                     <ccts:Definition>The date, assigned by the seller, on which the Catalogue was revised.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTerm>Revision Date</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Date</ccts:RepresentationTerm>
                     <ccts:DataType>Date. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:RevisionTime" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Revision Time. Time</ccts:DictionaryEntryName>
                     <ccts:Definition>The time, assigned by the seller, at which the Catalogue was revised.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTerm>Revision Time</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Time</ccts:RepresentationTerm>
                     <ccts:DataType>Time. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:Note" minOccurs="0" maxOccurs="unbounded">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Note. Text</ccts:DictionaryEntryName>
                     <ccts:Definition>Free-form text pertinent to this document, conveying information that is not contained explicitly in other structures.</ccts:Definition>
                     <ccts:Cardinality>0..n</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTerm>Note</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Text</ccts:RepresentationTerm>
                     <ccts:DataType>Text. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:Description" minOccurs="0" maxOccurs="unbounded">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Description. Text</ccts:DictionaryEntryName>
                     <ccts:Definition>Describes the Catalogue Revision.</ccts:Definition>
                     <ccts:Cardinality>0..n</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTerm>Description</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Text</ccts:RepresentationTerm>
                     <ccts:DataType>Text. Type</ccts:DataType>
                     <ccts:Examples>adjustment of prices for Christmas trading period </ccts:Examples>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:VersionID" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Version. Identifier</ccts:DictionaryEntryName>
                     <ccts:Definition>Indicates the current version of the catalogue.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTerm>Version</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Identifier</ccts:RepresentationTerm>
                     <ccts:DataType>Identifier. Type</ccts:DataType>
                     <ccts:Examples>1.1 </ccts:Examples>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cbc:LineCountNumeric" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>BBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Line Count. Numeric</ccts:DictionaryEntryName>
                     <ccts:Definition>The number of lines in the document.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTerm>Line Count</ccts:PropertyTerm>
                     <ccts:RepresentationTerm>Numeric</ccts:RepresentationTerm>
                     <ccts:DataType>Numeric. Type</ccts:DataType>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:ValidityPeriod" minOccurs="0" maxOccurs="unbounded">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Validity_ Period. Period</ccts:DictionaryEntryName>
                     <ccts:Definition>A period, assigned by the seller, during which the information in the Catalogue Revision is effective. This may be given as start and end dates or as a duration.</ccts:Definition>
                     <ccts:Cardinality>0..n</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Validity</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Period</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Period</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Period</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:RelatedCatalogueReference" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Related_ Catalogue Reference. Catalogue Reference</ccts:DictionaryEntryName>
                     <ccts:Definition>A reference to the Catalogue being updated.</ccts:Definition>
                     <ccts:Cardinality>1</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Related</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Catalogue Reference</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Catalogue Reference</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Catalogue Reference</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:ReferencedContract" minOccurs="0" maxOccurs="unbounded">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Referenced_ Contract. Contract</ccts:DictionaryEntryName>
                     <ccts:Definition>A contract or framework agreement with which the Catalogue is associated.</ccts:Definition>
                     <ccts:Cardinality>0..n</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Referenced</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Contract</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Contract</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Contract</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:Signature" minOccurs="0" maxOccurs="unbounded">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Signature</ccts:DictionaryEntryName>
                     <ccts:Definition>A signature applied to this document.</ccts:Definition>
                     <ccts:Cardinality>0..n</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTerm>Signature</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Signature</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Signature</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:ProviderParty" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Provider_ Party. Party</ccts:DictionaryEntryName>
                     <ccts:Definition>The party sending the Catalogue Pricing Update.</ccts:Definition>
                     <ccts:Cardinality>1</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Provider</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Party</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Party</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Party</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:ReceiverParty" minOccurs="1" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Receiver_ Party. Party</ccts:DictionaryEntryName>
                     <ccts:Definition>The party receiving the Catalogue Pricing Update.</ccts:Definition>
                     <ccts:Cardinality>1</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Receiver</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Party</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Party</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Party</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:SellerSupplierParty" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Seller_ Supplier Party. Supplier Party</ccts:DictionaryEntryName>
                     <ccts:Definition>The seller.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Seller</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Supplier Party</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Supplier Party</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Supplier Party</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:ContractorCustomerParty" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Contractor_ Customer Party. Customer Party</ccts:DictionaryEntryName>
                     <ccts:Definition>The customer party responsible for the contracts with which the Catalogue is associated.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Contractor</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Customer Party</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Customer Party</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Customer Party</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:TradingTerms" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Trading Terms</ccts:DictionaryEntryName>
                     <ccts:Definition>The trading terms associated with the Catalogue.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTerm>Trading Terms</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Trading Terms</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Trading Terms</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:DefaultLanguage" minOccurs="0" maxOccurs="1">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Default_ Language. Language</ccts:DictionaryEntryName>
                     <ccts:Definition>The default language for the catalogue pricing update.</ccts:Definition>
                     <ccts:Cardinality>0..1</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTermQualifier>Default</ccts:PropertyTermQualifier>
                     <ccts:PropertyTerm>Language</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Language</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Language</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
         <xsd:element ref="cac:CataloguePricingUpdateLine"
                      minOccurs="1"
                      maxOccurs="unbounded">
            <xsd:annotation>
               <xsd:documentation>
                  <ccts:Component>
                     <ccts:ComponentType>ASBIE</ccts:ComponentType>
                     <ccts:DictionaryEntryName>Catalogue Pricing Update. Catalogue Pricing Update Line</ccts:DictionaryEntryName>
                     <ccts:Definition>One or more lines in the Catalogue Pricing Update, each line updating a specific catalogue item.</ccts:Definition>
                     <ccts:Cardinality>1..n</ccts:Cardinality>
                     <ccts:ObjectClass>Catalogue Pricing Update</ccts:ObjectClass>
                     <ccts:PropertyTerm>Catalogue Pricing Update Line</ccts:PropertyTerm>
                     <ccts:AssociatedObjectClass>Catalogue Pricing Update Line</ccts:AssociatedObjectClass>
                     <ccts:RepresentationTerm>Catalogue Pricing Update Line</ccts:RepresentationTerm>
                  </ccts:Component>
               </xsd:documentation>
            </xsd:annotation>
         </xsd:element>
      </xsd:sequence>
   </xsd:complexType>
</xsd:schema>
<!-- ===== Copyright Notice ===== --><!--
  OASIS takes no position regarding the validity or scope of any 
  intellectual property or other rights that might be claimed to pertain 
  to the implementation or use of the technology described in this 
  document or the extent to which any license under such rights 
  might or might not be available; neither does it represent that it has 
  made any effort to identify any such rights. Information on OASIS's 
  procedures with respect to rights in OASIS specifications can be 
  found at the OASIS website. Copies of claims of rights made 
  available for publication and any assurances of licenses to be made 
  available, or the result of an attempt made to obtain a general 
  license or permission for the use of such proprietary rights by 
  implementors or users of this specification, can be obtained from 
  the OASIS Executive Director.

  OASIS invites any interested party to bring to its attention any 
  copyrights, patents or patent applications, or other proprietary 
  rights which may cover technology that may be required to 
  implement this specification. Please address the information to the 
  OASIS Executive Director.
  
  This document and translations of it may be copied and furnished to 
  others, and derivative works that comment on or otherwise explain 
  it or assist in its implementation may be prepared, copied, 
  published and distributed, in whole or in part, without restriction of 
  any kind, provided that the above copyright notice and this 
  paragraph are included on all such copies and derivative works. 
  However, this document itself may not be modified in any way, 
  such as by removing the copyright notice or references to OASIS, 
  except as needed for the purpose of developing OASIS 
  specifications, in which case the procedures for copyrights defined 
  in the OASIS Intellectual Property Rights document must be 
  followed, or as required to translate it into languages other than 
  English. 

  The limited permissions granted above are perpetual and will not be 
  revoked by OASIS or its successors or assigns. 

  This document and the information contained herein is provided on 
  an "AS IS" basis and OASIS DISCLAIMS ALL WARRANTIES, 
  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO ANY 
  WARRANTY THAT THE USE OF THE INFORMATION HEREIN 
  WILL NOT INFRINGE ANY RIGHTS OR ANY IMPLIED 
  WARRANTIES OF MERCHANTABILITY OR FITNESS FOR A 
  PARTICULAR PURPOSE.    
-->