%@(#)  xcbl_invoice_to_xml_itsoft.rte                     modif:06/10/2017
!==============================================================================
% Fichier       : xcbl_invoice_to_xml_itsoft.rte               version 1.0
!------------------------------------------------------------------------------
% Description   : <description succinte du programme>
!------------------------------------------------------------------------------
% Auteur        :  - GENERIXGROUP
!==============================================================================
! 20/06/2019    Creation
!==============================================================================
! D�finition du message
message "XML/xcbl/v4_0/financial/v1_0/financial.xsd" receiving
message "XML/AIO-8979.xsd" building


! Macros du fichier d'entr�e-sortie
!#include "generix_fct.inc"

!===========================================================
! Section d'initialisation

begin
	PROCESS_ERRONEOUS := TRUE
	nRec := 0
	nPos := 1
	
	bKo := FALSE
	nKo := 0
	nTAX := 0
	nLIN := 0
	pINDEX := build(cINDEX)
endbegin

!===========================================================
nodein SInvoice gGInvoice     ! M 1/1 
endnodein

! Groupe gGInvoice,gGInvoiceHeader : M 1/1 
nodein SInvoiceNumber gGInvoice,gGInvoiceHeader,gGInvoiceNumber     ! M 1/1 
	taHeader["InvoiceNumber"] := eEInvoiceNumber    ! M  
endnodein

nodein SInvoiceIssueDate gGInvoice,gGInvoiceHeader     ! M 1/1 
	taHeader["InvoiceIssueDate"] := eEInvoiceIssueDate    ! M  
endnodein

! --- * InvoiceReference * ---
! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceReferences : C 1/unbound 
! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceReferences,gGPurchaseOrderReference : C 1/1 
nodein Score:BuyerOrderNumber gGInvoice,gGInvoiceHeader,gGInvoiceReferences,gGPurchaseOrderReference     ! M 1/1 
	taHeader["PONum"] := eEcore:BuyerOrderNumber     ! M  
endnodein

! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceReferences,gGASNNumber : C 1/1 
nodein Score:RefNum gGInvoice,gGInvoiceHeader,gGInvoiceReferences,gGASNNumber     ! M 1/1 
	taHeader["DeliveryNoteNum"] := eEcore:RefNum    ! M  
endnodein

! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceReferences,gGListOfRelatedInvoiceRef : C 1/1 
! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceReferences,gGListOfRelatedInvoiceRef,gGRelatedInvoiceRef : M 1/unbound 
! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceReferences,gGListOfRelatedInvoiceRef,gGRelatedInvoiceRef,gGRelatedInvoiceType : M 1/1 
nodein SInvoiceTypeCoded gGInvoice,gGInvoiceHeader,gGInvoiceReferences,gGListOfRelatedInvoiceRef,gGRelatedInvoiceRef,gGRelatedInvoiceType     ! M 1/1 
endnodein

! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceReferences,gGListOfRelatedInvoiceRef,gGRelatedInvoiceRef,gGInvoiceNumber : M 1/1 
nodein Score:RefNum gGInvoice,gGInvoiceHeader,gGInvoiceReferences,gGListOfRelatedInvoiceRef,gGRelatedInvoiceRef,gGInvoiceNumber     ! M 1/1 
	taHeader["IV_Ref"] := eEcore:RefNum   ! M  
endnodein

! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceReferences,gGOtherInvoiceReferences : C 1/1 
! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceReferences,gGOtherInvoiceReferences,gGcore:ReferenceCoded : M 1/unbound 
nodein Score:ReferenceTypeCoded gGInvoice,gGInvoiceHeader,gGInvoiceReferences,gGOtherInvoiceReferences,gGcore:ReferenceCoded     ! M 1/1 
	taHeader["ReferenceTypeCoded"] := eEcore:ReferenceTypeCoded
endnodein

nodein Score:ReferenceTypeCodedOther gGInvoice,gGInvoiceHeader,gGInvoiceReferences,gGOtherInvoiceReferences,gGcore:ReferenceCoded     ! C 1/1 
	taHeader["ReferenceTypeCodedOther"] := peel(eEcore:ReferenceTypeCodedOther," ")   ! C  
endnodein

! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceReferences,gGOtherInvoiceReferences,gGcore:ReferenceCoded,gGcore:PrimaryReference : M 1/1 
nodein Score:RefNum gGInvoice,gGInvoiceHeader,gGInvoiceReferences,gGOtherInvoiceReferences,gGcore:ReferenceCoded,gGcore:PrimaryReference     ! M 1/1 
	taHeader[build(taHeader["ReferenceTypeCoded"],"_Ref")] := eEcore:RefNum    ! M  
endnodein   

nodein Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGContractReference, gGcore:ContractID, gGcore:Agency
	taHeader["ContractID_AgencyCoded"] := eEcore:AgencyCoded
endnodein

nodein Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGContractReference, gGcore:ContractID
	taHeader["ContractID"] := eEcore:Ident
endnodein

! Groupe gGInvoice,gGInvoiceHeader,gGInvoicePurpose : M 1/1 
nodein SInvoicePurposeCoded gGInvoice,gGInvoiceHeader,gGInvoicePurpose     ! M 1/1 
	taHeader["InvoicePurposeCoded"] := eEInvoicePurposeCoded   ! M  
endnodein

! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceType : M 1/1 
nodein SInvoiceTypeCoded gGInvoice,gGInvoiceHeader,gGInvoiceType     ! M 1/1 
	taHeader["InvoiceTypeCoded"] := peel(eEInvoiceTypeCoded," ")   ! M  
endnodein

! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceCurrency : C 1/1 
nodein Score:CurrencyCoded gGInvoice,gGInvoiceHeader,gGInvoiceCurrency     ! M 1/1 
	taHeader["CurrencyCoded"] := eEcore:CurrencyCoded   ! M  
endnodein

! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceLanguage : M 1/1 
nodein Score:LanguageCoded gGInvoice,gGInvoiceHeader,gGInvoiceLanguage     ! M 1/1 
	taHeader["LanguageCoded"] := eEcore:LanguageCoded   ! M  
endnodein

! -- * TaxReference * --
! Groupe gGInvoice,gGInvoiceHeader,gGTaxReference : C 1/unbound 
nodein Score:TaxTypeCoded gGInvoice,gGInvoiceHeader,gGTaxReference     ! M 1/1 
	taHeader["TaxTypeCoded"] := peel(eEcore:TaxTypeCoded," ")   ! M  
endnodein

nodein Score:TaxFunctionQualifierCoded gGInvoice,gGInvoiceHeader,gGTaxReference     ! M 1/1 
	taHeader["TaxFunctionQualifierCoded"] := peel(eEcore:TaxFunctionQualifierCoded," ")   ! M  
endnodein

nodein Score:TaxCategoryCoded gGInvoice,gGInvoiceHeader,gGTaxReference     ! M 1/1 
	taHeader["TaxCategoryCoded"] := peel(eEcore:TaxCategoryCoded," ")   ! M  
endnodein

nodein Score:TaxCategoryCodedOther gGInvoice,gGInvoiceHeader,gGTaxReference     ! C 1/1 
	taHeader["TaxCategoryCodedOther"] := peel(eEcore:TaxCategoryCodedOther," ")   ! C  
endnodein

nodein Score:TaxTreatmentCoded gGInvoice,gGInvoiceHeader,gGTaxReference     ! M 1/1 
	taHeader["TaxTreatmentCoded"] := peel(eEcore:TaxTreatmentCoded," ")   ! M  
endnodein	

nodein Score:ReasonTaxExemptCoded gGInvoice,gGInvoiceHeader,gGTaxReference     ! C 1/1 
	taHeader["ReasonTaxExemptCoded"] := peel(eEcore:ReasonTaxExemptCoded," ")   ! C  
endnodein

nodein Score:ReasonTaxExemptCodedOther gGInvoice,gGInvoiceHeader,gGTaxReference     ! C 1/1 
	if taHeader["TaxTypeCoded"] = "ValueAddedTax" and taHeader["TaxFunctionQualifierCoded"] = "TaxRelatedInformation" and taHeader["TaxCategoryCoded"] = "ExemptFromTax" and taHeader["ReasonTaxExemptCoded"] = "Other" and taHeader["TaxTreatmentCoded"] = "NoTaxApplies" then
		taHeader["FTX_SIN"] := peel(eEcore:ReasonTaxExemptCodedOther," ")   ! C  
	endif
	taHeader["TaxTypeCoded"] := ""
	taHeader["TaxFunctionQualifierCoded"] := ""
	taHeader["TaxCategoryCoded"] := ""
	taHeader["ReasonTaxExemptCoded"] := ""
	taHeader["TaxTreatmentCoded"] := ""
endnodein

nodein Score:TaxTreatmentCodedOther gGInvoice, gGInvoiceHeader, gGTaxReference
	taHeader["TaxTreatmentCodedOther"] := peel(eEcore:TaxTreatmentCodedOther," ")
endnodein

! -- * Dates * --
! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceDates,gGListOfOtherInvoiceDates : C 1/1 
! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceDates,gGListOfOtherInvoiceDates,gGcore:DateCoded : M 1/unbound 
nodein Score:Date gGInvoice,gGInvoiceHeader,gGInvoiceDates,gGListOfOtherInvoiceDates,gGcore:DateCoded     ! M 1/1 
	taHeader["Date"] := eEcore:Date   ! M  
endnodein

! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceDates,gGListOfOtherInvoiceDates,gGcore:DateCoded,gGcore:DateQualifier : M 1/1 
nodein Score:DateQualifierCoded gGInvoice,gGInvoiceHeader,gGInvoiceDates,gGListOfOtherInvoiceDates,gGcore:DateCoded,gGcore:DateQualifier     ! M 1/1 
endnodein

nodein Score:DateQualifierCodedOther gGInvoice,gGInvoiceHeader,gGInvoiceDates,gGListOfOtherInvoiceDates,gGcore:DateCoded,gGcore:DateQualifier     ! C 1/1 
	taHeader[build(eEcore:DateQualifierCodedOther,"_date")] := taHeader["Date"] 
endnodein

nodein SInvoiceDueDate gGInvoice, gGInvoiceHeader, gGInvoiceDates
	 taHeader["InvoiceDueDate"]  := eEInvoiceDueDate
endnodein

nodein Score:PaymentTermCoded gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms, gGcore:PaymentTerm
	taHeader[build("PaymentTermDescription_PaymentTermCoded")] := eEcore:PaymentTermDescription
endnodein

nodein Score:PaymentTermDescription gGInvoice, gGInvoiceHeader, gGInvoicePaymentInstructions, gGcore:PaymentTerms, gGcore:PaymentTerm
	taHeader[build("PaymentTermDescription_",taHeader[build("PaymentTermDescription_PaymentTermCoded")])] := eEcore:PaymentTermDescription
endnodein

! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceParty : M 1/1 
! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty : M 1/1 
! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty,gGcore:PartyID : M 1/1 
nodein Score:Ident gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty,gGcore:PartyID     ! M 1/1 
	taHeader["BY_Ident"] := eEcore:Ident
endnodein

nodein Score:Name1 gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty,gGcore:NameAddress     ! M 1/1 
	taHeader["BY_Name1"] := eEcore:Name1
endnodein

nodein Score:AgencyCoded gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty,gGcore:ListOfIdentifier,gGcore:Identifier,gGcore:Agency     ! M 1/1 
	taHeader["BY_Identifier_AgencyCoded"] := eEcore:AgencyCoded
endnodein

nodein Score:CodeListIdentifierCoded gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty,gGcore:ListOfIdentifier,gGcore:Identifier,gGcore:Agency     ! M 1/1 
	taHeader["BY_Identifier_CodeListIdentifierCoded"] := eEcore:CodeListIdentifierCoded
endnodein

nodein Score:CodeListIdentifierCodedOther gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty,gGcore:ListOfIdentifier,gGcore:Identifier,gGcore:Agency     ! M 1/1 
	taHeader["BY_Identifier_CodeListIdentifierCodedOther"] := eEcore:CodeListIdentifierCodedOther
endnodein

nodein Score:Ident gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty,gGcore:ListOfIdentifier,gGcore:Identifier    ! M 1/1 
	if taHeader["BY_Identifier_AgencyCoded"] = "FR-INSEE" and taHeader["BY_Identifier_CodeListIdentifierCoded"] = "Other" and taHeader["BY_Identifier_CodeListIdentifierCodedOther"] = "SIREN" then
		taHeader["BY_Identifier_SIREN"] := eEcore:Ident
	endif
	taHeader["BY_Identifier_AgencyCoded"] := ""
	taHeader["BY_Identifier_CodeListIdentifierCoded"] := ""
	taHeader["BY_Identifier_CodeListIdentifierCodedOther"] := ""
endnodein

nodein Score:AgencyCoded gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty,gGcore:ListOfIdentifier,gGcore:Identifier,gGcore:Agency     ! M 1/1 
	taHeader["BY_TaxIdentifier_AgencyCoded"] := eEcore:AgencyCoded
endnodein

nodein Score:CodeListIdentifierCoded gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty,gGcore:ListOfIdentifier,gGcore:Identifier,gGcore:Agency     ! M 1/1 
	taHeader["BY_TaxIdentifier_CodeListIdentifierCoded"] := eEcore:CodeListIdentifierCoded
endnodein

nodein Score:Ident gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty,gGcore:ListOfIdentifier,gGcore:Identifier     ! M 1/1 
	if taHeader["BY_TaxIdentifier_AgencyCoded"] = "CEC" and taHeader["BY_TaxIdentifier_CodeListIdentifierCoded"] = "ValueAddedTaxIdentification" then
		taHeader["BY_TaxIdentifier_TVA"] := eEcore:Ident
	endif
	taHeader["BY_TaxIdentifier_AgencyCoded"] := ""
	taHeader["BY_TaxIdentifier_CodeListIdentifierCoded"] := ""
endnodein

nodein Score:Street gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty,gGcore:NameAddress     ! M 1/1 
	taHeader["BY_Street"] := eEcore:Street
endnodein

nodein Score:StreetSupplement1 gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty,gGcore:NameAddress     ! M 1/1 
	taHeader["BY_StreetSupplement1"] := eEcore:StreetSupplement1
endnodein

nodein Score:PostalCode gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty,gGcore:NameAddress     ! M 1/1 
	taHeader["BY_PostalCode"] := eEcore:PostalCode
endnodein

nodein Score:City gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty,gGcore:NameAddress     ! M 1/1 
	taHeader["BY_City"] := eEcore:City
endnodein

nodein Score:CountryCoded gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBuyerParty,gGcore:NameAddress,gGcore:Country     ! M 1/1 
	taHeader["BY_CountryCoded"] := eEcore:CountryCoded
endnodein
				
! -- * Seller * --
! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGSellerParty : M 1/1 
! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGSellerParty,gGcore:PartyID : M 1/1 
nodein Score:Ident gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGSellerParty,gGcore:PartyID     ! M 1/1 
	taHeader["SU_IA_Ref"] := eEcore:Ident    ! M  
endnodein

nodein Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress
	taHeader["SU_Name1"] := eEcore:Name1
endnodein

nodein Score:Street gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress
	taHeader["SU_Street1"] := eEcore:Street1
endnodein

nodein Score:StreetSupplement1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress
	taHeader["SU_StreetSupplement1"] := eEcore:StreetSupplement1
endnodein

nodein Score:PostalCode gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress
	taHeader["SU_PostalCode"] := eEcore:PostalCode
endnodein

nodein Score:City gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress
	taHeader["SU_City"] := eEcore:City
endnodein

nodein Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress, gGcore:Country
	taHeader["SU_CountryCoded"] := eEcore:CountryCoded
endnodein

nodein Score:CountryCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress, gGcore:Country
	if taHeader["SU_CountryCoded"] = "Other" then
		taHeader["SU_CountryCoded"] := eEcore:CountryCodedOther
	endif
endnodein

nodein Score:RegisteredName gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGSellerParty,gGcore:PartyTaxInformation     ! M 1/1 
	taHeader["SU_RegisteredName"] := eEcore:RegisteredName   ! M  
endnodein

! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGSellerParty,gGcore:PartyTaxInformation,gGcore:TaxIdentifier,gGcore:Agency : C 1/1 
nodein Score:AgencyCoded gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGSellerParty,gGcore:PartyTaxInformation,gGcore:TaxIdentifier,gGcore:Agency     ! M 1/1 
	taHeader["SU_TaxIdentifier_TaxAgencyCoded"] := eEcore:AgencyCoded   ! M  
endnodein

nodein Score:CodeListIdentifierCoded gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGSellerParty,gGcore:PartyTaxInformation,gGcore:TaxIdentifier,gGcore:Agency     ! C 1/1 
	taHeader["SU_TaxIdentifier_TaxAgencyCodeListIdentifierCoded"] := eEcore:CodeListIdentifierCoded   ! C  
endnodein

! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGSellerParty,gGcore:PartyTaxInformation : C 1/1 
! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGSellerParty,gGcore:PartyTaxInformation,gGcore:TaxIdentifier : C 1/1 
nodein Score:Ident gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGSellerParty,gGcore:PartyTaxInformation,gGcore:TaxIdentifier     ! M 1/1 
	if taHeader["SU_TaxIdentifier_TaxAgencyCoded"] = "CEC" and taHeader["SU_TaxIdentifier_TaxAgencyCodeListIdentifierCoded"] = "ValueAddedTaxIdentification" then
		taHeader["SU_RFF_VA"] := eEcore:Ident    ! M 
	endif
	taHeader["SU_TaxIdentifier_TaxAgencyCoded"] := ""
	taHeader["SU_TaxIdentifier_TaxAgencyCodeListIdentifierCoded"] := ""
endnodein

nodein Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
	taHeader["SU_Identifier_AgencyCoded"] := eEcore:AgencyCoded
endnodein

nodein Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
	taHeader["SU_Identifier_AgencyCodedOther"] := eEcore:AgencyCoded
endnodein

nodein Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
	taHeader["SU_Identifier_CodeListIdentifierCoded"] := eEcore:CodeListIdentifierCoded
endnodein

nodein Score:CodeListIdentifierCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
	taHeader["SU_Identifier_CodeListIdentifierCodedOther"] := eEcore:CodeListIdentifierCodedOther
endnodein

nodein Score:AgencyDescription gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
	taHeader["SU_Identifier_AgencyDescription"] := eEcore:CodeListIdentifierCodedOther
endnodein

nodein Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:ListOfIdentifier, gGcore:Identifier
	if taHeader["SU_Identifier_AgencyCoded"] = "CEC" and taHeader["SU_Identifier_CodeListIdentifierCoded"] = "Other" and taHeader["SU_Identifier_CodeListIdentifierCodedOther"] = "LegalCapital" then
		taHeader["SU_Identifier_LegalCapital"] := eEcore:Ident
	endif
	if taHeader["SU_Identifier_AgencyCoded"] = "Other" and taHeader["SU_Identifier_AgencyCodedOther"] = "RCS-RCM" and taHeader["SU_Identifier_AgencyDescription"] = "French Trade and Companies Register" then
		taHeader["SU_RFF_XA"] := eEcore:Ident
	endif
	if taHeader["SU_Identifier_AgencyCoded"] = "FR-INSEE" and taHeader["SU_Identifier_CodeListIdentifierCoded"] = "Other" and taHeader["SU_Identifier_CodeListIdentifierCodedOther"] = "SIREN" then
		taHeader["SU_RFF_GN"] := eEcore:Ident
	endif
	if taHeader["SU_Identifier_AgencyCoded"] = "AssignedByNationalTradeAgency" and taHeader["SU_Identifier_CodeListIdentifierCoded"] = "BusinessLegalStructureType" then
		taHeader["SU_TaxIdentifier_BusinessLegalStructureType"] := eEcore:Ident    ! M 
	endif
	taHeader["SU_Identifier_AgencyCoded"] := ""
	taHeader["SU_Identifier_CodeListIdentifierCodedOther"] := ""
	taHeader["SU_Identifier_CodeListIdentifierCoded"] := ""
endnodein

! -- * Bill to party * --
! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBillToParty : C 1/1 
! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBillToParty,gGcore:PartyID : M 1/1 
/*
nodein Score:Ident gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBillToParty,gGcore:PartyID     ! M 1/1 
	taHeader["BY_IA_Ref"] := peel(eEcore:Ident," ")    ! M  
endnodein
*/

nodein Score:AgencyCodedOther gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBillToParty,gGcore:ListOfIdentifier,gGcore:Identifier,gGcore:Agency     ! C 1/1 
	tAgencyCodedOther := peel(eEcore:AgencyCodedOther," ") ! IA ou TSA   ! C  
endnodein

nodein Score:AgencyDescription gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGBillToParty,gGcore:ListOfIdentifier,gGcore:Identifier,gGcore:Agency     ! C 1/1 
	switch tAgencyCodedOther
		case "TSA":
			taHeader["BY_TSA_GenText"] := peel(eEcore:AgencyDescription," ")   ! C
		break
		
		case "IA":
			taHeader["BY_IA_Ref"] := peel(eEcore:AgencyDescription," ")   ! C
		break
	endswitch
	tAgencyCodedOther := ""
endnodein

nodein Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress
	taHeader["ITO_Name1"] := eEcore:Name1
endnodein

nodein Score:Street gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress
	taHeader["ITO_Street"] := eEcore:Street
endnodein

nodein Score:StreetSupplement1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress
	taHeader["ITO_StreetSupplement1"] := eEcore:StreetSupplement1
endnodein

nodein Score:City gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress
	taHeader["ITO_City"] := eEcore:City
endnodein

nodein Score:PostalCode gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress
	taHeader["ITO_PostalCode"] := eEcore:PostalCode
endnodein

nodein Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress, gGcore:Country
	taHeader["ITO_CountryCoded"] := eEcore:CountryCoded
endnodein

nodein Score:CountryCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress, gGcore:Country
	if taHeader["ITO_CountryCoded"] = "Other" then
		taHeader["ITO_CountryCoded"] := eEcore:CountryCodedOther
	endif
endnodein

! -- * Other party * --
! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGListOfPartyCoded : C 1/1 
! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGListOfPartyCoded,gGcore:PartyCoded : M 1/unbound 
! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGListOfPartyCoded,gGcore:PartyCoded,gGcore:PartyID : M 1/1 
nodein Score:PartyRoleCoded gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGListOfPartyCoded,gGcore:PartyCoded     ! M 1/1 
	taHeader["PartyRoleCoded"] := peel(eEcore:PartyRoleCoded," ")
endnodein

nodein Score:Ident gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGListOfPartyCoded,gGcore:PartyCoded,gGcore:PartyID     ! M 1/1 
	!taHeader["OtherPartyIdent"] := peel(eEcore:Ident," ")   ! M  
endnodein

! Groupe gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGListOfPartyCoded,gGcore:PartyCoded,gGcore:NameAddress : C 1/1 
nodein Score:Name1 gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGListOfPartyCoded,gGcore:PartyCoded,gGcore:NameAddress     ! M 1/1 
	taHeader[build(taHeader["PartyRoleCoded"],"_Name1")] := peel(eEcore:Name1," ")    ! M  
endnodein

nodein Score:Name2 gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGListOfPartyCoded,gGcore:PartyCoded,gGcore:NameAddress     ! C 1/1
	taHeader[build(taHeader["PartyRoleCoded"],"_Name2")] := peel(eEcore:Name2," ")   ! C  
endnodein

nodein Score:StreetSupplement1 gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGListOfPartyCoded,gGcore:PartyCoded,gGcore:NameAddress     ! M 1/1 
	taHeader[build(taHeader["PartyRoleCoded"],"_StreetSupplement1")] := peel(eEcore:StreetSupplement1," ")    ! M  
endnodein

nodein Score:City gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGListOfPartyCoded,gGcore:PartyCoded,gGcore:NameAddress     ! M 1/1 
	taHeader[build(taHeader["PartyRoleCoded"],"_City")] := peel(eEcore:City," ")    ! M  
endnodein

nodein Score:PostalCode gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGListOfPartyCoded,gGcore:PartyCoded,gGcore:NameAddress     ! M 1/1 
	taHeader[build(taHeader["PartyRoleCoded"],"_PostalCode")] := peel(eEcore:PostalCode," ")    ! M  
endnodein

nodein Score:CountryCoded gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGListOfPartyCoded,gGcore:PartyCoded,gGcore:NameAddress     ! M 1/1 
	taHeader[build(taHeader["PartyRoleCoded"],"_CountryCoded")] := peel(eEcore:CountryCoded," ")    ! M  
endnodein

nodein Score:CountryCodedOther gGInvoice,gGInvoiceHeader,gGInvoiceParty,gGListOfPartyCoded,gGcore:PartyCoded,gGcore:NameAddress     ! M 1/1 
	if build(taHeader["PartyRoleCoded"],"_CountryCoded") = "Other" then
		taHeader[build(taHeader["PartyRoleCoded"],"_CountryCoded")] := peel(eEcore:CountryCodedOther," ")    ! M  
	endif
endnodein

nodein Score:AgencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
	taHeader[build(taHeader["PartyRoleCoded"],"_AgencyCoded")] := eEcore:AgencyCoded
endnodein

nodein Score:AgencyCodedOther gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier, gGcore:Agency
	taHeader[build(taHeader["PartyRoleCoded"],"_AgencyCodedOther")] := eEcore:AgencyCodedOther
endnodein

nodein Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGListOfPartyCoded, gGcore:PartyCoded, gGcore:ListOfIdentifier, gGcore:Identifier
	if taHeader[build(taHeader["PartyRoleCoded"],"_AgencyCoded")] = "Other" and taHeader[build(taHeader["PartyRoleCoded"],"_AgencyCodedOther")] = "NumBankAccount" then
		taHeader[build(taHeader["PartyRoleCoded"],"_Ident")] := eEcore:Ident
	endif
endnodein

nodein Score:GeneralNote gGInvoice,gGInvoiceHeader,gGListOfStructuredNote,gGcore:StructuredNote     ! C 1/1 
	taHeader["GeneralNote_tmp"] := eEcore:GeneralNote	! C  
endnodein

/*
! Groupe gGInvoice,gGInvoiceHeader,gGListOfStructuredNote : C 1/1 
! Groupe gGInvoice,gGInvoiceHeader,gGListOfStructuredNote,gGcore:StructuredNote : M 1/unbound 
nodein Score:TextTypeCoded gGInvoice,gGInvoiceHeader,gGListOfStructuredNote,gGcore:StructuredNote     ! C 1/1 
	eEcore:TextTypeCoded   ! C  
endnodein
*/

nodein Score:TextTypeCodedOther gGInvoice,gGInvoiceHeader,gGListOfStructuredNote,gGcore:StructuredNote     ! C 1/1 
	taHeader[build("GeneralNote_",peel(eEcore:TextTypeCodedOther," "))] := taHeader["GeneralNote_tmp"]
endnodein

nodein Score:NoteID gGInvoice, gGInvoiceHeader, gGListOfStructuredNote, gGcore:StructuredNote
	taHeader[build("NoteID_",taHeader[build(taHeader["PartyRoleCoded"],"_AgencyCodedOther")])] := eEcore:NoteID
endnodein		

! Groupe gGInvoice,gGInvoiceDetail : M 1/1 
! Groupe gGInvoice,gGInvoiceDetail,gGListOfInvoiceItemDetail : M 1/1 
! Groupe gGInvoice,gGInvoiceDetail,gGListOfInvoiceItemDetail,gGInvoiceItemDetail : M 1/unbound 
! Groupe gGInvoice,gGInvoiceDetail,gGListOfInvoiceItemDetail,gGInvoiceItemDetail,gGInvoiceBaseItemDetail : M 1/1 
! Groupe gGInvoice,gGInvoiceDetail,gGListOfInvoiceItemDetail,gGInvoiceItemDetail,gGInvoiceBaseItemDetail,gGLineItemNum : M 1/1 
nodein Score:BuyerLineItemNum gGInvoice,gGInvoiceDetail,gGListOfInvoiceItemDetail,gGInvoiceItemDetail,gGInvoiceBaseItemDetail,gGLineItemNum     ! M 1/1 
endnodein

! Groupe gGInvoice,gGInvoiceDetail,gGListOfInvoiceItemDetail,gGInvoiceItemDetail,gGInvoiceBaseItemDetail,gGInvoicedQuantity : M 1/1 
! Groupe gGInvoice,gGInvoiceDetail,gGListOfInvoiceItemDetail,gGInvoiceItemDetail,gGInvoiceBaseItemDetail,gGInvoicedQuantity,gGcore:UnitOfMeasurement : M 1/1 
nodein Score:UOMCoded gGInvoice,gGInvoiceDetail,gGListOfInvoiceItemDetail,gGInvoiceItemDetail,gGInvoiceBaseItemDetail,gGInvoicedQuantity,gGcore:UnitOfMeasurement     ! M 1/1 
endnodein

nodein Score:ProductIdentifierQualifierCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:PartNumbers, gGcore:StandardPartNumber
endnodein

nodein Score:QuantityValue gGInvoice,gGInvoiceDetail,gGListOfInvoiceItemDetail,gGInvoiceItemDetail,gGInvoiceBaseItemDetail,gGInvoicedQuantity     ! M 1/1 
	taLine[build("QuantityValue_",nLIN)] := eEcore:QuantityValue
endnodein

nodein Score:PriceTypeCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:PricingType
	taLine[build("PriceTypeCoded_",nLIN)] := eEcore:PriceTypeCoded
endnodein

nodein Score:UnitPriceValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:UnitPrice
	taLine[build("UnitPriceValue_",taLine[build("PriceTypeCoded_",nLIN)],"_",nLIN)] := eEcore:UnitPriceValue
endnodein

nodein Score:UOMCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:PriceBasisQuantity, gGcore:UnitOfMeasurement
	taLine[build("UOMCodedOther_",taLine[build("PriceTypeCoded_",nLIN)],"_",nLIN)] := eEcore:UOMCodedOther
endnodein

nodein Score:ItemDescription gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers
	if eEcore:ItemDescription <> "Fake Line" then
		taLine[build("ItemDescription_",nLIN)] := eEcore:ItemDescription
	endif
endnodein

nodein Score:MonetaryAmount gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:LineItemTotal
	taLine[build("MonetaryAmount_",nLIN)] := eEcore:MonetaryAmount
endnodein

!nodein Score:TaxFunctionQualifierCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
!	taLine[build("TaxFunctionQualifierCoded_",nLIN)] := eEcore:TaxFunctionQualifierCoded :=  "TaxRelatedInformation"
!endnodein	
!
!nodein Score:TaxCategoryCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
!	taLine[build("TaxCategoryCoded_",nLIN)] := eEcore:TaxCategoryCoded :=  "ExemptFromTax"
!endnodein
!
!nodein Score:ReasonTaxExemptCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
!	taLine[build("ReasonTaxExemptCoded_",nLIN)] := eEcore:ReasonTaxExemptCoded := "Other"
!endnodein

nodein Score:ReasonTaxExemptCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
	taLine[build("ReasonTaxExemptCodedOther_",nLIN)] := eEcore:ReasonTaxExemptCodedOther
endnodein	

nodein Score:BuyerOrderNumber gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemReferences, gGPurchaseOrderReference
	taLine[build("BuyerOrderNumber_",nLIN)] := eEcore:BuyerOrderNumber
endnodein

nodein Score:BuyerLineItemNum gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemNum 
	nLIN++
	taLine[build("BuyerLineItemNum_",nLIN)] := eEcore:BuyerLineItemNum
endnodein

nodein Score:ProductIdentifier gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:PartNumbers, gGcore:StandardPartNumber
	if eEcore:ProductIdentifier <> "N/A" then
		taLine[build("ProductIdentifier_",nLIN)] := eEcore:ProductIdentifier
	endif
endnodein

nodein Score:PartID gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:PartNumbers, gGcore:SellerPartNumber
	taLine[build("PartID_",nLIN)] := eEcore:PartID
endnodein

nodein Score:TaxPercent gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
	taLine[build("TaxPercent_",nLIN)] := eEcore:TaxPercent
endnodein	

nodein Score:UOMCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGInvoicedQuantity, gGcore:UnitOfMeasurement 
	taLine[build("UOMCodedOther_",nLIN)] := eEcore:UOMCodedOther
endnodein

nodein SStartDate gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGLineItemDates, gGInvoicingPeriod
	taLine[build("StartDate_",nLIN)] := eEStartDate
endnodein	

nodein SEndDate gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGLineItemDates, gGInvoicingPeriod
	taLine[build("EndDate_",nLIN)] := eEEndDate
endnodein

nodein Score:Name1 gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGDeliveryDetail, gGcore:ShipToLocation, gGcore:NameAddress
	taLine[build("DP_Name1_",nLIN)] := eEcore:Name1
endnodein

nodein Score:Street gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGDeliveryDetail, gGcore:ShipToLocation, gGcore:NameAddress
	taLine[build("DP_Street_",nLIN)] := eEcore:Street
endnodein

nodein Score:StreetSupplement1  gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGDeliveryDetail, gGcore:ShipToLocation, gGcore:NameAddress
	taLine[build("DP_StreetSupplement1_",nLIN)] := eEcore:StreetSupplement1
endnodein

nodein Score:City gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGDeliveryDetail, gGcore:ShipToLocation, gGcore:NameAddress
	taLine[build("DP_City1_",nLIN)] := eEcore:City
endnodein

nodein Score:PostalCode gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGDeliveryDetail, gGcore:ShipToLocation, gGcore:NameAddress
	taLine[build("DP_PostalCode_",nLIN)] := eEcore:PostalCode
endnodein

nodein Score:CountryCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGDeliveryDetail, gGcore:ShipToLocation, gGcore:NameAddress, gGcore:Country
	taLine[build("DP_CountryCoded_",nLIN)] := eEcore:CountryCoded
endnodein

! -- * InvoiceSummary * --
! Groupe gGInvoice,gGInvoiceSummary : C 1/1 
! Groupe gGInvoice,gGInvoiceSummary,gGInvoiceTotals : C 1/1 
! Groupe gGInvoice,gGInvoiceSummary,gGInvoiceTotals,gGInvoiceTotal : M 1/1 
nodein Score:MonetaryAmount gGInvoice,gGInvoiceSummary,gGInvoiceTotals,gGInvoiceTotal     ! M 1/1 
	taSummary["GrossValue"] := eEcore:MonetaryAmount    ! M  
endnodein

! Groupe gGInvoice,gGInvoiceSummary,gGInvoiceTotals,gGTaxableValue : C 1/1 
nodein Score:MonetaryAmount gGInvoice,gGInvoiceSummary,gGInvoiceTotals,gGTaxableValue     ! M 1/1 
	taSummary["NetValue"] := eEcore:MonetaryAmount   ! M  
endnodein

! Groupe gGInvoice,gGInvoiceSummary,gGInvoiceTotals,gGTotalTaxAmount : C 1/1 
nodein Score:MonetaryAmount gGInvoice,gGInvoiceSummary,gGInvoiceTotals,gGTotalTaxAmount     ! M 1/1 
	taSummary["TaxValue"] := eEcore:MonetaryAmount    ! M  
endnodein

nodein Score:TaxCategoryCoded gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
	nTAX++
	taSummary[build("TaxCategoryCoded_",nTAX)] := eEcore:TaxCategoryCoded
endnodein

nodein Score:TaxTypeCoded gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
	taSummary[build("TaxTypeCoded_",nTAX)] := eEcore:TaxTypeCoded
endnodein

nodein Score:TaxFunctionQualifierCoded gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
	taSummary[build("TaxFunctionQualifierCoded_",nTAX)] := eEcore:TaxFunctionQualifierCoded
endnodein

nodein Score:TaxCategoryCodedOther gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
	taSummary[build("TaxCategoryCodedOther_",nTAX)] := eEcore:TaxCategoryCodedOther
endnodein

nodein Score:TaxableAmount gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
	taSummary[build("TaxableAmount_",nTAX)] := eEcore:TaxableAmount
endnodein

nodein Score:TaxAmount gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
	taSummary[build("TaxAmount_",nTAX)] := eEcore:TaxAmount
endnodein

nodein Score:TaxAmountInTaxAccountingCurrency gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
	taSummary[build("TaxAmountInTaxAccountingCurrency_",nTAX)] := eEcore:TaxAmountInTaxAccountingCurrency
endnodein
   
! Section des traitements par d�faut
default
enddefault

!===========================================================
! Section de fin des traitements
end
	bfWrite()
	print(MESSAGE_OUT)
	pDOF.OUTPUT.NAME := build("ITESOFT_FACTURE_FRN_",pINDEX,".XML")
	log(taLine)
endend

!===========================================================
function bfWrite()
	
	nodeout SINVOICES gGINVOICES     ! M 1/1 
		eAxmlns:xsi := "http://www.w3.org/2001/XMLSchema-instance"
	endnodeout

	! Groupe gGINVOICES,gGINVOICE : M 1/unbound 
	! Groupe gGINVOICES,gGINVOICE,gGSENDER : M 1/1 
	nodeout SI_NAME gGINVOICES,gGINVOICE,gGSENDER     ! M 1/1 
		eEI_NAME := taHeader["SU_Name1"] ! M  
	endnodeout

	! Groupe gGINVOICES,gGINVOICE,gGSENDER,gGI_TAXIDS : M 1/1 
	nodeout SI_TAXID gGINVOICES,gGINVOICE,gGSENDER,gGI_TAXIDS     ! M 1/3 
		eEI_TAXID := taHeader["SU_RFF_VA"] ! M  
	endnodeout
	
	nodeout SI_TAXID gGINVOICES,gGINVOICE,gGSENDER,gGI_TAXIDS     ! M 1/3 
		eEI_TAXID := taHeader["SU_RFF_XA"] ! M  
	endnodeout
	
	nodeout SI_TAXID gGINVOICES,gGINVOICE,gGSENDER,gGI_TAXIDS     ! M 1/3 
		eEI_TAXID := taHeader["SU_RFF_GN"] ! M  
	endnodeout

	nodeout SI_ADDR1 gGINVOICES,gGINVOICE,gGSENDER     ! C 1/1 
		eEI_ADDR1 := taHeader["SU_Street1"] ! C  
	endnodeout

	nodeout SI_ADDR2 gGINVOICES,gGINVOICE,gGSENDER     ! C 1/1 
		eEI_ADDR2 := taHeader["SU_StreetSupplement1"] ! C  
	endnodeout

	nodeout SI_ZIPCODE gGINVOICES,gGINVOICE,gGSENDER     ! C 1/1 
		eEI_ZIPCODE := taHeader["SU_PostalCode"] ! C  
	endnodeout

	nodeout SI_CITY gGINVOICES,gGINVOICE,gGSENDER     ! C 1/1 
		eEI_CITY := taHeader["SU_City"] ! C  
	endnodeout

	nodeout SI_COUNTRY gGINVOICES,gGINVOICE,gGSENDER     ! C 1/1 
		eEI_COUNTRY := taHeader["SU_CountryCoded"] ! C  
	endnodeout

	! Groupe gGINVOICES,gGINVOICE,gGSENDER : C 1/1 
	nodeout SI_BANKIDS gGINVOICES,gGINVOICE,gGSENDER     ! M 1/5 
		eEI_BANKIDS := taHeader["PartytoReceiveInvoiceFOrGoodsOrServices_Ident"] ! M  
	endnodeout

	! Groupe gGINVOICES,gGINVOICE,gGSENDER,gGI_LEGALMENTIONS : C 1/1 
	nodeout SI_LEGALMENTION gGINVOICES,gGINVOICE,gGSENDER,gGI_LEGALMENTIONS     ! M 1/10 
		eEI_LEGALMENTION := build(taHeader["SU_RegisteredName"]," - ",taHeader["SU_TaxIdentifier_BusinessLegalStructureType"]," - ",taHeader["SU_Identifier_LegalCapital"]) ! M  
		log(taHeader)
	endnodeout
	
	nodeout SI_LEGALMENTION gGINVOICES,gGINVOICE,gGSENDER,gGI_LEGALMENTIONS     ! M 1/10 
		eEI_LEGALMENTION := taHeader["FTX_SIN"] ! M  
	endnodeout
	
	nodeout SI_LEGALMENTION gGINVOICES,gGINVOICE,gGSENDER,gGI_LEGALMENTIONS     ! M 1/10 
		eEI_LEGALMENTION := taHeader["GeneralNote_SupplierRemarks"] ! M  
	endnodeout
	
	nodeout SI_LEGALMENTION gGINVOICES,gGINVOICE,gGSENDER,gGI_LEGALMENTIONS     ! M 1/10 
		eEI_LEGALMENTION := taHeader["PaymentTermDescription_PenaltyTerms"] ! M  
	endnodeout
	
	nodeout SI_LEGALMENTION gGINVOICES,gGINVOICE,gGSENDER,gGI_LEGALMENTIONS     ! M 1/10 
		eEI_LEGALMENTION := taHeader["PaymentTermDescription_LatePayment"] ! M  
	endnodeout

	! Groupe gGINVOICES,gGINVOICE,gGCLIENT : M 1/1 
	nodeout SC_NAME gGINVOICES,gGINVOICE,gGCLIENT     ! M 1/1 
		eEC_NAME := taHeader["BY_Name1"] ! M  
	endnodeout

	! Groupe gGINVOICES,gGINVOICE,gGCLIENT,gGC_TAXIDS : M 1/1 
	nodeout SC_TAXID gGINVOICES,gGINVOICE,gGCLIENT,gGC_TAXIDS     ! M 1/3 
		eEC_TAXID := taHeader["BY_Identifier_SIREN"] ! M  
	endnodeout
	
	nodeout SC_TAXID gGINVOICES,gGINVOICE,gGCLIENT,gGC_TAXIDS     ! M 1/3 
		eEC_TAXID := taHeader["BY_TaxIdentifier_TVA"] ! M  
	endnodeout

	nodeout SC_ADDR1 gGINVOICES,gGINVOICE,gGCLIENT     ! C 1/1 
		eEC_ADDR1 := taHeader["BY_Street"] ! C  
	endnodeout

	nodeout SC_ADDR2 gGINVOICES,gGINVOICE,gGCLIENT     ! C 1/1 
		eEC_ADDR2 := taHeader["BY_StreetSupplement1"] ! C  
	endnodeout

	nodeout SC_ZIPCODE gGINVOICES,gGINVOICE,gGCLIENT     ! C 1/1 
		eEC_ZIPCODE := taHeader["BY_PostalCode"] ! C  
	endnodeout

	nodeout SC_CITY gGINVOICES,gGINVOICE,gGCLIENT     ! C 1/1 
		eEC_CITY := taHeader["BY_City"] ! C  
	endnodeout

	nodeout SC_COUNTRY gGINVOICES,gGINVOICE,gGCLIENT     ! C 1/1 
		eEC_COUNTRY := taHeader["BY_CountryCoded"] ! C  
	endnodeout

	nodeout SH_CLIENTID gGINVOICES,gGINVOICE,gGHEADER     ! C 1/1 
		eEH_CLIENTID := taHeader["AccountNumber_Ref"] ! C  
	endnodeout

	! Groupe gGINVOICES,gGINVOICE,gGHEADER : M 1/1 
	nodeout SH_DOCDATE gGINVOICES,gGINVOICE,gGHEADER     ! M 1/1 
		eEH_DOCDATE := tfConvertDate(substr(taHeader["InvoiceIssueDate"],1,10),"YYYY-MM-DD","DD.MM.YYYY") ! M  
	endnodeout

	nodeout SH_DOCTYPE gGINVOICES,gGINVOICE,gGHEADER     ! M 1/1 
		if tolower(taHeader["InvoiceTypeCoded"]) = "commercialinvoice" then
			eEH_DOCTYPE := "Facture" ! M  
		else
			if tolower(taHeader["InvoiceTypeCoded"]) = "creditnotegoodsandservices" then
				eEH_DOCTYPE := "Avoir" ! M  
			endif
		endif
	endnodeout

	nodeout SH_CURRENCY gGINVOICES,gGINVOICE,gGHEADER     ! M 1/1 
		eEH_CURRENCY := taHeader["CurrencyCoded"] ! M  
	endnodeout

	nodeout SH_NETAMT gGINVOICES,gGINVOICE,gGHEADER     ! M 1/1 
		eEH_NETAMT := taSummary["NetValue"] ! M  
	endnodeout

	nodeout SH_TOTAMT gGINVOICES,gGINVOICE,gGHEADER     ! M 1/1 
		eEH_TOTAMT := taSummary["GrossValue"] ! M  
	endnodeout

	nodeout SH_DUEDATE gGINVOICES,gGINVOICE,gGHEADER     ! C 1/1 
		eEH_DUEDATE := tfConvertDate(substr(taHeader["InvoiceDueDate"],1,10),"YYYY-MM-DD","DD.MM.YYYY") ! C  
	endnodeout

	nodeout SH_DOCNBR1 gGINVOICES,gGINVOICE,gGHEADER     ! M 1/1 
		eEH_DOCNBR1 := taHeader["InvoiceNumber"] ! M  
	endnodeout

	nodeout SH_CONTRACTNBR gGINVOICES,gGINVOICE,gGHEADER     ! C 1/1 
		eEH_CONTRACTNBR := taHeader["ContractID"] ! C  
	endnodeout
	
	! Groupe gGINVOICES,gGINVOICE,gGHEADER,gGH_CUSTOMS : C 1/1 
	if taHeader["NoteID_GS1France"] <> EMPTY then
		nodeout SH_CUSTOM gGINVOICES,gGINVOICE,gGHEADER,gGH_CUSTOMS     ! M 1/10 
			eADescription := "String" ! M  
			eEH_CUSTOM := build("Facture de service (",taHeader["NoteID_GS1France"],")") ! C  
		endnodeout
	endif
	
	if taHeader["GeneralNote_GeneralInformation"] <> EMPTY then
		nodeout SH_CUSTOM gGINVOICES,gGINVOICE,gGHEADER,gGH_CUSTOMS     ! M 1/10 
			eADescription := "String" ! M  
			eEH_CUSTOM := build("Facture de service (",taHeader["GeneralNote_GeneralInformation"],")") ! C  
		endnodeout
	endif
	
	if taHeader["PartytoReceiveInvoiceFOrGoodsOrServices_Name1"] <> EMPTY then
		nodeout SH_CUSTOM gGINVOICES,gGINVOICE,gGHEADER,gGH_CUSTOMS     ! M 1/10 
			eADescription := "String" ! M  
			eEH_CUSTOM := build("Le payé : ",taHeader["PartytoReceiveInvoiceFOrGoodsOrServices_Name1"]," - ",taHeader["PartytoReceiveInvoiceFOrGoodsOrServices_Street"]," - ",taHeader["PartytoReceiveInvoiceFOrGoodsOrServices_StreetSupplement1"]," - ",taHeader["PartytoReceiveInvoiceFOrGoodsOrServices_City"]," - ",taHeader["PartytoReceiveInvoiceFOrGoodsOrServices_PostalCode"]," - ",taHeader["PartytoReceiveInvoiceFOrGoodsOrServices_CountryCoded"]) ! C  
		endnodeout
	endif
	
	if taHeader["ITO_Name1"] <> EMPTY then
		nodeout SH_CUSTOM gGINVOICES,gGINVOICE,gGHEADER,gGH_CUSTOMS     ! M 1/10 
			eADescription := "String" ! M  
			eEH_CUSTOM := build("Destinataire : ",taHeader["ITO_Name1"]," - ",taHeader["ITO_Street"]," - ",taHeader["ITO_StreetSupplement1"]," - ",taHeader["ITO_City"]," - ",taHeader["ITO_PostalCode"]," - ",taHeader["ITO_CountryCoded"]) ! C  
		endnodeout
	endif

	nTAXCPT := 0
	while nTAXCPT < nTAX do
		nTAXCPT++
		! Groupe gGINVOICES,gGINVOICE,gGFOOTER : C 1/1 
		! Groupe gGINVOICES,gGINVOICE,gGFOOTER,gGF_LINE : M 1/unbound 
		nodeout SF_AMOUNT gGINVOICES,gGINVOICE,gGFOOTER,gGF_LINE     ! C 1/1 
			eAcode := build("NETAMOUNT",nTAXCPT) ! C  
			eEF_AMOUNT := taSummary[build("TaxableAmount_",nTAXCPT)] ! C  
		endnodeout

		nodeout SF_TAXRATE gGINVOICES,gGINVOICE,gGFOOTER,gGF_LINE     ! C 1/1 
			eEF_TAXRATE := taSummary[build("TaxCategoryCodedOther_",nTAXCPT)] ! C  
		endnodeout

		nodeout SF_AMTTAG gGINVOICES,gGINVOICE,gGFOOTER,gGF_LINE     ! C 1/1 
			eEF_AMTTAG := taSummary[build("TaxAmount_",nTAXCPT)] ! C  
		endnodeout
	endwhile

	nLINCPT := 0
	while nLINCPT < nLIN do
		nLINCPT++
		! Groupe gGINVOICES,gGINVOICE,gGBODY : C 1/1 
		! Groupe gGINVOICES,gGINVOICE,gGBODY,gGTAB_LINE : M 1/unbound 
		nodeout STAB_ORDPOS gGINVOICES,gGINVOICE,gGBODY,gGTAB_LINE     ! C 1/1 
			eETAB_ORDPOS := taLine[build("BuyerLineItemNum_",nLINCPT)] ! C  
		endnodeout

		nodeout STAB_REF1 gGINVOICES,gGINVOICE,gGBODY,gGTAB_LINE     ! C 1/1 
			eETAB_REF1 := taLine[build("ProductIdentifier_",nLINCPT)] ! C  
		endnodeout

		nodeout STAB_REF2 gGINVOICES,gGINVOICE,gGBODY,gGTAB_LINE     ! C 1/1 
			eETAB_REF2 := taLine[build("PartID_",nLINCPT)] ! C  
		endnodeout

		nodeout STAB_DESC gGINVOICES,gGINVOICE,gGBODY,gGTAB_LINE     ! C 1/1 
			eETAB_DESC := taLine[build("ItemDescription_",nLINCPT)] ! C  
		endnodeout
		
		nodeout STAB_QTY gGINVOICES,gGINVOICE,gGBODY,gGTAB_LINE     ! M 1/1 
			eETAB_QTY := taLine[build("QuantityValue_",nLINCPT)] ! M  
		endnodeout

		nodeout STAB_UNITQTY gGINVOICES,gGINVOICE,gGBODY,gGTAB_LINE     ! C 1/1 
			eETAB_UNITQTY := taLine[build("UOMCodedOther_",nLINCPT)] ! C  
		endnodeout

		nodeout STAB_UNITPRC gGINVOICES,gGINVOICE,gGBODY,gGTAB_LINE     ! M 1/1 
			eETAB_UNITPRC := taLine[build("UnitPriceValue_CalculationNet_",nLINCPT)] ! M  
		endnodeout

		nodeout STAB_UNITSET gGINVOICES,gGINVOICE,gGBODY,gGTAB_LINE     ! C 1/1 
			eETAB_UNITSET := taLine[build("UOMCodedOther_CalculationNet_",nLINCPT)] ! C  
		endnodeout

		nodeout STAB_NETAMT gGINVOICES,gGINVOICE,gGBODY,gGTAB_LINE     ! M 1/1 
			eETAB_NETAMT := taLine[build("MonetaryAmount_",nLINCPT)] ! M  
		endnodeout

		nodeout STAB_TAXRATE gGINVOICES,gGINVOICE,gGBODY,gGTAB_LINE     ! C 1/1 
			eETAB_TAXRATE := taLine[build("TaxPercent_",nLINCPT)] ! C  
		endnodeout

		nodeout STAB_ORDERNBR gGINVOICES,gGINVOICE,gGBODY,gGTAB_LINE     ! C 1/1 
			eETAB_ORDERNBR := taLine[build("BuyerOrderNumber_",nLINCPT)] ! C  
		endnodeout

		! Groupe gGINVOICES,gGINVOICE,gGBODY,gGTAB_LINE,gGTAB_CUSTOMS : C 1/1 
		nodeout STAB_CUSTOM gGINVOICES,gGINVOICE,gGBODY,gGTAB_LINE,gGTAB_CUSTOMS     ! M 1/10 
			eADescription := "String" ! C  
			eETAB_CUSTOM := build("Periode du ",tfConvertDate(substr(taLine[build("StartDate_",nLINCPT)],1,10),"YYYY-MM-DD","DD.MM.YYYY")," au ",tfConvertDate(substr(taLine[build("EndDate_",nLINCPT)],1,10),"YYYY-MM-DD","DD.MM.YYYY")) ! M  
		endnodeout
		
		nodeout STAB_CUSTOM gGINVOICES,gGINVOICE,gGBODY,gGTAB_LINE,gGTAB_CUSTOMS     ! M 1/10 
			eADescription := "String" ! C  
			eETAB_CUSTOM := taLine[build("ReasonTaxExemptCodedOther_",nLINCPT)] ! M  
		endnodeout
		
		nodeout STAB_CUSTOM gGINVOICES,gGINVOICE,gGBODY,gGTAB_LINE,gGTAB_CUSTOMS     ! M 1/10 
			eADescription := "String" ! C  
			eETAB_CUSTOM := build(taLine[build("DP_Name1_",nLINCPT)]," - ",taLine[build("DP_Street_",nLINCPT)]," - ",taLine[build("DP_StreetSupplement1_",nLINCPT)]," - ",taLine[build("DP_City1_",nLINCPT)]," - ",taLine[build("DP_PostalCode_",nLINCPT)]," - ",taLine[build("DP_CountryCoded_",nLINCPT)]) ! M  
		endnodeout
	endwhile
endfunction

function tfConvertDate(tDate, tFormatIn, tFormatOut)
	
	if tDate=EMPTY then
		return tDate
	endif
	
	tYear   := EMPTY
	tMonth  := EMPTY
	tDay    := EMPTY
	tHour   := EMPTY
	tMinut  := EMPTY
	tSecond := EMPTY	
	
!	LOG("tDate", tDate)
!	LOG("tFormatIn", tFormatIn)
!	LOG("tFormatOut", tFormatOut)

	switch(toupper(tFormatIn)) 
		case "YYMMDD": 
			tYear := substr(tDate,1,2)
			tMonth := substr(tDate,3,2)
			tDay := substr(tDate,5,2)
		case "YYYYMMDD": 
			tYear := substr(tDate,1,4)
			tMonth := substr(tDate,5,2)
			tDay := substr(tDate,7,2)
		case "DDMMYYYY":
			tYear := substr(tDate,5,4)
			tMonth := substr(tDate,3,2)
			tDay := substr(tDate,1,2)
		case "DD/MM/YY" :
			tYear := build("20",substr(tDate,7,2))
			tMonth := substr(tDate,4,2)
			tDay := substr(tDate,1,2)
		case "DD-MM-YYYY" :
			tYear := substr(tDate,7,4)
			tMonth := substr(tDate,4,2)
			tDay := substr(tDate,1,2)
		case "DD/MM/YYYY" :
			tYear := substr(tDate,7,4)
			tMonth := substr(tDate,4,2)
			tDay := substr(tDate,1,2)
		case "DDMMYY" :
			tYear := build("20", substr(tDate,5,2))
			tMonth :=substr(tDate,3,2)
			tDay := substr(tDate,1,2)
		case "YYYYMMDDHHMM":		
			tYear := substr(tDate,1,4)
			tMonth := substr(tDate,5,2)
			tDay := substr(tDate,7,2)
			tHour := substr(tDate,9,2)
			tMinut := substr(tDate,11,2)
		case "YYYYMMDDHHMMSS" :
			tYear := substr(tDate,1,4)
			tMonth := substr(tDate,5,2)
			tDay := substr(tDate,7,2)
			tHour := substr(tDate,9,2)
			tMinut := substr(tDate,11,2)
			tSecond :=substr(tDate,13,2)
		case "DD.MM.YYYY" :
			tYear := substr(tDate,7,4)
			tMonth := substr(tDate,4,2)
			tDay := substr(tDate,1,2)	
		case "YYYY-MM-DD" :
			tYear := substr(tDate,1,4)
			tMonth := substr(tDate,6,2)
			tDay := substr(tDate,9,2)
		case "DDMMYYHHMM" :
			tYear := substr(tDate,5,2)
			tMonth := substr(tDate,3,2)
			tDay := substr(tDate,1,2)
			tHour := substr(tDate,7,2)
			tMinut := substr(tDate,9,2)
		case "YYYY-MM-DDTHH:MM:SS" :
			tYear := substr(tDate,1,4)
			tMonth := substr(tDate,6,2)
			tDay := substr(tDate,9,2)
			tHour := substr(tDate,12,2)
			tMinut := substr(tDate,15,2)
			tSecond :=substr(tDate,18,2)
		case "HHMM" :
			tHour := substr(tDate,1,2)
			tMinut := substr(tDate,3,2)
		case "DD-MMM-YYYY" :
			tYear := substr(tDate,8,4)
			tMonthName := toupper(substr(tDate,4,3))
			tDay := substr(tDate,1,2)
			switch tMonthName
				case "JAN" :
					tMonth := "01"
				case "FEB" :
					tMonth := "02"
				case "MAR" :
					tMonth := "03"
				case "APR" :
					tMonth := "04"
				case "MAI" :
					tMonth := "05"
				case "JUN" :
					tMonth := "06"
				case "JUL" :
					tMonth := "07"
				case "AUG" :
					tMonth := "08"
				case "SEP" :
					tMonth := "09"
				case "OCT" :
					tMonth := "10"
				case "NOV" :
					tMonth := "11"
				case "DEC" :
					tMonth := "12"
				default :
					tMonth := tMonthName
			endswitch
			
		case "DDMMYYYYHHMM" :
			tYear := substr(tDate,5,4)
			tMonth := substr(tDate,3,2)
			tDay := substr(tDate,1,2)
			tHour := substr(tDate,9,2)
			tMinut := substr(tDate,11,2)
			
		case "DD.MM.YYYY:HHMMSS" :
			tYear := substr(tDate,7,4)
			tMonth := substr(tDate,4,2)
			tDay := substr(tDate,1,2)
			tHour := substr(tDate,12,2)
			tMinut := substr(tDate,14,2)
			tSecond := substr(tDate,16,2)
	
		case "DD.MM.YYYY HH:MM" :
			tYear := substr(tDate,7,4)
			tMonth := substr(tDate,4,2)
			tDay := substr(tDate,1,2)
			tHour := substr(tDate,12,2)
			tMinut := substr(tDate,15,2)

		default :
			log("Format date d'entrée invalide", tFormatIn)
			exit(1)
	endswitch

    if number (tYear) = 0 or tYear = EMPTY then
		log("Date invalide - Annee", tYear)
		exit(1)
    endif

    if number(tMonth) < 1 or number (tMonth) > 12 or tMonth = EMPTY then
		log("Date invalide - Mois", tMonth)
		exit(1)
    endif

    if number(tDay) < 1 or number (tDay) > 31 or tDay = EMPTY then
		log("Date invalide - Jour", tDay)
		exit(1)
    endif

	switch(toupper(tFormatOut))
		case "YYYYMMDD" :
			if length(tYear)=2 then
   			    tYear:=build("20",tYear)
   			endif
			tReturnDate := build(tYear,tMonth,tDay)
			return(tReturnDate)
		case "YYYYMM" :
			tYear :=tYear
			if length(tYear)=2 then
   			    tYear:=build("20",tYear)
   			endif
			tReturnDate := build(tYear,tMonth)
			return(tReturnDate)
		case "DD/MM/YY" :
			tReturnDate := build(tDay, "/", tMonth, "/", substr(tYear, 3, 2))
			return(tReturnDate)
		case "MM/DD/YY" :
			tReturnDate := build(tMonth, "/", tDay, "/", substr(tYear, 3, 2))
			return(tReturnDate)
		case "DDMMYYYY":
			tReturnDate := build(tDay,tMonth,tYear)
			return(tReturnDate)
		case "DD/MM/YYYY" :
			if length(tYear)=2 then
   			    tYear:=build("20",tYear)
   			endif
			tReturnDate := build(tDay, "/", tMonth, "/", tYear)
			return(tReturnDate)
		case "DD.MM.YYYY" :
			if length(tYear)=2 then
   			    tYear:=build("20",tYear)
   			endif
			tReturnDate := build(tDay, ".", tMonth, ".", tYear)
			return(tReturnDate)
		case "DDMMYYYYHHMM":
			tReturnDate := build(tDay,tMonth,tYear,tHour,tMinut)
			return(tReturnDate)
		case "HHMM" :
			tReturnDate := build(tHour,tMinut)
			return(tReturnDate)
		case "HH:MM" :
			tReturnDate := build(tHour,":",tMinut)
			return(tReturnDate)
		case "HH MM" :
			tReturnDate := build(tHour," ",tMinut)
			return(tReturnDate)
		case "HH:MM:SS" :
			if  tHour=EMPTY  then
				tHour  :="00"
			endif
			if  tMinut=EMPTY  then
				tMinut :="00"
			endif
			if  tSecond=EMPTY  then
				tSecond :="00"
			endif
			tReturnDate := build(tHour,":",tMinut,":",tSecond)
			return(tReturnDate)	
		case "HHMMSS" :
			if  tHour=EMPTY  then
				tHour  :="00"
			endif
			if  tMinut=EMPTY  then
				tMinut :="00"
			endif
			if  tSecond=EMPTY  then
				tSecond :="00"
			endif
			tReturnDate := build(tHour,tMinut,tSecond)
			return(tReturnDate)
		case "YYYYMMDDHHMM":
			if length (tHour) = 0 then
			    tHour :="00"
			endif
			if length (tMinut) = 0 then
			    tMinut :="00"
			endif
			if length (tYear) = 2 then
			    tYear :=build("20",tYear)
			endif
			tReturnDate := build(tYear,tMonth,tDay,tHour,tMinut)
			return(tReturnDate)
		case "YYYYMMDDHHMMSS":
			if length (tHour) = 0 then
			    tHour :="00"
			endif
			if length (tMinut) = 0 then
			    tMinut :="00"
			endif
			if length (tYear) = 2 then
			    tYear :=build("20",tYear)
			endif
			if  tSecond=EMPTY  then
				tSecond :="00"
			endif			
			tReturnDate := build(tYear,tMonth,tDay,tHour,tMinut,tSecond)
			return(tReturnDate)
		case "DD.MM.YYYY HH:MM:SS":
			if length (tHour) = 0 then
			    tHour :="00"
			endif
			if length (tMinut) = 0 then
			    tMinut :="00"
			endif
			if length (tYear) = 2 then
			    tYear :=build("20",tYear)
			endif
			if  tSecond=EMPTY  then
				tSecond :="00"
			endif			
			tReturnDate := build(tDay,".",tMonth,".",tYear," ",tHour,":",tMinut,":",tSecond)
			return(tReturnDate)
		case "YYMMDD":
			tReturnDate := build(substr(tYear,3,2),tMonth,tDay)
			return(tReturnDate)
		case "DDMMYY":
			tReturnDate := build(tDay,tMonth,substr(tYear,3,2))
			return(tReturnDate)
		case "YYYYDDMM":
			tReturnDate := build(tYear,tDay,tMonth)
			return(tReturnDate)
        	case "YYYY/MM/DD":
			tReturnDate := build(tYear, "/", tMonth, "/", tDay)
			return(tReturnDate)
		case "YYYY-MM-DDTHH:MM:SS":
			if  tHour=EMPTY  then
				tHour  :="00"
			endif
			if  tMinut=EMPTY  then
				tMinut :="00"
			endif
			if  tSecond=EMPTY  then
				tSecond :="00"
			endif
			tReturnDate := build(tYear, "-", tMonth, "-", tDay, "T", tHour, ":", tMinut, ":", tSecond)
			return(tReturnDate)
        	case "YYYY-MM-DD":
			tReturnDate := build(tYear, "-", tMonth, "-", tDay)
			return(tReturnDate)
        	case "YYYY.MM.DD":
			tReturnDate := build(tYear, ".", tMonth, ".", tDay)
			return(tReturnDate)
        	case "MM":
			tReturnDate := tMonth
			return(tReturnDate)
		default :
			log("Format date de sortie invalide", tFormatout)
			exit(1)
	endswitch

endfunction
