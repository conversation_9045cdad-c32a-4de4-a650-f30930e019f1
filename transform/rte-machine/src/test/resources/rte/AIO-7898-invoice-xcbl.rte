%@(#)  xcbl_to_fatturaPA.rte     modif:19/06/2018
!==============================================================================
% Fichier       : xcbl_to_fatturaPA.rte   version 2.0
!------------------------------------------------------------------------------
% Description   : Mapping  XCBL vers FatturaPA (format Italien
!------------------------------------------------------------------------------
% Projet        : All in one
!------------------------------------------------------------------------------
% Auteur        : ES - GENERIX
!==============================================================================
! 09/12/2016  ES  Creation
! 11/04/2018  RNH Correction et Adaptation sur le schéma FatturaElectronica
! 19/06/2018  XR  Adaptation à l'interprétation Java
!==============================================================================
!
! <description détaillée du programme>
!
!==============================================================================
! fichier en entrée  : <nom ou regle de nommage du fichier en entree> 
! fichier en sortie  : <nom ou regle de nommage du fichier en sortie> 
! fichier temporaire : <nom ou regle de nommage du fichier temporaire> 
!==============================================================================
#define PROG_NAME "xcbl_to_fatturaPA"
#define PROG_DATE "19/06/2018"
#define PROG_VER "2.0"
#define PROG_INFO build(PROG_NAME," du ",PROG_DATE," v", PROG_VER)


schema "XML/xcbl/v4_0/financial/v1_0/financial.xsd"  validating, receiving

! Définition du message en sortie

schema "XML/SDI/fatturapa_v1.1.xsd" validating,building

!
! Fichier de fonctions
!
#include "edoc_function.h" 
!
!===========================================================

! Section d'initialisation

begin

   PROCESS_ERRONEOUS := TRUE
   
   bfBEGIN()
	bfVariablesInit()
 
	nLINE := 0
	bFirst := TRUE
	nFATTURAP := cFATTURAP
	if nFATTURAP > 99999 then
		remove(build(sHOME,"/.counters/FATTURAP"))
		nFATTURAP := cFATTURAP
	endif
endbegin

!===========================================================
!! DEBUT
nodein SInvoice gGInvoice     ! M 1/1 
   nLINE++
   nLinComEntete := 0
   remove(taLINCommentaireEntete)
   remove(taLINCommentaire) 
endnodein

! Groupe gGInvoice,gGInvoiceHeader : M 1/1 
nodein SInvoiceNumber gGInvoice,gGInvoiceHeader,gGInvoiceNumber     ! M 1/1 
    nLINE++
   
    bfInitHeader()
   
    nLin:=0
   
    tDocumentNbr := eEInvoiceNumber
	pNumber := tDocumentNbr
endnodein

nodein SInvoiceDueDate gGInvoice,gGInvoiceHeader,gGInvoiceDates     ! M 1/1 
  tDueDate :=  tfConvertDateXML(eEInvoiceDueDate, "102" )
endnodein

nodein SInvoiceTypeCodedOther gGInvoice,gGInvoiceHeader,gGInvoiceType     ! C 1/1 
  tCodeGenre := eEInvoiceTypeCodedOther
endnodein
   
nodein SInvoiceIssueDate gGInvoice,gGInvoiceHeader     ! M 1/1 
     split(eEInvoiceIssueDate,taDate,"T")   
     tDocumentDate := build(taDate[1])!tfConvertDateXML(eEInvoiceIssueDate, "102")
	 
     bInvoiceIssueDate := TRUE
endnodein

nodein Score:BuyerOrderNumber gGInvoice,gGInvoiceHeader,gGInvoiceReferences,gGPurchaseOrderReference     ! M 1/1 
    tCustomerPOReference :=  eEcore:BuyerOrderNumber 
endnodein

nodein Score:RefNum gGInvoice, gGInvoiceHeader, gGInvoiceReferences, gGProformaInvoiceNumber
    tOriginalDocumentNbr :=  eEcore:RefNum 
endnodein

nodein Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:PartyID
		tInternalCode_BDT := eEcore:Ident 
		pTo := tInternalCode_BDT
endnodein

nodein Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:NameAddress
		tNAD_BDT_NAME := eEcore:Name1  
endnodein

nodein Score:Street gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:NameAddress	
	tNAD_BDT_ADRESS1 :=	eEcore:Street  !eC059.3042.1
endnodein

nodein Score:City gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:NameAddress
		tNAD_BDT_CITY := eEcore:City  !e3164
endnodein

nodein Score:PostalCode gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:NameAddress
	tNAD_BDT_POSTCODE := eEcore:PostalCode  !e3251
endnodein

nodein Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:NameAddress, gGcore:Country	
	tNAD_BDT_COUNTRY := eEcore:CountryCoded !e3207
endnodein

nodein Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier, gGcore:Agency
	tCodeListIdentifierCoded := eEcore:CodeListIdentifierCoded
endnodein

nodein Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBuyerParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier
	if tCodeListIdentifierCoded = "ValueAddedTaxIdentification" then
		tNAD_BDT_VAT := eEcore:Ident 
	endif
endnodein

!! BillToPArty
nodein Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:PartyID
		tInternalCode_BDT := eEcore:Ident 
		pTo := tInternalCode_BDT
endnodein

nodein Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress
		tNAD_BDT_NAME := eEcore:Name1  
endnodein

nodein Score:Street gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress	
	tNAD_BDT_ADRESS1 :=	eEcore:Street  !eC059.3042.1
endnodein

nodein Score:City gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress
		tNAD_BDT_CITY := eEcore:City  !e3164
endnodein

nodein Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier, gGcore:Agency
	tCodeListIdentifierCoded := eEcore:CodeListIdentifierCoded
endnodein

nodein Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier
	if tCodeListIdentifierCoded = "ValueAddedTaxIdentification" then
		tNAD_BDT_VAT := eEcore:Ident 
	endif
endnodein

nodein Score:PostalCode gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress
	tNAD_BDT_POSTCODE := eEcore:PostalCode  !e3251
endnodein

nodein Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGBillToParty, gGcore:NameAddress, gGcore:Country	
	tNAD_BDT_COUNTRY := eEcore:CountryCoded !e3207
endnodein

nodein Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:PartyID
	tInternalCode_SDT := eEcore:Ident 
	pFrom := tInternalCode_SDT
endnodein

nodein Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress
 tNAD_SDT_NAME :=  eEcore:Name1   
endnodein

nodein Score:Street gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress
	tNAD_SDT_ADRESS1 := eEcore:Street     !eC059.3042.1
endnodein

nodein Score:City gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress	
	tNAD_SDT_CITY  := eEcore:City    !e3164
endnodein

nodein Score:PostalCode gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress
	tNAD_SDT_POSTCODE := eEcore:PostalCode  !e3251
endnodein

nodein Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:NameAddress, gGcore:Country	
	tNAD_SDT_COUNTRY := eEcore:CountryCoded   ! e3207
	tNAD_SDT_COUNTRY_CODE := eEcore:CountryCoded
endnodein

nodein Score:CodeListIdentifierCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier, gGcore:Agency
	tCodeListIdentifierCoded := eEcore:CodeListIdentifierCoded
endnodein

nodein Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGSellerParty, gGcore:PartyTaxInformation, gGcore:TaxIdentifier
	if tCodeListIdentifierCoded = "ValueAddedTaxIdentification" then
		tNAD_SDT_VAT := eEcore:Ident  ! eC506.1154
	endif
endnodein


nodein Score:CurrencyCoded gGInvoice, gGInvoiceHeader, gGInvoiceCurrency
	tCurrencyCode := eEcore:CurrencyCoded 
endnodein

nodein Score:Ident gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:PartyID
	tInternalCode_GCSG1 := eEcore:Ident 
endnodein
nodein Score:Name1 gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:NameAddress
	
	tGCSG1_NAME := eEcore:Name1  !eC080.3036.1
endnodein

nodein Score:Street gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:NameAddress
	
	tGCSG1_ADRESS1 := eEcore:Street ! eC059.3042.1
endnodein

nodein Score:City gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:NameAddress
	
	tGCSG1_CITY := eEcore:City !e3164
endnodein
nodein Score:PostalCode gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:NameAddress
	tGCSG1_POSTCODE := eEcore:PostalCode !e3251
endnodein

nodein Score:CountryCoded gGInvoice, gGInvoiceHeader, gGInvoiceParty, gGShipToParty, gGcore:NameAddress, gGcore:Country
	tGCSG1_COUNTRY := eEcore:CountryCoded !e3207
endnodein


nodein Score:TextTypeCodedOther gGInvoice, gGInvoiceHeader, gGListOfStructuredNote, gGcore:StructuredNote
	tTextTypeCodedOther := eEcore:TextTypeCodedOther
endnodein
nodein Score:GeneralNote gGInvoice, gGInvoiceHeader, gGListOfStructuredNote, gGcore:StructuredNote
	if tTextTypeCodedOther = "General information" then
		nLinComEntete++
		taLINCommentaireEntete[nLinComEntete] := eEcore:GeneralNote
	endif
endnodein

! Groupe gGInvoice,gGInvoiceHeader,gGListOfNameValueSet,gGcore_NameValueSet,gGcore_ListOfNameValuePair : M 1/1 
! Groupe gGInvoice,gGInvoiceHeader,gGListOfNameValueSet,gGcore_NameValueSet,gGcore_ListOfNameValuePair,gGcore_NameValuePair : M 1/unbound 
nodein Score:Name gGInvoice,gGInvoiceHeader,gGListOfNameValueSet,gGcore:NameValueSet,gGcore:ListOfNameValuePair,gGcore:NameValuePair     ! M 1/1 
	tEcore_Name := eEcore:Name
	log("tEcore_Name :",tEcore_Name,NL)
endnodein

nodein Score:Value gGInvoice,gGInvoiceHeader,gGListOfNameValueSet,gGcore:NameValueSet,gGcore:ListOfNameValuePair,gGcore:NameValuePair     ! M 1/1 
	switch tEcore_Name
	case "IdPaese" :
		tIdPaese := eEcore:Value
		log("tIdPaese : ",tIdPaese,NL)
	case "IdCodice" :
		tIdCodice := eEcore:Value
		log("tIdCodice : ",tIdCodice,NL)
	case "ProgressivoInvio" :
		tProgressivoInvio := eEcore:Value
		log("tProgressivoInvio : ",tProgressivoInvio,NL)
	endswitch
	
endnodein



!! LIGNE
nodein Score:BuyerLineItemNum gGInvoice,gGInvoiceDetail,gGListOfInvoiceItemDetail,gGInvoiceItemDetail,gGInvoiceBaseItemDetail,gGLineItemNum     ! M 1/1 
  nLin++
  taOrderLine[nLin] := eEcore:BuyerLineItemNum   ! M  
endnodein

nodein Score:ProductIdentifierQualifierCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:PartNumbers, gGcore:StandardPartNumber
	taProductIdentifierQualCoded[nLin] := eEcore:ProductIdentifierQualifierCoded
endnodein

nodein Score:ProductIdentifier gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:PartNumbers, gGcore:StandardPartNumber
	taProductIdentifier[nLin] := eEcore:ProductIdentifier
endnodein

nodein Score:SellerLineItemNum gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGLineItemNum 
 !taOrderLine[nLin] := eEcore:SellerLineItemNum 
endnodein

nodein Score:PartID gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:PartNumbers, gGcore:SellerPartNumber
	taSellerArticle[nLin] := eEcore:PartID  !eC212.1.7140

endnodein

nodein Score:PartID gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers, gGcore:PartNumbers, gGcore:BuyerPartNumber
	taBuyersArticle[nLin]  := eEcore:PartID
endnodein
       
nodein Score:ItemDescription gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGItemIdentifiers
		taDescription[nLin] := eEcore:ItemDescription
endnodein 

  
nodein Score:QuantityValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGInvoicedQuantity
		taQty[nLin] := eEcore:QuantityValue 
endnodein

nodein Score:UOMCodedOther gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoiceBaseItemDetail, gGInvoicedQuantity, gGcore:UnitOfMeasurement
		taQtyUnite[nLin] := eEcore:UOMCodedOther 
endnodein
 
	
nodein Score:PriceTypeCoded gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:PricingType
		tPriceTypeCoded := eEcore:PriceTypeCoded 
endnodein	

nodein Score:UnitPriceValue gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:ListOfPrice, gGcore:Price, gGcore:UnitPrice
		if tPriceTypeCoded = "NetItemPrice" then
			taUnitPrice[nLin] := eEcore:UnitPriceValue 
		endif
		
		if tPriceTypeCoded = "CalculationGross" then
			taUnitPrice2[nLin] := eEcore:UnitPriceValue 
		endif
endnodein	

nodein Score:TaxPercent gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
		taTaxPercent[nLin] := eEcore:TaxPercent 
endnodein	


nodein Score:TaxAmount gGInvoice, gGInvoiceDetail, gGListOfInvoiceItemDetail, gGInvoiceItemDetail, gGInvoicePricingDetail, gGcore:Tax
		taTaxAmount[nLin] := eEcore:TaxAmount 
endnodein	


! Pied
nodein Score:MonetaryAmount gGInvoice, gGInvoiceSummary, gGInvoiceTotals, gGTaxableValue
  tTaxableAmount :=	eEcore:MonetaryAmount 
endnodein

nodein Score:TaxAmount gGInvoice, gGInvoiceSummary, gGListOfTaxSummary, gGcore:TaxSummary
	tTaxAmount := eEcore:TaxAmount  !eC516.5004
endnodein


nodein Score:MonetaryAmount gGInvoice,gGInvoiceSummary,gGInvoiceTotals,gGInvoiceTotal     ! M 1/1 
  tInvoiceAmount := eEcore:MonetaryAmount
endnodein

nodein Score:MonetaryAmount gGInvoice,gGInvoiceSummary,gGInvoiceTotals,gGTotalAmountPayable     ! M 1/1 !MOA+9
	tTaxableAmount := eEcore:MonetaryAmount    
endnodein

!! FIN DEBUT



! Section des traitements par défaut
default

enddefault

!===========================================================

! Section de fin des traitements

end

	bfInvoic()
      
		print(MESSAGE_OUT) 
	   !bfEXIT(0)
	pFTP.REMOTENAME := build("FR",tInternalCode_SDT,"_",nFATTURAP:05,".xml")

 !bfEND()
endend

!=================================================================
			

function bfTestValue(tValue,tMsgErreur,bMandatory)
   if tValue = EMPTY then
      log(tMsgErreur, NL)
      
      if bMandatory = TRUE then
        bfEXIT(1)   
      endif
      return TRUE
   else
      return FALSE
   endif

endfunction

function bfInitHeader()
   
	bCREDITNOTE := FALSE
	bTAX := FALSE
	remove(taTaxRate)
	remove(taTaxableAmount)
	remove(taTaxAmount)
	bNAD_BDT := FALSE
	bNAD_SDT := FALSE
	
	tCodeGenre := EMPTY
	tDocumentNbr := EMPTY        
	tDocumentDate := EMPTY
	tDocumentDate := EMPTY
    remove(taLINCommentaireEntete)
    tCustomerPOReference := EMPTY
    tNAD_SDT_VAT := EMPTY
    tNAD_SDT_NAME := EMPTY
    tNAD_SDT_ADRESS1 := EMPTY
    tNAD_SDT_POSTCODE := EMPTY
    tNAD_SDT_CITY := EMPTY
    tNAD_SDT_COUNTRY := EMPTY
    tBankAccount := EMPTY
    tAccountNumber := EMPTY
    tBankName := EMPTY
    tSwift := EMPTY
    tNAD_SDT_CONTACT := EMPTY
    tNAD_SDT_PHONE := EMPTY
    tNAD_SDT_EMAIL := EMPTY
    tNAD_BDT_VAT := EMPTY
    tNAD_BDT_NAME := EMPTY
    tNAD_BDT_ADRESS1 := EMPTY
    tNAD_BDT_POSTCODE := EMPTY
    tNAD_BDT_CITY := EMPTY
    tNAD_BDT_COUNTRY := EMPTY
	tGCSG1_NAME := EMPTY
	tGCSG1_POSTCODE := EMPTY
	tGCSG1_CITY := EMPTY
	tGCSG1_COUNTRY := EMPTY
	tCurrencyCode := EMPTY
	tPaymentTerms := EMPTY
	tDueDate := EMPTY
	tETaxRate := EMPTY
	tTaxClass := EMPTY
	tTAXPREST := EMPTY
	tTAXGOODS := EMPTY
	nTotalQty := 0
	nTotalArticleItemAmount := 0
	tInvoiceAmount := EMPTY
	tTaxableAmount := EMPTY
	tTaxAmount := EMPTY
	
	remove(taQty)
	tOriginalDocumentNbr := EMPTY
	
endfunction
	

 
 
 

!
! Convertion de la date XML en date XCBL
!
function tfConvertDateXML(tDateXML, tFormatDate)

	tFormatDate_ := tFormatDate
	if tDateXML=EMPTY then
		log ("99InternalError0009","Date to convert is empty","", "","", "", nLINE)
		exit(6)
	endif

	if(tFormatDate_ = EMPTY) then
		tFormatDate_ := "102"
	endif
	switch(tFormatDate_)
		case "101" : ! YYMMDD
			tDate := build("20", substr(tDateXML,3,2),substr(tDateXML,6,2),substr(tDateXML,9,2))
			tHeure := "000000"
			return build(tDate,tHeure)
		case "102" : ! CCYYMMDD
		case "718" :
			tDate := build(substr(tDateXML,1,4),substr(tDateXML,6,2),substr(tDateXML,9,2))
			if (length(tDateXML) > 12) then
				tHeure := build(substr(tDateXML,12,2),substr(tDateXML,14,2),"00")
			!else
				!tHeure := "T00:00:00"
			endif
			return build(tDate,tHeure)
		default:
			tError := build("Date ", tDateXML, " format ", tFormatDate_, " not managed")
			log ("99InternalError0008",tError,"", "","", "", nLINE)
			exit(7)
	endswitch
endfunction

!
! Initialisation des tableaux
!
function bfVariablesInit()

	nTVA  := 0
	nLINE := 0
	nUNH  := 0
	nUNB  := 0
	nNAD  := 0
	bLIN := FALSE
	bInvoicedQuantity := FALSE
	bNADBY := FALSE
	bNADSE := FALSE
	bInvoiceDueDate := FALSE
	bInvoiceIssueDate := FALSE
	bIVName1Empty := FALSE
	bIVNameAddress := FALSE
	bBYName1Empty := FALSE
	bBYNameAddress := FALSE
	bSEName1Empty := FALSE
	bSENameAddress := FALSE
	bCOName1Empty := FALSE
	bCONameAddress := FALSE
	bLCName1Empty := FALSE
	bLCNameAddress := FALSE
	bDPName1Empty := FALSE
	bDPNameAddress := FALSE
	bREName1Empty := FALSE
	bRENameAddress := FALSE
	bDLName1Empty := FALSE
	bDLNameAddress := FALSE
	nDTM13 := 0

	tALCHeaderTypeReductionCharge := ""
	tALCHeaderTypeMethodOfHandling := ""
	tALCHeaderService := ""
	tALCHeaderCalculcationIndex := ""
	tALCHeaderBasisCodedOther   := ""
	tALCHeaderDescription       := ""

	tALCTypeReductionCharge := ""
	tALCTypeMethodOfHandling := ""
	tALCService := ""
	tALCCalculcationIndex := ""

	tFTXSIN := ""

	remove(taCalculationSequence)
	taCalculationSequence["1"] := "FirstStepOfCalculation"
	taCalculationSequence["2"] := "SecondStepOfCalculation"
	taCalculationSequence["3"] := "ThirdStepOfCalculation"
	taCalculationSequence["4"] := "FourthStepOfCalculation"
	taCalculationSequence["5"] := "FifthStepOfCalculation"
	taCalculationSequence["6"] := "SixthStepOfCalculation"
	taCalculationSequence["7"] := "SevendStepOfCalculation"
	taCalculationSequence["8"] := "EighthStepOfCalculation"
	taCalculationSequence["9"] := "NinthStepOfCalculation"

endfunction


!=================================================================
      
function bfInvoic() 


	 if bFirst = TRUE then
		nodeout SFatturaElettronica gGFatturaElettronica     ! M 1/1 
		 	!eAxmlns:p := "http://www.fatturapa.gov.it/sdi/fatturapa/v1.1"
	     		!eAversione :="1.1"
 			eAxmlns:p := "http://ivaservizi.agenziaentrate.gov.it/docs/xsd/fatture/v1.2"
      			eAxmlns:xsi := "http://www.w3.org/2001/XMLSchema-instance"
      			eAversione := "FPA12"   ! M  
      			eAxsi:schemaLocation := "http://ivaservizi.agenziaentrate.gov.it/docs/xsd/fatture/v1.2 fatturaordinaria_v1.2.xsd "  ! C 
	    	endnodeout
	 endif
	 bFirst := FALSE

     ! Groupe gGFatturaElettronica,gGFatturaElettronicaHeader : M 1/1 
     ! Groupe gGFatturaElettronica,gGFatturaElettronicaHeader,gGDatiTrasmissione : M 1/1 
     ! Groupe gGFatturaElettronica,gGFatturaElettronicaHeader,gGDatiTrasmissione,gGIdTrasmittente : M 1/1 
     nodeout SIdPaese gGFatturaElettronica,gGFatturaElettronicaHeader,gGDatiTrasmissione,gGIdTrasmittente     ! M 1/1 
tIdPaese := "IT"	
log("Je suis dans IdTrasmittente/tIdPaese : ",tIdPaese,NL)
     	eEIdPaese := tIdPaese
      
     endnodeout

     nodeout SIdCodice gGFatturaElettronica,gGFatturaElettronicaHeader,gGDatiTrasmissione,gGIdTrasmittente     ! M 1/1 
tIdCodice := "08373230013"
eEIdCodice := tIdCodice
     endnodeout

     nodeout SProgressivoInvio gGFatturaElettronica,gGFatturaElettronicaHeader,gGDatiTrasmissione     ! M 1/1 
     	eEProgressivoInvio := tDocumentNbr !tProgressivoInvio
     endnodeout

     nodeout SFormatoTrasmissione gGFatturaElettronica,gGFatturaElettronicaHeader,gGDatiTrasmissione     ! M 1/1 
     	eEFormatoTrasmissione := "FPA12"
     endnodeout

     nodeout SCodiceDestinatario gGFatturaElettronica,gGFatturaElettronicaHeader,gGDatiTrasmissione     ! M 1/1 
     	eECodiceDestinatario := "999999"
     endnodeout

     nodeout SIdPaese gGFatturaElettronica,gGFatturaElettronicaHeader,gGCedentePrestatore,gGDatiAnagrafici,gGIdFiscaleIVA     ! M 1/1 
     	eEIdPaese := tNAD_SDT_COUNTRY_CODE
     endnodeout

 
     nodeout SIdCodice gGFatturaElettronica,gGFatturaElettronicaHeader,gGCedentePrestatore,gGDatiAnagrafici,gGIdFiscaleIVA     ! M 1/1 
     	!eEIdCodice := tNAD_SDT_VAT
	if not valid(NUMERIC,substr(tNAD_SDT_VAT,1,2)) then
     		eEIdCodice := substr(tNAD_SDT_VAT,3,length(tNAD_SDT_VAT)-2)
	else
		eEIdCodice := tNAD_SDT_VAT
	endif

     endnodeout

     nodeout SDenominazione gGFatturaElettronica,gGFatturaElettronicaHeader,gGCedentePrestatore,gGDatiAnagrafici,gGAnagrafica     ! M 1/1 
     	eEDenominazione := tNAD_SDT_NAME
     endnodeout

     nodeout SRegimeFiscale gGFatturaElettronica,gGFatturaElettronicaHeader,gGCedentePrestatore,gGDatiAnagrafici     ! M 1/1 
     	eERegimeFiscale := "RF18"
     endnodeout

     nodeout SIndirizzo gGFatturaElettronica,gGFatturaElettronicaHeader,gGCedentePrestatore,gGSede     ! M 1/1 
     	eEIndirizzo := tNAD_SDT_ADRESS1
     endnodeout

     nodeout SCAP gGFatturaElettronica,gGFatturaElettronicaHeader,gGCedentePrestatore,gGSede     ! M 1/1 
     	eECAP := tNAD_SDT_POSTCODE
     endnodeout

     nodeout SComune gGFatturaElettronica,gGFatturaElettronicaHeader,gGCedentePrestatore,gGSede     ! M 1/1 
     	eEComune := tNAD_SDT_CITY
     endnodeout

     nodeout SNazione gGFatturaElettronica,gGFatturaElettronicaHeader,gGCedentePrestatore,gGSede     ! M 1/1 
     	eENazione := tNAD_SDT_COUNTRY_CODE
     endnodeout

     nodeout SIdPaese gGFatturaElettronica,gGFatturaElettronicaHeader,gGCessionarioCommittente,gGDatiAnagrafici,gGIdFiscaleIVA     ! M 1/1 
     	eEIdPaese := tNAD_BDT_COUNTRY
     endnodeout

 
   nodeout SIdCodice gGFatturaElettronica,gGFatturaElettronicaHeader,gGCessionarioCommittente,gGDatiAnagrafici,gGIdFiscaleIVA     ! M 1/1 
	if not valid(NUMERIC,substr(tNAD_BDT_VAT,1,2)) then
     		eEIdCodice := substr(tNAD_BDT_VAT,3,length(tNAD_BDT_VAT)-2)
	else
		eEIdCodice := tNAD_BDT_VAT
	endif
     endnodeout

     nodeout SDenominazione gGFatturaElettronica,gGFatturaElettronicaHeader,gGCessionarioCommittente,gGDatiAnagrafici,gGAnagrafica     ! M 1/1 
     	eEDenominazione := tNAD_BDT_NAME
     endnodeout

     nodeout SIndirizzo gGFatturaElettronica,gGFatturaElettronicaHeader,gGCessionarioCommittente,gGSede     ! M 1/1 
     	eEIndirizzo := tNAD_BDT_ADRESS1
     endnodeout

     nodeout SCAP gGFatturaElettronica,gGFatturaElettronicaHeader,gGCessionarioCommittente,gGSede     ! M 1/1 
     	eECAP := tNAD_BDT_POSTCODE
     endnodeout

     nodeout SComune gGFatturaElettronica,gGFatturaElettronicaHeader,gGCessionarioCommittente,gGSede     ! M 1/1 
     	eEComune := tNAD_BDT_CITY
     endnodeout

     nodeout SNazione gGFatturaElettronica,gGFatturaElettronicaHeader,gGCessionarioCommittente,gGSede     ! M 1/1 
     	eENazione := tNAD_BDT_COUNTRY
     endnodeout

     nodeout STipoDocumento gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiGenerali,gGDatiGeneraliDocumento     ! M 1/1 
     	switch tCodeGenre
		   case "380" :
		   		eETipoDocumento := "TD01"
		   case "381" :
		   		eETipoDocumento := "TD04"
		   case "383" :
		   		eETipoDocumento := "TD05"
		   case "386" :
		   		eETipoDocumento := "TD02"
		   default :
		   		eETipoDocumento := "TD03"
		endswitch
     endnodeout

     nodeout SDivisa gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiGenerali,gGDatiGeneraliDocumento     ! M 1/1 
     		eEDivisa := "EUR"
     endnodeout

     nodeout SData gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiGenerali,gGDatiGeneraliDocumento     ! M 1/1 
     	eEData := tDocumentDate
     endnodeout

     nodeout SNumero gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiGenerali,gGDatiGeneraliDocumento     ! M 1/1 
     	eENumero := tDocumentNbr
     endnodeout

     nodeout STipo gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiGenerali,gGDatiGeneraliDocumento,gGScontoMaggiorazione     ! M 1/1 
     	eETipo := "SC"
     endnodeout

   !!! LIGNE

   	 nLin2 := 0
			
	 while nLin2 < nLin do
				nLin2++
		     ! Groupe gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiGenerali,gGDatiOrdineAcquisto : M 1/1 
		     nodeout SRiferimentoNumeroLinea gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiGenerali,gGDatiOrdineAcquisto     ! M 1/unbound 
		     	eERiferimentoNumeroLinea := taOrderLine[nLin2]
		     endnodeout
	 endwhile
     
	 nodeout SIdDocumento gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiGenerali,gGDatiOrdineAcquisto     ! M 1/1 
		 eEIdDocumento :=    tDocumentNbr
     endnodeout

     nLin2 := 0
			
	 while nLin2 < nLin do
				nLin2++
	     ! Groupe gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiGenerali,gGDatiContratto : M 1/1 
	     nodeout SRiferimentoNumeroLinea gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiGenerali,gGDatiContratto     ! M 1/unbound 
	     	eERiferimentoNumeroLinea := taOrderLine[nLin2]
	     endnodeout

	  endwhile
	  
     nodeout SIdDocumento gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiGenerali,gGDatiContratto     ! M 1/1 
     		eEIdDocumento :=    tDocumentNbr
     endnodeout

       nLin2 := 0
			
	 while nLin2 < nLin do
				nLin2++
		     ! Groupe gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiGenerali,gGDatiConvenzione : M 1/1 
		     nodeout SRiferimentoNumeroLinea gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiGenerali,gGDatiConvenzione     ! M 1/unbound 
		     	eERiferimentoNumeroLinea := taOrderLine[nLin2]
		     endnodeout
    endwhile

     nodeout SIdDocumento gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiGenerali,gGDatiConvenzione     ! M 1/1 
     		eEIdDocumento :=    tDocumentNbr
     endnodeout

     
     nLin2 := 0
			
	 while nLin2 < nLin do
				nLin2++
	     ! Groupe gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiGenerali,gGDatiRicezione : M 1/1 
	     nodeout SRiferimentoNumeroLinea gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiGenerali,gGDatiRicezione     ! M 1/unbound 
	     	eERiferimentoNumeroLinea := taOrderLine[nLin2]
	     endnodeout
	endwhile
	
     nodeout SIdDocumento gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiGenerali,gGDatiRicezione     ! M 1/1 
     	eEIdDocumento :=    tDocumentNbr
     endnodeout

     nLin2 := 0
			
	 while nLin2 < nLin do
		nLin2++
	     ! Groupe gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi : M 1/1 
	     ! Groupe gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi,gGDettaglioLinee : M 1/unbound 
	     nodeout SNumeroLinea gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi,gGDettaglioLinee     ! M 1/1 
	     	eENumeroLinea := taOrderLine[nLin2]
	     endnodeout

     ! Groupe gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi,gGDettaglioLinee,gGCodiceArticolo : M 1/1 
     nodeout SCodiceTipo gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi,gGDettaglioLinee,gGCodiceArticolo     ! M 1/1 
     	eECodiceTipo := taProductIdentifierQualCoded[nLin2] 
     endnodeout

     nodeout SCodiceValore gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi,gGDettaglioLinee,gGCodiceArticolo     ! M 1/1 
     	eECodiceValore := taProductIdentifier[nLin2] 
     endnodeout

     nodeout SDescrizione gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi,gGDettaglioLinee     ! M 1/1 
     	eEDescrizione :=	taDescription[nLin2]
     endnodeout

     nodeout SQuantita gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi,gGDettaglioLinee     ! M 1/1 
     	eEQuantita := build(number(taQty[nLin2]):012.8)
     endnodeout

     nodeout SUnitaMisura gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi,gGDettaglioLinee     ! M 1/1 
     	eEUnitaMisura := taQtyUnite[nLin2]
     endnodeout

     nodeout SPrezzoUnitario gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi,gGDettaglioLinee     ! M 1/1 
	if taUnitPrice[nLin2] <> EMPTY then
     		eEPrezzoUnitario :=  build(number(taUnitPrice[nLin2]):011.8)
	else
		eEPrezzoUnitario :=  build(number(taUnitPrice2[nLin2]):011.8)
	endif
     endnodeout

     ! Groupe gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi,gGDettaglioLinee,gGScontoMaggiorazione : M 1/1 
     nodeout STipo gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi,gGDettaglioLinee,gGScontoMaggiorazione     ! M 1/1 
     	eETipo := "SC"
     endnodeout

     nodeout SPrezzoTotale gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi,gGDettaglioLinee     ! M 1/1 
     	nPrixtotal := number(taUnitPrice2[nLin2])*number(taQty[nLin2])
	eEPrezzoTotale := build(nPrixtotal:011.8)
     endnodeout

     nodeout SAliquotaIVA gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi,gGDettaglioLinee     ! M 1/1 
    tAliquotaIVA := build(number(taTaxPercent[nLin2]):03.2) 	
	eEAliquotaIVA := build(number(taTaxPercent[nLin2]):03.2)
     endnodeout
   if number(tAliquotaIVA) = 0 then
     	! Groupe gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi,gGDettaglioLinee M 1/unbound 
     	nodeout SNatura gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi,gGDettaglioLinee     ! M 1/1 
     		eENatura := build("N2")
     	endnodeout
     endif


 ! Groupe gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi,gGDatiRiepilogo : M 1/unbound 
     nodeout SAliquotaIVA gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi,gGDatiRiepilogo     ! M 1/1 
     	tAliquotaIVA := build(number(taTaxPercent[nLin2]):03.2)
	eEAliquotaIVA := tAliquotaIVA
     endnodeout
     if number(tAliquotaIVA) = 0 then
     	! Groupe gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi,gGDatiRiepilogo : M 1/unbound 
     	nodeout SNatura gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi,gGDatiRiepilogo     ! M 1/1 
     		eENatura := build("N2")
     	endnodeout
     endif


     nodeout SImponibileImporto gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi,gGDatiRiepilogo     ! M 1/1 
     	nTaxAmount := number(taUnitPrice2[nLin2])*number(taQty[nLin2])
	eEImponibileImporto := build(nTaxAmount:011.2)
     endnodeout

     nodeout SImposta gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi,gGDatiRiepilogo     ! M 1/1 
	nImposta := number(taUnitPrice2[nLin2])*number(taQty[nLin2])*number(taTaxPercent[nLin2])/100
     	eEImposta := build(nImposta:011.2)
     endnodeout

     nodeout SEsigibilitaIVA gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiBeniServizi,gGDatiRiepilogo     ! M 1/1 
     	eEEsigibilitaIVA := "I"
     endnodeout
     
     endwhile

     ! Groupe gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiPagamento : M 1/1 
     nodeout SCondizioniPagamento gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiPagamento     ! M 1/1 
     	eECondizioniPagamento := "TP01"
     endnodeout

     ! Groupe gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiPagamento,gGDettaglioPagamento : M 1/1 
     nodeout SModalitaPagamento gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiPagamento,gGDettaglioPagamento     ! M 1/1 
     	eEModalitaPagamento := "MP05"
     endnodeout
 
     nodeout SImportoPagamento gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiPagamento,gGDettaglioPagamento     ! M 1/1 
     	eEImportoPagamento := build(number(tInvoiceAmount):011.2)
     endnodeout
 
     nodeout SScontoPagamentoAnticipato gGFatturaElettronica,gGFatturaElettronicaBody,gGDatiPagamento,gGDettaglioPagamento     ! M 1/1 
     	eEScontoPagamentoAnticipato := build(number(tTaxableAmount):011.2)
     endnodeout
/*  TRON
    TROFF
*/
endfunction 


