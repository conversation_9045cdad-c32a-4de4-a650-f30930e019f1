!   Date: 25/01/2017 
!   Nom: QAL-AIO-5906.rte 
!   Auteur: CGA
!   But: ARGC et ARGV

begin
	!--------------- Test sur les arguments libres ------
	print("Programme de test sur les arguments libres", NL)
	if ARGC <> 0 then
		
		print("Le programme s'exécute avec ",ARGC," arguments libres.",NL)
		while tIndex in ARGV do
			nCpt++
			if number(tIndex) > 0 then
				print("L argument ",tIndex," est : ", ARGV[tIndex],NL)
			endif
		endwhile
		print("La liste des arguments sous forme cle=valeur est :")
		print(NL)
		
		!while nI < ARGC do
		!	nI++
		!	if nI > 0 then
		!		print("Argument : ",nI,"=",ARGV[nI],NL )
		!	endif		
		!endwhile
		print(ARGV)
	else

		print("L exécution du programme  se fait sans arguments libres",NL)
		
	endif	
	
	!------------------------------------------------------
	
endbegin


