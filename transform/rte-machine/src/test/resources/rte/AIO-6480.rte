base "syslog.cfg" SYSLOG2
base "syslog.cfg" SYSLOG

begin
	SYSLOG := new("syslog")
	SYSLOG.PARTNER := "3018604301100"
	SYSLOG.USERNUM := 23
		
	tDATABASE := "syslog"

	! TEST PARTNER : FIELD TEXT

	SYSLOG := find(tDATABASE, PARTNER="3018604301100")
		if(SYSLOG.PARTNER <> "") then 
	        print("OK TEST 1 WITH STRING FIELD",NL)
			print(SYSLOG.PARTNER,NL)
		endif
		
	SYSLOG := find(tDATABASE, PARTNER="111111111111")
		if(SYSLOG.PARTNER <> "") then 
	        print("KO TEST 2 WITH STRING FIELD",NL)
		endif	
      
      
    ! TEST USERNUM : FIELD NUMERIC
     
      
	SYSLOG := find(tDATABASE, USERNUM=23)
		if(SYSLOG.USERNUM <> "") then 
			print("OK TEST 3 WITH NUMERIC FIELD",NL)
			print(SYSLOG.USERNUM,NL)
		endif
		
	SYSLOG := find(tDATABASE, USERNUM="23")
		if(SYSLOG.USERNUM <> "") then 
			print("OK TEST 4 WITH NUMERIC FIELD AND STRING",NL)
			print(SYSLOG.USERNUM,NL)
		endif
		
	SYSLOG := find(tDATABASE, USERNUM=100)
		if(SYSLOG.USERNUM <> "") then 
			print("KO TEST 5 WITH NUMERIC FIELD",NL)
		endif	
		
	! TEST USERNUM : FIELD NUMERIC
	
	! INVALID FIELD : NUMERIC EXPECTED
	SYSLOG2 := new("syslog")
	SYSLOG2.USERNUM := 42
		
	SYSLOG2 := find(tDATABASE, USERNUM=42)
		if(SYSLOG2.USERNUM <> "") then 
			print("OK TEST 6 WITH NUMERIC FIELD",NL)
			print(SYSLOG2.USERNUM,NL)
		endif
		
	SYSLOG2 := find(tDATABASE, USERNUM="42")
		if(SYSLOG2.USERNUM <> "") then 
			print("OK TEST 7 WITH NUMERIC FIELD AND STRING",NL)
			print(SYSLOG2.USERNUM,NL)
		endif
		
	SYSLOG2 := find(tDATABASE, USERNUM=100)
		if(SYSLOG2.USERNUM <> "") then 
			print("KO TEST 8 WITH NUMERIC FIELD",NL)
		endif	
		
endbegin