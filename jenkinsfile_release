pipeline {
    agent any

    parameters {
        choice(name: '<PERSON><PERSON>OY_TYPE', choices: ['snapshot', 'qualif', 'release'], description: 'Deployment Type')
        string(name: 'VERSION', defaultValue: '', description: 'Version to release (required for qualif and release)')
    }

    stages {
        stage('Initialize') {
            steps {
                script {
                    echo "Starting pipeline with DEPLOY_TYPE: ${DEPLOY_TYPE}"
                    if ((DEPLOY_TYPE == 'qualif' || DEPLOY_TYPE == 'release') && !params.VERSION) {
                        error "VERSION parameter is required for ${DEPLOY_TYPE} deployment"
                    }
                }
            }
        }

        stage('Checkout') {
            steps {
                git credentialsId: '1ff6869d-4818-45ff-bbab-1c94a304cf3a',
                    url: 'http://git.chassagne-scm.generixgroup.com/generix/legal-referential'
                sh 'git checkout ${BRANCH_NAME}'
            }
        }

        stage('Configure Git') {
            steps {
                script {
                    sh """
                        git config user.email 'j<PERSON><PERSON>@generixgroup.com'
                        git config user.name '<PERSON>'
                    """
                }
            }
        }

        stage('Prepare Version') {
            when {
                expression { DEPLOY_TYPE == 'qualif' }
            }
            tools {
                maven 'Maven3.6.3'  // Add Maven tool configuration
            }
            steps {
                script {
                    // Extract major.minor from version (e.g., 2.2 from 2.2.1)
                    def versionParts = params.VERSION.split('\\.')
                    def branchVersion = "${versionParts[0]}.${versionParts[1]}"

                    echo "Preparing version ${params.VERSION} on branch qualif/${branchVersion}"

                    withCredentials([usernamePassword(credentialsId: '1ff6869d-4818-45ff-bbab-1c94a304cf3a',
                        usernameVariable: 'GIT_USERNAME', passwordVariable: 'GIT_PASSWORD')]) {
                        sh """
                            git remote set-url origin 'http://${GIT_USERNAME}:${GIT_PASSWORD}@git.chassagne-scm.generixgroup.com/generix/legal-referential'

                            if git show-ref --verify --quiet refs/heads/qualif/${branchVersion}; then
                                echo "Branch qualif/${branchVersion} exists. Checking out..."
                                git checkout qualif/${branchVersion}
                            else
                                echo "Creating new branch qualif/${branchVersion}"
                                git checkout -b qualif/${branchVersion}
                            fi

                            # Update version in pom.xml using Maven
                            mvn versions:set -DnewVersion=${params.VERSION} -DgenerateBackupFiles=false

                            # Check if there are changes to commit
                            if git diff --quiet; then
                                echo "No changes to pom.xml"
                            else
                                git add pom.xml
                                git commit -m "Update version to ${params.VERSION}"
                                git push origin qualif/${branchVersion}
                            fi
                        """
                    }
                }
            }
        }

        stage('Build') {
            when {
                expression { DEPLOY_TYPE != 'release' }
            }
            tools {
                maven 'Maven3.6.3'
            }
            steps {
                sh 'mvn clean package -DskipTests=true'
            }
        }

        stage('Run Tests') {
            when {
                expression { DEPLOY_TYPE != 'release' }
            }
            tools {
                maven 'Maven3.6.3'
            }
            steps {
                script {
                    try {
                        sh 'mvn test'
                    } catch (Exception e) {
                        echo "Tests failed, collecting reports anyway"
                        currentBuild.result = 'UNSTABLE'
                    }
                }
            }
            post {
                always {
                    junit allowEmptyResults: true, testResults: '**/target/surefire-reports/*.xml'
                }
            }
        }

        stage('Deploy to Nexus') {
            steps {
                script {
                    if (DEPLOY_TYPE != 'release') {
                        // Deploy to snapshot or qualif repository
                        withCredentials([usernamePassword(credentialsId: 'nexus-password',
                            usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
                            sh '''
                                rm -f ../settings.xml
                                cp settings.xml ../settings.xml
                                sed -i s/%USER%/$USERNAME/g ../settings.xml
                                sed -i s/%PASSWORD%/$PASSWORD/g ../settings.xml
                                mvn deploy -s ../settings.xml -DskipTests=true \
                                    -Dmaven.wagon.http.ssl.insecure=true \
                                    -Dmaven.wagon.http.ssl.allowall=true \
                                    -P ${DEPLOY_TYPE}
                            '''
                        }
                    } else {
                        // Copy from qualif to release repository
                        withCredentials([usernamePassword(credentialsId: 'nexus-password',
                            usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
                            def artifactId = 'legalreferential'
                            def groupId = 'com.generix'
                            def packaging = 'zip'

                            def sourceUrl = "$NEXUS_URL/$PATH_REPO_NEXUS/$QUALIF_REPO_LEGEL_REF/${groupId.replace('.', '/')}/${artifactId}/legal-referential/${VERSION}/legal-referential-${VERSION}-distribution.zip"
                            def sourcePath = "${WORKSPACE}/${artifactId}-${VERSION}-distribution.zip"

                            sh """
                                wget --no-check-certificate --http-user="${USERNAME}" --http-password="${PASSWORD}" \
                                    -O "${sourcePath}" "${sourceUrl}"

                                mvn deploy:deploy-file -s ../settings.xml \
                                    -DgroupId="${groupId}" \
                                    -DartifactId="${artifactId}" \
                                    -Dversion="${VERSION}" \
                                    -Dpackaging="${packaging}" \
                                    -Dfile="${sourcePath}" \
                                    -DrepositoryId="releasesLegalRef" \
                                    -Durl="$NEXUS_URL/$PATH_REPO_NEXUS/$RELEASE_REPO_LEGAL_REF" \
                                    -Dmaven.wagon.http.ssl.insecure=true \
                                    -Dmaven.wagon.http.ssl.allowall=true
                            """
                        }
                    }
                }
            }
        }

        stage('Create Tag') {
            when {
                expression { DEPLOY_TYPE == 'qualif' }
            }
            steps {
                withCredentials([usernamePassword(credentialsId: '1ff6869d-4818-45ff-bbab-1c94a304cf3a',
                    usernameVariable: 'GIT_USERNAME', passwordVariable: 'GIT_PASSWORD')]) {
                    sh """
                        git remote set-url origin 'http://${GIT_USERNAME}:${GIT_PASSWORD}@git.chassagne-scm.generixgroup.com/generix/legal-referential'
                        git tag -a legel-ref-${params.VERSION} -m 'Version ${params.VERSION}'
                        git push origin legel-ref-${params.VERSION}
                    """
                }
            }
        }
    }

    post {
        always {
            cleanWs()
        }
        success {
            echo "Pipeline completed successfully!"
        }
        failure {
            echo "Pipeline failed! Please check the logs for details."
        }
    }
}
