# -----------------------------------------------------------------------------
# P R O P E R T I E S
# Targeted at system administrators, to avoid touching the context XML files.
# -----------------------------------------------------------------------------

version=version-${timestamp}-r${buildNumber}
timezone=Europe/Paris
contact=mailto:<EMAIL>?subject=TradeXpress%20Evolution%20(version%20${version}-${timestamp}-r${buildNumber})
poweredby=Powered by <a href='http://www.generixgroup.com/' target='_blank' style='text-decoration:none'>Generix Group</a> &#169;2015

# -- DIRECTORIES LAYOUT --
webapp.dir = target/test-classes/webapp
data.dir   = target/test-classes/data
bin.dir   = ${data.dir}/bin
temp.dir   = ${data.dir}/tmp
resources.dir = ${data.dir}/resources
work.dir   = ${data.dir}/work
backup.dir = ${data.dir}/backup
input.dir  = ${data.dir}/in
output.dir = ${data.dir}/out

# -- AWS ARCHIVING --
aws.core.enabled=true
aws.core.accesskey=********************
aws.core.secretkey=lJyQUO9N4bUxBkn4GpvDeqV5+//Pvfr9ZQrIVkCP
aws.core.region=EU_WEST_1
aws.archiving.s3.ssealgorithm=AES256
aws.archiving.s3.bucket=aio-archives-it-dev-ew1-bb09eb40

# -- AWS Quicksight --
aws.kpi.quicksight.enabled=false
aws.kpi.quicksight.accesskey =
aws.kpi.quicksight.secretkey =
aws.kpi.quicksight.region =