package com.byzaneo.security.bean.authentication;

import java.util.*;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.v3.oas.annotations.media.Schema;

import static java.util.Optional.ofNullable;

public class ClientRepresentation {

  @Schema(description = "")
  private Map<String, Object> access = null;

  @Schema(description = "")
  private String adminUrl = null;

  @Schema(description = "")
  private Boolean alwaysDisplayInConsole = null;

  @Schema(description = "")
  private Map<String, Object> attributes = null;

  @Schema(description = "")
  private Map<String, Object> authenticationFlowBindingOverrides = null;

  @Schema(description = "")
  private Boolean authorizationServicesEnabled = null;

  @Schema(description = "")
  private String baseUrl = null;

  @Schema(description = "")
  private Boolean bearerOnly = null;

  @Schema(description = "")
  private String clientAuthenticatorType = null;

  @Schema(description = "")
  private String clientId = null;

  @Schema(description = "")
  private Boolean consentRequired = null;

  @Schema(description = "")
  private List<String> defaultClientScopes = null;

  @Schema(description = "")
  private String description = null;

  @Schema(description = "")
  private Boolean directAccessGrantsEnabled = null;

  @Schema(description = "")
  private Boolean enabled = null;

  @Schema(description = "")
  private Boolean frontchannelLogout = null;

  @Schema(description = "")
  private Boolean fullScopeAllowed = null;

  @Schema(description = "")
  private String id = null;

  @Schema(description = "")
  private Boolean implicitFlowEnabled = null;

  @Schema(description = "")
  private String name = null;

  @Schema(description = "")
  private Integer nodeReRegistrationTimeout = null;

  @Schema(description = "")
  private Integer notBefore = null;

  @Schema(description = "")
  private Boolean oauth2DeviceAuthorizationGrantEnabled = null;

  @Schema(description = "")
  private List<String> optionalClientScopes = null;

  @Schema(description = "")
  private String origin = null;

  @Schema(description = "")
  private String protocol = null;

  @Schema(description = "")
  private Boolean publicClient = null;

  @Schema(description = "")
  private List<String> redirectUris = null;

  @Schema(description = "")
  private Map<String, Object> registeredNodes = null;

  @Schema(description = "")
  private String registrationAccessToken = null;

  @Schema(description = "")
  private String rootUrl = null;

  @Schema(description = "")
  private String secret = null;

  @Schema(description = "")
  private Boolean serviceAccountsEnabled = null;

  @Schema(description = "")
  private Boolean standardFlowEnabled = null;

  @Schema(description = "")
  private Boolean surrogateAuthRequired = null;

  @Schema(description = "")
  private List<String> webOrigins = null;

  /**
   * Get access
   *
   * @return access
   **/
  @JsonProperty("access")
  public Map<String, Object> getAccess() {
    return access;
  }

  public void setAccess(Map<String, Object> access) {
    this.access = access;
  }

  public ClientRepresentation access(Map<String, Object> access) {
    this.access = access;
    return this;
  }

  public ClientRepresentation putAccessItem(String key, Object accessItem) {
    this.access.put(key, accessItem);
    return this;
  }

  /**
   * Get adminUrl
   *
   * @return adminUrl
   **/
  @JsonProperty("adminUrl")
  public String getAdminUrl() {
    return adminUrl;
  }

  public void setAdminUrl(String adminUrl) {
    this.adminUrl = adminUrl;
  }

  public ClientRepresentation adminUrl(String adminUrl) {
    this.adminUrl = adminUrl;
    return this;
  }

  /**
   * Get alwaysDisplayInConsole
   *
   * @return alwaysDisplayInConsole
   **/
  @JsonProperty("alwaysDisplayInConsole")
  public Boolean isAlwaysDisplayInConsole() {
    return alwaysDisplayInConsole;
  }

  public void setAlwaysDisplayInConsole(Boolean alwaysDisplayInConsole) {
    this.alwaysDisplayInConsole = alwaysDisplayInConsole;
  }

  public ClientRepresentation alwaysDisplayInConsole(Boolean alwaysDisplayInConsole) {
    this.alwaysDisplayInConsole = alwaysDisplayInConsole;
    return this;
  }

  /**
   * Get attributes
   *
   * @return attributes
   **/
  @JsonProperty("attributes")
  public Map<String, Object> getAttributes() {
    return attributes;
  }

  public void setAttributes(Map<String, Object> attributes) {
    this.attributes = attributes;
  }

  public ClientRepresentation attributes(Map<String, Object> attributes) {
    this.attributes = attributes;
    return this;
  }

  public ClientRepresentation putAttributesItem(String key, Object attributesItem) {
    this.attributes.put(key, attributesItem);
    return this;
  }

  /**
   * Get authenticationFlowBindingOverrides
   *
   * @return authenticationFlowBindingOverrides
   **/
  @JsonProperty("authenticationFlowBindingOverrides")
  public Map<String, Object> getAuthenticationFlowBindingOverrides() {
    return authenticationFlowBindingOverrides;
  }

  public void setAuthenticationFlowBindingOverrides(Map<String, Object> authenticationFlowBindingOverrides) {
    this.authenticationFlowBindingOverrides = authenticationFlowBindingOverrides;
  }

  public ClientRepresentation authenticationFlowBindingOverrides(Map<String, Object> authenticationFlowBindingOverrides) {
    this.authenticationFlowBindingOverrides = authenticationFlowBindingOverrides;
    return this;
  }

  public ClientRepresentation putAuthenticationFlowBindingOverridesItem(String key, Object authenticationFlowBindingOverridesItem) {
    this.authenticationFlowBindingOverrides.put(key, authenticationFlowBindingOverridesItem);
    return this;
  }

  /**
   * Get authorizationServicesEnabled
   *
   * @return authorizationServicesEnabled
   **/
  @JsonProperty("authorizationServicesEnabled")
  public Boolean isAuthorizationServicesEnabled() {
    return ofNullable(authorizationServicesEnabled).orElse(false);
  }

  public void setAuthorizationServicesEnabled(Boolean authorizationServicesEnabled) {
    this.authorizationServicesEnabled = authorizationServicesEnabled;
  }

  public ClientRepresentation authorizationServicesEnabled(Boolean authorizationServicesEnabled) {
    this.authorizationServicesEnabled = authorizationServicesEnabled;
    return this;
  }

  /**
   * Get baseUrl
   *
   * @return baseUrl
   **/
  @JsonProperty("baseUrl")
  public String getBaseUrl() {
    return baseUrl;
  }

  public void setBaseUrl(String baseUrl) {
    this.baseUrl = baseUrl;
  }

  public ClientRepresentation baseUrl(String baseUrl) {
    this.baseUrl = baseUrl;
    return this;
  }

  /**
   * Get bearerOnly
   *
   * @return bearerOnly
   **/
  @JsonProperty("bearerOnly")
  public Boolean isBearerOnly() {
    return bearerOnly;
  }

  public void setBearerOnly(Boolean bearerOnly) {
    this.bearerOnly = bearerOnly;
  }

  public ClientRepresentation bearerOnly(Boolean bearerOnly) {
    this.bearerOnly = bearerOnly;
    return this;
  }

  /**
   * Get clientAuthenticatorType
   *
   * @return clientAuthenticatorType
   **/
  @JsonProperty("clientAuthenticatorType")
  public String getClientAuthenticatorType() {
    return clientAuthenticatorType;
  }

  public void setClientAuthenticatorType(String clientAuthenticatorType) {
    this.clientAuthenticatorType = clientAuthenticatorType;
  }

  public ClientRepresentation clientAuthenticatorType(String clientAuthenticatorType) {
    this.clientAuthenticatorType = clientAuthenticatorType;
    return this;
  }

  /**
   * Get clientId
   *
   * @return clientId
   **/
  @JsonProperty("clientId")
  public String getClientId() {
    return clientId;
  }

  public void setClientId(String clientId) {
    this.clientId = clientId;
  }

  public ClientRepresentation clientId(String clientId) {
    this.clientId = clientId;
    return this;
  }

  /**
   * Get consentRequired
   *
   * @return consentRequired
   **/
  @JsonProperty("consentRequired")
  public Boolean isConsentRequired() {
    return consentRequired;
  }

  public void setConsentRequired(Boolean consentRequired) {
    this.consentRequired = consentRequired;
  }

  public ClientRepresentation consentRequired(Boolean consentRequired) {
    this.consentRequired = consentRequired;
    return this;
  }

  /**
   * Get defaultClientScopes
   *
   * @return defaultClientScopes
   **/
  @JsonProperty("defaultClientScopes")
  public List<String> getDefaultClientScopes() {
    return defaultClientScopes;
  }

  public void setDefaultClientScopes(List<String> defaultClientScopes) {
    this.defaultClientScopes = defaultClientScopes;
  }

  public ClientRepresentation defaultClientScopes(List<String> defaultClientScopes) {
    this.defaultClientScopes = defaultClientScopes;
    return this;
  }

  public ClientRepresentation addDefaultClientScopesItem(String defaultClientScopesItem) {
    this.defaultClientScopes.add(defaultClientScopesItem);
    return this;
  }

  /**
   * Get description
   *
   * @return description
   **/
  @JsonProperty("description")
  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public ClientRepresentation description(String description) {
    this.description = description;
    return this;
  }

  /**
   * Get directAccessGrantsEnabled
   *
   * @return directAccessGrantsEnabled
   **/
  @JsonProperty("directAccessGrantsEnabled")
  public Boolean isDirectAccessGrantsEnabled() {
    return directAccessGrantsEnabled;
  }

  public void setDirectAccessGrantsEnabled(Boolean directAccessGrantsEnabled) {
    this.directAccessGrantsEnabled = directAccessGrantsEnabled;
  }

  public ClientRepresentation directAccessGrantsEnabled(Boolean directAccessGrantsEnabled) {
    this.directAccessGrantsEnabled = directAccessGrantsEnabled;
    return this;
  }

  /**
   * Get enabled
   *
   * @return enabled
   **/
  @JsonProperty("enabled")
  public Boolean isEnabled() {
    return enabled;
  }

  public void setEnabled(Boolean enabled) {
    this.enabled = enabled;
  }

  public ClientRepresentation enabled(Boolean enabled) {
    this.enabled = enabled;
    return this;
  }

  /**
   * Get frontchannelLogout
   *
   * @return frontchannelLogout
   **/
  @JsonProperty("frontchannelLogout")
  public Boolean isFrontchannelLogout() {
    return frontchannelLogout;
  }

  public void setFrontchannelLogout(Boolean frontchannelLogout) {
    this.frontchannelLogout = frontchannelLogout;
  }

  public ClientRepresentation frontchannelLogout(Boolean frontchannelLogout) {
    this.frontchannelLogout = frontchannelLogout;
    return this;
  }

  /**
   * Get fullScopeAllowed
   *
   * @return fullScopeAllowed
   **/
  @JsonProperty("fullScopeAllowed")
  public Boolean isFullScopeAllowed() {
    return fullScopeAllowed;
  }

  public void setFullScopeAllowed(Boolean fullScopeAllowed) {
    this.fullScopeAllowed = fullScopeAllowed;
  }

  public ClientRepresentation fullScopeAllowed(Boolean fullScopeAllowed) {
    this.fullScopeAllowed = fullScopeAllowed;
    return this;
  }

  /**
   * Get id
   *
   * @return id
   **/
  @JsonProperty("id")
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public ClientRepresentation id(String id) {
    this.id = id;
    return this;
  }

  /**
   * Get implicitFlowEnabled
   *
   * @return implicitFlowEnabled
   **/
  @JsonProperty("implicitFlowEnabled")
  public Boolean isImplicitFlowEnabled() {
    return implicitFlowEnabled;
  }

  public void setImplicitFlowEnabled(Boolean implicitFlowEnabled) {
    this.implicitFlowEnabled = implicitFlowEnabled;
  }

  public ClientRepresentation implicitFlowEnabled(Boolean implicitFlowEnabled) {
    this.implicitFlowEnabled = implicitFlowEnabled;
    return this;
  }

  /**
   * Get name
   *
   * @return name
   **/
  @JsonProperty("name")
  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public ClientRepresentation name(String name) {
    this.name = name;
    return this;
  }

  /**
   * Get nodeReRegistrationTimeout
   *
   * @return nodeReRegistrationTimeout
   **/
  @JsonProperty("nodeReRegistrationTimeout")
  public Integer getNodeReRegistrationTimeout() {
    return nodeReRegistrationTimeout;
  }

  public void setNodeReRegistrationTimeout(Integer nodeReRegistrationTimeout) {
    this.nodeReRegistrationTimeout = nodeReRegistrationTimeout;
  }

  public ClientRepresentation nodeReRegistrationTimeout(Integer nodeReRegistrationTimeout) {
    this.nodeReRegistrationTimeout = nodeReRegistrationTimeout;
    return this;
  }

  /**
   * Get notBefore
   *
   * @return notBefore
   **/
  @JsonProperty("notBefore")
  public Integer getNotBefore() {
    return notBefore;
  }

  public void setNotBefore(Integer notBefore) {
    this.notBefore = notBefore;
  }

  public ClientRepresentation notBefore(Integer notBefore) {
    this.notBefore = notBefore;
    return this;
  }

  /**
   * Get oauth2DeviceAuthorizationGrantEnabled
   *
   * @return oauth2DeviceAuthorizationGrantEnabled
   **/
  @JsonProperty("oauth2DeviceAuthorizationGrantEnabled")
  public Boolean isOauth2DeviceAuthorizationGrantEnabled() {
    return oauth2DeviceAuthorizationGrantEnabled;
  }

  public void setOauth2DeviceAuthorizationGrantEnabled(Boolean oauth2DeviceAuthorizationGrantEnabled) {
    this.oauth2DeviceAuthorizationGrantEnabled = oauth2DeviceAuthorizationGrantEnabled;
  }

  public ClientRepresentation oauth2DeviceAuthorizationGrantEnabled(Boolean oauth2DeviceAuthorizationGrantEnabled) {
    this.oauth2DeviceAuthorizationGrantEnabled = oauth2DeviceAuthorizationGrantEnabled;
    return this;
  }

  /**
   * Get optionalClientScopes
   *
   * @return optionalClientScopes
   **/
  @JsonProperty("optionalClientScopes")
  public List<String> getOptionalClientScopes() {
    return optionalClientScopes;
  }

  public void setOptionalClientScopes(List<String> optionalClientScopes) {
    this.optionalClientScopes = optionalClientScopes;
  }

  public ClientRepresentation optionalClientScopes(List<String> optionalClientScopes) {
    this.optionalClientScopes = optionalClientScopes;
    return this;
  }

  public ClientRepresentation addOptionalClientScopesItem(String optionalClientScopesItem) {
    this.optionalClientScopes.add(optionalClientScopesItem);
    return this;
  }

  /**
   * Get origin
   *
   * @return origin
   **/
  @JsonProperty("origin")
  public String getOrigin() {
    return origin;
  }

  public void setOrigin(String origin) {
    this.origin = origin;
  }

  public ClientRepresentation origin(String origin) {
    this.origin = origin;
    return this;
  }

  /**
   * Get protocol
   *
   * @return protocol
   **/
  @JsonProperty("protocol")
  public String getProtocol() {
    return protocol;
  }

  public void setProtocol(String protocol) {
    this.protocol = protocol;
  }

  public ClientRepresentation protocol(String protocol) {
    this.protocol = protocol;
    return this;
  }

  /**
   * Get publicClient
   *
   * @return publicClient
   **/
  @JsonProperty("publicClient")
  public Boolean isPublicClient() {
    return publicClient;
  }

  public void setPublicClient(Boolean publicClient) {
    this.publicClient = publicClient;
  }

  public ClientRepresentation publicClient(Boolean publicClient) {
    this.publicClient = publicClient;
    return this;
  }

  /**
   * Get redirectUris
   *
   * @return redirectUris
   **/
  @JsonProperty("redirectUris")
  public List<String> getRedirectUris() {
    return redirectUris;
  }

  public void setRedirectUris(List<String> redirectUris) {
    this.redirectUris = redirectUris;
  }

  public ClientRepresentation redirectUris(List<String> redirectUris) {
    this.redirectUris = redirectUris;
    return this;
  }

  public ClientRepresentation addRedirectUrisItem(String redirectUrisItem) {
    if (redirectUris == null)
      setRedirectUris(new ArrayList<>());
    this.redirectUris.add(redirectUrisItem);
    return this;
  }

  /**
   * Get registeredNodes
   *
   * @return registeredNodes
   **/
  @JsonProperty("registeredNodes")
  public Map<String, Object> getRegisteredNodes() {
    return registeredNodes;
  }

  public void setRegisteredNodes(Map<String, Object> registeredNodes) {
    this.registeredNodes = registeredNodes;
  }

  public ClientRepresentation registeredNodes(Map<String, Object> registeredNodes) {
    this.registeredNodes = registeredNodes;
    return this;
  }

  public ClientRepresentation putRegisteredNodesItem(String key, Object registeredNodesItem) {
    this.registeredNodes.put(key, registeredNodesItem);
    return this;
  }

  /**
   * Get registrationAccessToken
   *
   * @return registrationAccessToken
   **/
  @JsonProperty("registrationAccessToken")
  public String getRegistrationAccessToken() {
    return registrationAccessToken;
  }

  public void setRegistrationAccessToken(String registrationAccessToken) {
    this.registrationAccessToken = registrationAccessToken;
  }

  public ClientRepresentation registrationAccessToken(String registrationAccessToken) {
    this.registrationAccessToken = registrationAccessToken;
    return this;
  }

  /**
   * Get rootUrl
   *
   * @return rootUrl
   **/
  @JsonProperty("rootUrl")
  public String getRootUrl() {
    return rootUrl;
  }

  public void setRootUrl(String rootUrl) {
    this.rootUrl = rootUrl;
  }

  public ClientRepresentation rootUrl(String rootUrl) {
    this.rootUrl = rootUrl;
    return this;
  }

  /**
   * Get secret
   *
   * @return secret
   **/
  @JsonProperty("secret")
  public String getSecret() {
    return secret;
  }

  public void setSecret(String secret) {
    this.secret = secret;
  }

  public ClientRepresentation secret(String secret) {
    this.secret = secret;
    return this;
  }

  /**
   * Get serviceAccountsEnabled
   *
   * @return serviceAccountsEnabled
   **/
  @JsonProperty("serviceAccountsEnabled")
  public Boolean isServiceAccountsEnabled() {
    return serviceAccountsEnabled;
  }

  public void setServiceAccountsEnabled(Boolean serviceAccountsEnabled) {
    this.serviceAccountsEnabled = serviceAccountsEnabled;
  }

  public ClientRepresentation serviceAccountsEnabled(Boolean serviceAccountsEnabled) {
    this.serviceAccountsEnabled = serviceAccountsEnabled;
    return this;
  }

  /**
   * Get standardFlowEnabled
   *
   * @return standardFlowEnabled
   **/
  @JsonProperty("standardFlowEnabled")
  public Boolean isStandardFlowEnabled() {
    return standardFlowEnabled;
  }

  public void setStandardFlowEnabled(Boolean standardFlowEnabled) {
    this.standardFlowEnabled = standardFlowEnabled;
  }

  public ClientRepresentation standardFlowEnabled(Boolean standardFlowEnabled) {
    this.standardFlowEnabled = standardFlowEnabled;
    return this;
  }

  /**
   * Get surrogateAuthRequired
   *
   * @return surrogateAuthRequired
   **/
  @JsonProperty("surrogateAuthRequired")
  public Boolean isSurrogateAuthRequired() {
    return surrogateAuthRequired;
  }

  public void setSurrogateAuthRequired(Boolean surrogateAuthRequired) {
    this.surrogateAuthRequired = surrogateAuthRequired;
  }

  public ClientRepresentation surrogateAuthRequired(Boolean surrogateAuthRequired) {
    this.surrogateAuthRequired = surrogateAuthRequired;
    return this;
  }

  /**
   * Get webOrigins
   *
   * @return webOrigins
   **/
  @JsonProperty("webOrigins")
  public List<String> getWebOrigins() {
    return webOrigins;
  }

  public void setWebOrigins(List<String> webOrigins) {
    this.webOrigins = webOrigins;
  }

  public ClientRepresentation webOrigins(List<String> webOrigins) {
    this.webOrigins = webOrigins;
    return this;
  }

  public ClientRepresentation addWebOriginsItem(String webOriginsItem) {
    this.webOrigins.add(webOriginsItem);
    return this;
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ClientRepresentation {\n");

    sb.append("    access: ")
        .append(toIndentedString(access))
        .append("\n");
    sb.append("    adminUrl: ")
        .append(toIndentedString(adminUrl))
        .append("\n");
    sb.append("    alwaysDisplayInConsole: ")
        .append(toIndentedString(alwaysDisplayInConsole))
        .append("\n");
    sb.append("    attributes: ")
        .append(toIndentedString(attributes))
        .append("\n");
    sb.append("    authenticationFlowBindingOverrides: ")
        .append(toIndentedString(authenticationFlowBindingOverrides))
        .append("\n");
    sb.append("    authorizationServicesEnabled: ")
        .append(toIndentedString(authorizationServicesEnabled))
        .append("\n");
    sb.append("    baseUrl: ")
        .append(toIndentedString(baseUrl))
        .append("\n");
    sb.append("    bearerOnly: ")
        .append(toIndentedString(bearerOnly))
        .append("\n");
    sb.append("    clientAuthenticatorType: ")
        .append(toIndentedString(clientAuthenticatorType))
        .append("\n");
    sb.append("    clientId: ")
        .append(toIndentedString(clientId))
        .append("\n");
    sb.append("    consentRequired: ")
        .append(toIndentedString(consentRequired))
        .append("\n");
    sb.append("    defaultClientScopes: ")
        .append(toIndentedString(defaultClientScopes))
        .append("\n");
    sb.append("    description: ")
        .append(toIndentedString(description))
        .append("\n");
    sb.append("    directAccessGrantsEnabled: ")
        .append(toIndentedString(directAccessGrantsEnabled))
        .append("\n");
    sb.append("    enabled: ")
        .append(toIndentedString(enabled))
        .append("\n");
    sb.append("    frontchannelLogout: ")
        .append(toIndentedString(frontchannelLogout))
        .append("\n");
    sb.append("    fullScopeAllowed: ")
        .append(toIndentedString(fullScopeAllowed))
        .append("\n");
    sb.append("    id: ")
        .append(toIndentedString(id))
        .append("\n");
    sb.append("    implicitFlowEnabled: ")
        .append(toIndentedString(implicitFlowEnabled))
        .append("\n");
    sb.append("    name: ")
        .append(toIndentedString(name))
        .append("\n");
    sb.append("    nodeReRegistrationTimeout: ")
        .append(toIndentedString(nodeReRegistrationTimeout))
        .append("\n");
    sb.append("    notBefore: ")
        .append(toIndentedString(notBefore))
        .append("\n");
    sb.append("    oauth2DeviceAuthorizationGrantEnabled: ")
        .append(toIndentedString(oauth2DeviceAuthorizationGrantEnabled))
        .append("\n");
    sb.append("    optionalClientScopes: ")
        .append(toIndentedString(optionalClientScopes))
        .append("\n");
    sb.append("    origin: ")
        .append(toIndentedString(origin))
        .append("\n");
    sb.append("    protocol: ")
        .append(toIndentedString(protocol))
        .append("\n");
    sb.append("    publicClient: ")
        .append(toIndentedString(publicClient))
        .append("\n");
    sb.append("    redirectUris: ")
        .append(toIndentedString(redirectUris))
        .append("\n");
    sb.append("    registeredNodes: ")
        .append(toIndentedString(registeredNodes))
        .append("\n");
    sb.append("    registrationAccessToken: ")
        .append(toIndentedString(registrationAccessToken))
        .append("\n");
    sb.append("    rootUrl: ")
        .append(toIndentedString(rootUrl))
        .append("\n");
    sb.append("    secret: ")
        .append(toIndentedString(secret))
        .append("\n");
    sb.append("    serviceAccountsEnabled: ")
        .append(toIndentedString(serviceAccountsEnabled))
        .append("\n");
    sb.append("    standardFlowEnabled: ")
        .append(toIndentedString(standardFlowEnabled))
        .append("\n");
    sb.append("    surrogateAuthRequired: ")
        .append(toIndentedString(surrogateAuthRequired))
        .append("\n");
    sb.append("    webOrigins: ")
        .append(toIndentedString(webOrigins))
        .append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private static String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString()
        .replace("\n", "\n    ");
  }
}
