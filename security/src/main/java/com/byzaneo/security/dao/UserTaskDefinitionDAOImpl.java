package com.byzaneo.security.dao;

import javax.persistence.NoResultException;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.byzaneo.commons.dao.hibernate.GenericJpaDAO;
import com.byzaneo.security.bean.*;

@Repository(UserTaskDefinitionDAO.DAO_NAME)
public class UserTaskDefinitionDAOImpl extends GenericJpaDAO<UserTaskDefinition, Long> implements UserTaskDefinitionDAO {

  @Override
  @Transactional(readOnly = true)
  public UserTaskDefinition getUserTaskDefinitionByPortlet(User user, Long portletId) {
    try {
      return getEntityManager()
          .createQuery("SELECT utd FROM " + this.getEntityName() + " utd WHERE utd.user = :user AND utd.portletId = :portletId",
              UserTaskDefinition.class)
          .setParameter("user", user)
          .setParameter("portletId", portletId)
          .getSingleResult();
    }
    catch (NoResultException e) {
      return null;
    }
  }

  @Override
  @Transactional
  public void updateDescriptor(UserTaskDefinition userTaskDefinition) {
    this.getEntityManager()
        .createQuery("UPDATE " + getEntityName() + " utd SET utd.descriptor = :descriptor WHERE utd.id = :id")
        .setParameter("descriptor", userTaskDefinition.getDescriptor())
        .setParameter("id", userTaskDefinition.getId())
        .executeUpdate();
  }

  @Override
  @Transactional
  public int removeByPortletId(Long portletId) {
    return getEntityManager().createQuery("DELETE FROM " + getEntityName() + " p WHERE p.portletId = :portletId")
        .setParameter("portletId", portletId)
        .executeUpdate();
  }


  @Override
  @Transactional
  public int removeByPortletIdAndUser(Long portletId,User user) {
    return getEntityManager().createQuery("DELETE FROM " + getEntityName() + " p WHERE p.portletId = :portletId and  p.user = :user" )
            .setParameter("portletId", portletId)
            .setParameter("user", user)
            .executeUpdate();
  }
}
