package com.byzaneo.security.spring;

import static com.byzaneo.commons.ui.util.JSFHelper.getExternalContext;
import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.springframework.security.core.context.SecurityContextHolder.getContext;

import java.util.*;

import javax.servlet.http.*;

import org.slf4j.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import com.byzaneo.security.api.AuthenticationManager;
import com.byzaneo.security.bean.*;
import com.byzaneo.security.service.AccountService;

/**
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 */
@Service(UserDetailsService.BEAN_NAME)
public class UserDetailsService implements org.springframework.security.core.userdetails.UserDetailsService {
  private static final Logger log = LoggerFactory.getLogger(UserDetailsService.class);

  public static final String BEAN_NAME = "secUserDetailsService";

  /** Authentication manager property (default: {@link AccountService}) */
  private AuthenticationManager manager;

  /**
   * In the web.xml:<br/>
   * <code>
   *     &lt;listener-class&gt;<br/>
   *         org.springframework.web.context.request.RequestContextListener<br/>
   *     &lt;/listener-class&gt;<br/>
   * </code>
   */
  @Autowired(required = false)
  private HttpServletRequest request;

  /*
   * -- IMPL --
   */

  /** @see org.springframework.security.core.userdetails.UserDetailsService#loadUserByUsername(java.lang.String) */
  @Override
  public UserDetails loadUserByUsername(String username)
      throws UsernameNotFoundException, DataAccessException {
    return new UserDetails(
        this.getManager()
            .authenticate(username, request == null ? null : request.getServerName(), true),
        this.getManager());
  }

  public UserDetails loadUserByUsernameWithoutSave(String username)
      throws UsernameNotFoundException, DataAccessException {
    return new UserDetails(
        this.getManager()
            .authenticate(username, request == null ? null : request.getServerName(), false),
        this.getManager());
  }

  public void updateAccountLoginDetails(UserDetails userDetails) {
    this.getManager()
        .updateAccountLoginDetails(userDetails.getUser());
  }
  /*
   * -- HELPER --
   */

  /**
   * @return the current User set by spring security in the {@link HttpSession} holds by the JSF external context.
   */
  public static User getAuthenticatedUser() {
    try {
      UserDetails ud = getAuthenticatedUser((HttpSession) ofNullable(getExternalContext())
          .map(ctx -> ctx.getSession(false))
          .orElse(null));
      return ud == null ? null : ud.getUser();
    }
    catch (NullPointerException npe) {
      log.debug("Error getting getAuthenticatedUser() {}", getRootCauseMessage(npe));
      // usually NPE when the method is called outside the HTTP context
      log.debug("Authenticated user not found: {}", npe.getMessage());
    }
    catch (Exception e) {
      log.warn("Error getting authenticated user: {}", getRootCauseMessage(e));
    }
    return null;
  }

  public static Set<String> getUserScopeCodes(User user) {
    Set<String> codes = new HashSet<String>();
    for (Group group : user.getGroups()) {
      if (Partner.DESCRIPTION.equals(group.getDescription())) {
        codes.add(((Partner) group).getCode());
      }
    }

    // add the parnter's code from the perimeters
    if (user.getPerimeters() != null) {
      user.getPerimeters()
          .forEach(perimeter ->
          {
            List<Partner> partners = Arrays.asList(perimeter.getSelectedPartners());
            if (partners != null && partners.size() > 0) {
              partners.forEach(partner -> codes.add(partner.getCode()));
            }
          });
    }
    return codes;
  }
  public static List<Partner> getTechnicalUserScopePartners(TechnicalUser technicalUser) {
    Set<Partner> partners = new LinkedHashSet<>();

    List<Group> techUserGroups = new ArrayList<>(technicalUser.getGroups());
    techUserGroups.add(technicalUser.getPrimaryGroup());

    for (Group group : techUserGroups) {
      if (Partner.DESCRIPTION.equals(group.getDescription()) && group instanceof Partner) {
        partners.add((Partner) group);
      }
    }

    if (technicalUser.getPerimeters() != null) {
      technicalUser.getPerimeters().forEach(perimeter -> {
        Partner[] selectedPartners = perimeter.getSelectedPartners();
        if (selectedPartners != null) {
          partners.addAll(Arrays.asList(selectedPartners));
        }
      });
    }

    return new ArrayList<>(partners);
  }

  public static Set<String> getTechnicalUserScopeCodes(TechnicalUser technicalUser) {
    Set<String> codes = new HashSet<String>();
    List<Group> techUserGroups = technicalUser.getGroups();
    techUserGroups.add(technicalUser.getPrimaryGroup());

    for (Group group : techUserGroups) {
      if (Partner.DESCRIPTION.equals(group.getDescription())) {
        codes.add(((Partner) group).getCode());
      }
    }

    // add the parnter's code from the perimeters
    if (technicalUser.getPerimeters() != null) {
      technicalUser.getPerimeters()
          .forEach(perimeter ->
          {
            List<Partner> partners = Arrays.asList(perimeter.getSelectedPartners());
            if (partners != null && partners.size() > 0) {
              partners.forEach(partner -> codes.add(partner.getCode()));
            }
          });
    }
    return codes;
  }
  /**
   * @return the current User set by spring security in the given {@link HttpSession}.
   */
  public static UserDetails getAuthenticatedUser(final HttpSession session) {
    SecurityContext context;
    if (session == null) {
      context = ofNullable(getContext())
          .filter(ctx -> ctx.getAuthentication() != null)
          .orElse(null);
      if (context == null) {
        log.debug("Security Context not available.");
        return null;
      }
    }
    else if ((context = (SecurityContext) session.getAttribute("SPRING_SECURITY_CONTEXT")) == null ||
        context.getAuthentication() == null) {
          log.debug("Security Context not yet initialized.");
          return null;
        }

    Object userDetails = context.getAuthentication()
        .getPrincipal();
    if (userDetails != null && userDetails instanceof UserDetails) {
      return (UserDetails) userDetails;
    }

    // in the method authenticate(Authentication authentication) from SAMLAuthenticationProvider the user details will be set on the
    // "details" field.
    userDetails = context.getAuthentication()
        .getDetails();
    if (userDetails != null && userDetails instanceof UserDetails) {
      return (UserDetails) userDetails;
    }

    log.info("Authenticated user not found.");
    return null;
  }

  /*
   * -- ACCESSORS --
   */

  public AuthenticationManager getManager() {
    if (manager == null) {
      manager = getBean(AccountService.class, AccountService.SERVICE_NAME);
      log.info("Default authentication manager: {}", manager);
    }
    return manager;
  }

  public void setManager(AuthenticationManager manager) {
    this.manager = manager;
  }
}
