package com.byzaneo.security.ui.convert;

import com.google.gson.*;

import java.util.*;

public class GroupExclusionStrategy implements ExclusionStrategy {

  private static final List<String> ACCEPTED_FIELDS = Arrays.asList("name", "fullname", "freeViewProfile",
      "code", "registration", "vat", "duns", "registeredName",
      "location", "address", "streetName", "addressComplement", "postalCode", "city", "country",
      "orderContact", "orderPhone", "orderFax", "orderEmail", "shareCapital", "legalStructure",
      "vatRegime", "eReportingOption", "referenceCurrency",
      "freeText01", "freeText02", "freeText03", "freeText04", "freeText05", "freeText06", "freeText07", "freeText08", "freeText09",
      "freeLongText01", "freeLongText02", "freeDouble01", "freeDouble02", "freeBoolean01", "freeBoolean02", "freeDate01", "freeDate02",
      "invoicePreferencesPaymentDate", "invoicePreferencesPenaltyTerms", "invoicePreferencesDiscountTerms", "invoicePreferencesLatePayment",
      "invoicePreferencesVatType", "invoicePreferencesReasonExemption", "invoicePreferencesPaymentChoice");

  @Override
  public boolean shouldSkipField(FieldAttributes f) {
    return !ACCEPTED_FIELDS.contains(f.getName());
  }

  @Override
  public boolean shouldSkipClass(Class<?> clazz) {
    return false;
  }

}
