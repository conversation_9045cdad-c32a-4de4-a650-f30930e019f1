package com.byzaneo.generix.signature.poc;

import java.io.*;
import java.security.*;
import java.security.cert.*;
import java.security.cert.Certificate;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.junit.jupiter.api.Test;
import com.byzaneo.generix.test.JcaUtils;

import eu.europa.esig.dss.model.*;
import eu.europa.esig.dss.model.x509.CertificateToken;
import eu.europa.esig.dss.simplereport.SimpleReport;
import eu.europa.esig.dss.spi.x509.*;
import eu.europa.esig.dss.validation.*;
import eu.europa.esig.dss.validation.reports.Reports;

class ValidatorPOC_Pkcs12 {

  static {
    Security.addProvider(new BouncyCastleProvider());
  }

  // private final static String CERTIF_PATH = "D:\\test_sig\\bcCert_long.p12";
  private final static String CERTIF_PATH = "D:\\test_sig\\bcCert.p12";
  // private final String CERTIF_PATH = "/home/<USER>/test_sig/npikeystore";
  // private final String CERTIF_PATH = "/home/<USER>/test_sig/npikeystoreRSA2048";
  // private final String CERTIF_PATH = "/home/<USER>/chassagne/datadir/docs/ssl/287/test_jks";

  // private final static String SIGNED_DOC_PATH_PADES = "/home/<USER>/chassagne/datadir/in/Maf__DDD__R__PDFact__1234d5__20140419-sgd.pdf";
  private final static String SIGNED_DOC_PATH_PADES = "D:\\test_sig\\doc_puce_pkcs12_B.pdf";
  // private final static String SIGNED_DOC_PATH_PADES = "D:\\test_sig\\doc_puce_pkcs12_long.pdf";
  // private final static String SIGNED_DOC_PATH_PADES = "D:\\test_sig\\doc_puce_pkcs12_horo_B.pdf";
  // private final static String SIGNED_DOC_PATH_PADES = "D:\\test_sig\\doc_puce_pkcs12_horo_LT.pdf";

  private final static String SIGNED_DOC_PATH_XADES = "D:\\test_sig\\xml\\FR01234567890_11001.xml";

  @Test
  void verifPAdES() throws KeyStoreException, NoSuchProviderException, NoSuchAlgorithmException, CertificateException,
      FileNotFoundException, IOException {

    // AbstractSignatureTokenConnection token = new Pkcs12SignatureToken(JcaUtils.KEY_PASSWD, new File(CERTIF_PATH));

    KeyStore pkcs12Store = KeyStore.getInstance("PKCS12", "BC");
    pkcs12Store.load(new FileInputStream(CERTIF_PATH), JcaUtils.KEY_PASSWD);
    Certificate[] certificateChain = pkcs12Store.getCertificateChain("Eric's key");
    final CertificateToken trustedCertificate = new CertificateToken((X509Certificate) certificateChain[0]);

    // DSSPrivateKeyEntry privateKey = token.getKeys().get(0);
    // final X509Certificate[] certificateChain = privateKey.getCertificateChain();
    // final X509Certificate trustedCertificate = certificateChain[0];

    // Already signed document
    String toValidateFilePath = SIGNED_DOC_PATH_PADES;
    DSSDocument document = new FileDocument(toValidateFilePath);

    SignedDocumentValidator validator = SignedDocumentValidator.fromDocument(document);

    CommonCertificateVerifier verifier = new CommonCertificateVerifier();
    // AlwaysValidOCSPSource ocspSource = new AlwaysValidOCSPSource(CERTIF_PATH, JcaUtils.KEY_PASSWD_STRING);
    // verifier.setOcspSource(ocspSource);
    CommonTrustedCertificateSource trustedCertSource = new CommonTrustedCertificateSource();
    // import the keystore as trusted
    CommonCertificateSource certificateSource = new CommonCertificateSource();
    certificateSource.addCertificate(trustedCertificate);
    trustedCertSource.importAsTrusted(certificateSource);

    verifier.setTrustedCertSources(trustedCertSource);
    validator.setCertificateVerifier(verifier);

    Reports validateDocumentReports = validator.validateDocument();
    SimpleReport simpleReport = validateDocumentReports.getSimpleReport();
    System.out.println("*******************************************************************************");
    System.out.println("detailedReport");
    System.out.println(validateDocumentReports.getDetailedReport());
    System.out.println("*******************************************************************************");
    System.out.println("diagnosticData");
    System.out.println(validateDocumentReports.getDiagnosticData());
    System.out.println("*******************************************************************************");
    System.out.println("simpleReport");
    System.out.println(simpleReport);
    System.out.println("*******************************************************************************");
    System.out.println("valid sig count : " + simpleReport.getValidSignaturesCount());
    System.out.println("total sig count : " + simpleReport.getSignaturesCount());
  }

  @Test
  void verifXAdES() throws KeyStoreException, NoSuchProviderException, NoSuchAlgorithmException, CertificateException,
      FileNotFoundException, IOException {

    // AbstractSignatureTokenConnection token = new Pkcs12SignatureToken(JcaUtils.KEY_PASSWD, new File(CERTIF_PATH));

    KeyStore pkcs12Store = KeyStore.getInstance("PKCS12", "BC");
    pkcs12Store.load(new FileInputStream(CERTIF_PATH), JcaUtils.KEY_PASSWD);
    Certificate[] certificateChain = pkcs12Store.getCertificateChain("Eric's key");
    final CertificateToken trustedCertificate = new CertificateToken((X509Certificate) certificateChain[0]);

    // Already signed document
    String toValidateFilePath = SIGNED_DOC_PATH_XADES;
    DSSDocument document = new FileDocument(toValidateFilePath);

    SignedDocumentValidator validator = SignedDocumentValidator.fromDocument(document);

    CommonCertificateVerifier verifier = new CommonCertificateVerifier();
    CommonTrustedCertificateSource trustedCertSource = new CommonTrustedCertificateSource();
    // import the keystore as trusted
    CommonCertificateSource certificateSource = new CommonCertificateSource();
    certificateSource.addCertificate(trustedCertificate);
    trustedCertSource.importAsTrusted(certificateSource);

    verifier.setTrustedCertSources(trustedCertSource);
    validator.setCertificateVerifier(verifier);

    Reports validateDocumentReports = validator.validateDocument();
    SimpleReport simpleReport = validateDocumentReports.getSimpleReport();
    System.out.println("*******************************************************************************");
    System.out.println("detailedReport");
    System.out.println(validateDocumentReports.getDetailedReport());
    System.out.println("*******************************************************************************");
    System.out.println("diagnosticData");
    System.out.println(validateDocumentReports.getDiagnosticData());
    System.out.println("*******************************************************************************");
    System.out.println("simpleReport");
    System.out.println(simpleReport);
    System.out.println("*******************************************************************************");
    System.out.println("valid sig count : " + simpleReport.getValidSignaturesCount());
    System.out.println("total sig count : " + simpleReport.getSignaturesCount());
  }
}
