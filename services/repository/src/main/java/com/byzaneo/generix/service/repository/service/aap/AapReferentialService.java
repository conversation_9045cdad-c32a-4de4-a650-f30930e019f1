/*
 * Copyright (c) 2024.
 * Created by Mansour SRIDI
 */

package com.byzaneo.generix.service.repository.service.aap;

import com.byzaneo.generix.service.repository.bean.aap.AapReferential;
import com.byzaneo.query.Query;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface AapReferentialService {

  String SERVICE_NAME = "gnxAapReferentialService";

  AapReferential findById(Long id);

  List<AapReferential> search(Query query);
  Page<AapReferential> search(Query query, Pageable pageable);
  AapReferential getAapReferential(Query query);
}
