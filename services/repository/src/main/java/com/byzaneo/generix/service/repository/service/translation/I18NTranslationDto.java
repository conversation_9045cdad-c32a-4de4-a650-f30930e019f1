/*
 * Copyright (c) 2023.
 * created by <PERSON><PERSON>
 */
package com.byzaneo.generix.service.repository.service.translation;

import lombok.*;

import javax.xml.bind.annotation.XmlRootElement;
import java.util.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XmlRootElement( name = "I18NTranslation")
public class I18NTranslationDto {
  private Long id;
  private String code;
  private String defaultValue;
  private String newValue;
  private Locale locale;
  private Date defaultValueChangedAt;
  private Date newValueChangedAt;
  private TranslationUserDto newValueChangedBy;
  private Long i18NModuleId;
  private String localeAsString;
}