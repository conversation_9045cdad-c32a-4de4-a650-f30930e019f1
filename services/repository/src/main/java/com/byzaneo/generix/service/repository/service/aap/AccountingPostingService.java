/*
 * Copyright (c) 2024.
 * Created by Mansour SRIDI
 */

package com.byzaneo.generix.service.repository.service.aap;

import com.byzaneo.generix.service.repository.bean.aap.AccountingPosting;
import com.byzaneo.query.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

public interface AccountingPostingService {

  String SERVICE_NAME = "gnxAccountingPostingService";

  AccountingPosting getAccountingPosting(Query query);
  AccountingPosting getAccountingPostingById(long id);

  AccountingPosting getAccountingPostingByPartnerId(String partnerId);

  Map<String, AccountingPosting> fetchAccountingPostings(List<String> partnerIds);

  AccountingPosting getAccountingPostingByOwnerAndPartnerId(String owner, String partnerId);

  AccountingPosting saveAccountingPosting(AccountingPosting accountingPosting);

  void removeAccountingPosting (AccountingPosting accountingPosting);
}
