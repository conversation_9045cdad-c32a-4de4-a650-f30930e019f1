package com.byzaneo.generix.integration.ui;

import static com.byzaneo.commons.ui.util.JSFHelper.getRequestParameter;
import static com.byzaneo.commons.ui.util.JSFHelper.getSpringBean;
import static com.byzaneo.commons.ui.util.MessageHelper.error;
import static com.byzaneo.commons.ui.util.MessageHelper.getMessage;
import static com.byzaneo.commons.ui.util.MessageHelper.info;
import static com.byzaneo.commons.util.CollectionsHelper.toArrayList;
import static com.byzaneo.xtrade.ipm.ui.AbstractProcessHandler.REQUEST_PARAM_PROCESS_ID;
import static com.byzaneo.xtrade.ipm.ui.AbstractProcessHandler.REQUEST_PARAM_PROJECT_ID;
import static java.lang.String.format;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Stream.empty;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.eclipse.core.internal.registry.RegistryProperties.empty;
import static org.primefaces.context.RequestContext.getCurrentInstance;

import java.util.*;

import javax.annotation.*;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.event.ActionEvent;

import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.generix.bean.Instance;
import lombok.*;
import org.primefaces.model.*;

import com.byzaneo.commons.service.ServiceException;
import com.byzaneo.generix.integration.service.IntegrationService;
import com.byzaneo.generix.ui.instance.AbstractInstanceServiceHandler;
import com.byzaneo.xtrade.ipm.bean.*;
import com.byzaneo.xtrade.ipm.bean.Process;
import com.byzaneo.xtrade.ipm.service.ProjectService;

/**
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date Apr 19, 2013
 * @since 2.3 GNX-413 TODO manages handler's behaviors in separate beans (handlers per tabs and dialogs framework ready)
 */
@ManagedBean(name = IntegrationISHandler.MANAGED_BEAN_NAME)
@ViewScoped
public class IntegrationISHandler extends AbstractInstanceServiceHandler {
  private static final long serialVersionUID = -4298827605207365761L;

  public static final String MANAGED_BEAN_NAME = "gnxIntegrationISHandler";

  private transient IntegrationService integrationService;

  private transient ProjectService projectService;

  // WORKSPACE
  private Workspace workspace;

  // PROJECTS
  private List<Project> filtered;

  private Project project;

  private Process process;

  @Getter
  @Setter
  private String processFilterValue;

  @Getter
  @Setter
  private String projectFilterValue;

  /*
   * -- LIFE CYCLE --
   */

  @Override
  @PostConstruct
  public void init() {
    super.init();
    this.integrationService = getSpringBean(IntegrationService.class, IntegrationService.SERVICE_NAME);
    this.projectService = getSpringBean(ProjectService.class, ProjectService.SERVICE_NAME);
    this.load(getRequestParameter(REQUEST_PARAM_PROJECT_ID));
    this.process = this.projectService.getProcess(getRequestParameter(REQUEST_PARAM_PROCESS_ID));

  }

  @Override
  @PreDestroy
  public void reset() {
    // workspace
    this.workspace = null;
    // projects
    this.filtered = null;
    this.project = null;
    this.projectFilterValue = null;
    this.processFilterValue = null;
  }

  /*
   * -- EVENTS --
   */

  // -- PROJECT --

  public void onAddProject(ActionEvent event) {
    this.project = this.projectService.createProject(this.workspace);
  }

  public void onSaveProject(ActionEvent event) {
    try {
      this.project = this.projectService.save(project);
      List<Project> projects = this.projectService.getWorkspaceProjects(getWorkspace());
      if (!projects.contains(project)) {
        projects.add(project);
      }
      // this.load(project.getId());
      // this.workspace = null;
      info("itglbls.project_saved");
    }
    catch (ServiceException | IllegalArgumentException se) {
      error(se.getMessage());
      getCurrentInstance().addCallbackParam("success", false);
    }
    catch (Exception e) {
      error(e, "itglbls.error_saving_project", getRootCauseMessage(e));
      getCurrentInstance().addCallbackParam("success", false);
    }
  }

  public List<BusinessProcess> getAllProcessesForAnEnvironment() {
    if (processFilterValue != null && projectFilterValue != null) {
      log.error("It is not possible to use both filters in the same time");
      MessageHelper.error("labels.double_filter_error", "It is not possible to use both filters in the same time", getLanguage());
      setProcessFilterValue(null);
      return Collections.emptyList();
    }
    List<Project> projects = this.projectService.getWorkspaceProjects(getWorkspace());
    List<BusinessProcess> processList = new ArrayList<>();
    List<BusinessProcess> auxList = new ArrayList<>();
    projects.forEach(p -> auxList.addAll(toArrayList(p.getProcesses(BusinessProcess.class))));
    processList.addAll(auxList);
    if (processFilterValue != null && !processFilterValue.isEmpty()) {
      String processFilterValueLowerCase = processFilterValue.toLowerCase();
      processList = auxList.stream()
          .filter(p -> getProcessName(p)
              .contains(processFilterValueLowerCase))
          .collect(toList());
    }
    return processList;

  }

  private String getProcessName(BusinessProcess businessProcess) {
    return ofNullable(businessProcess).map(BusinessProcess::getName)
        .map(String::toLowerCase)
        .orElse(empty);
  }
  /*
   * -- PRIVATE --
   */

  private void load(String projectid) {
    this.project = this.projectService.getProject(projectid);
  }

  private <P extends Process> DefaultTreeNode createProcessesMenu(Class<P> type, String title, String url) {
    List<P> process = integrationService.getLastModifiedProcesses(instance, type, 2);
    DefaultTreeNode menu = new DefaultTreeNode(title);
    for (P p : process) {
      menu.getChildren()
          .add(createMenuNode(
              p.getName(),
              format("/ui/admin/instance/integration/%s.jsf?iid=%s&pcid=%s", url, instance.getId(), p.getId()),
              p.getProject()
                  .getName(),
              16));
    }
    return menu;
  }

  public void onOpenResources(ActionEvent event) {
    ResourcesDialogHandler.openResourcesDialog("resources-dialog", null, "??title??", this.instance.getId(), this.process.getId());
  }

  /*
   * -- ACCESSORS --
   */

  public Workspace getWorkspace() {
    return workspace == null ? (this.workspace = this.integrationService.getWorkspace(this.instance)) : this.workspace;
  }

  public void setWorkspace(Workspace workspace) {
    this.workspace = workspace;
  }

  public List<Project> getWorkspaceProjects() {
    return this.projectService.getWorkspaceProjects(getWorkspace());
  }

  public List<Project> getFiltered() {
    return filtered;
  }

  public void setFiltered(List<Project> filtered) {
    this.filtered = filtered;
  }

  public Project getProject() {
    return project;
  }

  public void setProject(Project project) {
    this.project = project;
  }

  @Override
  public TreeNode getMenuTree() {
    TreeNode result = new DefaultTreeNode();
    result.getChildren()
        .add(this.createProcessesMenu(BusinessProcess.class,
            getMessage("labels.gnx-labels.imenu_integration_recent_business", "Recent business", null),
            "business"));
    return result;
  }

}
