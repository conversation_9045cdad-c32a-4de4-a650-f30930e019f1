package com.byzaneo.generix.customer.dao;

import javax.persistence.NoResultException;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.byzaneo.commons.dao.hibernate.GenericCustomJpaDAO;
import com.byzaneo.generix.customer.bean.*;

@Repository(ArrondiDAO.DAO_NAME)
public class ArrondiDAOImpl extends GenericCustomJpaDAO<Arrondi, ArrondiId> implements ArrondiDAO {

  @Override
  @Transactional(readOnly = true)
  public Arrondi findArrondi(Article article, Client client) {
    if (article == null)
      return null;
    try {
      return getEntityManager().createQuery(
          "SELECT a FROM " + Arrondi.class.getName() + " a WHERE a.id.article.id =:articleId AND a.id.client.id =:clientId", Arrondi.class)
          .setParameter("articleId", article.getId())
          .setParameter("clientId", client.getId())
          .getSingleResult();
    } catch (NoResultException nre) {
      return null;
    }
  }

}
