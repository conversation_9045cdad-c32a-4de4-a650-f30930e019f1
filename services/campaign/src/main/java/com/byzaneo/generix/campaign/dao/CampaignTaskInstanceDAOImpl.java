package com.byzaneo.generix.campaign.dao;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.byzaneo.commons.dao.hibernate.GenericJpaDAO;
import com.byzaneo.generix.campaign.bean.CampaignTask;
import com.byzaneo.generix.campaign.bean.CampaignTaskInstance;
import com.byzaneo.generix.campaign.bean.CampaignTaskInstance.State;

/**
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Jan 23, 2013
 * @version 2.0 GNX-163
 */
@Repository(CampaignTaskInstanceDAO.DAO_NAME)
public class CampaignTaskInstanceDAOImpl extends GenericJpaDAO<CampaignTaskInstance, Long> implements CampaignTaskInstanceDAO {

  @Override
  @Transactional
  public int updateState(CampaignTask campaignTask, State state) {
    if (campaignTask == null || state == null)
      return 0;
    return this.entityManager
        .createQuery("UPDATE " + this.getEntityName() + " cti SET cti.state = :state WHERE cti.campaignTask = :ct")
        .setParameter("state", state)
        .setParameter("ct", campaignTask)
        .executeUpdate();
  }

}
