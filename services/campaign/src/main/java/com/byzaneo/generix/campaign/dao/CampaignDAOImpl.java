package com.byzaneo.generix.campaign.dao;

import static com.byzaneo.security.util.ResourceHelper.generateId;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static org.apache.commons.lang3.ArrayUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.isEmpty;
import static org.slf4j.LoggerFactory.getLogger;

import java.util.List;

import javax.persistence.NoResultException;

import org.slf4j.Logger;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.byzaneo.commons.dao.DataAccessException;
import com.byzaneo.commons.dao.hibernate.GenericJpaDAO;
import com.byzaneo.generix.bean.*;
import com.byzaneo.generix.campaign.bean.*;
import com.byzaneo.generix.campaign.bean.Campaign.State;
import com.byzaneo.generix.campaign.service.CampaignService;

/**
 * <AUTHOR> Fung <<EMAIL>>
 * @date 13 nov. 2011
 * @company Byzaneo
 */
@Repository(CampaignDAO.DAO_NAME)
@Transactional(readOnly = true)
public class CampaignDAOImpl extends GenericJpaDAO<Campaign, Long> implements CampaignDAO {
  private static final Logger log = getLogger(CampaignDAOImpl.class);

  /** @see com.byzaneo.generix.campaign.dao.CampaignDAO#findByInstance(Instance) */
  @Override
  @Transactional(readOnly = true)
  public List<Campaign> findByInstance(Instance instance) throws DataAccessException {
    return instance == null || instance.getId() == null
        ? emptyList()
        : this.getRepository()
            .findAll((root, query, cb) -> cb.equal(root.join(Campaign_.instance)
                .get(Instance_.id), instance.getId()));
  }

  /** @see com.byzaneo.generix.campaign.dao.CampaignDAO#findStateById(java.lang.Long) */
  @Override
  @Transactional(readOnly = true)
  public State findStateById(Long id) {
    return this.entityManager.createQuery("SELECT c.state FROM " + getEntityName() + " c WHERE c.id = :cid", State.class)
        .setParameter("cid", id)
        .getSingleResult();
  }

  /**
   * @see com.byzaneo.generix.campaign.dao.CampaignDAO#findByStateAndTaskId(com.byzaneo.generix.campaign.bean.Campaign.State,
   *      java.lang.Long)
   */
  @Override
  @Transactional(readOnly = true)
  public Campaign findByStateAndTaskId(State state, Long tid) {
    return state == null || tid == null
        ? null
        : this.getRepository()
            .findOne((root, query, cb) -> cb.and(
                cb.equal(root.join(Campaign_.tasks)
                    .get(CampaignTask_.taskId), tid),
                cb.equal(root.get(Campaign_.state), state)))
            .orElse(null);
  }

  /** @see com.byzaneo.generix.campaign.dao.CampaignDAO#findByState(com.byzaneo.generix.campaign.bean.Campaign.State) */
  @Override
  @Transactional(readOnly = true)
  public List<Campaign> findByState(State state) {
    return state == null
        ? emptyList()
        : this.getRepository()
            .findAll((root, query, cb) -> cb.equal(root.get(Campaign_.state), state));
  }

  /**
   * @see com.byzaneo.generix.campaign.dao.CampaignDAO#findByInstanceAndState(com.byzaneo.generix.bean.Instance,
   *      com.byzaneo.generix.campaign.bean.Campaign.State)
   */
  @Override
  @Transactional(readOnly = true)
  public Campaign findByInstanceAndState(Instance instance, State state) {
    // better thrown an exception if multiple results
    if (state == null || instance == null)
      return null;
    try {
      return this.entityManager
          .createQuery("SELECT c FROM " + getEntityName() + " c WHERE c.state = :cstate AND c.instance = :instance", Campaign.class)
          .setParameter("cstate", state)
          .setParameter("instance", instance)
          .getSingleResult();
    }
    catch (NoResultException e) {
      log.debug("Failed to find campaign by state and task id", e);
      return null;
    }
  }

  /**
   * @see com.byzaneo.generix.campaign.dao.CampaignDAO#findByInstanceAndStates(com.byzaneo.generix.bean.Instance,
   *      com.byzaneo.generix.campaign.bean.Campaign.State[])
   */
  @Override
  @Transactional(readOnly = true)
  public List<Campaign> findByInstanceAndStates(Instance instance, State... states) {
    if (instance == null || isEmpty(states))
      return emptyList();
    return this.entityManager
        .createQuery("SELECT c FROM " + getEntityName() + " c WHERE c.state IN :cstates AND c.instance = :instance", Campaign.class)
        .setParameter("cstates", asList(states))
        .setParameter("instance", instance)
        .getResultList();
  }

  /** @see com.byzaneo.generix.campaign.dao.CampaignDAO#findByInstanceAndCode(com.byzaneo.generix.bean.Instance, java.lang.String) */
  @Override
  @Transactional(readOnly = true)
  public List<Campaign> findByInstanceAndCode(Instance instance, String code) {
    if (instance == null || isEmpty(code))
      return emptyList();
    return this.entityManager
        .createQuery("SELECT c FROM " + getEntityName() + " c WHERE c.code = :ccode AND c.instance = :instance", Campaign.class)
        .setParameter("ccode", code)
        .setParameter("instance", instance)
        .getResultList();
  }

  /** @see com.byzaneo.generix.campaign.dao.CampaignDAO#reset(com.byzaneo.generix.campaign.bean.Campaign) */
  @Override
  @Transactional
  public void reset(Campaign campaign) {
    this.clear(campaign, false);
  }

  /** @see com.byzaneo.commons.dao.hibernate.GenericJpaDAO#remove(com.byzaneo.commons.bean.Persistent) */
  @Override
  @Transactional
  public void remove(Campaign campaign) throws DataAccessException {
    this.clear(campaign, true);
  }

  /*
   * -- PRIVATE --
   */

  private void clear(Campaign campaign, boolean remove) throws DataAccessException {
    int count;
    Campaign camp = this.findById(campaign.getId());

    if (camp == null) {
      log.error("Campaign not found");
      return;
    }

    // - ct -
    for (CampaignTask ct : camp.getTasks()) {
      // - state transitions -
      count = this.entityManager.createQuery("DELETE FROM StateTransition WHERE resource = :presource")
          .setParameter("presource", generateId(ct))
          .executeUpdate();
      log.debug("Removed {} CT {}'s state transitions", count, ct.getId());

      // - ct -
      if (remove) {
        count = this.entityManager.createQuery("DELETE FROM CampaignTask WHERE id = :ctid")
            .setParameter("ctid", ct.getId())
            .executeUpdate();
        log.debug("Removed CT {}", ct.getId());
      }
    }

    // - tasks results -
    count = this.entityManager.createQuery("DELETE FROM TaskResult WHERE process = :pname AND processId = :pid")
        .setParameter("pname", CampaignService.PROCESS_NAME)
        .setParameter("pid", String.valueOf(camp.getId()))
        .executeUpdate();
    log.debug("Removed {} campaign {}'s tasks results", count, camp.getCode());

    // - state transitions -
    count = this.entityManager.createQuery("DELETE FROM StateTransition WHERE resource = :presource")
        .setParameter("presource", generateId(camp))
        .executeUpdate();
    log.debug("Removed {} campaign {}'s state transitions", count, camp.getCode());

    if (remove)
      super.remove(camp.getId());
  }

}
