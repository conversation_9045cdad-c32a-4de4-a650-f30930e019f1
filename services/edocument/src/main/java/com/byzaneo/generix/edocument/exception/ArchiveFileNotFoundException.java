/**
 * 
 */
package com.byzaneo.generix.edocument.exception;

import static java.lang.String.format;
import static org.apache.commons.lang3.ArrayUtils.isEmpty;

/**
 * <AUTHOR>
 * @company Generix Group
 * @date 17 avr. 2018
 */
public class ArchiveFileNotFoundException extends Exception {

  private static final long serialVersionUID = 1L;

  public ArchiveFileNotFoundException() {
  }

  public ArchiveFileNotFoundException(String message, Object... args) {
    super(isEmpty(args) ? message : format(message, args));
  }

  public ArchiveFileNotFoundException(String message, Throwable cause) {
    super(message, cause);
  }

  public ArchiveFileNotFoundException(Throwable cause) {
    super(cause);
  }
}
