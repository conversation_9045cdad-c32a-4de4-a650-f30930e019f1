package com.byzaneo.generix.edocument;

public class MaxSearchResultException extends SafeboxException {

  private static final long serialVersionUID = 6262162765472725893L;

  public MaxSearchResultException() {
    super();
  }

  public MaxSearchResultException(String message, Object... args) {
    super(message, args);
  }

  public MaxSearchResultException(String message, Throwable cause) {
    super(message, cause);
  }

  public MaxSearchResultException(Throwable cause, String message, Object... args) {
    super(cause, message, args);
  }

  public MaxSearchResultException(Throwable cause) {
    super(cause);
  }

}
