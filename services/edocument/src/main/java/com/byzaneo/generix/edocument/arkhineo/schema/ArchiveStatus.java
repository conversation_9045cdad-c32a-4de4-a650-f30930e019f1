//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.09.04 at 11:26:18 AM CEST 
//

package com.byzaneo.generix.edocument.arkhineo.schema;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for anonymous complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;all&gt;
 *         &lt;element name="processes" type="{http://www.arkhineo.fr/CFE/metadata/1.1}processes-type"/&gt;
 *       &lt;/all&gt;
 *       &lt;attribute name="status" use="required" type="{http://www.arkhineo.fr/CFE/metadata/1.1}cfePlatformProcessStatus" /&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {

})
@XmlRootElement(name = "archive-status")
public class ArchiveStatus {

  @XmlElement(required = true)
  protected ProcessesType processes;

  @XmlAttribute(name = "status", required = true)
  protected CfePlatformProcessStatus status;

  /**
   * Gets the value of the processes property.
   * 
   * @return possible object is {@link ProcessesType }
   */
  public ProcessesType getProcesses() {
    return processes;
  }

  /**
   * Sets the value of the processes property.
   * 
   * @param value allowed object is {@link ProcessesType }
   */
  public void setProcesses(ProcessesType value) {
    this.processes = value;
  }

  /**
   * Gets the value of the status property.
   * 
   * @return possible object is {@link CfePlatformProcessStatus }
   */
  public CfePlatformProcessStatus getStatus() {
    return status;
  }

  /**
   * Sets the value of the status property.
   * 
   * @param value allowed object is {@link CfePlatformProcessStatus }
   */
  public void setStatus(CfePlatformProcessStatus value) {
    this.status = value;
  }

}
