package com.byzaneo.generix.edocument.spi;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import org.apache.commons.httpclient.methods.multipart.MultipartRequestEntity;
import org.apache.commons.httpclient.methods.multipart.Part;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.http.entity.AbstractHttpEntity;

/**
 * Cette HttpEntity encapsule une {@link MultipartRequestEntity} et sert ainsi de bridge entre l'API commons-httpclient et httpcore pour
 * l'envoie de requête HTTP POST MULTIPART.
 * 
 * <AUTHOR>
 */
public class MultipartHttpEntity extends AbstractHttpEntity {

  private final MultipartRequestEntity entity;

  public MultipartHttpEntity(List<Part> parts) {
    entity = new MultipartRequestEntity(parts.toArray(new Part[parts.size()]), new HttpMethodParams());
    setContentType(entity.getContentType());
  }

  @Override
  public boolean isRepeatable() {
    return true;
  }

  @Override
  public long getContentLength() {
    return entity.getContentLength();
  }

  @Override
  public InputStream getContent() throws IOException {
    ByteArrayOutputStream stream = new ByteArrayOutputStream();
    entity.writeRequest(stream);
    return new ByteArrayInputStream(stream.toByteArray());
  }

  @Override
  public void writeTo(OutputStream outstream) throws IOException {
    entity.writeRequest(outstream);
  }

  @Override
  public boolean isStreaming() {
    return false;
  }

}
