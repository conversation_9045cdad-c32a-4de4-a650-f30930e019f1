package com.byzaneo.generix.edocument.repository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.core.convert.MongoConverter;
import org.springframework.stereotype.Repository;

import com.byzaneo.commons.dao.mongo.MongoOperations;
import com.byzaneo.generix.edocument.bean.DocumentLink;

/**
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Dec 11, 2015
 * @since 1.0
 */
@Repository(DocumentLinkOperations.OPERATIONS_NAME)
public class DocumentLinkOperationsImpl extends MongoOperations<DocumentLink> implements DocumentLinkOperations {

  @Autowired
  public DocumentLinkOperationsImpl(
      @Qualifier("xtdMongoDbFactory") MongoDatabaseFactory mongoDbFactory,
      @Qualifier("xtdMongoMappingConverter") MongoConverter mongoConverter) {
    super(mongoDbFactory, mongoConverter);
  }

}
