package com.byzaneo.generix.edocument.util;

import static com.byzaneo.commons.util.I18NHelper.toLabelSet;
import static java.util.stream.Collectors.toCollection;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.supercsv.prefs.CsvPreference.EXCEL_NORTH_EUROPE_PREFERENCE;

import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Stream;

import com.byzaneo.commons.bean.*;
import com.byzaneo.generix.edocument.bean.*;
import com.byzaneo.generix.edocument.bean.ArchivedIndexableType.*;
import com.byzaneo.generix.edocument.bean.RecapListItem.DematPartnerQuality;
import com.byzaneo.generix.ui.ApplicationHandler.EDateFormat;
import com.byzaneo.generix.xcbl.bean.*;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.bean.Report;
import com.byzaneo.xtrade.dao.mongo.MongoIndexableDocument;
import com.byzaneo.xtrade.xcbl.bean.*;

import org.apache.commons.io.ByteOrderMark;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.*;
import org.supercsv.cellprocessor.Optional;
import org.supercsv.cellprocessor.*;
import org.supercsv.cellprocessor.ift.CellProcessor;
import org.supercsv.io.*;
import org.supercsv.io.dozer.*;
import org.supercsv.prefs.CsvPreference;
import org.supercsv.prefs.CsvPreference.Builder;

public class CsvExportHelper {

  private static final String DATE_FORMAT_WITH_TIME = "dd/MM/yyyy HH:mm:ss";

  private static final String DATE_FORMAT_WITHOUT_TIME = "dd/MM/yyyy";

  private static final Logger log = LoggerFactory.getLogger(CsvExportHelper.class);

  public static final List<String> dateFieldsToFormatWithTime = Arrays.asList("creationDate",
      "documentPreparationDate");

  // XXX : add fields name for the recap list
  private static final List<String> fieldsToDisplayWithoutScientificNotation = Stream
      .of("buyerPartyID",
          "sellerPartyID")
      .collect(toCollection(ArrayList::new));

  public final static void exportAsCSV(final List<Object> items, BeanDescriptor descriptor, final OutputStream output, Locale usedLocale,
      String instanceCode)
      throws IOException {

    exportAsCSV(items, descriptor, output, usedLocale, null, true, null, instanceCode);
  }

  public final static void exportAsCSV(final List<Object> items, BeanDescriptor descriptor, final OutputStream output, Locale usedLocale,
      Class<?> clazz, String instanceCode)
      throws IOException {

    exportAsCSV(items, descriptor, output, usedLocale, null, true, clazz, instanceCode);
  }

  public final static void exportAsCSV(final List<Object> items, BeanDescriptor descriptor, final OutputStream output, Locale usedLocale,
      boolean withHeader, String instanceCode)
      throws IOException {

    exportAsCSV(items, descriptor, output, usedLocale, null, withHeader, null, instanceCode);
  }

  public final static void exportAsCSV(final List<Object> items, BeanDescriptor descriptor, final OutputStream output,
      Locale usedLocale, String userDateFormat, boolean withHeader, Class<?> clazz, String instanceCode)
      throws IOException {

    Class<?> writerClass = clazz == null ? CsvBeanWriter.class : CsvDozerBeanWriter.class;

    try (Writer outputStreamWriter = new OutputStreamWriter(output, StandardCharsets.UTF_8)) {
      outputStreamWriter.write(ByteOrderMark.UTF_BOM);
      Builder builder = new CsvPreference.Builder('\u0000', CsvPreference.EXCEL_NORTH_EUROPE_PREFERENCE.getDelimiterChar(),
          CsvPreference.EXCEL_NORTH_EUROPE_PREFERENCE.getEndOfLineSymbols());

      try (final ICsvWriter writer = (ICsvWriter) writerClass.getConstructor(Writer.class, CsvPreference.class)
          .newInstance(outputStreamWriter, builder.build())) {

        List<PropertyDescriptor> properties = descriptor.getProperties();

        if (withHeader) {
          writeCSVHeader(properties, writer, usedLocale);
        }

        if (!items.isEmpty()) {

          final CellProcessor[] processors = buildCsvProcessor(descriptor, usedLocale, userDateFormat, descriptor.getPropertyNames(),
              instanceCode);
          String[] nameMapping = properties.stream()
              .map(PropertyDescriptor::getName)
              .toArray(String[]::new);

          if (clazz != null) {
            ((CsvDozerBeanWriter) writer).configureBeanMapping(clazz, nameMapping);
            for (Object item : items) {
              ((CsvDozerBeanWriter) writer).write(item, processors);
            }
          }
          else {
            for (Object item : items) {
              ((CsvBeanWriter) writer).write(item, nameMapping, processors);
            }
          }
        }
      }
      catch (InstantiationException | IllegalAccessException | IllegalArgumentException | InvocationTargetException
          | NoSuchMethodException | SecurityException e) {
        log.error("CsvExportHelper.exportAsCSV() : " + getRootCauseMessage(e));
      }
    }
  }

  private static void writeCSVHeader(List<PropertyDescriptor> properties, ICsvWriter writer, Locale usedLocale) throws IOException {
    String[] header = properties.stream()
        .map(p -> labelOf(p, usedLocale))
        .toArray(String[]::new);
    writer.writeHeader(header);
  }

  private static String labelOf(PropertyDescriptor p, Locale locale) {
    return toLabelSet(p.getLabel(), locale).getLabelValue(locale);
  }

  public final static <T extends Archive> void exportDocumentArchivedAsCsv(final List<T> items, BeanDescriptor descriptor,
      final OutputStream output, Locale usedLocale, boolean isCompanyUser, String instanceCode) throws IOException {
    try (Writer outputStreamWriter = new OutputStreamWriter(output, StandardCharsets.UTF_8)) {
      outputStreamWriter.write(ByteOrderMark.UTF_BOM);
      try (final ICsvDozerBeanWriter writer = new CsvDozerBeanWriter(outputStreamWriter,
          EXCEL_NORTH_EUROPE_PREFERENCE)) {

        // properties
        List<String> properties = descriptor.getPropertyNames();

        // processors
        final CellProcessor[] processors = buildCsvProcessor(descriptor, usedLocale, null, properties, instanceCode);

        // headers
        writeCSVHeader(descriptor.getProperties(), writer, usedLocale);
        Indexable o = getIndexableDocumentFromArchive(items);

        if (o instanceof InvoiceIndex) {
          ArchivedIndexableInvoice archivedIndexableInvoice = new ArchivedIndexableInvoice(o);
          archivedIndexableInvoice.configureBeanMappingAndWrite(writer, properties, items, processors);
        }
        else if (o instanceof Report) {
          ArchivedIndexableReport archivedIndexableReport = new ArchivedIndexableReport(o);
          archivedIndexableReport.configureBeanMappingAndWrite(writer, properties, items, processors);
        }
        else if (o instanceof History) {
          ArchivedIndexableHistory archivedIndexableHistory = new ArchivedIndexableHistory(o);
          archivedIndexableHistory.configureBeanMappingAndWrite(writer, properties, items, processors);
        }
        else if (o instanceof PlanningSchedule) {
          ArchivedIndexablePlanningSchedule archivedIndexablePlanningSchedule = new ArchivedIndexablePlanningSchedule(o);
          archivedIndexablePlanningSchedule.configureBeanMappingAndWrite(writer, properties, items, processors);
        }
        else if (o instanceof AdvanceShipmentNotice) {
          ArchivedIndexableAdvanceShipmentNotice archivedIndexableAdvanceShipmentNotice = new ArchivedIndexableAdvanceShipmentNotice(o);
          archivedIndexableAdvanceShipmentNotice.configureBeanMappingAndWrite(writer, properties, items, processors);
        }
        else if (o instanceof ApplicationResponse) {
          ArchivedIndexableApplicationResponse archivedIndexableApplicationResponse = new ArchivedIndexableApplicationResponse(o);
          archivedIndexableApplicationResponse.configureBeanMappingAndWrite(writer, properties, items, processors);
        }
        else if (o instanceof Contract) {
          ArchivedIndexableContract archivedIndexableContract = new ArchivedIndexableContract(o);
          archivedIndexableContract.configureBeanMappingAndWrite(writer, properties, items, processors);
        }
        else if (o instanceof InventoryReport) {
          ArchivedIndexableInventoryReport archivedIndexableInventoryReport = new ArchivedIndexableInventoryReport(o);
          archivedIndexableInventoryReport.configureBeanMappingAndWrite(writer, properties, items, processors);
        }
        else if (o instanceof OrderChange) {
          ArchivedIndexableOrderChange archivedIndexableOrderChange = new ArchivedIndexableOrderChange(o);
          archivedIndexableOrderChange.configureBeanMappingAndWrite(writer, properties, items, processors);
        }
        else if (o instanceof OrderIndex) {
          ArchivedIndexableOrder archivedIndexableOrder = new ArchivedIndexableOrder(o);
          archivedIndexableOrder.configureBeanMappingAndWrite(writer, properties, items, processors);
        }
        else if (o instanceof OrderResponse) {
          ArchivedIndexableOrderResponse archivedIndexableOrderResponse = new ArchivedIndexableOrderResponse(o);
          archivedIndexableOrderResponse.configureBeanMappingAndWrite(writer, properties, items, processors);
        }
        else if (o instanceof Penalty) {
          ArchivedIndexablePenalty archivedIndexablePenalty = new ArchivedIndexablePenalty(o);
          archivedIndexablePenalty.configureBeanMappingAndWrite(writer, properties, items, processors);
        }
        else if (o instanceof MongoIndexableDocument) {
          ArchivedIndexableCarrefourFaxes archivedIndexableCarrefourFaxes = new ArchivedIndexableCarrefourFaxes(
              ((CarrefourArchivedFaxes) items.get(0)).getIndexable());
          archivedIndexableCarrefourFaxes.configureBeanMappingAndWrite(writer, properties, items, processors);
        }
      }
    }
  }

  private static <T extends Archive> Indexable getIndexableDocumentFromArchive(final List<T> items) {
    Indexable o;
    if (items.get(0) instanceof ArchivedIndexable) {
      o = ((ArchivedIndexable) items.get(0)).getIndexable();
    }
    else {
      o = ((CarrefourArchivedFaxes) items.get(0)).getIndexable();
    }
    return o;
  }

  public static String[] writeCsvHeader(Locale usedLocale, ICsvBeanWriter writer, List<String> properties,
      boolean withHeader) throws IOException {
    String[] headers = properties.toArray(new String[properties.size()]);

    if (withHeader) {
      ResourceBundle headersResourceBundle = ResourceBundle.getBundle("labels.edctsk-labels", usedLocale);
      String[] headersLocalise = properties.stream()
          .map(property ->
      {
            try {
              return headersResourceBundle.getString(property);
            }
            catch (MissingResourceException e) {
              return property;
            }
          })
          .toArray(String[]::new);

      writer.writeHeader(headersLocalise);
    }
    return headers;
  }

  public static String[] writeCsvDozerHeader(Locale usedLocale, ICsvDozerBeanWriter writer, List<String> properties,
      boolean withHeader) throws IOException {
    String[] headers = properties.toArray(new String[properties.size()]);

    if (withHeader) {
      ResourceBundle headersResourceBundleArchiveInvoice = ResourceBundle.getBundle("labels.archtsk-labels", usedLocale);
      ResourceBundle headersResourceBundle = ResourceBundle.getBundle("labels.archdocstsk-labels", usedLocale);
      String[] headersLocalise = properties.stream()
          .map(property ->
      {
            String rslt = getValueFromBundle(headersResourceBundleArchiveInvoice, property);
            if (StringUtils.isEmpty(rslt)) {
              rslt = getValueFromBundle(headersResourceBundle, property);
            }
            return StringUtils.isEmpty(rslt) ? property : rslt;
          })
          .toArray(String[]::new);

      writer.writeHeader(headersLocalise);
    }
    return headers;
  }

  public static String getValueFromBundle(ResourceBundle bundle, String key) {
    if (StringUtils.isEmpty(key)) {
      return null;
    }
    try {
      return bundle.getString(key.replace(".", "_"));
    }
    catch (MissingResourceException e) {
      return null;
    }
  }

  public static void writeCsvDozerHeader(Locale usedLocale, ICsvDozerBeanWriter writer, Map<String, String> propertiesAndLabels,
      boolean withHeader) throws IOException {

    List<String> properties = new ArrayList<String>(propertiesAndLabels.values());

    // Necessary to keep the column traduction order
    List<String> labelsInOrder = new ArrayList<>();
    properties.stream()
        .forEach(name -> labelsInOrder.add(propertiesAndLabels.get(name)));

    if (withHeader) {
      writer.writeHeader(labelsInOrder.toArray(new String[properties.size()]));
    }
    else {
      writer.writeHeader(properties.toArray(new String[properties.size()]));
    }
  }

  public static CellProcessor[] buildCsvProcessor(BeanDescriptor descriptor, Locale usedLocale, String userDateFormat,
      List<String> properties, String instanceCode) {
    final CellProcessor[] processors = new CellProcessor[properties.size()];
    for (int i = 0; i < processors.length; i++) {
      PropertyDescriptor pdesc = descriptor.get(properties.get(i));
      Class<?> ptype = pdesc.getType();
      if (ptype.isAssignableFrom(Date.class) && StringUtils.isNotEmpty(userDateFormat)) {
        EDateFormat format = EDateFormat.findPatternByValue(userDateFormat);
        processors[i] = new Optional(new FmtDate(format.getPattern()));
      }
      else if (ptype.isAssignableFrom(Date.class) && !StringUtils.isNotEmpty(userDateFormat)) {
        formatDateFieldForCsv(processors, i, pdesc);
      }
      else if (ptype.isAssignableFrom(Number.class)) {
        processors[i] = new Optional(new FmtNumber("# ##0,00"));
      }
      else if (ptype.isAssignableFrom(EDocumentErrors.class)) {
        processors[i] = new Optional(new EDocumentErrorsProcessor(usedLocale));
      }
      else if (StringUtils.endsWith(pdesc.getName(), "Country") || "gnxCountryConverter".equals(pdesc.getConverter())) {
        processors[i] = new Optional(new CountryProcessor(usedLocale));
      }
      else if (ptype == DocumentStatusEntityInterface.class) {
        processors[i] = new Optional(new DocumentStatusProcessor(usedLocale, instanceCode));
      }
      else if (ptype == DocumentStage.class) {
        processors[i] = new Optional(new DocumentStageProcessor(usedLocale));
      }
      else if (ptype == ArchiveStatus.class) {
        processors[i] = new Optional(new ArchiveStatusProcessor(usedLocale));
      }
      else if (ptype == DematPartnerQuality.class) {
        processors[i] = new Optional(new DocumentPartnerQualityProcessor(usedLocale));
      }
      else if ("messageType".equals(pdesc.getName())) {
        processors[i] = new Optional(new InvoiceTypeCodeTypeProcessor(usedLocale));
      }
      else if ("gnxTypeConverter".equals(pdesc.getConverter())) {
        processors[i] = new Optional(new RecapListTypeNormProcessor());
      }
      else if ("documentTypeConverter".equals(pdesc.getConverter())) {
        processors[i] = new Optional(new EDocumentTypeProcessor());
      }
      else if (fieldsToDisplayWithoutScientificNotation.contains(pdesc.getName())) {
        processors[i] = new Optional(new NumberWithoutScientificNotation());
      }
      else {
        processors[i] = new Optional();
      }
    }
    return processors;
  }

  public static void formatDateFieldForCsv(final CellProcessor[] processors, int i, PropertyDescriptor pdesc) {
    if (dateFieldsToFormatWithTime.contains(pdesc.getName())) {
      processors[i] = new Optional(new FmtDate(DATE_FORMAT_WITH_TIME));
    }
    else {
      processors[i] = new Optional(new FmtDate(DATE_FORMAT_WITHOUT_TIME));
    }
  }

}
