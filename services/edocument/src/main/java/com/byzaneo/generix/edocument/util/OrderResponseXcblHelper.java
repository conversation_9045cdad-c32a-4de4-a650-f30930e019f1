package com.byzaneo.generix.edocument.util;

import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.DATA_DIR;
import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.OUTPUT_DIR;
import static com.byzaneo.commons.util.SpringContextHelper.getConfigurationService;
import static com.byzaneo.generix.edocument.util.XcblHelper.createOrUpdateXcblFile;
import static com.byzaneo.generix.edocument.util.XcblHelper.getPrimaryContact;
import static com.byzaneo.xtrade.api.DocumentType.ORDRSP;
import static com.byzaneo.xtrade.xcbl.util.OrderResponseHelper.getOrderResponseIsseDate;
import static com.byzaneo.xtrade.xcbl.util.OrderResponseHelper.getOrderResponseNumber;
import static com.byzaneo.xtrade.xcbl.util.OrderResponseHelper.getOrderResponseType;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.ShipmentStatusEventCodeType.PARTIALLY_SHIPPED;
import static org.apache.commons.lang3.time.DateUtils.addDays;

import java.io.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.*;

import javax.xml.bind.JAXBException;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import com.byzaneo.generix.ui.SessionHandler;
import com.byzaneo.generix.xcbl.bean.PlanningSchedule;
import com.byzaneo.xtrade.api.DocumentStatus;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.xcbl.bean.OrderResponse;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement.*;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.*;

public class OrderResponseXcblHelper {

  private static final int EMPTY_CODE = -1;

  private static final String STANDARD_PRODUCT_QUALIFIER = "EAN2-5-5-1";

  private static final String NEW_ORDER = "NewOrder";

  public static final String ORDRSPDIR = "/orderresponse/";
  
  public static final String DOC_ORDRSP_DIR = "/documents/orderresponse/";

  protected OrderResponseXcblHelper() {
    throw new AssertionError("Singleton");
  }

  /**
   * @param planningSchedule
   * @return
   * @throws Exception
   */
  public static List<OrderResponse> initOrderResponse(PlanningSchedule planningSchedule, String status) throws Exception {
    if (planningSchedule == null) {
      return null;
    }

    List<OrderResponse> orderResponseList = new ArrayList<>();
    //// PlanningSchedule/PlanningScheduleDetail/LocationGroupedPlanningDetail/ListOfLocationPlanningItemDetail/LocationPlanningItemDetail/
    PlanningScheduleHelper.getPlanningItemStream(planningSchedule, status)
        .forEach(locationItem -> {

          // Header
          OrderResponseHeaderType orderResponseHeader = new OrderResponseHeaderType();

          // Header : OrderReference
          // [Numéro de l'Order] = BasePlanningDetail/ItemScheduleReference/PurchaseOrderReference/BuyerOrderNumber
          ReferenceType orderResponseNumber = new ReferenceType();
          orderResponseNumber.setRefNum(Optional.of(locationItem)
              .map(LocationPlanningItemDetailType::getBasePlanningDetail)
              .map(ScheduleBaseItemDetailType::getItemScheduleReference)
              .map(ScheduleReferencesType::getPurchaseOrderReference)
              .map(PurchaseOrderReferenceType::getBuyerOrderNumber)
              .orElse(StringUtils.EMPTY));
          orderResponseHeader.setOrderReference(orderResponseNumber);
          // Header : OrderResponseNumber
          // RSP[Numéro de l'Order] = BasePlanningDetail/ItemScheduleReference/PurchaseOrderReference/BuyerOrderNumber
          OrderResponseNumberType orderResponseNumberType = new OrderResponseNumberType();
          orderResponseNumberType.setBuyerOrderResponseNumber("RSP" + orderResponseNumber.getRefNum());
          orderResponseNumberType.setSellerOrderResponseNumber("RSP" + orderResponseNumber.getRefNum());
          orderResponseHeader.setOrderResponseNumber(orderResponseNumberType);

          // Header : OrderResponseIssueDate
          orderResponseHeader.setOrderResponseIssueDate(new Date());

          // Header : OrderResponseDocTypeCoded
          orderResponseHeader.setOrderResponseDocTypeCoded(OrderResponseDocTypeCodeType.ORDER_RESPONSE);

          // Header : ResponseType
          ResponseTypeType responseTypeType = new ResponseTypeType();
          responseTypeType.setResponseTypeCoded(ResponseTypeCodeType.ACCEPTED);
          orderResponseHeader.setResponseType(responseTypeType);

          SchedulePartyType scheduleParty = Optional.of(planningSchedule)
              .map(PlanningSchedule::getPlanningScheduleHeader)
              .map(PlanningScheduleHeaderType::getScheduleParty)
              .orElse(new SchedulePartyType());

          // Header : SellerParty /BuyerParty
          // Informations Fournisseurs = /PlanningSchedule/PlanningScheduleHeader/ScheduleParty/SellerParty/
          orderResponseHeader.setSellerParty(XcblHelper.cloner.deepClone(scheduleParty.getSellerParty()));
          // Informations Acheteur = /PlanningSchedule/PlanningScheduleHeader/ScheduleParty/BuyerParty/
          orderResponseHeader.setBuyerParty(XcblHelper.cloner.deepClone(scheduleParty.getBuyerParty()));

          // Header : OriginalOrderHeaderWithChanges
          orderResponseHeader.setOriginalOrderHeaderWithChanges(createOriginalOrderHeader(scheduleParty, orderResponseNumber.getRefNum()));

          // Detail
          OrderResponseDetailType orderResponseDetailType = new OrderResponseDetailType();

          // Detail : ListOfOrderResponseItemDetailType
          ListOfOrderResponseItemDetailType listOfOrderResponseItemDetailType = new ListOfOrderResponseItemDetailType();
          listOfOrderResponseItemDetailType.getOrderResponseItemDetail()
              .add(createResponseItemDetail(Optional.of(locationItem)));
          orderResponseDetailType.setListOfOrderResponseItemDetail(listOfOrderResponseItemDetailType);

          OrderResponse orderResponse = new OrderResponse();
          orderResponse.setOrderResponseHeader(orderResponseHeader);
          orderResponse.setOrderResponseDetail(orderResponseDetailType);
          orderResponse.setOrderResponseSummary(createOrderResponseSummary());

          orderResponseList.add(orderResponse);

        });
    return orderResponseList;
  }

  public static OrderResponse initOrderResponse(OrderType order, SessionHandler sessionHandler) throws Exception {
    if (order == null) {
      return null;
    }

    // Header
    OrderResponseHeaderType orderResponseHeader = new OrderResponseHeaderType();

    // Header : OrderReference
    ReferenceType orderResponseNumber = new ReferenceType();
    orderResponseNumber.setRefNum(order.getOrderHeader()
        .getOrderNumber()
        .getBuyerOrderNumber());
    orderResponseNumber.setRefDate(order.getOrderHeader()
        .getOrderIssueDate());
    orderResponseHeader.setOrderReference(orderResponseNumber);

    // Header : OrderResponseNumber
    OrderResponseNumberType orderResponseNumberType = new OrderResponseNumberType();
    orderResponseNumberType.setBuyerOrderResponseNumber("RSP" + orderResponseNumber.getRefNum());
    orderResponseNumberType.setSellerOrderResponseNumber("RSP" + orderResponseNumber.getRefNum());
    orderResponseHeader.setOrderResponseNumber(orderResponseNumberType);

    // Header : OrderResponseIssueDate
    // TODO
    // orderResponseHeader.setOrderResponseIssueDate(DatatypeFactory.newInstance().newXMLGregorianCalendar(new
    // GregorianCalendar()));
    orderResponseHeader.setOrderResponseIssueDate(new Date());

    // Header : OrderResponseDocTypeCoded
    orderResponseHeader.setOrderResponseDocTypeCoded(OrderResponseDocTypeCodeType.ORDER_RESPONSE);

    // Header : ResponseType
    ResponseTypeType responseTypeType = new ResponseTypeType();
    responseTypeType.setResponseTypeCoded(ResponseTypeCodeType.ACCEPTED);
    orderResponseHeader.setResponseType(responseTypeType);

    // Header : SellerParty
    orderResponseHeader.setSellerParty(XcblHelper.cloner.deepClone(order.getOrderHeader()
        .getOrderParty()
        .getSellerParty()));
    if (sessionHandler != null) {
      orderResponseHeader.getSellerParty()
          .setPrimaryContact(getPrimaryContact(sessionHandler));
    }

    // Header : BuyerParty
    orderResponseHeader.setBuyerParty(XcblHelper.cloner.deepClone(order.getOrderHeader()
        .getOrderParty()
        .getBuyerParty()));

    // Header : OriginalOrderHeaderWithChanges
    orderResponseHeader.setOriginalOrderHeaderWithChanges(XcblHelper.cloner.deepClone(order.getOrderHeader()));

    if(orderResponseHeader.getOriginalOrderHeaderWithChanges().getOrderDates()==null)
      orderResponseHeader.getOriginalOrderHeaderWithChanges().setOrderDates(new OrderDatesType());

    // Header : Note
    orderResponseHeader.setOrderResponseHeaderNote(order.getOrderHeader()
        .getOrderHeaderNote());

    OrderResponse orderResponse = new OrderResponse();

    orderResponse.setOrderResponseHeader(orderResponseHeader);

    // Detail
    OrderResponseDetailType orderResponseDetailType = new OrderResponseDetailType();

    // Detail : ListOfOrderResponseItemDetailType
    ListOfOrderResponseItemDetailType listOfOrderResponseItemDetailType = new ListOfOrderResponseItemDetailType();

    for (ItemDetailType itemDetailType : order.getOrderDetail()
        .getListOfItemDetail()
        .getItemDetail()) {
      createListOfOrderResponseItemDetailType(order, listOfOrderResponseItemDetailType, itemDetailType);
    }

    orderResponseDetailType.setListOfOrderResponseItemDetail(listOfOrderResponseItemDetailType);

    createListOfOrderResponsePackageDetail(order, orderResponseDetailType);

    orderResponse.setOrderResponseDetail(orderResponseDetailType);

    // Summary
    OrderResponseSummaryType orderResponseSummaryType = createOrderResponseSummary();

    orderResponseSummaryType.setOriginalOrderSummary(XcblHelper.cloner.deepClone(order.getOrderSummary()));

    orderResponseSummaryType.setRevisedOrderSummary(XcblHelper.cloner.deepClone(order.getOrderSummary()));

    orderResponse.setOrderResponseSummary(orderResponseSummaryType);

    return orderResponse;
  }

  public static void checkCountryOnBuyerAndSeller(OrderResponseHeaderType orderResponseHeader) {
    XcblHelper.checkCountry(orderResponseHeader.getBuyerParty());
    XcblHelper.checkCountry(orderResponseHeader.getSellerParty());
  }

  public static void createListOfOrderResponsePackageDetail(OrderType order, OrderResponseDetailType orderResponseDetailType) {
    // Detail : ListOfOrderResponsePackageDetailType
    if (order.getOrderDetail()
        .getListOfPackageDetail() != null &&
        order.getOrderDetail()
            .getListOfPackageDetail()
            .getPackageDetail() != null) {
      ListOfOrderResponsePackageDetailType listOfOrderResponsePackageDetailType = new ListOfOrderResponsePackageDetailType();

      for (PackageDetailType packageDetailType : order.getOrderDetail()
          .getListOfPackageDetail()
          .getPackageDetail()) {
        // Detail : OrderResponsePackageDetailType
        OrderResponsePackageDetailType orderResponsePackageDetailType = new OrderResponsePackageDetailType();
        orderResponsePackageDetailType
            .setOriginalPackageDetailWithChanges(XcblHelper.cloner.deepClone(packageDetailType));

        listOfOrderResponsePackageDetailType.getOrderResponsePackageDetail()
            .add(orderResponsePackageDetailType);
      }

      orderResponseDetailType.setListOfOrderResponsePackageDetail(listOfOrderResponsePackageDetailType);
    }
  }

  public static void createListOfOrderResponseItemDetailType(OrderType order,
      ListOfOrderResponseItemDetailType listOfOrderResponseItemDetailType,
      ItemDetailType itemDetailType) {
    // Detail : OriginalItemDetailWithChangesType
    OriginalItemDetailWithChangesType originalItemDetailWithChangesType = new OriginalItemDetailWithChangesType();
    originalItemDetailWithChangesType
        .setLineItemAttachments(XcblHelper.cloner.deepClone(itemDetailType.getLineItemAttachments()));
    originalItemDetailWithChangesType.setLineItemNote(itemDetailType.getLineItemNote());
    originalItemDetailWithChangesType
        .setListOfNameValueSet(XcblHelper.cloner.deepClone(itemDetailType.getListOfNameValueSet()));
    originalItemDetailWithChangesType
        .setListOfStructuredNote(XcblHelper.cloner.deepClone(itemDetailType.getListOfStructuredNote()));
    originalItemDetailWithChangesType.setPricingDetail(XcblHelper.cloner.deepClone(itemDetailType.getPricingDetail()));
    originalItemDetailWithChangesType
        .setRoundTripInformation(XcblHelper.cloner.deepClone(itemDetailType.getRoundTripInformation()));
    originalItemDetailWithChangesType
        .setSpecialHandling(XcblHelper.cloner.deepClone(itemDetailType.getSpecialHandling()));

    // Detail : ResponseBaseItemDetailType
    ResponseBaseItemDetailType responseBaseItemDetailType = new ResponseBaseItemDetailType();
    responseBaseItemDetailType.setBaseItemReferences(
        XcblHelper.cloner.deepClone(itemDetailType.getBaseItemDetail()
            .getBaseItemReferences()));
    responseBaseItemDetailType.setConditionsOfSale(
        XcblHelper.cloner.deepClone(itemDetailType.getBaseItemDetail()
            .getConditionsOfSale()));
    responseBaseItemDetailType.setCountryOfDestination(
        XcblHelper.cloner.deepClone(itemDetailType.getBaseItemDetail()
            .getCountryOfDestination()));
    responseBaseItemDetailType
        .setCountryOfOrigin(XcblHelper.cloner.deepClone(itemDetailType.getBaseItemDetail()
            .getCountryOfOrigin()));
    responseBaseItemDetailType
        .setFinalRecipient(XcblHelper.cloner.deepClone(itemDetailType.getBaseItemDetail()
            .getFinalRecipient()));
    responseBaseItemDetailType.setHazardousMaterials(
        XcblHelper.cloner.deepClone(itemDetailType.getBaseItemDetail()
            .getHazardousMaterials()));
    responseBaseItemDetailType
        .setItemIdentifiers(XcblHelper.cloner.deepClone(itemDetailType.getBaseItemDetail()
            .getItemIdentifiers()));
    responseBaseItemDetailType
        .setLineItemNum(XcblHelper.cloner.deepClone(itemDetailType.getBaseItemDetail()
            .getLineItemNum()));
    responseBaseItemDetailType
        .setLineItemType(XcblHelper.cloner.deepClone(itemDetailType.getBaseItemDetail()
            .getLineItemType()));
    responseBaseItemDetailType
        .setListOfDimension(XcblHelper.cloner.deepClone(itemDetailType.getBaseItemDetail()
            .getListOfDimension()));
    responseBaseItemDetailType.setListOfPartyCoded(
        XcblHelper.cloner.deepClone(itemDetailType.getBaseItemDetail()
            .getListOfPartyCoded()));
    responseBaseItemDetailType.setListOfQuantityCoded(
        XcblHelper.cloner.deepClone(itemDetailType.getBaseItemDetail()
            .getListOfQuantityCoded()));
    responseBaseItemDetailType.setParentItemNumber(
        XcblHelper.cloner.deepClone(itemDetailType.getBaseItemDetail()
            .getParentItemNumber()));
    responseBaseItemDetailType
        .setOrderedQuantity(XcblHelper.cloner.deepClone(itemDetailType.getBaseItemDetail()
            .getTotalQuantity()));
    responseBaseItemDetailType
        .setResponseQuantity(XcblHelper.cloner.deepClone(itemDetailType.getBaseItemDetail()
            .getTotalQuantity()));

    originalItemDetailWithChangesType.setBaseItemDetail(responseBaseItemDetailType);
    originalItemDetailWithChangesType.setDeliveryDetail(new ResponseDeliveryDetailType());
    originalItemDetailWithChangesType.getDeliveryDetail()
        .setListOfSplitQuantity(new ListOfSplitQuantityType());

    // Delivery detail
    Date headerDeliveryDate = order.getOrderHeader()
        .getOrderDates() == null ? null
            : order.getOrderHeader()
                .getOrderDates()
                .getRequestedDeliverByDate();
    if (itemDetailType.getDeliveryDetail() != null &&
        !itemDetailType.getDeliveryDetail()
            .getListOfScheduleLine()
            .getScheduleLine()
            .isEmpty()) {
      for (ScheduleLineType scheduleLine : itemDetailType.getDeliveryDetail()
          .getListOfScheduleLine()
          .getScheduleLine()) {
        final SplitQuantityType newSplitQuantity = new SplitQuantityType();
        newSplitQuantity.setSplitQuantity(new QuantityType());
        newSplitQuantity.setShipmentStatusEventCoded(PARTIALLY_SHIPPED);
        newSplitQuantity.setSplitQuantity(scheduleLine.getQuantity());

        Date deliveryDate = scheduleLine.getRequestedDeliveryDateOrListOfOtherDeliveryDate()
            .isEmpty() ? (headerDeliveryDate == null ? addDays(new Date(), 1) : headerDeliveryDate)
                : scheduleLine.getRequestedDeliveryDateOrListOfOtherDeliveryDate()
                    .get(0) instanceof Date
                        ? (Date) scheduleLine.getRequestedDeliveryDateOrListOfOtherDeliveryDate()
                            .get(0)
                        : headerDeliveryDate;

        newSplitQuantity.setEstimatedDeliveryDate(deliveryDate);
        originalItemDetailWithChangesType.getDeliveryDetail()
            .getListOfSplitQuantity()
            .getSplitQuantity()
            .add(newSplitQuantity);
      }
    }

    // Detail : OrderResponseItemDetailType
    OrderResponseItemDetailType orderResponseItemDetailType = new OrderResponseItemDetailType();
    orderResponseItemDetailType.setOriginalItemDetailWithChanges(originalItemDetailWithChangesType);
    orderResponseItemDetailType.setItemDetailResponseCoded(DetailResponseCodeType.ITEM_ACCEPTED);
    listOfOrderResponseItemDetailType.getOrderResponseItemDetail()
        .add(orderResponseItemDetailType);
  }

  public static Document createOrderResponse(Document document, OrderResponse orderResponse, String codeInstance) throws Exception {
    return createOrderResponse(document, orderResponse, codeInstance, null, null);
  }

  public static Document createOrderResponse(Document document, OrderResponse orderResponse, String codeInstance, String outputDir,
      File serializedXcblFile)
      throws Exception {
    String parentDirectory = StringUtils.isEmpty(outputDir) ? getConfigurationService().getString(OUTPUT_DIR)
        : getConfigurationService().getString(DATA_DIR);
    return createOrderResponse(document, orderResponse, codeInstance, parentDirectory, outputDir,
        serializedXcblFile, StringUtils.isEmpty(outputDir) ? ORDRSPDIR : null, null, false, false);
  }

  public static Document createOrderResponse(Document document, OrderResponse orderResponse, String codeInstance, String parentDirectory, String outputDir,
      File serializedXcblFile, String ordrspDir, String directoryToCopy, boolean makeCopyOfOutputFile, boolean useDocUUID) throws IOException, JAXBException {
    Document orderResponseDocument = new Document();
    if (document != null && orderResponse != null) {
      orderResponseDocument.setOwners(document.getOwners());
      orderResponseDocument.setFrom(document.getTo());
      orderResponseDocument.setTo(document.getFrom());
      orderResponseDocument.setOrderingParty(document.getOrderingParty());
      orderResponseDocument.setReference(
          orderResponse.getOrderResponseHeader()
              .getOrderResponseNumber()
              .getSellerOrderResponseNumber());
      orderResponseDocument.setType(ORDRSP);
      orderResponseDocument.setNumber(getOrderResponseNumber(orderResponse));
      orderResponseDocument.setIssueDate(getOrderResponseIsseDate(orderResponse));
      orderResponseDocument.setSubtype(getOrderResponseType(orderResponse));

      ResponseTypeCodeType responseTypeCoded = orderResponse.getOrderResponseHeader()
          .getResponseType()
          .getResponseTypeCoded();
      if (null != responseTypeCoded) {
        switch (responseTypeCoded) {
        case ACCEPTED:
          orderResponseDocument.setStatusWithEnumValue(DocumentStatus.ACCEPTED);
          document.setStatusWithEnumValue(DocumentStatus.ACCEPTED);
          break;
        case ACCEPTED_WITH_AMENDMENT:
          // ResponseTypeCodeType!=DocumentStatus
          // We treat ResponseTypeCodeType.ACCEPTED_WITH_AMENDMENT as DocumentStatus.ACCEPTED (AIO-7684)
          orderResponseDocument.setStatusWithEnumValue(DocumentStatus.ACCEPTED);
          document.setStatusWithEnumValue(DocumentStatus.ACCEPTED);
          break;
        case REJECTED:
          orderResponseDocument.setStatusWithEnumValue(DocumentStatus.REFUSED);
          document.setStatusWithEnumValue(DocumentStatus.REFUSED);
          PurposeType purpose = new PurposeType();
          purpose.setPurposeCoded(PurposeCodeType.CANCELLATION);
          orderResponse.getOrderResponseHeader()
              .setPurpose(purpose);
          break;
        default:
          break;
        }
      }
      createOrUpdateXcblFile(orderResponseDocument, orderResponse, ordrspDir,
          getOrderResponseNumber(orderResponse), null, null,
          parentDirectory, useDocUUID ? outputDir + orderResponseDocument.getUuid() : outputDir, serializedXcblFile, directoryToCopy, makeCopyOfOutputFile);
    }

    return orderResponseDocument;
  }

  /**
   * @param scheduleParty
   * @param refNum
   * @return
   */
  private static OrderHeaderType createOriginalOrderHeader(SchedulePartyType scheduleParty, String refNum) {
    OrderNumberType orderNumberType = new OrderNumberType();
    // Numéro de l'Order
    orderNumberType.setBuyerOrderNumber(refNum);

    OrderHeaderType orderHeaderType = new OrderHeaderType();
    orderHeaderType.setOrderNumber(orderNumberType);

    PurposeType purposeType = new PurposeType();
    purposeType.setPurposeCoded(PurposeCodeType.ORIGINAL);
    orderHeaderType.setPurpose(purposeType);

    OrderTypeType orderTypeType = new OrderTypeType();
    orderTypeType.setOrderTypeCoded(NEW_ORDER);
    orderHeaderType.setOrderType(orderTypeType);

    OrderPartyType orderPartyType = new OrderPartyType();
    // Informations Fournisseurs = /PlanningSchedule/PlanningScheduleHeader/ScheduleParty/SellerParty/
    orderPartyType.setSellerParty(XcblHelper.cloner.deepClone(scheduleParty.getSellerParty()));
    // Informations Acheteur = /PlanningSchedule/PlanningScheduleHeader/ScheduleParty/BuyerParty/
    orderPartyType.setBuyerParty(XcblHelper.cloner.deepClone(scheduleParty.getBuyerParty()));
    // Informations Livré à = /PlanningSchedule/PlanningScheduleHeader/ScheduleParty/ShipToParty/
    orderPartyType.setShipToParty(XcblHelper.cloner.deepClone(scheduleParty.getShipToParty()));
    // Informations Facturé à = /PlanningSchedule/PlanningScheduleHeader/ScheduleParty/BillToParty/
    orderPartyType.setBillToParty(XcblHelper.cloner.deepClone(scheduleParty.getBillToParty()));

    orderHeaderType.setOrderParty(orderPartyType);
    return orderHeaderType;
  }

  /**
   * @param locationItem
   * @return
   */
  private static OrderResponseItemDetailType createResponseItemDetail(Optional<LocationPlanningItemDetailType> locationItem) {

    OrderResponseItemDetailType orderResponseDetail = new OrderResponseItemDetailType();
    orderResponseDetail.setItemDetailResponseCoded(DetailResponseCodeType.ITEM_ACCEPTED);

    // Base Item Detail
    OriginalItemDetailWithChangesType originalItemDetailWithChangesType = new OriginalItemDetailWithChangesType();
    originalItemDetailWithChangesType.setBaseItemDetail(createBaseItemDeailType(locationItem));

    // SplitQuantities list and item packaging detail
    ResponseDeliveryDetailType deliveryDetailType = new ResponseDeliveryDetailType();
    deliveryDetailType.setListOfSplitQuantity(createSplitQuantitiesList(locationItem));
    deliveryDetailType.setItemPackagingReference(createItemPackaging(locationItem));

    originalItemDetailWithChangesType.setDeliveryDetail(deliveryDetailType);
    orderResponseDetail.setOriginalItemDetailWithChanges(originalItemDetailWithChangesType);
    return orderResponseDetail;
  }

  /**
   * @param locationItem
   * @return
   */
  private static ResponseBaseItemDetailType createBaseItemDeailType(Optional<LocationPlanningItemDetailType> locationItem) {

    ResponseBaseItemDetailType baseItemDetailType = new ResponseBaseItemDetailType();
    LineItemNumType lineItemNum = new LineItemNumType();
    int orderLineNumber = locationItem.map(LocationPlanningItemDetailType::getBasePlanningDetail)
        .map(ScheduleBaseItemDetailType::getItemScheduleReference)
        .map(ScheduleReferencesType::getPurchaseOrderReference)
        .map(PurchaseOrderReferenceType::getPurchaseOrderLineItemNumber)
        .filter(item -> NumberUtils.isCreatable(item))
        .map(itemNumber -> Integer.valueOf(itemNumber))
        .orElse(EMPTY_CODE);
    // Numéro ligne Order = BasePlanningDetail/ItemScheduleReference/PurchaseOrderReference/PurchaseOrderLineItemNumber
    lineItemNum.setBuyerLineItemNum(orderLineNumber);
    baseItemDetailType.setLineItemNum(lineItemNum);

    Optional<ItemIdentifiersType> itemIdentifiers = locationItem.map(LocationPlanningItemDetailType::getBasePlanningDetail)
        .map(ScheduleBaseItemDetailType::getItemIdentifiers);

    ItemIdentifiersType identifiersType = new ItemIdentifiersType();
    // PartNumbers
    identifiersType.setPartNumbers(createPartNumbers(itemIdentifiers));
    // Description Produit = BasePlanningDetail/ItemIdentifiers/core:ItemDescription
    identifiersType.setItemDescription(itemIdentifiers.map(ItemIdentifiersType::getItemDescription)
        .orElse(null));
    baseItemDetailType.setItemIdentifiers(identifiersType);

    // Total Quantity Scheduled
    baseItemDetailType.setResponseQuantity(computeTotalQuantity(locationItem, PlanningScheduleHelper.QUANTITY_SCHEDULED));
    // Total Quantity backordered (restant)
    baseItemDetailType.setOrderedQuantity(computeTotalQuantity(locationItem, PlanningScheduleHelper.BACKORDERED_QUANTITY));

    return baseItemDetailType;
  }

  /**
   * @param itemIdentifiers
   * @return
   */
  private static PartNumbersType createPartNumbers(Optional<ItemIdentifiersType> itemIdentifiers) {

    PartNumbersType schedulePlanningPartNumbers = itemIdentifiers.map(ItemIdentifiersType::getPartNumbers)
        .orElse(new PartNumbersType());

    PartNumbersType partNumbersType = new PartNumbersType();
    // Seller Part Number = BasePlanningDetail/ItemIdentifiers/core:PartNumbers/core:SellerPartNumber
    partNumbersType.setSellerPartNumber(XcblHelper.cloner.deepClone(schedulePlanningPartNumbers.getSellerPartNumber()));
    // Buyer Part Number = BasePlanningDetail/ItemIdentifiers/core:PartNumbers/BuyerPartNumber/
    partNumbersType.setBuyerPartNumber(XcblHelper.cloner.deepClone(schedulePlanningPartNumbers.getBuyerPartNumber()));

    // standard Part Number
    ProductIdentifierCodedType identifierCodedType = new ProductIdentifierCodedType();
    identifierCodedType.setProductIdentifierQualifierCoded(STANDARD_PRODUCT_QUALIFIER);
    identifierCodedType.setProductIdentifier(itemIdentifiers.map(ItemIdentifiersType::getPartNumbers)
        .map(PartNumbersType::getStandardPartNumber)
        .map(ProductIdentifierCodedType::getProductIdentifier)
        .orElse(null));
    partNumbersType.setStandardPartNumber(identifierCodedType);
    return partNumbersType;
  }

  /**
   * @param locationItem
   * @return
   */
  private static ItemPackagingReferenceType createItemPackaging(Optional<LocationPlanningItemDetailType> locationItem) {

    // Packaging Reference level 1 and 2 based on the ListOfDimensions
    ItemPackagingReferenceType packagingReferenceType = new ItemPackagingReferenceType();

    locationItem.map(LocationPlanningItemDetailType::getBasePlanningDetail)
        .map(ScheduleBaseItemDetailType::getListOfDimension)
        .map(ListOfDimensionType::getDimension)
        .orElse(new ArrayList<>())
        .stream()
        .forEach(dimension -> {
          PackageReferenceType referenceType = new PackageReferenceType();

          // QUANTITY
          QuantityType quantityType = new QuantityType();
          QuantityValueType value = new QuantityValueType();
          value.setValue(PlanningScheduleHelper.getDimensionQuantity(dimension));
          quantityType.setQuantityValue(value);
          referenceType.setQuantity(quantityType);

          // Package Code
          String code = dimension.getDimensionCodedOther();
          referenceType.setPackageIDReference(NumberUtils.isCreatable(code) ? Integer.valueOf(code) : EMPTY_CODE);

          // Package Type
          PackageReferenceType reference = new PackageReferenceType();
          reference.setPackageIDReference(Optional.ofNullable(dimension)
              .map(DimensionType::getMeasurement)
              .map(MeasurementType::getUnitOfMeasurement)
              .map(UnitOfMeasurementType::getUOMCodedOther)
              .filter(uomCode -> NumberUtils.isCreatable(uomCode))
              .map(uomCode -> Integer.valueOf(code))
              .orElse(EMPTY_CODE));
          referenceType.setPackageReference(reference);

          packagingReferenceType.getPackageReference()
              .add(referenceType);
        });

    return packagingReferenceType;
  }

  /**
   * @param locationItem
   * @return
   */
  private static ListOfSplitQuantityType createSplitQuantitiesList(Optional<LocationPlanningItemDetailType> locationItem) {
    ListOfSplitQuantityType listOfSplitQuantityType = new ListOfSplitQuantityType();

    Stream<ScheduleDetailType> scheduleDetails = locationItem.map(LocationPlanningItemDetailType::getListOfScheduleDetail)
        .map(ListOfScheduleDetailType::getScheduleDetail)
        .orElse(new ArrayList<>())
        .stream()
        .filter(scheduleDetail -> CommitmentLevelCodeType.FIRM.equals(scheduleDetail.getCommitmentLevelCoded()));

    // For all the FIRM scheduledDetails of this itemLocation
    scheduleDetails.forEach(scheduleDetail -> {
      List<DateCodedType> scheduleDates = Optional.of(scheduleDetail)
          .map(ScheduleDetailType::getScheduleDates)
          .map(ListOfDateCodedType::getDateCoded)
          .orElse(Collections.emptyList());
      List<QuantityCodedType> quantites = Optional.of(scheduleDetail)
          .map(ScheduleDetailType::getScheduleQuantities)
          .map(ListOfQuantityCodedType::getQuantityCoded)
          .orElse(Collections.emptyList());

      // set the quantities and their corresponding estimation dates
      for (int index = 0; index < scheduleDates.size(); index++) {
        DateCodedType dateCodedType = scheduleDates.get(index);
        QuantityCodedType quantityCodedType = quantites.get(index);
        listOfSplitQuantityType.getSplitQuantity()
            .add(createSplitQuantity(quantityCodedType, dateCodedType));
      }
    });
    return listOfSplitQuantityType;
  }

  private static SplitQuantityType createSplitQuantity(QuantityCodedType quantity, DateCodedType dateCodedType) {
    SplitQuantityType splitQuantityType = new SplitQuantityType();
    splitQuantityType.setShipmentStatusEventCoded(ShipmentStatusEventCodeType.SHIPMENT_COMPLETE);
    splitQuantityType.setSplitQuantity(XcblHelper.cloner.deepClone(quantity));
    splitQuantityType.setEstimatedDeliveryDate(dateCodedType.getDate());
    return splitQuantityType;
  }

  private static QuantityType computeTotalQuantity(Optional<LocationPlanningItemDetailType> locationItem, String quantityType) {
    // TODO possibly change the calculation method after more info from MANITOU
    // Presumably the unitOfMeasurment is the same for every value and the same with the PlanningSchedule totalQuantity
    // .. otherwise more calculation are required

    // calculate the total quantity as the sum of all quantities with QuantityQualifierCoded == given quantityType
    List<QuantityCodedType> list = locationItem.map(LocationPlanningItemDetailType::getListOfScheduleDetail)
        .map(ListOfScheduleDetailType::getScheduleDetail)
        .orElse(new ArrayList<>())
        .stream()
        .flatMap(scheduleItem -> scheduleItem.getScheduleQuantities()
            .getQuantityCoded()
            .stream())
        .collect(Collectors.toList());

    double totalQuantityValue = list.stream()
        .filter(quantityCoded -> quantityType.equals(quantityCoded.getQuantityQualifierCoded()))
        .mapToDouble(quantityCoded -> Optional.of(quantityCoded)
            .map(QuantityCodedType::getQuantityValue)
            .map(QuantityValueType::getValue)
            .map(BigDecimal::doubleValue)
            .orElse(0.0))
        .sum();

    // clone the original total quantity of the planning schedule item and set the computed value on it
    // if the total quantity is missing create a new object and set only the quantityValue
    QuantityType totalQuantity = XcblHelper.cloner.deepClone(locationItem.map(LocationPlanningItemDetailType::getBasePlanningDetail)
        .map(ScheduleBaseItemDetailType::getTotalQuantity)
        .orElse(new QuantityType()));
    QuantityValueType quantityValue = totalQuantity.getQuantityValue() != null ? totalQuantity.getQuantityValue() : new QuantityValueType();
    quantityValue.setValue(new BigDecimal(totalQuantityValue));
    totalQuantity.setQuantityValue(quantityValue);
    if (totalQuantity.getUnitOfMeasurement() == null) {
      totalQuantity.setUnitOfMeasurement(list.isEmpty() ? new UnitOfMeasurementType()
          : list.get(0)
              .getUnitOfMeasurement());
    }

    return totalQuantity;
  }

  /**
   * @return
   */
  private static OrderResponseSummaryType createOrderResponseSummary() {
    OrderResponseSummaryType summaryType = new OrderResponseSummaryType();
    OrderSummaryType value = new OrderSummaryType();
    value.setNumberOfLines(1); // since there will be an orderResponse for every item
    summaryType.setOriginalOrderSummary(value);
    return summaryType;
  }
}