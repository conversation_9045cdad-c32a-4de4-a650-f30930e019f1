/**
 * 
 */
package com.byzaneo.generix.edocument.util;

import java.util.Optional;

import org.supercsv.cellprocessor.CellProcessorAdaptor;
import org.supercsv.cellprocessor.ift.StringCellProcessor;
import org.supercsv.util.CsvContext;

import com.byzaneo.generix.api.DocumentCompoundType;
import com.byzaneo.generix.util.DocumentCompoundTypeHelper;

/**
 * <AUTHOR> Aboulaye <<EMAIL>>
 * @company Generix Group
 * @date 18 janv. 2018
 */
public class EDocumentTypeProcessor extends CellProcessorAdaptor implements StringCellProcessor {

  @SuppressWarnings("unchecked")
  @Override
  public String execute(Object value, CsvContext context) {
    return value == null ? ""
        : Optional.of(value)
            .map(Object::toString)
            .map(DocumentCompoundTypeHelper::fromJson)
            .map(DocumentCompoundType::getNorm)
            .orElse(value.toString());
  }
}
