D96ADatesFacture0011 = Documentdatums: De bepaling van het formaat van de facturatieperiode is niet juist
D96ADatesFacture0010 = Documentdatums: De facturatieperiode is niet numeriek
D96AReferencesIntervenant0001 = Rappel adresreferenties: De bepaling van de referentie is niet juist
D96AReferencesIntervenant0003 = Rappel adresreferenties: Het inschrijvingsnummer in het RCS-RCM van de verkoper werd dubbel doorgestuurd
D96AReferencesIntervenant0002 = Rappel adresreferenties: Het inschrijvingsnummer in het RCS-RCM van de verkoper ontbreekt
D96ADatesFacture0017 = Documentdatums: De documentdatum is groter dan de datum van vandaag
D96ADatesFacture0016 = Documentdatums: Er bestaat een incoherentie tussen de identificaties van de dag en de maand van de datum van het document
D96ADatesFacture0015 = Documentdatums: De identificatie van de dag in de datum van het document is niet juist
D96ADatesFacture0014 = Documentdatums: De identificatie van de maand in de datum van het document is niet juist
D96ADatesFacture0013 = Documentdatums: De info over de datums/uren/periodes op het briefhoofd van het document ontbreekt
D96ADatesFacture0012 = Documentdatums: De lengte van de facturatieperiode is niet juist
D96ADatesFacture0009 = Documentdatums: De werkelijke datum/het werkelijke uur van de levering is niet numeriek
D96AMontants0002 = Betalingsvoorwaarden: Het bedrag van de voorwaardelijke discontering is niet numeriek
D96ADatesFacture0008 = Documentdatums: De datum/het uur van ophaling is niet numeriek
D96ADatesFacture0007 = Documentdatums: De datum/het uur van verzending is niet numeriek
D96ADatesFacture0006 = Documentdatums: De lengte van de datum/het uur van het document is niet juist
D96ADatesFacture0005 = Documentdatums: De bepaling van het formaat van de datum/het uur van het document is niet juist
D96ADatesFacture0004 = Documentdatums: De datum/het uur van het document is niet numeriek
D96ADatesFacture0003 = Documentdatums: De datum/het uur van het document werd dubbel doorgestuurd
D96ADatesFacture0002 = Documentdatums: De datum/het uur van het document ontbreekt
D96ADatesFacture0001 = Documentdatums: De bepaling van datum/uur/periode is niet juist
D96AMontantTaxeLigne0001 = BTW bedrag lijn: De kosteninfo van de parentlijn (ALC + C) van het kostenbedrag MOA+23 ontbreekt
D96ADateReference0003 = Globale referentiedatums: De bepaling van het formaat van de geldigheidsperiode is niet juist
D96ADateReference0004 = Globale referentiedatums: De duur van de geldigheidsperiode van het contract is niet juist
D96AEnteteFacture0001 = Briefhoofd zending: De identificatie van de verzender van de interchange is niet juist
D96AEnteteFacture0002 = Briefhoofd zending: De identificatie van de bestemmeling van de interchange is niet juist
D96AEnteteFacture0003 = Briefhoofd zending: De voorbereidingsdatum van de interchange is niet numeriek
D96AEnteteFacture0004 = Briefhoofd zending: De lengte van de voorbereidingsdatum van de interchange is niet juist
D96AEnteteFacture0005 = Briefhoofd zending: De identificatie van de jaren in de voorbereidingsdatum van de interchange is niet juist
D96AMontantLigneReductionCharge0002 = Kortingen-Kosten lijn: Het kostenbedrag is niet numeriek
D96AEnteteFacture0006 = Briefhoofd zending: De identificatie van de maand in de voorbereidingsdatum van de interchange is niet juist
D96AMontantLigneReductionCharge0001 = Kortingen-Kosten lijn: Het kortingsbedrag is niet numeriek
D96AEnteteFacture0007 = Briefhoofd zending: De identificatie van de dag in de voorbereidingsdatum van de interchange is niet juist
D96AMontantLigneReductionCharge0004 = Kortingen-Kosten lijn: Het kostenbedrag werd dubbel doorgestuurd
D96AEnteteFacture0008 = Briefhoofd zending: Er bestaat een incoherentie tussen de identificaties van de dag en de maand van de voorbereidingsdatum van de interchange
D96AMontantLigneReductionCharge0003 = Kortingen-Kosten lijn: Het kortingsbedrag werd dubbel doorgestuurd
D96AEnteteFacture0009 = Briefhoofd zending: Het uur van de voorbereiding van de interchange is niet numeriek
D96ADateReference0001 = Globale referentiedatums: De bepaling van datum/uur/periode van het document als referentie is niet juist
D96ADateReference0002 = Globale referentiedatums: De geldigheidsperiode is niet numeriek
D96AEnteteFacture0010 = Briefhoofd zending: De lengte van het uur van de voorbereiding van de interchange is niet juist
D96AEnteteFacture0011 = Briefhoofd zending: De identificatie van de uren in het voorbereidingsuur van de interchange is niet juist
D96AEnteteFacture0012 = Briefhoofd zending: De identificatie van de minuten in het voorbereidingsuur van de interchange is niet juist
D96AReductionCharge0005 = Globale Kortingen-Kosten: De bepaling van de korting/kost is niet juist
D96AEnteteFacture0013 = Briefhoofd zending: De controlereferentie van de interchange is niet juist
D96AReductionCharge0003 = Globale Kortingen-Kosten: Het percentage en het bedrag van de korting ontbreken
D96AReductionCharge0004 = Globale Kortingen-Kosten: Het percentage en het kostenbedrag ontbreken
D96AReductionCharge0001 = Globale Kortingen-Kosten: De betaalcode van de korting/kost is niet juist
D96AReductionCharge0002 = Globale Kortingen-Kosten: Het eerste element van het label van de korting/kost is niet juist
D96AReference0001 = Globale referenties: Het factuurnummer en de geldigheidsperiode/facturatieperiode (document zelf of contract) als referentie bij de kredietnota ontbreken
D96AReference0002 = Globale referenties: Het factuurnummer als referentie is niet juist
D96AReference0003 = Globale referenties: Het contractnummer als referentie is niet juist
D96AMontantsTotaux0020 = Totale bedragen: Het totaal bedrag met alle taksen inbegrepen ontbreekt
D96AMontantsTotaux0021 = Totale bedragen: Het totaal bedrag met alle taksen inbegrepen is niet numeriek
D96AMontantsTotaux0022 = Totale bedragen: Het totaal bedrag zonder taksen van de artikellijnen ontbreekt
D96AMontantsTotaux0012 = Totale bedragen: De voorschotreferentie ontbreekt voor het totaal bedrag van het voorschot
D96AMontantsTotaux0014 = Totale bedragen: Het totaal voorschotbedrag is niet numeriek
D96AMontantsTotaux0015 = Totale bedragen: Het totaal netto te betalen bedrag ontbreekt terwijl het totaal voorschotbedrag doorgestuurd werd
D96AMontantsTotaux0017 = Totale bedragen: Het totaal netto te betalen bedrag is niet numeriek
D96AMontantsTotaux0018 = Totale bedragen: Het totaal BTW bedrag ontbreekt
D96AMontantsTotaux0019 = Totale bedragen: Het totaal BTW bedrag is niet numeriek
D96ARecapTaxesParafiscales0001 = Overzicht parafiscale taks: De bepaling van de korting/kost is niet juist
D96ARecapTaxesParafiscales0003 = Overzicht parafiscale taks: Het eerste element van het label van de parafiscale taks is niet juist
D96AMontantsTotaux0023 = Totale bedragen: Het totaal bedrag zonder taksen van de artikellijnen is niet numeriek
D96AMontantsTotaux0024 = Totale bedragen: Het voorschotbedrag is niet numeriek
D96AMontantsTotaux0025 = Totale bedragen: Het totaal BTW bedrag wordt minstens driedubbel doorgestuurd, of dubbel doorgestuurd zonder muntcodes (1 muntcode verwacht voor elk bedrag)
D96ATaxes0008 = Globale Kortingen-Kosten: Het tarief van de taks ontbreekt in het overzicht van de taksen onderaan het bericht
D96AConditionsPaiement0007 = Betalingsvoorwaarden: De boetevoorwaarden ontbreken zowel in tekst- als in gecodeerde vorm
D96AConditionsPaiement0008 = Betalingsvoorwaarden: De boetevoorwaarden werden zowel in tekst- als in gecodeerde vorm doorgestuurd (er kan slechts 1 model bestaan)
D96AConditionsPaiement0005 = Betalingsvoorwaarden: De voorwaardelijke disconteringsvoorwaarden ontbreken zowel in tekst- als in gecodeerde vorm
D96AConditionsPaiement0006 = Betalingsvoorwaarden: De voorwaardelijke disconteringsvoorwaarden werden zowel in tekst- als in gecodeerde vorm doorgestuurd (er kan slechts 1 model bestaan)
D96AConditionsPaiement0003 = Betalingsvoorwaarden: De info over de voorwaardelijke disconteringsvoorwaarden ontbreekt
D96AConditionsPaiement0004 = Betalingsvoorwaarden: De info over de boetevoorwaarden ontbreekt
D96ADatesPaiement0006 = Betalingsvoorwaarden: De betaaldatum is niet numeriek
D96AConditionsPaiement0001 = Betalingsvoorwaarden: De info over de betalingsvoorwaarden ontbreekt
D96ADatesPaiement0005 = Betalingsvoorwaarden: De betaaldatum ontbreekt
D96ALigne0001 = Detail: Het lijnnummer is niet numeriek
D96AConditionsPaiement0002 = Betalingsvoorwaarden: De info over de vervaldatum ontbreekt
D96ADatesPaiement0004 = Betalingsvoorwaarden: De vervaldatum is niet numeriek
D96ADatesPaiement0003 = Betalingsvoorwaarden: De vervaldatum ontbreekt
D96ADatesPaiement0002 = Betalingsvoorwaarden: De bepaling van datum/uur van de betalingsvoorwaarden is niet juist
D96ADatesPaiement0001 = Betalingsvoorwaarden: De datum van de betalingsvoorwaarden werd dubbel doorgestuurd
D96AIntervenantsFacture0036 = Adressen: De identificatie van het organisme dat verantwoordelijk is voor de codelijst (codetype) van de aankoper is niet juist
D96AIntervenantsFacture0037 = Adressen: Het eerste element van de naam en het adres van de aankoper is niet juist
D96AIntervenantsFacture0038 = Adressen: Het tweede element van de naam en het adres van de aankoper is niet juist
D96AIntervenantsFacture0039 = Adressen: Het eerste element van de vennootschapsnaam van de aankoper is niet juist
D96AFindeSection0001 = Einde sectie: De identificatiecode van de sectie is niet juist
D96AIntervenantsFacture0032 = Adressen: Het eerste element van de naam en het adres van de verkoper is niet juist
D96AIntervenantsFacture0033 = Adressen: Het tweede element van de naam en het adres van de verkoper is niet juist
D96AIntervenantsFacture0034 = Adressen: De info van de aankoper ontbreekt
D96AIntervenantsFacture0035 = Adressen: De identificatiecode van de aankoper is niet juist
D96ACommentairesLigne0001 = Opmerkingen lijn: Het eerste element van de tekst die de reden voor de vrijstelling van de taks beschrijft op de lijn van het artikel is niet juist
D96AIntervenantsFacture0030 = Adressen: De info over de regels ontbreekt
D96AIntervenantsFacture0031 = Adressen: De identificatie van het organisme dat verantwoordelijk is voor de codelijst (codetype) van de verkoper is niet juist
D96AIntervenantsFacture0029 = Adressen: De landcode van de btw-plichtige is niet juist
D96AIntervenantsFacture0025 = Adressen: Het eerste element van de vennootschapsnaam van de btw-plichtige is niet juist
D96AIntervenantsFacture0026 = Adressen: Het eerste element van het adres van de btw-plichtige is niet juist
D96AIntervenantsFacture0027 = Adressen: De naam van de plaats van de btw-plichtige is niet juist
D96AIntervenantsFacture0028 = Adressen: De postcode van de btw-plichtige is niet juist
D96AReferenceLigne0001 = Referenties lijn: Het nummer van het BL/verzendingsbericht als referentie ontbreekt
D96AIntervenantsFacture0021 = Adressen: De naam van de plaats van de maatschappelijke zetel van de verkoper is niet juist
D96ADateReferenceLigne0001 = Referentiedatums lijn: De datum/het uur van het BL/verzendingsbericht als referentie ontbreekt
D96AIntervenantsFacture0022 = Adressen: De postcode van de maatschappelijke zetel van de verkoper is niet juist
D96ADateReferenceLigne0002 = Referentiedatums lijn: De datum/het uur van het BL/verzendingsbericht als referentie is niet numeriek
D96AIntervenantsFacture0023 = Adressen: De landcode van de maatschappelijke zetel van de verkoper is niet juist
D96AIntervenantsFacture0024 = Adressen: De info van de btw-plichtige werd dubbel doorgestuurd
D96AReferenceLigne0002 = Referenties lijn: Het nummer van het BL/verzendingsbericht als referentie is niet juist
D96AIntervenantsFacture0020 = Adressen: Het eerste element van het adres van de maatschappelijke zetel van de verkoper is niet juist
D96AIntervenantsFacture0018 = Adressen: De info over de maatschappelijke zetel van de verkoper werd dubbel doorgestuurd
D96APourcentageLigneReduction0001 = Kortingen-Kosten lijn: Het kortingspercentage is niet numeriek
D96AIntervenantsFacture0019 = Adressen: Het eerste element van de vennootschapsnaam van de maatschappelijke zetel is niet juist
D96APourcentageLigneReduction0002 = Kortingen-Kosten lijn: Het kostenpercentage is niet numeriek
D96AIntervenantsFacture0014 = Adressen: Het eerste element van het adres van de verkoper is niet juist
D96AIntervenantsFacture0015 = Adressen: De naam van de plaats van de verkoper is niet juist
D96AIntervenantsFacture0016 = Adressen: De postcode van de verkoper is niet juist
D96AIntervenantsFacture0017 = Adressen: De landcode van de verkoper is niet juist
D96ADescriptionObjet0005 = Omschrijving: Het eerste element van het label van het artikel is niet juist
D96AIntervenantsFacture0010 = Adressen: De info van de verkoper ontbreekt
D96ADescriptionObjet0004 = Omschrijving: Het eerste element van het label van het artikel is niet juist
D96AIntervenantsFacture0011 = Adressen: De info van de verkoper werd dubbel doorgestuurd
D96ADescriptionObjet0003 = Omschrijving: De code van de beschrijving van het label van het artikel ontbreekt
D96AIntervenantsFacture0012 = Adressen: De identificatiecode van de verkoper is niet juist
D96ADescriptionObjet0002 = Omschrijving: De code van het beschrijvingstype van het object is niet juist
D96AIntervenantsFacture0013 = Adressen: Het eerste element van de vennootschapsnaam van de verkoper is niet juist
D96ADescriptionObjet0001 = Omschrijving: De info over de omschrijving van het object ontbreekt
D96AQuantites0003 = Hoeveelheid detail: De bepaling van de gefactureerde hoeveelheid ontbreekt
D96AQuantites0002 = Hoeveelheid detail: De bepaling van de hoeveelheid is niet juist
D96APourcentageLigneReduction0003 = Kortingen-Kosten lijn: Het kortingspercentage is niet numeriek
D96AQuantites0001 = Hoeveelheid detail: De info over de hoeveelheden ontbreekt
99InternalError0001 = BERICHT argument is niet bepaald
D96AIntervenantsFacture0007 = Adressen: De naam van de plaats van de gefactureerde partner is niet juist
99InternalError0002 = Het bestand met het argument bestaat niet
D96AIntervenantsFacture0008 = Adressen: De postcode van de gefactureerde partner is niet juist
D96AQuantites0006 = Hoeveelheid detail: De maateenheid van de gefactureerde hoeveelheid is niet juist
D96AReferencesMontantsTotaux0001 = Referenties totale bedragen: Het voorschotnummer als referentie is niet juist
99InternalError0003 = Het bestand met het argument heeft als grootte 0
D96AIntervenantsFacture0009 = Adressen: De landcode van de gefactureerde partner is niet juist
D96AQuantites0005 = Hoeveelheid detail: De gefactureerde hoeveelheid is niet numeriek
99InternalError0004 = Onmogelijk het bestand met het argument te lezen
D96AQuantites0004 = Hoeveelheid detail: De bepaling van de gefactureerde hoeveelheid werd dubbel doorgestuurd
D96AIntervenantsFacture0003 = Adressen: De info over de gefactureerde partner werd dubbel doorgestuurd
D96AIntervenantsFacture0004 = Adressen: De identificatiecode van de gefactureerde partner is niet juist
D96AIntervenantsFacture0005 = Adressen: Het eerste element van de vennootschapsnaam van de gefactureerde partner is niet juist
D96AIntervenantsFacture0006 = Adressen: Het eerste element van het adres van de gefactureerde partner is niet juist
D96ATaxes0004 = Globale Kortingen-Kosten:  Het tarief van de taks is niet numeriek
D96ATaxes0003 = Globale Kortingen-Kosten: De code van het type taks [VAT] verbonden aan de bepaling van de functie van de taks [7] ontbreekt
D96ATaxes0002 = Globale Kortingen-Kosten: De bepaling van de functie van de taks [7] ontbreekt
D96ATaxesLigne0009 = BTW lijn: De code van de takscategorie is niet juist
D96ATaxes0001 = Globale Kortingen-Kosten: De info over de taksen ontbreekt
D96ATaxesLigne0008 = BTW lijn: Het tarief van de taks ontbreekt in het overzicht van de taksen onderaan het bericht
D96ATaxesLigne0007 = BTW lijn: De info over de taksen werd dubbel doorgestuurd
D96ATaxesLigne0004 = BTW lijn: Het tarief van de taks is niet numeriek
D96ADevises0011 = Deviezen: De referentiemunteenheid ontbreekt
D96ATaxesLigne0003 = BTW lijn: De code van het type taks [VAT] verbonden aan de bepaling van de functie van de taks [7] ontbreekt
D96ADevises0010 = Deviezen: De deviesinfo ontbreekt
D96ATaxesLigne0002 = BTW lijn: De bepaling van de functie van de taks [7] ontbreekt
D96ADevises0013 = Deviezen: De code van de facturatiemunteenheid is niet juist
D96ATaxesLigne0001 = BTW lijn: De info over de taksen ontbreekt
D96ADevises0012 = Deviezen: De bepaling van de referentiemunteenheid is niet juist
D96AIntervenantsFacture0001 = Adressen: De info over de partners ontbreekt
D96AIntervenantsFacture0002 = Adressen: De info over de gefactureerde partner ontbreekt
99InternalError0005 = Het niveau van de logboodschappen is niet bepaald
99InternalError0006 = Het bericht bevat geen UNB of UNH segment
99InternalError0007 = De interchange bevat meerdere berichten
D96ADevises0004 = Deviezen: De code van de referentiemunteenheid is niet juist.
D96ADevises0003 = Deviezen: De bepaling van de referentiemunteenheid is niet juist
D96ADevises0006 = Deviezen: De bepaling van het gebruik van de doelmunteenheid is niet juist
D96ADevises0005 = Deviezen: De code van de referentiemunteenheid is niet juist.
D96ADevises0008 = Deviezen: De code van de doelmunteenheid is niet juist
D96ADevises0007 = Deviezen: De bepaling van de doelmunteenheid is niet juist
D96AMontantsTotaux0010 = Totale bedragen: Het totaal bedrag met alle taksen inbegrepen wordt minstens driedubbel doorgestuurd, of dubbel doorgestuurd zonder muntcodes (1 muntcode verwacht voor elk bedrag)
D96ADevises0009 = Deviezen: De code van de doelmunteenheid is niet juist
D96AMontantsTotaux0011 = Totale bedragen: Het totaal bedrag met alle taksen inbegrepen is niet numeriek
D96AMontantsTotaux0001 = Totale bedragen: De info van de totale bedragen ontbreekt
D96AMontantsTotaux0002 = Totale bedragen: Het totaal bedrag zonder taksen ontbreekt
D96AMontantsTotaux0003 = Totale bedragen: Het totaal bedrag zonder taksen wordt minstens driedubbel doorgestuurd, of dubbel doorgestuurd zonder muntcodes (1 muntcode verwacht voor elk bedrag)
D96AMontantsTotaux0004 = Totale bedragen: Het totaal bedrag zonder taksen is niet numeriek
D96AMontantsTotaux0005 = Totale bedragen: Het totaal BTW bedrag ontbreekt
D96AMontantsTotaux0006 = Totale bedragen: Het tweede totaal BTW bedrag uitgedrukt in de doelmunt ontbreekt
D96ADevises0002 = Deviezen: De bepaling van het gebruik van de referentiemunteenheid is niet juist
D96AMontantsTotaux0007 = Totale bedragen: Het totaal BTW bedrag wordt dubbel doorgestuurd terwijl geen enkele doelmunt ingevuld is
D96AMontantsTotaux0008 = Totale bedragen: Het totaal BTW bedrag is niet numeriek
D96AMontantReductionCharge0003 = Globale Kortingen-Kosten: Het kortingsbedrag werd dubbel doorgestuurd
D96AMontantsTotaux0009 = Totale bedragen: Het totaal bedrag met alle taksen inbegrepen ontbreekt
D96ATaxesLigneReductionCharge0001 = Kortingen-Kosten lijn: De bepaling van de functie van de taks [7] ontbreekt
D96AMontantReductionCharge0002 = Globale Kortingen-Kosten: Het kostenbedrag is niet numeriek
D96ATaxesLigneReductionCharge0002 = Kortingen-Kosten lijn: De code van het type taks [VAT] verbonden aan de bepaling van de functie van de taks [7] ontbreekt
D96AMontantReductionCharge0001 = Globale Kortingen-Kosten: Het kortingsbedrag is niet numeriek
D96ATaxesLigneReductionCharge0003 = Kortingen-Kosten lijn: Het tarief van de taks is niet numeriek
D96ATaxesLigneReductionCharge0005 = Kortingen-Kosten lijn: De info over de taksen werd dubbel doorgestuurd
D96ATaxesLigneReductionCharge0006 = Kortingen-Kosten lijn: Het tarief van de taks ontbreekt in het overzicht van de taksen onderaan het bericht
D96AMontantReductionCharge0004 = Globale Kortingen-Kosten: Het kostenbedrag werd dubbel doorgestuurd
D96AVentilationsTva0003 = BTW overzicht: De code van het type taks [VAT] verbonden aan de bepaling van de functie van de taks [7] ontbreekt
D96AVentilationsTva0004 = BTW overzicht: Het belastbaar bedrag is niet numeriek
D96APourcentages0003 = Betalingsvoorwaarden: Het boetepercentage is niet numeriek
D96AVentilationsTva0005 = BTW overzicht: Het tarief van de taks is niet numeriek
D96APourcentages0002 = Betalingsvoorwaarden: Het percentage van de voorwaardelijke discontering is niet numeriek
D96APourcentages0005 = Betalingsvoorwaarden: Er bestaat een incoherentie tussen de boete\u00EFndicator en het percentagetype
D96APourcentages0004 = Betalingsvoorwaarden: Er bestaat een incoherentie tussen de indicator van de voorwaardelijke discontering en het percentagetype
D96AVentilationsTva0001 = BTW overzicht: De info over de taksen ontbreekt
D96AVentilationsTva0002 = BTW overzicht: De bepaling van de functie van de taks [7] ontbreekt
D96ACommentaires0002 = Opmerkingen: De reglementaire info van de verkoper ontbreekt
D96ACommentaires0001 = Opmerkingen: De opmerkingen van het briefhoofd van het document ontbreken
D96ACommentaires0006 = Opmerkingen: Het aandelenkapitaal en de munteenheid van de verkoper zijn niet juist
D96ACommentaires0005 = Opmerkingen: De juridische vorm van de verkoper is niet juist
D96ACommentaires0004 = Opmerkingen: De handelsnaam van de verkoper is niet juist
D96ACommentaires0003 = Opmerkingen: De reglementaire info van de verkoper werd dubbel doorgestuurd
D96ADatesReferencesMontantsTotaux0001 = Referentiedatums totale bedragen: De bepaling van datum/uur van de referentie van het bedrag is niet juist
D96ADatesReferencesMontantsTotaux0002 = Referentiedatums totale bedragen: De datum voor het voorschot ontbreekt
D96ADatesReferencesMontantsTotaux0003 = Referentiedatums totale bedragen: De datum voor het voorschot is niet numeriek
D96ADatesLigne0004 = Detaildatums: De werkelijke datum/het werkelijke uur van de levering is niet numeriek
D96AVentilationsTva0007 = BTW overzicht: Het tarief van de taks ontbreekt in de rest van het bericht
D96AVentilationsTva0008 = BTW overzicht: De code van de takscategorie is niet juist
D96AVentilationsTva0009 = BTW overzicht: Het tarief van de taks werd dubbel doorgestuurd
D96ADatesLigne0001 = Detaildatums: De bepaling van datum/uur/periode is niet juist
D96ADatesLigne0002 = Detaildatums: De datum/het uur van verzending is niet numeriek
D96ADatesLigne0003 = Detaildatums: De datum/het uur van ophaling is niet numeriek
D96ACommentaires0013 = Opmerkingen: De vaste vergoeding bij betalingsachterstand ontbreekt.
D96ACommentaires0012 = Opmerkingen: Het eerste element van de tekst die de reden voor de vrijstelling van de taks beschrijft op het briefhoofd van het document, is niet juist
D96ACommentaires0011 = Opmerkingen: Er bestaat een incoherentie tussen de opmerkingen die het motief voor de vrijstelling van de taks beschrijven en de code van de vrijstelling van de taks op het briefhoofd van het document
D96ACommentaires0010 = Opmerkingen: Het eerste element van de tekst die de boetevoorwaarden beschrijft, is niet juist
D96ARegimesTva0001 = Taks recht: Er bestaat een incoherentie tussen de code voor de vrijstelling van taks en de opmerkingen op het briefhoofd die het motief voor de vrijstelling van de taks beschrijven
D96ACommentaires0017 = Opmerkingen: HET RCS-RCM en de stad van de verkoper zijn niet juist
D96ACommentaires0016 = Opmerkingen: Het label van de disconteringsvoorwaarden van de verkoper is niet juist
D96ACommentaires0015 = Opmerkingen: Het label van het BTW tarief van de verkoper is niet juist
D96ACommentaires0014 = Opmerkingen: De info over de soort BTW en de discontering van de verkoper ontbreken
D96ACommentaires0009 = Opmerkingen: De tekst die de boetevoorwaarden beschrijft, werd dubbel doorgestuurd
D96ACommentaires0008 = Opmerkingen: Het eerste element van de tekst die de voorwaardelijke disconteringsvoorwaarden beschrijft, is niet juist
D96ACommentaires0007 = Opmerkingen: De tekst die de voorwaardelijke disconteringsvoorwaarden beschrijft, werd dubbel doorgestuurd
D96AIdentificationMessage0001 = Begin bericht: De documentcode is niet juist
D96AIdentificationMessage0004 = Begin bericht: De functiecode van het bericht is niet juist
D96AIdentificationMessage0003 = Begin bericht: Het documentnummer is niet juist
D96APourcentageReductionCharge0002 = Globale Kortingen-Kosten: Het kostenpercentage is niet numeriek
D96APourcentageReductionCharge0001 = Globale Kortingen-Kosten: Het kortingspercentage is niet numeriek
D96AReferencesIntervenant0027 = Rappel adresreferenties: De SIREN code van de maatschappelijke zetel van de verkoper is niet juist
D96AReferencesIntervenant0026 = Rappel adresreferenties: De SIREN code van de maatschappelijke zetel van de verkoper ontbreekt
D96AReferencesIntervenant0028 = Rappel adresreferenties: Het BTW identificatienummer van de gefactureerde partner ontbreekt
D96AReferencesIntervenant0021 = Rappel adresreferenties: Het inschrijvingsnummer in het RCS-RCM van de maatschappelijke zetel van de verkoper is niet juist
D96AReferencesIntervenant0020 = Rappel adresreferenties: Het inschrijvingsnummer in het RCS-RCM van de maatschappelijke zetel van de verkoper ontbreekt
D96AReferencesIntervenant0023 = Rappel adresreferenties: Het BTW identificatienummer van de maatschappelijke zetel van de verkoper is niet juist
D96AReferencesIntervenant0022 = Rappel adresreferenties: Het BTW identificatienummer van de maatschappelijke zetel van de verkoper ontbreekt
D96AReferencesIntervenant0025 = Rappel adresreferenties: De SIREN code van de verkoper ontbreekt
D96AReferencesIntervenant0024 = Rappel adresreferenties: De SIREN code van de Franse gefactureerde partner ontbreekt
D96AMontantsTva0002 = Bedragen BTW overzicht: Het taksbedrag ontbreekt
D96AMontantsTva0001 = Bedragen BTW overzicht: De info over het taksbedrag ontbreekt
D96AMontantsTva0004 = Bedragen BTW overzicht: Het taksbedrag is niet numeriek
D96AMontantsTva0003 = Bedragen BTW overzicht: Het taksbedrag werd dubbel doorgestuurd
D96AMontantsTaxes0002 = Overzicht parafiscale taks: Het taksbedrag ontbreekt
D96AMontantsTva0006 = Bedragen BTW overzicht: Het belastbaar bedrag ontbreekt
D96AMontantsTaxes0001 = Overzicht parafiscale taks: De info over het taksbedrag ontbreekt
D96AMontantsTva0005 = Bedragen BTW overzicht: Het opgegeven bedrag beantwoordt niet aan een taksbedrag
D96AMontantsTva0008 = Bedragen BTW overzicht: Het taksbedrag ontbreekt
D96AMontantsTva0007 = Bedragen BTW overzicht: Het belastbaar bedrag is niet numeriek
D96AReductionChargeLigne0005 = Kortingen-Kosten lijn: De bepaling van de korting/kost is niet juist
D96AReferencesIntervenant0016 = Rappel adresreferenties: De SIREN code van de gefactureerde partner is niet juist
D96AReductionChargeLigne0004 = Kortingen-Kosten lijn: Het percentage en het kostenbedrag ontbreken
D96AReferencesIntervenant0015 = Rappel adresreferenties: De SIREN code van de gefactureerde partner werd dubbel doorgestuurd
D96AMontantsTaxes0004 = Overzicht parafiscale taks: Het taksbedrag is niet numeriek
D96AReductionChargeLigne0003 = Kortingen-Kosten lijn: Het percentage en het bedrag van de korting ontbreken
D96AReferencesIntervenant0018 = Rappel adresreferenties: De SIREN code van de verkoper werd dubbel doorgestuurd
D96AMontantsTaxes0003 = Overzicht parafiscale taks: Het taksbedrag werd dubbel doorgestuurd
D96AReductionChargeLigne0002 = Kortingen-Kosten lijn: Het eerste element van het label van de korting/kost is niet juist
D96AReductionChargeLigne0001 = Kortingen-Kosten lijn: De betaalcode van de korting/kost is niet juist
D96AReferencesIntervenant0019 = Rappel adresreferenties: De SIREN code van de verkoper is niet juist
D96AReferencesIntervenant0010 = Rappel adresreferenties: Het BTW identificatienummer van de verkoper is niet juist
D96APrix0007 = Prijs: De brutoprijs zonder taksen is niet numeriek
D96AReferencesIntervenant0012 = Rappel adresreferenties: Het btw-identificatienummer van de btw-plichtige werd dubbel doorgestuurd
D96APrix0008 = Prijs: De basis van de nettoprijs zonder taksen is niet numeriek
D96AReferencesIntervenant0011 = Rappel adresreferenties: Het btw-identificatienummer van de btw-plichtige ontbreekt
D96APrix0009 = Prijs: De maateenheid van de nettoprijs zonder taksen is niet juist
D96AReferencesIntervenant0014 = Rappel adresreferenties: De SIREN code en het BTW identificatienummer van de Franse gefactureerde partner ontbreken
D96AReferencesIntervenant0013 = Rappel adresreferenties: Het btw-identificatienummer van de btw-plichtige is niet juist
D96APrix0003 = Prijs: De nettoprijs zonder taksen werd dubbel doorgestuurd
D96APrix0004 = Prijs: De nettoprijs zonder taksen is niet numeriek
D96APrix0005 = Prijs: De brutoprijs zonder taksen ontbreekt terwijl een lijnkorting of lijnkost opgegeven is
D96ATypeMessage0002 = Briefhoofd bericht: De identificatie van het berichttype is niet juist
D96APrix0006 = Prijs: De brutoprijs zonder taksen werd dubbel doorgestuurd
D96ATypeMessage0001 = Briefhoofd bericht: Het referentienummer van het bericht is niet juist
D96APrix0001 = Prijs: De prijsinfo ontbreekt
D96APrix0002 = Prijs: De nettoprijs zonder taksen ontbreekt
D96ATypeMessage0004 = Briefhoofd bericht: Het uitgavenummer is niet juist
D96ATypeMessage0003 = Briefhoofd bericht: Het nummer van de berichtversie is niet juist
D96ACodeComplementaireProduit0001 = Interne detailcode: De interne detailcode werd dubbel doorgestuurd
D96ATypeMessage0006 = Briefhoofd bericht: De code die door de vereniging werd toegekend is niet juist
D96ATypeMessage0005 = Briefhoofd bericht: Het controleagentschap is niet juist
D96AReferencesIntervenant0005 = Rappel adresreferenties: Het BTW identificatienummer van de gefactureerde partner buiten Frankrijk ontbreekt
D96AReferencesIntervenant0004 = Rappel adresreferenties: Het inschrijvingsnummer in het RCS-RCM van de verkoper is niet juist
D96AReferencesIntervenant0007 = Rappel adresreferenties: Het BTW identificatienummer van de gefactureerde partner is niet juist
D96AReferencesIntervenant0006 = Rappel adresreferenties: Het BTW identificatienummer van de gefactureerde partner werd dubbel doorgestuurd
D96AReductionChargeLigne0012 = Kortingen-Kosten lijn: Het kortingsbedrag ontbreekt
D96AReferencesIntervenant0009 = Rappel adresreferenties: Het BTW identificatienummer van de verkoper werd dubbel doorgestuurd
D96AReductionChargeLigne0011 = Kortingen-Kosten lijn: Het kortingspercentage ontbreekt
D96AReferencesIntervenant0008 = Rappel adresreferenties: Het BTW identificatienummer van de verkoper ontbreekt
D96AReductionChargeLigne0010 = Kortingen-Kosten lijn: De code van de particuliere service van de korting is niet juist
D96AConditionsPaiement0010 = Betalingsvoorwaarden: De voorwaardelijke disconteringsvoorwaarden werden dubbel doorgestuurd in gecodeerde vorm.
D96AMontantsTotaux0027 = Totale bedragen: Het totaal voorschotbedrag ontbreekt terwijl het totaal netto te betalen bedrag doorgestuurd werd
D96AConditionsPaiement0009 = Betalingsvoorwaarden: De boetevoorwaarden werden dubbel doorgestuurd in gecodeerde vorm.
D96ACommentaires0018 = Opmerkingen: De vaste vergoeding bij betalingsachterstand werd dubbel doorgestuurd.

D96ATaxes0004b = Globale vergoedingen/toeslagen: Het belastingtarief ontbreekt
D96AMontantLigneReductionCharge0006 = Lijn vergoedingen/toeslagen: Het bedrag van de toeslag is leeg
D96AMontantLigneReductionCharge0005 = Allowance-Charge-regel: Het kortingsbedrag is leeg
D96AReductionCharge0007 = Meer dan \u00E9\u00E9n percentage voor globale lading (PCD+2)
D96AReductionCharge0006 = Meer dan \u00E9\u00E9n percentage voor de globale vergoeding (PCD+1)
D96AEnteteFacture0014 = Koptekst uitwisseling: De voorbereidingstijd is leeg
D96APrix0005bis = Prijs: De brutoprijs ontbreekt
D96AVentilationsTva0004b = BTW-uitsplitsing: De maatstaf van heffing ontbreekt
D96APourcentageLigneReduction0004 = Lijntoelagen/-toeslagen: Het toeslagpercentage is leeg
D96ADevises0014 = Valuta: De details qualifier van de doelvaluta is leeg
D96ATaxesLigne0004b = Regel BTW: het belastingtarief ontbreekt
D96AMontantReductionCharge0006 = Globale vergoedingen/kosten: Het toeslagbedrag is leeg
D96AMontantReductionCharge0005 = Globale vergoedingen/toeslagen: Het bedrag van de vergoeding is leeg
D96AVentilationsTva0006 = BTW-uitsplitsing: Het belastingtarief ontbreekt
D96APourcentageReductionCharge0004 = Globale rechten/kosten: Het toeslagpercentage is leeg
D96APourcentageReductionCharge0003 = Globale vergoedingen/toeslagen: Het percentage emissierechten is leeg
D96AReductionChargeLigne0008 = Zeker een ontbrekende lijn vergoeding / toeslag gegevens (een PCD segment volgt direct een TAX segment in groep 33)
D96AReductionChargeLigne0007 = Meer dan \u00E9\u00E9n percentage voor lijnkosten (PCD+2)
D96AReductionChargeLigne0006 = Meer dan \u00E9\u00E9n percentage voor lijntoelage (PCD+1)
D96AEnteteFacture0015 = Koptekst uitwisseling: De voorbereidingsdatum is leeg