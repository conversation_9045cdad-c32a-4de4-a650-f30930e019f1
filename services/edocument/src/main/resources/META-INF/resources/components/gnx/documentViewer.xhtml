<?xml version="1.0" encoding="UTF-8"?>
<ui:component
        xmlns="http://www.w3.org/1999/xhtml"
        xmlns:f="http://xmlns.jcp.org/jsf/core"
        xmlns:h="http://xmlns.jcp.org/jsf/html"
        xmlns:cc="http://xmlns.jcp.org/jsf/composite"
        xmlns:p="http://primefaces.org/ui"
        xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
        xmlns:b="http://byzaneo.com/ui">

    <cc:interface name="viewer">
        <cc:attribute name="value" type="com.byzaneo.generix.edocument.bean.DocumentViewer"/>
        <cc:attribute name="showTitle" type="java.lang.Boolean" default="true"/>
        <cc:attribute name="styleClassSelectFile" type="java.lang.String" default=""/>
    </cc:interface>

    <cc:implementation>
    <p:outputPanel rendered="#{cc.attrs.value != null}">
        <h:panelGrid styleClass="w-100 document-viewer" columns="2" columnClasses="left nowrap,right nowrap" style="margin-bottom: 10px">
            <h:panelGrid style="width: #{cc.attrs.showTitle ? 'auto' : '100%'};float: left" columns="2">
                <p:selectOneMenu value="#{cc.attrs.value.currentFile}" converter="#{cc.attrs.value.fileConverter}" styleClass="#{cc.attrs.styleClassSelectFile}"
                                 rendered="#{cc.attrs.value.validFiles.size() > 1}">
                    <p:ajax event="change" process="@this" update="dvFile" />
                    <f:selectItems value="#{cc.attrs.value.validFiles}" var="dfv" itemValue="#{dfv}" itemLabel="#{dfv.type}" />
                </p:selectOneMenu>
                <h:panelGroup>
                    <cc:renderFacet name="actions" />
                </h:panelGroup>
            </h:panelGrid>
            <h:outputText rendered="#{cc.attrs.showTitle}" value="#{cc.attrs.value.baseName}" styleClass="title2 document-viewer-title" />
        </h:panelGrid>
        <p:outputPanel id="dvFile">
            <h:outputText escape="false" styleClass="center"
                          value="&lt;iframe id='dvFrame' width='100%' height='1000px' src='#{cc.attrs.value.currentFile.url}' frameborder='0' style='margin:0;padding:0;'&gt;&lt;/iframe&gt;"
                          rendered="#{cc.attrs.value.currentFile.type.binary or cc.attrs.value.currentFile.type=='HTML'}" />
            <b:gnxeditor value="#{cc.attrs.value.currentFile.content}"
                           lineWrapping="true" readonly="true" lineNumbers="true"
                           rendered="#{not cc.attrs.value.currentFile.type.binary and cc.attrs.value.currentFile.type!='HTML'}"/>

<!-- If you want to use the component GnxEditor make sure you use the one defined in byzaneo-faces with the tag-lib <b:gnxeditor/> -->
<!--             <ge:gnxeditor widgetVar="wGnxEditor" value="#{cc.attrs.value.currentFile.content}" mode="#{cc.attrs.value.currentFile.mode}" -->
<!-- 						  theme="eclipse" lineNumbers="true" lineWrapping="true" keyMap="sublime" -->
<!-- 						  rendered="#{not cc.attrs.value.currentFile.type.binary and cc.attrs.value.currentFile.type!='HTML'}"  gutter="true" /> -->
        </p:outputPanel>
    </p:outputPanel>
    </cc:implementation>
</ui:component>
