/*
 * Copyright (c) 2023.
 * created by <PERSON><PERSON>
 */

package com.byzaneo.generix.rest.service.jwt;

import com.byzaneo.generix.rest.service.basic.RestCommonService;
import com.byzaneo.generix.api.service.internal.delegators.RestJWTServiceDelegator;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.Response;

import static org.slf4j.LoggerFactory.getLogger;

@Component
@RequiredArgsConstructor
public class RestJWTServiceDelegatorImpl implements RestJWTServiceDelegator {

  private static final Logger log = getLogger(RestJWTServiceDelegatorImpl.class);

  // - SERVICES -
  @Autowired
  @Qualifier(RestCommonService.SERVICE_NAME)
  private RestCommonService restCommonService;

  // - PROPERTIES -

  /**
   * @param instanceCode  the instance code
   * @param eDocumentType The eDocument's type requested
   * @param bql           The BQL query used to search the eDocuments
   * @param page          The zero-based page index
   * @param size          The size of the page to be returned
   * @return
   */
  @Override
  @PreAuthorize("hasRole('ROLE_COMPANY')")
  public Response searchEDocuments(@Context HttpServletRequest request,String instanceCode, String eDocumentType, String bql, int page, int size) {
    return restCommonService.searchEDocuments(request, instanceCode, eDocumentType, bql, page, size);
  }

  /**
   * @param instanceCode  the instance code
   * @param eDocumentType The eDocument's type requested
   * @param number        The eDocument's number
   * @param recipient     The eDocument's recipient code
   * @param format        The eDocument returned format: PDF or JSON (default: JSON)
   * @return
   */
  @Override
  @PreAuthorize("hasRole('ROLE_COMPANY')")
  public Response getEDocument(@Context HttpServletRequest request,String instanceCode, String eDocumentType, String number, String recipient, String format) {
    return restCommonService.getEDocument(request, instanceCode, eDocumentType, number, recipient, format);
  }

}