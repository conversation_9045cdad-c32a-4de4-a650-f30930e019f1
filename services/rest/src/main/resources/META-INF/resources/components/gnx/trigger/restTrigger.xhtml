<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml"
              xmlns:h="http://xmlns.jcp.org/jsf/html"
              xmlns:f="http://xmlns.jcp.org/jsf/core"
              xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
              xmlns:cc="http://xmlns.jcp.org/jsf/composite"
              xmlns:p="http://primefaces.org/ui"
>
    <!-- INTERFACE -->
    <cc:interface name="restTrigger">
        <cc:attribute name="value" required="true" type="com.byzaneo.generix.rest.trigger.RestTrigger"/>
        <cc:attribute name="readonly" default="false"/>
    </cc:interface>
    <!-- IMPLEMENTATION -->
    <cc:implementation>
        <!-- mapping -->
        <h:panelGroup layout="block" styleClass="form-group">
            <p:outputLabel styleClass="col-sm-2 control-label" value="URL" for="trgRestMapping" />
            <p:outputPanel layout="block" styleClass="col-sm-10">
                <p:inputText id="trgRestMapping" value="#{cc.attrs.value.mapping}"
                             required="true" styleClass="form-control"
                             readonly="#{cc.attrs.readonly}"
                             validatorMessage="URL must be in lowercase with '-' or '_'">
                    <f:validateRegex pattern="[a-z_-]*" />
                </p:inputText>
            </p:outputPanel>
        </h:panelGroup>
    </cc:implementation>
</ui:component>