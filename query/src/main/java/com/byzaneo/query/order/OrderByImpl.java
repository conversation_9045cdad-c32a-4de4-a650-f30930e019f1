package com.byzaneo.query.order;

import static java.util.Collections.emptyList;
import static java.util.Collections.unmodifiableList;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.lang3.ArrayUtils.isEmpty;
import static org.springframework.util.Assert.noNullElements;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

/**
 * Default implementation of {@link com.byzaneo.query.order.OrderBy}.
 * 
 * <AUTHOR> <<EMAIL>>
 * @date 05/12/2012
 * @since 1.0
 */
public class OrderByImpl implements OrderBy {
  /**  */
  private static final long serialVersionUID = -6329507444568119677L;

  public static final OrderByImpl NO_ORDER = new OrderByImpl(Collections.<SearchSort> emptyList());

  private final List<SearchSort> searchSorts;

  public OrderByImpl(final SearchSort... searchSorts) {
    this(isEmpty(searchSorts) ? null : Arrays.asList(searchSorts));
  }

  public OrderByImpl(final Collection<SearchSort> searchSorts) {
    if (isEmpty(searchSorts)) {
      this.searchSorts = emptyList();
    }
    else {
      noNullElements(searchSorts.toArray(),"");
      this.searchSorts = unmodifiableList(new ArrayList<>(searchSorts));
    }
  }

  @Override
  public List<SearchSort> getSearchSorts() {
    return searchSorts;
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    if (!searchSorts.isEmpty()) {
      sb.append("ORDER BY ");
    }
    for (Iterator<SearchSort> searchSortIterator = searchSorts.iterator(); searchSortIterator.hasNext();) {
      SearchSort searchSort = searchSortIterator.next();
      sb.append(searchSort.getField());
      if (searchSort.getOrder() != null) {
        sb.append(' ')
            .append(searchSort.getOrder());
      }
      if (searchSortIterator.hasNext()) {
        sb.append(", ");
      }
    }
    return sb.toString();
  }

  @Override
  public boolean equals(final Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }

    final OrderByImpl orderBy = (OrderByImpl) o;

    if (!searchSorts.equals(orderBy.searchSorts)) {
      return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    return searchSorts.hashCode();
  }
}
