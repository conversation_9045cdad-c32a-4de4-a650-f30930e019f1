package com.byzaneo.query.clause;

import static org.springframework.util.Assert.notEmpty;
import static org.springframework.util.Assert.notNull;

import java.util.Collections;
import java.util.List;

import com.byzaneo.query.operand.MultiValueOperand;
import com.byzaneo.query.operand.Operand;
import com.byzaneo.query.operand.SingleValueOperand;
import com.byzaneo.query.operator.Operator;

/**
 * Used to represent a terminal node in the query tree.
 *
 * <AUTHOR> <<EMAIL>>
 * @date 05/12/2012
 * @since 1.0
 */
public class TerminalClauseImpl implements TerminalClause {
  private static final long serialVersionUID = -9070449537991199434L;

  private final String name;
  private final Operator operator;
  private final Operand operand;

  /**
   * Creates a terminal clause with the specified name, operator and turns the string value into a
   * {@link com.byzaneo.query.operand.SingleValueOperand} populated with a string value.
   *
   * @param name the name for the clause.
   * @param operator the operator for the clause.
   * @param operand the string value that will be wrapped in a SingleValueOperand.
   */
  public TerminalClauseImpl(final String name, final Operator operator, final String operand) {
    this(name, operator, new SingleValueOperand(operand));
  }

  /**
   * Creates a terminal clause with the specified name, operator and turns the long value into a
   * {@link com.byzaneo.query.operand.SingleValueOperand} populated with a long value.
   *
   * @param name the name for the clause.
   * @param operator the operator for the clause.
   * @param operand the long value that will be wrapped in a SingleValueOperand.
   */
  public TerminalClauseImpl(final String name, final Operator operator, final long operand) {
    this(name, operator, new SingleValueOperand(operand));
  }

  /**
   * Creates a terminal clause with the specified name, operator and operand.
   *
   * @param name the name for the clause.
   * @param operator the operator for the clause.
   * @param operand the right-hand-side value of the clause.
   */
  public TerminalClauseImpl(String name, Operator operator, Operand operand) {
    notNull(operator,"");
    notNull(operand,"");
    notNull(name,"");
    this.operator = operator;
    this.operand = operand;
    this.name = name;
  }

  /**
   * A convienience constructor that will create a clause with the {@link com.byzaneo.query.operator.Operator#EQUALS} operator if there is
   * only one value in the array and with the {@link com.byzaneo.query.operator.Operator#IN} operator if there are more than one value in
   * the array.
   *
   * @param name the name for the clause.
   * @param values the values that will be turned into {@link com.byzaneo.query.operand.SingleValueOperand}'s containing a string value.
   */
  public TerminalClauseImpl(String name, Object... values) {
    notNull(values,"");
    notEmpty(values,"");
    notNull(name,"");
    this.name = name;
    if (values.length == 1) {
      this.operator = Operator.EQUALS;
      this.operand = new SingleValueOperand(values[0]);
    }
    else {
      this.operator = Operator.IN;
      this.operand = new MultiValueOperand(values);
    }
  }

  /** @see com.byzaneo.query.clause.TerminalClause#getOperand() */
  @Override
  public Operand getOperand() {
    return operand;
  }

  /** @see com.byzaneo.query.clause.TerminalClause#getOperator() */
  @Override
  public Operator getOperator() {
    return operator;
  }

  /** @see com.byzaneo.query.clause.Clause#getName() */
  @Override
  public String getName() {
    return name;
  }

  /** @see com.byzaneo.query.clause.Clause#getClauses() */
  @Override
  public List<Clause> getClauses() {
    return Collections.emptyList();
  }

  /** @see com.byzaneo.query.clause.Clause#accept(com.byzaneo.query.clause.ClauseVisitor) */
  @Override
  public <R> R accept(final ClauseVisitor<R> visitor) {
    return visitor.visit(this);
  }

  /** @see java.lang.Object#toString() */
  @Override
  public String toString() {
    return new StringBuilder(getName())
        .append(" ")
        .append(operator.getDisplayString())
        .append(" ")
        .append(operand.getDisplayString())
        .toString();
  }

  // /CLOVER:OFF
  @Override
  public boolean equals(final Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }

    final TerminalClauseImpl that = (TerminalClauseImpl) o;

    if (!name.equals(that.name) || !operand.equals(that.operand) || operator != that.operator) {
      return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int result = operand.hashCode();
    result = 31 * result + operator.hashCode();
    result = 31 * result + name.hashCode();
    return result;
  }
  // /CLOVER:ON
}
