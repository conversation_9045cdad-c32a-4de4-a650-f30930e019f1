<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <link rel="stylesheet" href="css/reveal.css">
    <link rel="stylesheet" href="css/theme/night.css" id="theme">
    <link rel="stylesheet" href="lib/css/zenburn.css">
    <style type="text/css">
      nav > ul {
        z-index: 8000;
        position: absolute;
        top: 0;
        left: 0;
        padding: 5px;
        margin: 0;
        border: 0;
      }
      nav > ul > li {
        display: inline-block;
      }
      nav > ul > li > a {
        color: white;
        text-decoration: none;
      }
      nav > ul > li > a::after {
        content: " |";
      }
      nav > ul > li:last-child > a::after {
        content: "";
      }
    </style>
  </head>

  <body>
    <nav>
      <ul>
        <li><a href="/?docker/">Docker</a></li>
        <li><a href="/?factory/">Usine</a></li>
      </ul>
    </nav>
    <div class="reveal">
      <div class="slides">
        <section data-markdown="" />
      </div>
    </div>
  </body>
</html>
<script src="lib/js/head.min.js"></script>
<script src="js/reveal.js"></script>
<script>
  var printPdfRegex = /print-pdf/gi;
  var reserved = {
    search: [printPdfRegex],
    hash: []
  };

  (function () {
    var link = document.createElement('link');
    link.rel = 'stylesheet';
    link.type = 'text/css';
    link.href = window.location.search.match(printPdfRegex) ? 'css/print/pdf.css' : 'css/print/paper.css';
    document.getElementsByTagName('head')[0].appendChild(link);
  })();

  var config = (function() {
    var override = {};

    function isReserved(location, value) {
      for (var i = 0, size = reserved[location].length; i < size; i++) {
        if (value.match(reserved[location][0])) {
          return true;
        }
      }
      return false;
    }

    function overrideConfig(location, regex, property, defaultValue) {
      override[property] = defaultValue;
      window.location[location].replace(regex, function(match, group) {
        if (!isReserved(location, group)) {
          override[property] = group;
        }
      });
    }

    overrideConfig('search', /([^&?/]+)/gmi, 'markdown', 'docker');
    overrideConfig('hash', /([0-9]+)$/gmi, 'pageNum', 0);

    return override;
  })();

  Reveal.initialize({
    controls: false,
    progress: true,
    history: true,
    center: true,
    transition: 'slide',
    dependencies: [
      {
        src: 'lib/js/classList.js',
        condition: function() {
          return !document.body.classList;
        }
      }, {
        src: 'plugin/markdown/marked.js'
      }, {
        src: 'plugin/markdown/markdown.js'
      }, {
        src: 'plugin/highlight/highlight.js',
        callback: function() {
          hljs.initHighlightingOnLoad();
        }
      }]
  });
  
  function removeAllChild(element) {
    while (element.firstChild) {
      element.removeChild(element.firstChild);
    }
  }

  function loadMarkdown() {
    var slides = document.getElementsByClassName('slides')[0];
    removeAllChild(slides);

    var section = document.createElement('section');
    section.setAttribute('data-markdown', './doc/' + config.markdown + '.md');
    section.setAttribute('data-separator', '\n\n\n');
    section.setAttribute('data-charset', 'utf-8');

    slides.appendChild(section);

    RevealMarkdown.initialize();
    Reveal.navigateTo(config.pageNum);
  }
  
  Reveal.addEventListener('ready', function() {
    loadMarkdown();
  });
</script>