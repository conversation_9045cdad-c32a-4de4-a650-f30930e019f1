package com.byzaneo.generix.ui.listener;

import static com.byzaneo.commons.ui.util.JSFHelper.getFacesContext;
import static com.byzaneo.commons.ui.util.JSFHelper.getRequestParameter;
import static java.lang.Boolean.FALSE;
import static javax.faces.event.PhaseId.APPLY_REQUEST_VALUES;
import static org.apache.commons.lang3.BooleanUtils.toBoolean;
import static org.slf4j.LoggerFactory.getLogger;
import com.sun.faces.application.view.ViewScopeContextManager;

import javax.faces.context.FacesContext;
import javax.faces.event.PhaseEvent;
import javax.faces.event.PhaseId;
import javax.faces.event.PhaseListener;

import org.slf4j.Logger;

public class ViewScopedCleaner implements PhaseListener {
  private static final long serialVersionUID = 7675074380443649734L;
  private static final Logger log = getLogger(ViewScopedCleaner.class);

  /** Request parameter "viewScopedCleaner" */
  private static final String RP_VIEW_SCOPED_CLEANER = "viewScopedCleaner";

  /** Clear view navigation */
  private static final String VIEW_CLEAR = "/ui/clear";

  /** @see javax.faces.event.PhaseListener#afterPhase(javax.faces.event.PhaseEvent) */
  @Override
  public void afterPhase(PhaseEvent event) {
    final FacesContext ctx = getFacesContext();
    // Only flush view scoped beans managed if:
    // - the parameter "viewScopedCleaner" is transmitted with a value equals to "true".
    // - it is an Ajax request;
    if (toBoolean(getRequestParameter(RP_VIEW_SCOPED_CLEANER, FALSE.toString())) &&
        ctx.getPartialViewContext()
            .isAjaxRequest()) {

      // traces
      if (log.isDebugEnabled()) {
        ctx.getViewRoot()
            .getViewMap()
            .forEach((k, v) -> log.debug("View to clear {};{}", k, v));
      }

      // clears views
      ctx.getViewRoot()
          .getViewMap()
          .clear();
      ViewScopeContextManager contextManager = new ViewScopeContextManager();
      contextManager.clear(ctx);

      // stops processing by navigating to a clean page
      ctx.getApplication()
          .getNavigationHandler()
          .handleNavigation(ctx, null, VIEW_CLEAR);
    }
  }

  /** @see javax.faces.event.PhaseListener#beforePhase(javax.faces.event.PhaseEvent) */
  @Override
  public void beforePhase(PhaseEvent event) {
    // Nothing to do
  }

  /** @see javax.faces.event.PhaseListener#getPhaseId() */
  @Override
  public PhaseId getPhaseId() {
    // This listener can be only triggered when a "jsf" form is submitted (due to
    // the viewId only transmitted when the "jsf" form is submitted).
    // APPLY_REQUEST_VALUES is one of the phases used to handle treatment on "jsf" form submission.
    return APPLY_REQUEST_VALUES;
  }

}
