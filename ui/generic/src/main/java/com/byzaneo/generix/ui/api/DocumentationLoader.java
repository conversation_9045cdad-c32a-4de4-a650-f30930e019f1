package com.byzaneo.generix.ui.api;

import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.nio.file.Path;
import java.util.List;
import java.util.Locale;

import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

import com.byzaneo.commons.ui.util.JSFHelper;

/**
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Apr 14, 2015
 * @since 3.4 GNX-2167
 */
public interface DocumentationLoader extends ResourceLoader {

  /**
   * Return a DocumentationResource handle for the specified resource. The handle should always be a reusable resource descriptor, allowing
   * for multiple {@link Resource#getInputStream()} calls.
   * <p>
   * Note that a Resource handle does not imply an existing resource; you need to invoke {@link Resource#exists} to check for existence.
   * 
   * @param uri of the documentation resource location
   * @param (optional) the language of the documentation resource. If <code>null</code>, the default locale is used.
   * @return a corresponding DocumentationResource handle
   */
  <R extends DocumentationResource> R getResource(URI uri, Locale locale);

  /**
   * @return the list of the available languages for documentation.
   */
  List<Locale> getLanguages();

  /**
   * @param locale language to add
   * @return <code>true</code> if the language has been added.
   */
  boolean addLanguage(Locale locale);

  /**
   * @param locale the language to remove
   * @return <code>true</code> if the language has been added
   */
  boolean removeLanguage(Locale locale);

  /**
   * @return the default language
   * @see JSFHelper#getLocale()
   */
  Locale getDefaultLanguage();

  /**
   * @param language the language to export
   * @return the archive file containing the documentation on the given language
   * @throws IOException
   */
  Path exports(Locale locale) throws IOException;

  /**
   * @param archive to uncompress containing language code as first level directories.
   * @param override replace target documentation if <code>true</code>
   */
  void imports(InputStream archive, boolean override) throws IOException;

  /**
   * @param resources to import
   * @param overwrite replace target documentation if <code>true</code>
   * @throws IOException in case of I/O issue
   */
  void imports(Resource[] resources, boolean overwrite) throws IOException;

}
