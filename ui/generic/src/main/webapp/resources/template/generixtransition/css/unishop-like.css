@font-face {
    font-family: feather;
    src: url("#{resource['template/generixtransition:fonts/feather-webfont.eot']}");
    src: url("#{resource['template/generixtransition:fonts/feather-webfont.eot']}#iefix") format("embedded-opentype"), url("#{resource['template/generixtransition:fonts/feather-webfont.woff']}") format("woff"), url("#{resource['template/generixtransition:fonts/feather-webfont.ttf']}") format("truetype"), url("#{resource['template/generixtransition:fonts/feather-webfont.svg']}#feather") format("svg");
    font-weight: 400;
    font-style: normal
}

#sidebar-container.sidebar-collapsed .list-group a.sidebar-item-active i.fa-chevron-down {
    color: white
}

#sidebar-container .list-group a.sidebar-item-active {
    color: #eb5d15
}

.ui-datatable .ui-paginator.ui-paginator-bottom select {
    -webkit-appearance: none;
    -moz-appearance: none;
    background: url("#{resource['template:generixtransition/images/drop-down-arrow.png']}");
    background-size: 8px 8px;
    background-position: 80% !important;
    background-repeat: no-repeat;
    border: 1px solid #e1e7ec;
    border-width: 100% !important
}

.info-icon {
    display: inline-block;
    width: 17px;
    height: 17px;
    vertical-align: sub;
    background: url("#{resource['template:generixtransition/images/info-simple.svg']}") no-repeat
}

.logo {
    width: 259px;
    height: 207px;
    margin: 22px auto;
    background-size: 260px !important;
    background: url("#{resource['template:generixtransition/images/logo.svg']}") no-repeat
}

.bgLogin {
    background: url("#{resource['template:generixtransition/images/background.jpg']}");
    background-size: cover;
    background-repeat: no-repeat;
    background-position-y: 62%
}

.btn-ok-process-list {
    background-image: url("#{resource['template/generixtransition/images/icon-check.png']}")
}

.uploadpdf .btn-ko-process-list {
    background-image: url("#{resource['template/generixtransition/images/icon-close.png']}")
}

.counterManagement {
    margin-top: -60px;
    float: right
}

.counterDateRangeButton {
    margin-left: 5px;
    margin-top: -85px;
    height: 25px;
    background-color: transparent;
    color: gray
}

.reconciliation-footer {
    width: 120%
}
