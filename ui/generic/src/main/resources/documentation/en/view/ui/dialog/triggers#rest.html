<p>Use Rest Trigger to launch a process by calling a REST API with some input (usually files) either :</p>
          
<ul>
  <li>
	<p><span class="bold"><strong>Directly</strong></span>: the user waits for the process to finish.</p>
  </li>
  <li>
	<p><span class="bold"><strong>Synchronously</strong></span>: The files sent for processing will be added to a queue and processed sequentially.</p>
  </li>
</ul>
          
<p>To configure the trigger, fill in the following fields:</p>
          
<table frame="border" rules="all" style="margin:25px;">
  <thead>
	<tr>
	  <th>
		Champ
	  </th>
	  <th>
		Description
	  </th>
	  <th>
		Mandatory
	  </th>
	</tr>
  </thead>
  <tbody>
	<tr>
	  <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">Name</span>
		</p>
	  </td>
	  <td>
		[@triggername]
	  </td>
	  <td>
		<p>Mandatory</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">Description</span>
		</p>
	  </td>
	  <td>
		[@triggerdescription]
	  </td>
	  <td>
		<p>Optional</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">URL</span>
		</p>
	  </td>
	  <td>
		<p>Specify an ID.</p>
		<p>Example: <strong class="userinput" style="background-color:#f9f2f4; color:#c7254e; border-radius:4px; padding: 2px 4px;"><code>csv-to-factur-x</code></strong></p>
		<p>The ID accepts:</p>
		<div class="itemizedlist">
		  <ul>
			<li>
			  <p>Lowercase letters.</p>
			</li>
			<li>
			  <p>Alphanumeric characters.</p>
			</li>
			<li>
			  <p>Special characters: "_", "-"</p>
			</li>
		  </ul>
		</div>
		<p>Insert the ID at the end of the URL that will trigger the business process.</p>
		<p>Example for a synchronous trigger: <strong class="userinput" style="background-color:#f9f2f4; color:#c7254e; border-radius:4px; padding: 2px 4px;"><code>Customer portal URL</code></strong>/aio/rest/secure/<strong class="userinput" style="background-color:#f9f2f4; color:#c7254e; border-radius:4px; padding: 2px 4px;"><code>GIS environment trigram</code></strong>/process/<strong class="userinput" style="background-color:#f9f2f4; color:#c7254e; border-radius:4px; padding: 2px 4px;"><code>Trigger's name</code></strong></p>
		<p>Example for an asynchronous trigger: <strong class="userinput" style="background-color:#f9f2f4; color:#c7254e; border-radius:4px; padding: 2px 4px;"><code>Customer portal URL</code></strong>/aio/rest/secure/async/<strong class="userinput" style="background-color:#f9f2f4; color:#c7254e; border-radius:4px; padding: 2px 4px;"><code>GIS environment trigram</code></strong>/process/<strong class="userinput" style="background-color:#f9f2f4; color:#c7254e; border-radius:4px; padding: 2px 4px;"><code>Trigger's name</code></strong></p>
	  </td>
	  <td>
		<p>Mandatory</p>
	  </td>
	</tr>
  </tbody>
</table>
          
        <p>Once you have configured the trigger,<span class="guilabel" style="font-weight:bold;"> save</span> it. Then <a href="https://helpcenter-gis.generixgroup.com/en/index-en.html#UUID-ac6d091d-9c17-101b-e010-5cd9ed4423a7_UUID-3870eeda-6dee-c58b-3ed0-c308dcf2ba9c" target="_blank">deploy the business process and the trigger</a> at the same time.</p>
		
		<p>You can also <a href="https://helpcenter-gis.generixgroup.com/en/index-en.html#UUID-ac6d091d-9c17-101b-e010-5cd9ed4423a7_UUID-0b2744c0-b80c-7314-55b0-b3fa23cb25a2" target="_blank">find all the triggers created in the environment</a> on the<span class="guilabel" style="font-weight:bold;">Activity</span> &gt;<span class="guilabel" style="font-weight:bold;">Process</span> page.</p>
   