         
<h1>General</h1>
                    
	<h2>General</h2> 
            
		<table frame="border" rules="all" style="margin:25px;">
		<thead>
		  <tr>
			<th>Field </th>
			<th>Description </th>
			<th>Mandatory </th>
		  </tr>
		</thead>
		<tbody>
		  <tr>
			<td>
			  <p>
				<span class="guilabel" style="font-weight:bold;">Code</span>
			  </p>
			</td>
			<td>
				[@2gnlcode]
			</td>
			<td>
			  <p>Mandatory</p>
			</td>
		  </tr>
		  <tr>
			<td>
			  <p>
				<span class="guilabel" style="font-weight:bold;">Name</span>
			  </p>
			</td>
			<td>
			[@2gnlname]
			</td>
			<td>
			  <p>Mandatory</p>
			</td>
		  </tr>
		  <tr>
			<td>
			  <p>
				<span class="guilabel" style="font-weight:bold;">Profile</span>
			  </p>
			</td>
			<td>
			  <p>Select the profile of the partner.</p>
			  <p>If you don't select a profile, the system sets the profile to <span class="guilabel" style="font-weight:bold;">PARTNER</span>.</p>
			</td>
			<td>
			  <p>Optional</p>
			</td>
		  </tr>
		  <tr>
			<td>
			  <p>
				<span class="guilabel" style="font-weight:bold;">Unique identification number</span> 
			  </p>
			</td>
			<td>
			  <p>Number automatically created by All-In-One to allow partner contacts to sign in by themselves to the customer front-office portal.</p>
			</td>
			<td>
			  <p>Optional</p>
			</td>
		  </tr>
		</tbody>
		</table>
                   
    <h2>Identifier</h2>
              
        [@2gnlidentifier]    
		
					
    <h2>Address</h2>
              
		[@2gnladdress]
            
          
    <h2>Contact Information</h2>
         [@2contactinfo]    
	
          
    <h2>Ereporting options</h2>
              
                    
        <p><span class="guilabel">Option for the VAT regime</span>: Select one of the following VAR regime:</p>
        
          <ul>
            <li>
              <p>
                <span class="guilabel" style="font-weight:bold;">Company subject to the normal monthly real regime
</span>
              </p>
            </li>
            <li>
              <p>
                <span class="guilabel" style="font-weight:bold;">Company having opted for the real normal quarterly regime
</span>
              </p>
            </li>
            <li>
              <p>
                <span class="guilabel" style="font-weight:bold;">Company subject to the simplified VAT tax regime
</span>
              </p>
            </li>
            <li>
              <p>
                <span class="guilabel" style="font-weight:bold;">Company benefiting from the franchise regime based on VAT
</span>
              </p>
            </li>
          </ul>
        
        <p>To learn more about VAT regimes, see the following table and <a class="link" href="https://www.economie.gouv.fr/entreprises/regime-tva" target="_blank">the article by Bercy Infos about the different tax regimes.</a></p>
        
          <table frame="border" rules="all" style="margin:25px;">
            <thead>
              <tr>
                <th>
                  VAT regime
                </th>
                <th>
                  Description
                </th>
                <th>
                  Transaction type
                </th>
                <th>
                  Data collection period
                </th>
                <th>
                  <p>Transmission date</p>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td rowspan="2">
                  <p>
                    <span class="guilabel" style="font-weight:bold;">Company having opted for the real normal quarterly regime</span>
                  </p>
                </td>
                <td rowspan="2">
                  <p>Company subject to income tax or corporation tax.</p>
                </td>
                <td>
                  <p>Transaction data</p>
                </td>
                <td>
                  <p>The previous month</p>
                </td>
                <td>
                  <p>The 9th or 10th of each month</p>
                </td>
              </tr>
              <tr>
                <td>
                  <p>Payment data</p>
                </td>
                <td>
                  <p>The previous month</p>
                </td>
                <td>
                  <p>The 9th or 10th of each month</p>
                </td>
              </tr>
              <tr>
                <td rowspan="4">
                  <p>
                    <span class="guilabel" style="font-weight:bold;">Company subject to the normal monthly real regime</span>
                  </p>
                </td>
                <td rowspan="4">
                  <p>Company subject to income tax or corporation tax. Only companies with an annual turnover of less than &euro;4,000 can opt for quarterly declaration and payment.</p>
                </td>
                <td rowspan="3">
                  <p>Transaction data</p>
                </td>
                <td>
                  <p>Between the 21 and the end of the previous month</p>
                </td>
                <td>
                  <p>The 9 or 10 of each month</p>
                </td>
              </tr>
              <tr>
                <td>
                  <p>Between the 1st and the tenth of the previous month.</p>
                </td>
                <td>
                  <p>The 19 or 20 of each month.</p>
                </td>
              </tr>
              <tr>
                <td>
                  <p>Between the 11 and 20 of the previous month.</p>
                </td>
                <td>
                  <div>
                    <ul>
                      <li>
                        <p>February 27 or 28</p>
                      </li>
                      <li>
                        <p>29, 30 or 31 of other months of the year</p>
                      </li>
                    </ul>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                   <p>Payment data</p>
                </td>
                <td>
                  <p>The previous month</p>
                </td>
                <td>
                  <p>The 9 or 10 of each month</p>
                </td>
              </tr>
              <tr>
                <td rowspan="2">
                  <p>
                    <span class="guilabel" style="font-weight:bold;">Company subject to the simplified VAT tax regime
</span>
                  </p>
                </td>
                <td rowspan="2">
                  <p>Company taxed on the basis of its profits. It declares its VAT once a year.</p>
                </td>
                <td>
                  <p>Transaction data</p>
                </td>
                <td>
                  <p>The previous month</p>
                </td>
                <td>
                  <div>
                    <ul>
                      <li>
                        <p>February 27 or 28</p>
                      </li>
                      <li>
                        <p>29, 30 or 31 of other months of the year</p>
                      </li>
                    </ul>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                   <p>Payment data</p>
                </td>
                <td>
                  <p>The previous month</p>
                </td>
                <td>
                  <div>
                    <ul>
                      <li>
                        <p>February 27 or 28</p>
                      </li>
                      <li>
                        <p>29, 30 or 31 of other months of the year</p>
                      </li>
                    </ul>
                  </div>
                </td>
              </tr>
              <tr>
                <td rowspan="2">
                  <p>
                    <span class="guilabel" style="font-weight:bold;">Company benefiting from the franchise regime based on VAT
</span>
                  </p>
                </td>
                <td rowspan="2">
                  <p>A company that is exempt from declaring and paying VAT on the services it provides or the sales it makes.</p>
                </td>
                <td>
                  <p>Transaction data</p>
                </td>
                <td>
                  <p>The two previous months</p>
                </td>
                <td>
                  <p>9 or 10 of January, March, May, July, September and November</p>
                </td>
              </tr>
              <tr>
                <td>
                   <p>Payment data</p>
                </td>
                <td>
                  <p>The two previous months</p>
                </td>
                <td>
                  <p>9 or 10 of January, March, May, July, September and November</p>
                </td>
              </tr>
            </tbody>
          </table>
        
        <p><span class="guilabel" style="font-weight:bold;">Option for the eReporting</span>: Select one of the following options</p>
        <div>
          <ul>
            <li>
              <p>I ask Generix to extract the international B2B invoicing data and undertake to provide in a separate flow the invoicing data of invoices that have not passed through the PDP.</p>
            </li>
            <li>
              <p>I ask Generix not to extract any invoicing data, I prefer to provide Generix with all eReporting data in a separate feed.</p>
            </li>
          </ul>
        </div>
		
        <div style="background: rgb(238, 238, 238); border: 1px solid rgb(204, 204, 204); padding: 5px 10px; margin: 5px 10px;">
			<strong>Note</strong>
                <p>Consultants and customers must define the format of the file to send to Generix, the transmission method and the transmission date.</p>
        </div>
         
        
          
<h1>Details</h1>
            
	<p>If the customer wants to define profiles to request additional information from some partners, consultants can add them from the <span class="guilabel" style="font-weight:bold;">Freetax</span> tab of the customer form.</p>
	  <p>Each profile can contain one or more customized fields, called free text.</p>
	  <p>The customized fields the partner should complete are in the <span class="guilabel" style="font-weight:bold;">Details</span> tab of the partner form.</p>
		  <div style="background: rgb(238, 238, 238); border: 1px solid rgb(204, 204, 204); padding: 5px 10px; margin: 5px 10px;">
		  <strong>Note</strong>
                
		  <p>If the customer requests additional information, it is available on the front-office portal on the <span class="guilabel" style="font-weight:bold;">Details</span> tab of the <span class="guilabel" style="font-weight:bold;">My partner information</span> page.</p>
		  <p>To do so, the consultant must grant the user role the following rights</p>
		  
		<table frame="border" rules="all" style="margin:25px;">
		  <thead>
			<tr>
			  <th>
				Tab
			  </th>
			  <th>
				Section
			  </th>
			  <th>
				Right
			  </th>
			  <th>
				Checkbox
			  </th>
			</tr>
		  </thead>
		  <tbody>
			<tr>
			  <td rowspan="3" class="td">
				<p>General</p>
			  </td>
			  <td rowspan="3" class="td">
				<p>Security</p>
			  </td>
			  <td>
				<p>General</p>
			  </td>
			  <td>
				<p>Read</p>
			  </td>
			</tr>
			<tr>
			  <td rowspan="2" class="td">
				<p>Freetext / Details</p>
			  </td>
			  <td>
				<p>Read</p>
			  </td>
			</tr>
			<tr>
			  <td>
				<p>Update</p>
			  </td>
			</tr>
		  </tbody>
		</table>
          </div>
		  
        
          
<h1>Notifications</h1>
        
			[@2notifications]
	
	  
<h1>eDocument</h1>
            
	[@2edocument]
        
        
       
          
<h1>Keystores</h1>
       [@2keystores]        
       
          
<h1>Templates</h1>
    [@2templates]
        
         
<h1>Channels</h1>
            
[@2channels]
            
          
          <br class="table table-responsive-break" />
        
        
<h1>Logistics</h1>
            
[@2logistics]
          
        
<h1>Banking</h1>
            
	<p>In the banking tab, you can see, add, and delete the company's bank details.</p>
	<p>The banking details listed in this tab can be added from this window or the front-office portal. The customer uses the banking details to pay provider invoices.</p>
	<p>To add a line to the list:</p>

	<ol type="1">
	  <li>
		<p>Click on <span class="bold"><strong>...</strong></span> on the right of the table headers, and select <span class="bold"><strong>Add</strong></span>.</p>
	  </li>
	  <li>
		<p>Fill in the fields.</p>
	  </li>
	  <li>
		<p>Click <span class="bold"><strong>Save</strong></span>.</p>
	  </li>
	  <li>
		<p>To delete a line, click on <span class="bold"><strong>...</strong></span> on the right-hand side of the line, and select <span class="bold"><strong>Delete</strong></span>.</p>
	  </li>
	</ol>
          
<h1>Authentication</h1>
[@2authentication]
           
         
           