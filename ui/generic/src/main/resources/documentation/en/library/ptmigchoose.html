<p><strong>Choose:</strong> Select the RTE executable you want to associate and confirm. The RTE executable is loaded either separately or in a .zip file that can possibly include properties files with translations. Loaded .zip files are unzipped and the system checks that they contain at least one executable (the files must be directly included in a .zip file. Folders will be ignored.). All the files in the .zip are stored in a specific folder under the instance of the task and with the name of the associated MIG.</p>

<div style="background:#eee;border:1px solid #ccc;padding:5px 10px;"><span style="color:#575756"><span style="font-size:14px"><strong>Note:</strong> RTE executables or .zip files with an RTE executable must be imported beforehand in the customer&#39;s environment via <strong>Repository</strong> &gt; <strong>Templates </strong>&gt; <strong>Import</strong> &gt; <strong>RTE</strong>.</span></span></div>