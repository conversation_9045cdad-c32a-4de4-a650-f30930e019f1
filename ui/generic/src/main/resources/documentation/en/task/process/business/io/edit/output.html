<p>This business task copies the files that are identified with the internal variable ${process_input_dir_string} in the execution directory to an output directory. You can customize the default target directory by specifying a different path.</p>

<p><strong>Input: </strong>Files in an execution directory</p>

<p><strong>Output:</strong>&nbsp;Files in an output directory</p>

<h1>Description</h1>

<ul>
	<li>[@bttitle]</li>
	<li>[@btdisabled]</li>
	<li>[@bttarget]</li>
	<li>[@btfileset]
	<ul>
		<li>[@btfsincl]</li>
		<li>[@btfsexcl]</li>
		<li>[@btfsregex]</li>
		<li>[@btfsex]<br />[@btfsnote]</li>
		<li>[@btfscs]</li>
	</ul>
	</li>
</ul>

<ul>
	<li>[@btoverw]</li>
	<li>[@btflat]</li>
</ul>

<div style="margin-left:15.0pt;">
<div style="background:#eeeeee;border:1px solid #cccccc;padding:5px 10px;"><strong>Note: </strong>this option implies that the Filter field includes **/*.<extension> to copy files from the source directory and subdirectories.</extension></div>
</div>

<ul>
	<li>[@btlimit]</li>
	<li><strong>Master/Slave Files</strong><br />These fields are used to define which files are considered as master and which ones as slave using regular expressions. When used, fill in both fields.
	<ul>
		<li>[@btmaster]</li>
		<li>[@btslave]</li>
	</ul>
	</li>
</ul>

<p>&nbsp;</p>