
        
        <div class="sidebar Introduction">
          
          <p>The Invoice portlet displays the invoices of a legal entity or a list of invoices filtered by the consultant in the back office.</p>
          
          <p>According to the portlet configuration and the portal user's role, portal users can:</p>
          
            <ul>
              <li>
                <p>Download or view the invoice readable, tax original, declared data, errors, and attachments.</p>
              </li>
              <li>
                <p>Attach documents to invoices.</p>
              </li>
              <li>
                <p>Capture invoices.</p>
              </li>
              <li>
                <p>Correct invoices, including those captured by OCR.</p>
              </li>
              <li>
                <p>Export one or more invoices in .XLS format.</p>
              </li>
              <li>
                <p>Diagnose invoices with the <span class="guilabel" style="font-weight:bold;">Refused</span> status.</p>
              </li>
              <li>
                <p>Audit invoices.</p>
              </li>
            </ul>
          
          <p>The Invoice portlet is also used to <span class="bold"><strong>launch actions linked to other functionalities</strong></span> such as reconciliations, legal and business controls, and workflows.</p>
          <p>As part of workflows, portal users can :</p>
          
            <ul>
              <li>
                <p>Enrich invoices.</p>
              </li>
              <li>
                <p>Add comments up to 2 000 characters.</p>
              </li>
              <li>
                <p>Attach files.</p>
              </li>
              <li>
                <p>Lock invoices to prevent other users from performing actions on them.</p>
              </li>
              <li>
                <p>Validate invoices.</p>
              </li>
              <li>
                <p>Force the validation of invoices with the status <span class="guilabel" style="font-weight:bold;">To be corrected</span>.</p>
              </li>
              <li>
                <p>Assign an action to another user.</p>
              </li>
              <li>
                <p>Refuse invoices.</p>
              </li>
            </ul>
          
          <div style="background: rgb(238, 238, 238); border: 1px solid rgb(204, 204, 204); padding: 5px 10px; margin: 5px 10px;">
            <strong>Note</strong>
                  <p>Learn more about this topic on <a href="https://helpcenter-gis.generixgroup.com/en/index-en.html#workflows" target="_blank">the Workflows tile</a>.</p>
          </div>
          <p>Portal users can also perform general actions on this portlet. Thus, they can:</p>
          
            <ul>
              <li>
                <p>Filter the content of the list.</p>
              </li>
              <li>
                <p>Save filters to reuse them later.</p>
              </li>
              <li>
                <p>Change the column's order, rename them, or hide them.</p>
              </li>
              <li>
                <p>Export the table to an XLS file. If users customize its display, the export applies the customization.</p>
              </li>
              <li>
                <p>Change how to sort the specified columns.</p>
              </li>
            </ul>
          
        </div>
          
<h1>Description</h1>
              
<table frame="border" rules="all" style="margin:25px;">
  <thead>
		<tr>
		<th>
		  Field
		</th>
		<th>
		  Description
		</th>
		<th>
		Mandatory
		</th>
		</tr>
		</thead>
  <tbody>
	<tr>
	  <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">Title</span>
		</p>
	  </td>
	  <td>
		[@2pttitle]
	  </td>
	  <td>
		<p>Mandatory</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">Description</span>
		</p>
	  </td>
	  <td>
		[@2ptdesc]
	  </td>
	  <td>
		<p>Optional</p>
	  </td>
	</tr>
  </tbody>
</table>
        

<h1>Display</h1>
              
<table frame="border" rules="all" style="margin:25px;">
  <thead>
	<tr>
	  <th>
		Field
	  </th>
	  <th>
		Description
	  </th>
	  <th>
	  Mandatory
	  </th>
	</tr>
  </thead>
  <tbody>
	<tr>
		<td>
		<p>
		<span class="guilabel" style="font-weight:bold;">Sort by</span>
		</p>
		</td>
		<td>
		[@2ptdispsort]
		</td>
		<td>
		<p>Optional</p>
		</td>
	</tr>
	<tr>
		  <td>
			<p>
		  <span class="guilabel" style="font-weight:bold;">Ascending</span>
		</p>
		  </td>
		  <td>
			[@2ptascending]
		  </td>
		  <td>
			<p>Optional</p>
		  </td>
		</tr>
	<tr>
	  <td>
		<span class="guilabel" style="font-weight:bold;">View the total number of invoices</span>
	  </td>
	  <td>
		<p>Indicate whether the total number of invoices in the list is displayed at the bottom of the portlet.</p>
		<p>Default value: <span class="guilabel" style="font-weight:bold;">No</span>.</p>
	  </td>
	  <td>
	  </td>
	</tr>
	<tr>
		  <td>
			<p>
			  <span class="guilabel" style="font-weight:bold;">Checkbox</span>
			</p>
		  </td>
		  <td>
			[@2ptdischk]
		  </td>
		  <td>
			<p>Optional</p>
		  </td>
		</tr>
	<tr>
	  <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">Fieldname</span>
		</p>
	  </td>
	  <td>
		[@2ptdisfie]
	  </td>
	  <td>
		<p>Optional</p>
	  </td>
	</tr>
	<tr>
		  <td>
			<p>
			  <span class="guilabel" style="font-weight:bold;">Column Title</span>
			</p>
		  </td>
		  <td>
			[@2ptdiscol]	
		  </td>
		  <td>
			<p>Optional</p>
		  </td>
		</tr>
	<tr>
	  <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">Style</span>
		</p>
	  </td>
	  <td>
		[@2ptdisty]
	  </td>
	  <td>
		<p>Optional</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">List</span>
		</p>
	  </td>
	  <td>
		[@2ptdislist]
	  </td>
	  <td>
		<p>Optional</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>
		  <span class="bold">
			<strong>Autocomplete/No autocomplete</strong>
		  </span>
		</p>
	  </td>
	  <td>
		[@2ptdisautocom]
	  </td>
	  <td>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">Searchable/Non-searchable</span>
		</p>
	  </td>
	  <td>
		[@2ptdissea]
		<p>Default value: <span class="guilabel" style="font-weight:bold;">Searchable</span>.</p>
	  </td>
	  <td>
		<p>Optional</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">Delete</span>
		</p>
	  </td>
	  <td>
	  [@2ptdisdel]
		<p>This criterion is currently unclickable.</p>
	  </td>
	  <td>
	  Optional
	  </td>
	</tr>
	<tr>
		  <td>
			<p>
			  <span class="guilabel" style="font-weight:bold;">Radio button</span>
			</p>
		  </td>
		  <td>
		  [@2ptdisrb]
		  </td>
		  <td>
			<p>Optional</p>
		  </td>
		</tr>
	<tr>
		  <td>
			<p>
			  <span class="guilabel" style="font-weight:bold;">Arrow</span>
			</p>
		  </td>
		  <td>
			[@2ptdisarr]	
		  </td>
		  <td>
			<p>Optional</p>
		  </td>
		</tr>
	<tr>
	  <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">Indexation collection</span>
		</p>
	  </td>
	  <td>
		[@2ptinvrptindex]
	  </td>
	  <td>
		<p>Optional</p>
	  </td>
	</tr>
  </tbody>
</table>
          

<h1>Add invoices portlet</h1>

<table frame="border" rules="all" style="margin:25px;">
  <thead>
	<tr>
	  <th>
		Field
	  </th>
	  <th>
		Description
	  </th>
	</tr>
  </thead>
  <tbody>
	<tr>
	  <td>
		<span class="guilabel" style="font-weight:bold;">Creation</span>
	  </td>
	  <td>
		<div style="background: rgb(252, 232, 218); border: 1px solid rgb(247, 185, 145); padding: 5px 10px; margin: 5px 10px;">	
			<strong>Prerequisites</strong>
			<p>Create the portlet first.</p>
		</div>
		<p>Select a Standard Invoice Edition portlet to allow users to capture invoices.</p>
		<p>Default value: <span class="guilabel" style="font-weight:bold;">Select</span>. It means "no action".</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<span class="guilabel" style="font-weight:bold;">Edition</span>
	  </td>
	  <td>
		<div style="background: rgb(252, 232, 218); border: 1px solid rgb(247, 185, 145); padding: 5px 10px; margin: 5px 10px;">	
			<strong>Prerequisites</strong>
			<p>Create the portlet first.</p>
		</div>
		<p>Select a Standard Invoice Edition portlet to allow users to edit invoices.</p>
		<p>Default value: <span class="guilabel" style="font-weight:bold;">Select</span>. It means "no action".</p>
	  </td>
	</tr>
	<tr>
	  <td>
	  <p>
		<span class="guilabel" style="font-weight:bold;">List of status authorizing the edition</span>
		</p>
	  </td>
	  <td>
		
		  <ul>
			<li>
			  <p><span class="bold"><strong>Select 1 or more statuses</strong></span>: the action is only available for the statuses selected.</p>
			</li>
			<li>
			  <p>Do not select statuses: The action is never available.</p>
			</li>
		  </ul>
		
	  </td>
	</tr>
	<tr>
	  <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">List of status authorizing the ocr verification</span>
		</p>
	  </td>
	  <td>
		
		  <ul>
			<li>
			  <p>Select 1 or more statuses: the OCR Verify button is only available to users for the statuses selected.</p>
			</li>
			<li>
			  <p>No status selected: the <span class="guibutton">OCR Verify</span> button won't be displayed to portal users.</p>
			</li>
		  </ul>
		
		<div style="background: rgb(252, 232, 218); border: 1px solid rgb(247, 185, 145); padding: 5px 10px; margin: 5px 10px;">	
	<strong>Caution</strong>
				<p>
				  This action is subject to rights.
				</p>
			  
		</div>
	  </td>
	</tr>
  </tbody>
</table>
          

<h1>Invoice viewer portlet</h1>
                       
<table frame="border" rules="all" style="margin:25px;">
  <thead>
	<tr>
	  <th>
		Field
	  </th>
	  <th>
		Description
	  </th>
	  <th>
		Mandatory
	  </th>
	</tr>
  </thead>
  <tbody>
	<tr>
	  <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">Portlet</span>
		</p>
	  </td>
	  <td>
		<div style="background: rgb(252, 232, 218); border: 1px solid rgb(247, 185, 145); padding: 5px 10px; margin: 5px 10px;">	
			<strong>Prerequisites</strong>
			<p>Create the portlet first.</p>
		</div>
		<p>Select the portlet the system must use by default to display invoices:</p>
		
		  <ul>
			<li>
			  <p><span class="guilabel" style="font-weight:bold;">Invoice Compliance</span>: Display information about legal controls.</p>
			</li>
			<li>
			  <p><span class="guilabel" style="font-weight:bold;">Invoice Control</span>: Display information about other controls (invoice reconciliation, workflow, etc).</p>
			</li>
		  </ul>
		
		<p>If you added an Invoice Compliance and Invoice Control portlet to the front-office portal and users have the right to access the two portlets, they can switch from one to another.</p>
		<p>Default value: <span class="guilabel" style="font-weight:bold;">Select</span>. It means "no action".</p>
	  </td>
	  <td>
		Mandatory
	  </td>
	</tr>
  </tbody>
</table>
          
<h1>Settings of quick search</h1>        
      
       
<table frame="border" rules="all" style="margin:25px;">
  <thead>
	<tr>
	  <th>
		<p>Field</p>
	  </th>
	  <th>
		<p>Description</p>
	  </th>
	  <th>
		<p>Mandatory</p>
	  </th>
	</tr>
  </thead>
  <tbody>
	<tr>
	  <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">Number of search fields</span>
		</p>
	  </td>
	  <td>
		[@2ptseano]
	  </td>
	  <td>
		<p>Mandatory</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">Settings of quick search</span>
		</p>
	  </td>
	  <td>
		[@2ptstgquicksea]
	  </td>
	  <td>
		<p>Mandatory</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">Case sensitive </span>
		</p>
	  </td>
	  <td>
		[@2ptseacases]
	  </td>
	  <td>
		<p>Optional</p>
	  </td>
	</tr>
  </tbody>
</table>           
        
<h1>Filter</h1>
              
          
            <table frame="border" rules="all" style="margin:25px;">
              <thead>
                <tr>
                  <th>
                    Field
                  </th>
                  <th>
                    Description
                  </th>
				  <th>
				  Mandatory
				  </th>
                </tr>
              </thead>
              <tbody>
                <tr>
		  <td>
			<p>
			  <span class="guilabel" style="font-weight:bold;">Filter</span>
			</p>
		  </td>
		  <td>
			[@2ptdisfil]
		  </td>
		  <td>
			<p>Optional</p>
		  </td>
		</tr>
                <tr>
                  <td>
                    <p>
                      <span class="guilabel" style="font-weight:bold;">Display only invoices requiring a workflow action</span>
                    </p>
                  </td>
                  <td>
                   
                      <ul>
                        <li>
                          <p><span class="guilabel" style="font-weight:bold;">Yes</span>: Only display the invoices that require a workflow action from the user connected to the portal.</p>
                        </li>
                        <li>
                          <p><span class="guilabel" style="font-weight:bold;">No</span>: Display all invoices.</p>
                        </li>
                      </ul>
                    
                  </td>
				  <td>
				  </td>
                </tr>
                <tr>
                  <td>
                    <p>
                      <span class="guilabel" style="font-weight:bold;">Portlet flow direction</span>
                    </p>
                  </td>
                  <td>
                    [@2ptflowdirection]
                  </td>
                <td>
				</td>
                </tr>
              </tbody>
            </table>
          
          
<h1>Export list</h1>
              
<table frame="border" rules="all" style="margin:25px;">
	  <thead>
		<tr>
		<th>
		  Field
		</th>
		<th>
		  Description
		</th>
		<th>
		Mandatory
		</th>
		</tr>
		</thead>
		<tbody>
		<tr>
		  <td>
			<p>
			  <span class="guilabel" style="font-weight:bold;">Asynchronous download trigger threshold</span>
			</p>
		  </td>
		  <td>
			[@2ptexpasync]		  
			</td>
		  <td>
			<p>Optional</p>
		  </td>
		</tr>
		<tr>
		  <td>
			<p>
			  <span class="guilabel" style="font-weight:bold;">Max export limit</span>
			</p>
		  </td>
		  <td>
		  [@2ptexpmax]
			<p>Maximum value: <span class="guilabel" style="font-weight:bold;">20000</span></p>
		  </td>
		  <td>
			<p>Optional</p>
		  </td>
		</tr>
		</tbody>
	</table>

         
<h1>Export document</h1>
              
<table frame="border" rules="all" style="margin:25px;">
  <thead>
	<tr>
	  <th>
		Field
	  </th>
	  <th>
		Description
	  </th>
	  <th>
		Mandatory
	  </th>
	</tr>
  </thead>
  <tbody>
	<tr>
	  <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">Asynchronous download trigger threshold</span>
		</p>
	  </td>
	  <td>
		[@2ptexpasync]
	  </td>
	  <td>
		<p>Optional</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">Max. export limit</span>
		</p>
	  </td>
	  <td>
		[@2ptexpmaxdoc]
	  </td>
	  <td>
		<p>Optional</p>
	  </td>
	</tr>
  </tbody>
</table>

        
         
<h1>Print</h1>
             
<table frame="border" rules="all" style="margin:25px;">
  <thead>
	<tr>
	  <th>
		Field
	  </th>
	  <th>
		Description
	  </th>
	</tr>
  </thead>
  <tbody>
	<tr>
	  <td>
		<p>
		  <span class="guilabel" style="font-weight:bold;">Manage PDF cache</span>
		</p>
	  </td>
	  <td>
	  [@2ptpdfcache]
	  </td>
	</tr>
  </tbody>
</table>
          
        
      
          
<h1>Manage actions</h1>
              
<table frame="border" rules="all" style="margin:25px;">
	  <thead>
		<tr>
		<th>
		  Field
		</th>
		<th>
		  Description
		</th>
		<th>
		Mandatory
		</th>
		</tr>
		</thead>
	  <tbody>
		<tr>
		  <td>
			<p>
			  <span class="guilabel" style="font-weight:bold;">Add</span>
			</p>
		  </td>
		  <td>
			<p>Add an action.</p>
		  </td>
		  <td>
			<p>Optional</p>
		  </td>
		</tr>
		<tr>
		  <td>
			<p>
			  <span class="bold">
				<strong>Global</strong>
			  </span>
			</p>
		  </td>
		  <td>
		   [@2ptmanactglob] 
			
		  </td>
		  <td>
			<p>Optional</p>
		  </td>
		</tr>
		<tr>
		  <td>
			<p>
			  <span class="bold">
				<strong>Action name</strong>
			  </span>
			</p>
		  </td>
		  <td>
		  [@2ptmanactname]
		   
		  </td>
		  <td>
			<p>Optional</p>
		  </td>
		</tr>
		<tr>
		  <td>
			<p>
			  <span class="bold">
				<strong>List of authorized status</strong>
			  </span>
			</p>
		  </td>
		  <td>
			[@2ptmanactautsta]
		  </td>
		  <td>
			<p>Optional</p>
		  </td>
		</tr>
		<tr>
		  <td>
			<p>
			  <span class="bold">
				<strong>Action</strong>
			  </span>
			</p>
		  </td>
		  <td>
			[@2ptmanactaction]
		  </td>
		  <td>
			<p>Mandatory</p>
		  </td>
		</tr>
		<tr>
		  <td>
			<p>
			  <span class="bold">
				<strong>Template from page</strong>
			  </span>
			</p>
		  </td>
		  <td>
		  [@2ptmanacttemp]
		  </td>
		  <td>
			<p>Optional</p>
		  </td>
		</tr>
		<tr>
		  <td>
			<p>
			  <span class="bold">
				<strong>Delete</strong>
			  </span>
			</p>
		  </td>
		  <td>
			[@2ptmanactdel]
		  </td>
		  <td>
			<p>Optional</p>
		  </td>
		</tr>
	  </tbody>
	</table>
         
          
<h1>Rights</h1>
              
<p>To access these rights go to the <span class="guilabel" style="font-weight:bold;">General</span> tab of the rights matrix.</p>
          
<table frame="border" rules="all" style="margin:25px;">
  <thead>
	<tr>
	  <th>
		<p>Right</p>
	  </th>
	  <th>
		<p>Checkbox name</p>
	  </th>
	  <th>
		Description
	  </th>
	</tr>
  </thead>
  <tbody>
	<tr>
	  <td>
		<p>INVOICE: Diagnostic</p>
	  </td>
	  <td rowspan="14" class="td">
		<p>Create</p>
	  </td>
	  <td>
		<p>Export diagnosis of invoices in <span class="bold"><strong>Refused </strong></span>status.</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>INVOICE: Refuse manually</p>
	  </td>
	  <td>
		<p>Manually refuse invoices in <span class="bold"><strong>To be corrected </strong></span>status.</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>INVOICE: Force</p>
	  </td>
	  <td>
		<p>Send an invoice without correcting it by selecting the <span class="bold"><strong>Force </strong></span>button. </p>
		<p>The invoice must be in<span class="bold"><strong> To be corrected </strong></span>status.</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>INVOICE: Add</p>
	  </td>
	  <td>
		<p>Manually add an invoice to the application.</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>INVOICE: Modify</p>
	  </td>
	  <td>
		<p>Edit invoices.</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>INVOICE: Export</p>
	  </td>
	  <td>
		<p>Export selected invoices.</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>INVOICE: Export list</p>
	  </td>
	  <td>
		<p>Export the list of invoices (global button).</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>INVOICE: Import</p>
	  </td>
	  <td>
		<p>Import invoices in CSV format.</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>INVOICE: Attach</p>
	  </td>
	  <td>
		<p>Attach a file to an invoice from the Invoice, Invoice Compliance, and Invoice Control portlets.</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>INVOICE: Open</p>
	  </td>
	  <td>
		<p>Open invoices in PDF format in another tab.</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>INVOICE: Print</p>
	  </td>
	  <td>
		<p>Download invoices in PDF format.</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>INVOICE: View Attachment</p>
	  </td>
	  <td>
		<p>See the attachments (tax original + digitalization errors).</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>INVOICE: Audit</p>
	  </td>
	  <td>
		<p>Audit invoices.</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>INVOICE: Remove</p>
	  </td>
	  <td>
		<p>Delete invoices</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>INVOICE: Correct</p>
	  </td>
	  <td>
		<p>Read</p>
	  </td>
	  <td>
		<p>Correct invoices.</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>INVOICE: Action X (1 to 10)</p>
	  </td>
	  <td>
		<p>Create</p>
	  </td>
	  <td>
		<p>Execute the action.</p>
	  </td>
	</tr>
	<tr>
	  <td>
		<p>INVOICE: OCR Verify</p>
	  </td>
	  <td>
		<p>Create</p>
	  </td>
	  <td>
		<p>Open the data correction studio.</p>
	  </td>
	</tr>
  </tbody>
</table>