<p>Cette t&acirc;che de processus permet de param&eacute;trer les diff&eacute;rents aspects du traitement de doublons&nbsp;:</p>

<ul>
	<li>r&egrave;gles d&eacute;finissant ce qu&#39;est un doublon dans le contexte du processus consid&eacute;r&eacute;,</li>
	<li>traitement &agrave; appliquer en cas de d&eacute;tection de doublons.</li>
</ul>

<div style="line-height: 20.8px;">
<hr /></div>

<p><strong>Titre*</strong><br />
Titre de la t&acirc;che. Cette information est obligatoire.</p>

<div style="line-height: 20.8px;">
<hr /></div>

<p><strong>D&eacute;sactiver</strong><br />
Cocher cette case pour d&eacute;sactiver la t&acirc;che de processus. Par d&eacute;faut la case n&rsquo;est pas coch&eacute;e et la t&acirc;che&nbsp;est activ&eacute;e.</p>

<div style="line-height: 20.8px;">
<hr /></div>

<p><strong>Filtre des documents du contexte</strong><br />
Ce champ (optionnel) permet de filtrer les documents du contexte d&#39;ex&eacute;cution du processus qui seront soumis &agrave; la t&acirc;che de recherche de doublons.</p>

<p>Si ce champ n&rsquo;est pas renseign&eacute;, tous les documents sont s&eacute;lectionn&eacute;s.</p>

<p>La valeur accept&eacute;e dans ce champ est une requ&ecirc;te BQL destin&eacute;e &agrave; &ecirc;tre ex&eacute;cut&eacute;e pour les documents du contexte. Par exemple, <samp>type=ORDERS</samp> pour indiquer que seules les commandes seront trait&eacute;es.</p>

<p>Pour la syntaxe d&rsquo;une requ&ecirc;te BQL, voir la section<em> Informations compl&eacute;mentaires -</em> <em>Requ&ecirc;tes BQL</em>.</p>

<div>
<hr /></div>

<p><strong>Stockage*</strong><br />
Ce champ indique le syst&egrave;me de stockage consid&eacute;r&eacute; pour la recherche de doublons :&nbsp;</p>

<ul>
	<li>CONTEXT : les documents du contexte du processus m&eacute;tier courant</li>
	<li>DATABASE : la base de donn&eacute;es relationnelle (Oracle ou MySQL)</li>
	<li>INDEX : la base NoSQL (MongoDB)</li>
</ul>

<p>Cette information est obligatoire.</p>

<div>
<hr /></div>

<p><strong>Requ&ecirc;te*</strong><br />
Ce champ permet de sp&eacute;cifier une requ&ecirc;te &agrave; ex&eacute;cuter dans le stockage pr&eacute;alablement renseign&eacute;, et visant, pour chacun des documents examin&eacute;, &agrave; chercher d&#39;&eacute;ventuels doublons. La requ&ecirc;te est param&eacute;trable avec des num&eacute;ros entre parenth&egrave;ses (&agrave; la fa&ccedil;on de placeholders), qui seront remplac&eacute;s &agrave; l&#39;ex&eacute;cution par les vraies valeurs.&nbsp;</p>

<p>Cette information est obligatoire.</p>

<p>La saisie par autocompl&eacute;tion est disponible. Pour plus d&rsquo;information sur la saisie de requ&ecirc;tes BQL, se reporter &agrave; la section <em>Requ&ecirc;tes BQL</em>.</p>

<p>Exemple de requ&ecirc;te&nbsp;:<samp> type = (0) AND from = (1) AND to = (2) AND owners = (3)</samp></p>

<p><u>Remarque</u> : en&nbsp;cas d&rsquo;oubli des parenth&egrave;ses, la requ&ecirc;te est consid&eacute;r&eacute;e comme correcte d&rsquo;un point de vue syntaxique (coche verte). En revanche, elle ne pourra pas &ecirc;tre ex&eacute;cut&eacute;e correctement.</p>

<div>
<hr /></div>

<p><strong>Param&egrave;tres de requ&ecirc;te*</strong><br />
Ce champ est une liste de valeurs repr&eacute;sentant chacune une propri&eacute;t&eacute; du document examin&eacute; dont la valeur doit remplacer un des placeholders de la requ&ecirc;te.</p>

<ul>
	<li>Le formalisme utilis&eacute; est&nbsp;: &quot;valeur&quot; .</li>
	<li>Les diff&eacute;rentes valeurs sont s&eacute;par&eacute;es entre elles par des virgules.</li>
	<li>L&rsquo;ensemble des valeurs doit &ecirc;tre d&eacute;clar&eacute; entre crochets.</li>
</ul>

<p>Par exemple,&nbsp;<samp>[&quot;type&quot;, &quot;from&quot;, &quot;to&quot;, &ldquo;owners&rdquo;]</samp></p>

<p>Dans l&#39;exemple, le placeholder (0) sera remplac&eacute; par la valeur de la propri&eacute;t&eacute; &quot;type&quot; du document, (1) par la valeur de&nbsp;&quot;from&quot;, (2) par la valeur de&nbsp;&quot;to&quot;, et (3) par la valeur de&nbsp;&quot;owners&quot;.</p>

<p>Cette information est obligatoire.</p>

<div>
<hr /></div>

<p><strong>Gestion des doublons</strong><br />
Ce champ permet de sp&eacute;cifier une politique &agrave; appliquer en cas de d&eacute;tection de doublons&nbsp;:</p>

<ul>
	<li>NONE&nbsp;: Rien ne se passe</li>
	<li>IGNORE&nbsp;: Le document courant du contexte est supprim&eacute; du contexte et par cons&eacute;quent ignor&eacute;.</li>
	<li>DEAD&nbsp;: Le document courant du contexte est redirig&eacute; dans la liste des documents rejet&eacute;s (Dead Queue).</li>
	<li>REPLACE&nbsp;: Le document courant remplace le(les) document(s) existant(s) (remont&eacute;(s) par la requ&ecirc;te).</li>
	<li>LINK :&nbsp;cr&eacute;e un lien entre le document et ses doubles. Pour chaque doublon, on d&eacute;finit un document parent (g&eacute;n&eacute;ralement le premier document du contexte).</li>
</ul>

<h3 style="color:#aaa; font-style:italic">Informations compl&eacute;mentaires</h3>

<div style="background:#eee;border:1px solid #ccc;padding:5px 10px;"><strong>Requ&ecirc;tes BQL</strong></div>

<p>[@requetebql]</p>
