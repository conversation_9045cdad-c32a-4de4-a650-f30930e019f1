package com.byzaneo.generix.specifics.ui.edition;

import com.byzaneo.generix.edocument.util.InvoiceAllowOrChargeXcblHelper;
import com.byzaneo.generix.edocument.util.InvoiceXcblHelper;
import com.byzaneo.generix.service.repository.bean.ObjectAttribute;
import com.byzaneo.generix.service.repository.bean.Product;
import com.byzaneo.generix.service.repository.util.product.AllowanceOrCharge;
import com.byzaneo.generix.service.repository.util.product.TypeAttributeValue;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceItemDetailType;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.primefaces.behavior.ajax.AjaxBehavior;
import org.primefaces.component.autocomplete.AutoComplete;
import org.primefaces.event.SelectEvent;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;

import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.buildInvoiceItemDetail;
import static com.byzaneo.generix.edocument.util.InvoiceItemDetailXcblHelper.setCalculationGross;
import static com.byzaneo.generix.edocument.util.InvoiceTaxXcblHelper.createInvoiceTaxtype;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.SHIP_TO;
import static com.byzaneo.generix.edocument.util.UnitOfMeasurement.UOM_EA;
import static com.byzaneo.xtrade.xcbl.util.InvoiceHelper.toComplexBigDecimalType;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.BasisCodeType.MONETARY_AMOUNT;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.BasisCodeType.PERCENT;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.IndicatorCodeType.LINE_ITEM_ALLOWANCE;
import static com.byzaneo.xtrade.xcbl.v4_0.core.core.IndicatorCodeType.LINE_ITEM_CHARGE;
import static java.math.BigDecimal.TEN;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CarrefourInvoiceLinesSectionEditionTest extends AbstractCarrrefourInvoiceSectionsTest {

  private CarrefourInvoiceLinesSectionEdition section;

  @BeforeEach
  public void before() {
    super.before();
    this.section = (CarrefourInvoiceLinesSectionEdition) this.task.getWizard()
        .getSections()
        .get(3);
  }

  @BeforeAll
  public static void beforeAll() {
    AbstractCarrrefourInvoiceSectionsTest.beforeAll();
  }

  @AfterAll
  public static void afterAll() {
    AbstractCarrrefourInvoiceSectionsTest.afterAll();
  }

  @Test
  void validationTest() {

    Invoice invoice = previousSteps(false);

    // third step
    section = (CarrefourInvoiceLinesSectionEdition) this.task.getWizardEdition()
        .getCurrentSection();

    assertFalse(section.validate());// no items

    section.newItem(createProduct("1", "desc", "ref_1", "base_1", "nam_1",
        "KGM", 20.0, 100.0, 120.0,
        new ObjectAttribute(AllowanceOrCharge.ALLOWANCE.toString(), 1, TypeAttributeValue.AMOUNT.toString(), "allowence of 1")));
    section.onSaveItem(null);
    assertTrue(section.validate());

    section.newItem(createProduct("1", null, "ref_2", "base_1", "nam_1",
        "KGM", 20.0, 100.0, 120.0,
        new ObjectAttribute(AllowanceOrCharge.ALLOWANCE.toString(), 1, TypeAttributeValue.AMOUNT.toString(), "allowence of 1")));
    section.onSaveItem(null);
    assertFalse(section.validate());// error_empty_invoice_item_description

    clearItmes(invoice);

    section.newItem(createProduct("1", "desc", "ref_1", "base_1", "nam_1",
        "KGM", null, 100.0, 120.0,
        new ObjectAttribute(AllowanceOrCharge.ALLOWANCE.toString(), 1, TypeAttributeValue.AMOUNT.toString(), "allowence of 1")));
    section.onSaveItem(null);
    assertFalse(section.validate());// error_empty_vat

    clearItmes(invoice);

    section.newItem(createProduct("1", "desc", "ref_1", "base_1", "nam_1",
        "KGM", 20.0, 0.0, null,
        new ObjectAttribute(AllowanceOrCharge.ALLOWANCE.toString(), 1, TypeAttributeValue.AMOUNT.toString(), "allowence of 1")));
    section.onSaveItem(null);
    assertEquals(0, section.getItems()
        .size());// error_alloworchrg_greater_price_item

  }

  private void clearItmes(Invoice invoice) {
    section.getItems()
        .clear();
    InvoiceXcblHelper.setDetails(invoice, section.getItems());
  }

  private Product createProduct(String name, String description, String reference, String baseProductNumber,
      String manuPartNumber, String unitOfMeasurementType, Double vat, Double calculationNet,
      Double calculationGross, ObjectAttribute... objectAttributes) {
    Product product = new Product();
    product.setProductName(name);
    product.setDescription(description);
    product.setReference(reference);
    product.setBaseProductNumber(baseProductNumber);
    product.setManuPartNumber(manuPartNumber);
    product.setUnitOfMeasurementType(unitOfMeasurementType);
    product.setVat(vat);
    product.setCalculationGross(calculationGross);
    product.setCalculationNet(calculationNet);
    product.setObjectAttribute(Arrays.asList(objectAttributes));
    return product;
  }

  private Invoice previousSteps(boolean isTaxFree) {
    // first step
    Invoice invoice = createValidInvoice();
    this.task.getWizardEdition()
        .onNextSection(null);

    // second step
    CarrefourInvoicePartiesSectionEdition partySection = (CarrefourInvoicePartiesSectionEdition) this.task.getWizardEdition()
        .getCurrentSection();
    AutoComplete component = new AutoComplete();
    component.setConverterMessage(SHIP_TO);
    partySection.onSelectAddress(new SelectEvent(component, new AjaxBehavior(), ADDRESS_1));
    this.task.getWizardEdition()
        .onNextSection(null);

    // third step
    CarrefourInvoicePaymentSectionEdition paymentSection = (CarrefourInvoicePaymentSectionEdition) this.task.getWizardEdition()
        .getCurrentSection();
    paymentSection.setTaxFree(isTaxFree);
    paymentSection.onChangeTaxTvaExempt();
    this.task.getWizardEdition()
        .onNextSection(null);
    return invoice;
  }

  @Test
  public void populateTest() {
    Invoice invoice = createValidInvoice();

    InvoiceItemDetailType item = createInvoiceItemDetail(TEN, new BigDecimal("32.2"), new BigDecimal("5.5"), null, null);
    item.getInvoicePricingDetail()
        .getItemAllowancesOrCharges()
        .getAllowOrCharge()
        .add(createAllowOrChargeType("3001000002442", new BigDecimal("0.2")));
    item.getInvoicePricingDetail()
        .getItemAllowancesOrCharges()
        .getAllowOrCharge()
        .add(createAllowOrChargeType("3001000005442", new BigDecimal("22")));
    invoice.getInvoiceDetail()
        .getListOfInvoiceItemDetail()
        .getInvoiceItemDetail()
        .add(item);

    item = createInvoiceItemDetail(TEN, new BigDecimal("62.2"), new BigDecimal("5.5"), null, null);
    item.getInvoicePricingDetail()
        .getItemAllowancesOrCharges()
        .getAllowOrCharge()
        .add(createAllowOrChargeType("3001000002442", new BigDecimal("0.4")));
    invoice.getInvoiceDetail()
        .getListOfInvoiceItemDetail()
        .getInvoiceItemDetail()
        .add(item);

    this.section.populate();
    invoice = this.section.getDocument();
    AllowOrChargeSummaryType allowOrChargeSummary = invoice.getInvoiceSummary()
        .getAllowOrChargeSummary();
    assertNotNull(allowOrChargeSummary);
    assertEquals(2, allowOrChargeSummary.getTotalAllowOrCharge()
        .size());
    for (TotalAllowOrChargeType totalAllowOrCharge : allowOrChargeSummary.getTotalAllowOrCharge()) {
      switch (totalAllowOrCharge.getAllowOrChargeIndicatorCodedOther()) {
      case "3001000002442":
        assertEquals(new BigDecimal("0.60"), totalAllowOrCharge.getSummaryAllowOrCharge()
            .getMonetaryAmount());
        break;
      case "3001000005442":
        assertEquals(new BigDecimal("22"), totalAllowOrCharge.getSummaryAllowOrCharge()
            .getMonetaryAmount());
        break;
      default:
        fail("This charge is unknown " + totalAllowOrCharge.getAllowOrChargeIndicatorCodedOther());
      }
    }
  }

  private InvoiceAllowOrChargeType createAllowOrChargeType(String id, BigDecimal monetaryAmount) {
    InvoiceAllowOrChargeType allowOrCharge = new InvoiceAllowOrChargeType();
    allowOrCharge.setIndicatorCoded(IndicatorCodeType.SERVICE);
    allowOrCharge.setAllowanceOrChargeDescription(new InvoiceAllowOrChgDescType());
    allowOrCharge.getAllowanceOrChargeDescription()
        .setRefID(id);
    allowOrCharge.getAllowanceOrChargeDescription()
        .setServiceCoded("SERVICE_CODED");
    allowOrCharge.setBasisCoded(BasisCodeType.MONETARY_AMOUNT);
    allowOrCharge.setTypeOfAllowanceOrCharge(new InvoiceTypeOfAllowanceOrChargeType());
    allowOrCharge.getTypeOfAllowanceOrCharge()
        .setMonetaryValue(new InvoiceMonetaryValueType());
    allowOrCharge.getTypeOfAllowanceOrCharge()
        .getMonetaryValue()
        .setMonetaryAmount(toComplexBigDecimalType(monetaryAmount));
    return allowOrCharge;
  }

  protected InvoiceItemDetailType createInvoiceItemDetail(BigDecimal qty, BigDecimal grossPrice, BigDecimal tax, BigDecimal allowance,
      BigDecimal charge) {
    InvoiceItemDetailType detail = buildInvoiceItemDetail();
    // -- PRODUCT --
    // Quantity
    detail.getInvoiceBaseItemDetail()
        .getInvoicedQuantity()
        .getQuantityValue()
        .setValue(qty);
    // UOM
    detail.getInvoiceBaseItemDetail()
        .getInvoicedQuantity()
        .getUnitOfMeasurement()
        .setUOMCoded(UOM_EA.getCode());
    // Gross Price
    setCalculationGross(detail, grossPrice, 6, RoundingMode.HALF_EVEN);
    // Tax
    detail.getInvoicePricingDetail()
        .getTax()
        .add(createInvoiceTaxtype());
    detail.getInvoicePricingDetail()
        .getTax()
        .get(0)
        .getTaxPercent()
        .setValue(tax);
    // -- ALLOWANCE OR CHARGE --
    InvoiceAllowOrChargeType allow = InvoiceAllowOrChargeXcblHelper.buildInvoiceAllowOrCharge();
    InvoiceAllowOrChargeType chrge = InvoiceAllowOrChargeXcblHelper.buildInvoiceAllowOrCharge();
    // Allowance
    allow.setIndicatorCoded(LINE_ITEM_ALLOWANCE);
    allow.setBasisCoded(PERCENT);
    allow.getTypeOfAllowanceOrCharge()
        .getPercentageAllowanceOrCharge()
        .getPercent()
        .setValue(allowance);
    // Charge
    chrge.setIndicatorCoded(LINE_ITEM_CHARGE);
    chrge.setBasisCoded(MONETARY_AMOUNT);
    chrge.getTypeOfAllowanceOrCharge()
        .getMonetaryValue()
        .setMonetaryAmount(toComplexBigDecimalType(charge));

    detail.getInvoicePricingDetail()
        .getItemAllowancesOrCharges()
        .getAllowOrCharge()
        .add(allow);
    detail.getInvoicePricingDetail()
        .getItemAllowancesOrCharges()
        .getAllowOrCharge()
        .add(chrge);
    return detail;
  }
}
