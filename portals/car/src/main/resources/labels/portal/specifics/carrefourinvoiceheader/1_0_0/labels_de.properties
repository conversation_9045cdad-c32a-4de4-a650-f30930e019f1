data = Daten
references = Referenzen
invoice_contract_start_placeholder = Vertragsbeginn
order_reference_placeholder = Bestellnummer
incorrect_credit_number = Die Gutschrift-Nr. ist falsch
invoice_contract_end_placeholder = Vertragsende
min_c = (min. 3\u00A0Zeichen)
invoice_order_date = Bestelldatum
CommercialInvoice = Rechnung
invoice_order_number = Bestell-Nr.
incorrect_invoice_number = Die Rechnungs-Nr. ist falsch
invoice_date = Rechnungsdatum
error_unselected_invoice_type_nature = Sie m\u00FCssen den Typ und den Untertyp des Dokuments ausw\u00E4hlen, um zum n\u00E4chsten Schritt zu gelangen
number = Nr.
required_invoice_nature = Bitte geben Sie die Art der Rechnung an
PrepaymentInvoice = Anzahlungsrechnung
error_validity_contract_date = Das Datum des Vertragsbeginns muss vor dem Datum des Vertragsendes liegen.
error_duplicate_invoice_number = Die Rechnungsnummer existiert bereits.
invoice_asn_date = Lieferschein-/Pr\u00FCfberichtdatum/...
required_order_number = Bestell-Nr. erforderlich
invoice_asn_number = Lieferschein-/Pr\u00FCfberichts-Nr./...
invoice_reference_placeholder = Referenzrechnungsnummer
required_supplier_info = Bevor Sie eine Rechnung eingeben k\u00F6nnen, m\u00FCssen Sie die folgenden Informationen in der Partnerakte ausf\u00FCllen.
incorrect = falsch
required_invoice_number = Rechnungs-Nr. erforderlich
GENERALEXPENSE = Allgemeine Kosten
required_asn_number = Lieferschein-Nr. erforderlich
required_order_entry = Technischer Fehler: Bestellreferenz leer \u2013 Bitte wenden Sie sich an den Support.
CreditInvoice = Haben
invoice_due_date = F\u00E4lligkeitsdatum
error_validity_due_date = Das F\u00E4lligkeitsdatum muss vor dem Rechnungsdatum liegen
error_validity_delivery_date = Das Lieferdatum muss vor dem Rechnungsdatum liegen.
invoice_delivery_date = Lieferdatum
number_ref = Referenzrechnungsnummer
required_seller_entry = Technischer Fehler: Lieferantenreferenz leer \u2013 Bitte wenden Sie sich an den Support.
GOODS = Waren
required_credit_number = Gutschrifts-Nr. erforderlich
informations = Informationen