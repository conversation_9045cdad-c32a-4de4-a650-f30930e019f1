<p:outputPanel xmlns="http://www.w3.org/1999/xhtml"
               xmlns:h="http://xmlns.jcp.org/jsf/html"
               xmlns:p="http://primefaces.org/ui"
               xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
               rendered="#{cc.attrs.value.wizard.currentSection.name == 'InvoiceValidationSectionEdition'}"
               styleClass="summary-tab">
    
    <ui:param name="resourceBundle" value="#{cc.attrs.value.getBundle('InvoiceValidationSectionEdition')}"/>
    
    <p:outputPanel rendered="#{empty cc.attrs.value.exceptions}">
        <p:outputPanel styleClass="check text-center"/>
		<p:outputPanel styleClass="text-center">
        	<h:outputText value="#{gnxHandler.label(cc.attrs.value.wizard.currentSection.freeText1Step5, gnxSessionHandler.locale, null, '')}" 
       	 		rendered="#{cc.attrs.value.wizard.currentSection.displayMultipleFreeTextStep5()}" style="font-size:3em !important"/>
        </p:outputPanel>
        <p:spacer rendered="#{cc.attrs.value.wizard.currentSection.displayMultipleFreeTextStep5()}" width="100%" height="25"/>

		<p:outputPanel styleClass="text-center">
	        <h:outputText value="#{gnxHandler.label(cc.attrs.value.wizard.currentSection.freeText2Step5, gnxSessionHandler.locale, null, '')}" 
	        rendered="#{cc.attrs.value.wizard.currentSection.displayMultipleFreeTextStep5()}" style="font-size:2em !important;font-weight: bold;"/>
        </p:outputPanel>
        <p:spacer rendered="#{cc.attrs.value.wizard.currentSection.displayMultipleFreeTextStep5()}" width="100%" height="25"/>

		<p:outputPanel styleClass="text-center">
	        <h:outputText value="#{gnxHandler.label(cc.attrs.value.wizard.currentSection.freeText3Step5, gnxSessionHandler.locale, null, '')}"
	        rendered="#{cc.attrs.value.wizard.currentSection.displayMultipleFreeTextStep5()}" style="font-size:1.5em !important"/>
        </p:outputPanel>
        <p:spacer rendered="#{cc.attrs.value.wizard.currentSection.displayMultipleFreeTextStep5()}" width="100%" height="10"/>
		
		<p:outputPanel styleClass="text-center">
	        <h:outputText value="#{gnxHandler.label(cc.attrs.value.wizard.currentSection.freeText4Step5, gnxSessionHandler.locale, null, '')}"
	        rendered="#{cc.attrs.value.wizard.currentSection.displayMultipleFreeTextStep5()}" style="font-size:3em !important" />
    	</p:outputPanel>
    </p:outputPanel>
    <p:outputPanel rendered="#{not empty cc.attrs.value.exceptions}" styleClass="text-center">
        <p:outputPanel styleClass="error text-center"/>
        <h:outputText value="#{gnxxcblinvlbls.error_in_invoice_generation}" style="font-size:3em !important" />
    </p:outputPanel>
</p:outputPanel>