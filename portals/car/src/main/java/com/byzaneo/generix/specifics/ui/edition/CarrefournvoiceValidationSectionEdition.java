package com.byzaneo.generix.specifics.ui.edition;

import com.byzaneo.generix.xcbl.ui.edition.invoice.InvoiceValidationSectionEdition;

/**
 * Validation wizard section specific for Carrefour
 * 
 * <AUTHOR> <<EMAIL>>
 */
public class CarrefournvoiceValidationSectionEdition extends InvoiceValidationSectionEdition {

  public static final String LABEL_FAMILY = "carxcblinvvdtlbls";
  private static final String LABEL_FAMILY_BASE_NAME = "labels.portal.specifics.carrefourinvoicevalidation.1_0_0.labels";

  private String freeText1Step5;
  private String freeText2Step5;
  private String freeText3Step5;
  private String freeText4Step5;

  @Override
  public String getLabelFamily() {
    return LABEL_FAMILY_BASE_NAME;
  }

  @Override
  public boolean displayMultipleFreeTextStep5() {
    return true;
  }

  public String getFreeText1Step5() {
    return freeText1Step5;
  }

  public void setFreeText1Step5(String freeText1Step5) {
    this.freeText1Step5 = freeText1Step5;
  }

  public String getFreeText2Step5() {
    return freeText2Step5;
  }

  public void setFreeText2Step5(String freeText2Step5) {
    this.freeText2Step5 = freeText2Step5;
  }

  public String getFreeText3Step5() {
    return freeText3Step5;
  }

  public void setFreeText3Step5(String freeText3Step5) {
    this.freeText3Step5 = freeText3Step5;
  }

  public String getFreeText4Step5() {
    return freeText4Step5;
  }

  public void setFreeText4Step5(String freeText4Step5) {
    this.freeText4Step5 = freeText4Step5;
  }
}
