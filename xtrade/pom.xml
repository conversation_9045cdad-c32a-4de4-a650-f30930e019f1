<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.byzaneo.generix</groupId>
    <artifactId>byzaneo</artifactId>
    <version>10.0.0-SNAPSHOT</version>
    <relativePath>../pom.xml</relativePath>
  </parent>
  <groupId>com.byzaneo.xtrade</groupId>
  <artifactId>byzaneo-xtrade</artifactId>
  <packaging>pom</packaging>
  <name>[XTD] Byzaneo xTrade</name>
  <properties>
    <!-- thirds -->
  </properties>
  <modules>
    <module>core</module>
    <module>process</module>
    <module>services</module>
  <module>broker</module>
  </modules>
  <dependencies>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
    </dependency>
    <!-- LINK -->
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-transports-http-jetty</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-junit</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-beans</artifactId>
    </dependency>
  </dependencies>
  <dependencyManagement>
    <dependencies>
      <!-- BYZANEO -->
      <dependency>
        <groupId>com.generix.bom</groupId>
        <artifactId>generix-primefaces-bom</artifactId>
        <version>${bom-prime-version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.byzaneo.commons</groupId>
        <artifactId>byzaneo-commons</artifactId>
        <version>${byzaneo-com-version}</version>
      </dependency>
      <dependency>
        <groupId>com.byzaneo.faces</groupId>
        <artifactId>byzaneo-faces</artifactId>
        <version>${byzaneo-fcs-version}</version>
      </dependency>
      <dependency>
        <groupId>com.byzaneo.link</groupId>
        <artifactId>byzaneo-link</artifactId>
        <version>${byzaneo-lnk-version}</version>
      </dependency>
      <dependency>
        <groupId>com.byzaneo.security</groupId>
        <artifactId>byzaneo-security</artifactId>
        <version>${byzaneo-sec-version}</version>
      </dependency>
      <dependency>
        <groupId>com.byzaneo.angular</groupId>
        <artifactId>byzaneo-angular</artifactId>
        <version>${byzaneo-angular-version}</version>
      </dependency>
      <dependency>
        <groupId>com.byzaneo.task</groupId>
        <artifactId>byzaneo-task-api</artifactId>
        <version>${byzaneo-tsk-version}</version>
      </dependency>
      <dependency>
        <groupId>com.byzaneo.task</groupId>
        <artifactId>byzaneo-task-core</artifactId>
        <version>${byzaneo-tsk-version}</version>
      </dependency>
      <dependency>
        <groupId>com.byzaneo.transform</groupId>
        <artifactId>byzaneo-transform-core</artifactId>
        <version>${byzaneo-trf-version}</version>
      </dependency>
      <dependency>
        <groupId>com.byzaneo.transform</groupId>
        <artifactId>byzaneo-transform-birt</artifactId>
        <version>${byzaneo-trf-version}</version>
      </dependency>
      <dependency>
        <groupId>com.byzaneo.transform</groupId>
        <artifactId>byzaneo-transform-edi</artifactId>
        <version>${byzaneo-trf-version}</version>
      </dependency>
      <!-- XTRADE -->
      <dependency>
        <groupId>com.byzaneo.xtrade</groupId>
        <artifactId>byzaneo-xtrade-core</artifactId>
        <version>${byzaneo-xtd-version}</version>
      </dependency>
      <dependency>
        <groupId>com.byzaneo.xtrade.process</groupId>
        <artifactId>byzaneo-xtrade-process-ant</artifactId>
        <version>${byzaneo-xtd-version}</version>
      </dependency>
      <dependency>
        <groupId>com.byzaneo.xtrade.process</groupId>
        <artifactId>byzaneo-xtrade-process-document</artifactId>
        <version>${byzaneo-xtd-version}</version>
      </dependency>
      <dependency>
        <groupId>com.byzaneo.xtrade.process</groupId>
        <artifactId>byzaneo-xtrade-process-ebics</artifactId>
        <version>${byzaneo-xtd-version}</version>
      </dependency>
      <dependency>
        <groupId>com.byzaneo.xtrade.process</groupId>
        <artifactId>byzaneo-xtrade-process-edi</artifactId>
        <version>${byzaneo-xtd-version}</version>
      </dependency>
      <dependency>
        <groupId>com.byzaneo.xtrade.process</groupId>
        <artifactId>byzaneo-xtrade-process-utils</artifactId>
        <version>${byzaneo-xtd-version}</version>
      </dependency>
      <dependency>
        <groupId>com.byzaneo.xtrade.services</groupId>
        <artifactId>byzaneo-xtrade-service-ipm</artifactId>
        <version>${byzaneo-xtd-version}</version>
      </dependency>
      <dependency>
        <groupId>com.byzaneo.xtrade.services</groupId>
        <artifactId>byzaneo-xtrade-service-product</artifactId>
        <version>${byzaneo-xtd-version}</version>
      </dependency>
      <dependency>
        <groupId>com.byzaneo.xtrade.services</groupId>
        <artifactId>byzaneo-xtrade-service-xcbl</artifactId>
        <version>${byzaneo-xtd-version}</version>
      </dependency>
      <!-- TESTS -->
      <dependency>
        <groupId>com.byzaneo.commons</groupId>
        <artifactId>byzaneo-commons</artifactId>
        <version>${byzaneo-com-version}</version>
        <classifier>tests</classifier>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.byzaneo.xtrade</groupId>
        <artifactId>byzaneo-xtrade-core</artifactId>
        <version>${byzaneo-xtd-version}</version>
        <classifier>tests</classifier>
        <scope>test</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
	<build>
		<plugins>
			<plugin>
				<groupId>com.byzaneo.generix</groupId>
				<artifactId>enforcer-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>rule-execution</id>
						<phase>verify</phase>
						<goals>
							<goal>require-transient-services</goal>
						</goals>
						<configuration>
							<taskDirs>
								<taskDir>${project.basedir}/target/classes/com/byzaneo/xtrade/process/task</taskDir>
								<taskDir>${project.basedir}/target/classes/com/byzaneo/xtrade/process/task</taskDir>
								<taskDir>${project.basedir}/target/classes/com/byzaneo/xtrade/process/task</taskDir>
							</taskDirs>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
