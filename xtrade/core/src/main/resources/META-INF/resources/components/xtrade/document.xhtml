<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml" xmlns:f="http://xmlns.jcp.org/jsf/core" xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:p="http://primefaces.org/ui" xmlns:xtd="http://xmlns.jcp.org/jsf/composite/components/xtrade" xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
	xmlns:cc="http://xmlns.jcp.org/jsf/composite">
	<!-- INTERFACE -->
	<cc:interface name="document">
		<cc:attribute name="embedded" default="false" />
		<cc:attribute name="renderHeader" default="true" />
	</cc:interface>
	<!-- IMPLEMENATION -->
	<cc:implementation>
		<h:panelGroup id="docContainer" layout="block" rendered="#{not xtdDocumentHandler.report and xtdDocumentHandler.document!=null}" style="position: relative;height: 100%;width: 100%;">
			<h:form id="docForm" prependId="false" style="height:100%;">
				<h:panelGroup id="docHeader" layout="block" style="height: 40px;border-bottom: 1px solid #CCC;" rendered="#{cc.attrs.renderHeader}">
					<h:panelGrid id="docHeaderGrid" columns="4" width="100%">
						<h:panelGroup id="docTitle" layout="block" style="width:150px;white-space:nowrap;">
							<h:graphicImage value="#{request.contextPath}/javax.faces.resource/document-#{xtdDocumentHandler.document.status}16.gif.jsf?ln=images/xtrade"
								style="vertical-align: middle;" />
							<h:outputText id="docReference" value="#{xtdDocumentHandler.document.reference}" styleClass="bold"
								style="vertical-align: middle;font-size:14px;padding-left:4px;" />
							<p:tooltip for="docReference">
								<h:panelGrid columns="2" columnClasses="label-l, none">
									<h:outputText value="#{comlbls.id}" />
									<h:outputText value="#{xtdDocumentHandler.document.id}" />
									<h:outputText value="#{xtdlbls.status}" />
									<h:outputText value="#{xtdDocumentHandler.document.status}" />
									<h:outputText value="#{xtdlbls.owners}" />
									<h:outputText value="#{xtdDocumentHandler.document.owners}" />
									<h:outputText value="#{xtdlbls.from}" />
									<h:outputText value="#{xtdDocumentHandler.document.from}" />
									<h:outputText value="#{xtdlbls.to}" />
									<h:outputText value="#{xtdDocumentHandler.document.to}" />
									<h:outputText value="#{xtdlbls.creationDate}" />
									<h:outputText value="#{xtdDocumentHandler.document.creationDate}">
										<f:convertDateTime locale="#{gnxSessionHandler.locale}" type="both" dateStyle="medium" timeStyle="short" timeZone="#{secSessionHandler.timeZone}" />
									</h:outputText>
									<h:outputText value="#{xtdlbls.modificationDate}" />
									<h:outputText value="#{xtdDocumentHandler.document.modificationDate}">
										<f:convertDateTime locale="#{gnxSessionHandler.locale}" type="both" dateStyle="medium" timeStyle="short" timeZone="#{secSessionHandler.timeZone}" />
									</h:outputText>
									<h:outputText value="#{xtdlbls.type}" />
									<h:outputText value="#{xtdDocumentHandler.document.type}" />
								</h:panelGrid>
								<h:panelGroup style="font-size:10pt;margin-top:10px;">
									<h:outputText value="#{xtdDocumentHandler.document.info}" />
									<h:outputText value="#{xtdDocumentHandler.document.warning}" style="color:orange;" />
									<h:outputText value="#{xtdDocumentHandler.document.error}" style="color:red;" />
								</h:panelGroup>
							</p:tooltip>
						</h:panelGroup>
						<!-- DOFS -->
						<h:panelGroup>
							<p:selectOneMenu id="dofSelector" value="#{xtdDocumentHandler.documentFileId}" style="width:100%;"
								onchange="jQuery('.jqsDocumentFileRefresh').click()"
								rendered="#{not xtdDocumentHandler.singleDocumentFile}">
								<f:selectItems value="#{xtdDocumentHandler.documentFileItems}" />
							</p:selectOneMenu>
							<p:commandLink id="dofRefresh" value="." actionListener="#{xtdDocumentHandler.onChangeDocumentFile}" 
								process="@this dofSelector" update="-messages docPreviewGroup" 
								style="text-decoration:none;color:white;" styleClass="jqsDocumentFileRefresh" />
						</h:panelGroup>
						<!-- ACTIONS -->
						<h:panelGrid id="docActions" columns="4" style="float:right;">
							<h:outputLabel value="#{xtdlbls.status}" />
							<p:selectOneMenu id="docStatus" value="#{xtdDocumentHandler.document.status}">
								<f:selectItem itemValue="NONE" itemLabel="#{xtdlbls.NONE}" />
								<f:selectItem itemValue="PENDING" itemLabel="#{xtdlbls.PENDING}" />
								<f:selectItem itemValue="APPROVED" itemLabel="#{xtdlbls.APPROVED}" />
								<f:selectItem itemValue="SENT" itemLabel="#{xtdlbls.SENT}" />
								<f:selectItem itemValue="WARNING" itemLabel="#{xtdlbls.WARNING}" />
								<f:selectItem itemValue="ERROR" itemLabel="#{xtdlbls.ERROR}" />
								<f:selectItem itemValue="ARCHIVED" itemLabel="#{xtdlbls.ARCHIVED}" />
								<f:selectItem itemValue="REMOVED" itemLabel="#{xtdlbls.REMOVED}" />
							</p:selectOneMenu>
							<p:commandButton icon="ui-icon-disk" actionListener="#{xtdDocumentHandler.onSave}" update="-messages docTitle" alt="Save" title="Save" />
							<p:commandButton icon="ui-icon-arrowthick-1-s" ajax="false" alt="Download" title="Download">
								<p:fileDownload value="#{xtdDocumentHandler.streamedContent}" />
							</p:commandButton>
						</h:panelGrid>
					</h:panelGrid>
				</h:panelGroup>
				<h:panelGroup id="docPreviewGroup" layout="block" style="position:absolute;top:#{cc.attrs.renderHeader ? '41px' : '0'};bottom:0;left:0;right:0;overflow:hidden;" styleClass="jqsDofPreviewDiv">
					<iframe id="docFrame" src="#{xtdDocumentHandler.documentFileRequestPath}&amp;download=false" width="100%" height="100%" frameborder="0" style="margin:0;padding:0;" />
					<h:panelGroup rendered="#{cc.attrs.embedded}">
						<script type="text/javascript">
							var doframe = jQuery(".jqsDofPreviewDiv");
							doframe.ready(function() { 
								doframe.height(jQuery(window).height()-(doframe.position().top + jQuery("#header").height() + jQuery("#menubar").height() + jQuery("#footer").height() + 5)); 
							});
						</script>
					</h:panelGroup>
				</h:panelGroup>
			</h:form>
		</h:panelGroup>
		<!-- REPORT -->
		<h:panelGroup rendered="#{xtdDocumentHandler.report}">
			<h:outputText value="#{xtdDocumentHandler.reportHtml}" escape="false" />
		</h:panelGroup>
	</cc:implementation>
</ui:component>