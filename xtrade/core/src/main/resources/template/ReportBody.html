<div id="report" style="background-color: #FFFFFF; font-family: Arial, Helvetica, sans-serif; font-size: 10pt; color: #333333; margin: 0; padding: 0;">
	<div style="margin: 0px; padding: 0px 15 0px 15px; width: 100%;">
		<table border="0" cellspacing="0" cellpadding="0">
			<tr>
				<td width="40" align="center"><img th:attr="src=@{${imagesUrl+'/status-'+report.Status+'32.png'}}" border="0" align="absmiddle"
					th:alt="${report.status}" th:title="${report.status}" /></td>
				<td valign="middle" nowrap="nowrap">
					<h1 style="margin: 5px 0px 5px 0px; padding: 0px; font-size: 150%; color: #334d55; margin: 0px; padding: 0px;">
						[[${report.processInstanceId}]] le [[${#dates.format(report.creationDate, 'dd/MMM/yyyy')}]] &#224; [[${#dates.format(report.creationDate, 'HH:mm:ss.SSS')}]] en [[${report.formattedDuration}]]</h1>
				</td>
			</tr>
		</table>
	</div>
	<div id="content" style="border-top: 1px solid #cccccc; border-bottom: 1px solid #cccccc; float: none; margin: 0 px 15 0px 15px; padding: 10px;">
		<div id="contentblock" style="margin-bottom: 35px;">
			<h2 style="margin: 15px 0px 10px 0px; font-size: 120%; color: #006699;">Informations g&#233;n&#233;rales :</h2>
			<table width="70%" border="0" cellspacing="0" cellpadding="0">
				<tr>
					<td nowrap="nowrap"><b>Instance Id.</b></td>
					<td width="10px" align="center"><b>:</b></td>
					<td nowrap="nowrap" th:text="${report.processInstanceId}" />
					<td nowrap="nowrap"><b>Status</b></td>
					<td align="center"><b>:</b></td>
					<td nowrap="nowrap" th:text="${report.status}" />
				</tr>
				<tr>
					<td nowrap="nowrap"><b>Date</b></td>
					<td width="10px" align="center"><b>:</b></td>
					<td nowrap="nowrap" th:text="${#dates.format(report.creationDate, 'dd/MMM/yyyy HH:mm:ss.SSS')}" />
					<td nowrap="nowrap"><b>Dur&#233;e</b></td>
					<td align="center"><b>:</b></td>
					<td nowrap="nowrap" th:text="${report.formattedDuration}" />
				</tr>
				<tr>
					<td nowrap="nowrap"><b>Nb. Documents</b></td>
					<td width="10px" align="center"><b>:</b></td>
					<td nowrap="nowrap" th:text="${report.documentCount}" />
					<td nowrap="nowrap"><b>Nb. Deads</b></td>
					<td width="10px" align="center"><b>:</b></td>
					<td nowrap="nowrap" th:text="${report.deadCount}" />
				</tr>
			</table>
		</div>
        <!-- Logs from html log file -->
        <div id="logsContent" th:if="${logs!=null}">
            <h2 style="margin: 15px 0px 10px 0px; font-size: 120%; color: #006699;">Logs :</h2>
            <div th:remove="tag" th:utext="${logs}" />
        </div>
		<div id="contentblock" th:if="${report.logs!=null and report.logs.logEventCount > 0}" style="margin-bottom: 35px;">
			<h2 style="margin: 15px 0px 10px 0px; font-size: 120%; color: #006699;">Actions execut&#233;es :</h2>
			<table width="90%" border="0" cellspacing="0" cellpadding="0">
				<th:block th:if="${report.splittedLogs!=null and report.splittedLogs.size() > 1}">
					<th:block th:each="log : ${report.splittedLogs}">
						<th:block th:if="${log.name!=null}">
							<tr>
								<td width="20px"><img th:attr="src=@{${imagesUrl+'/status-'+log.status+'16.png'}}" th:title="${log.status}" th:alt="${log.status}" border="0"
									align="absmiddle" style="padding: 0 2px 2px 0;" /></td>
								<td nowrap="nowrap"><b th:text="${log.name}" /></td>
								<td nowrap="nowrap" align="right" valign="top"><i><b th:text="${log.formattedDuration}" /></i></td>
							</tr>
							<tr th:if="${log.logEventList.size() > 0}">
								<td>&#160;</td>
								<td colspan="2">
									<table width="100%" border="0" cellspacing="0" cellpadding="0">
										<th:block th:each="loge : ${log.logEventList}">
											<tr>
												<td valign="top" width="20px"><img th:attr="src=@{${imagesUrl+'/status-'+loge.level+'16.png'}}" th:title="${loge.level}" th:alt="${loge.level}"
													border="0" align="absmiddle" style="padding: 0 2px 2px 0;" /></td>
												<td th:utext="${loge.messageAsHtml}" />
											</tr>
											<tr th:if="${loge.throwable!=null}">
												<td>&#160;</td>
												<td><span style="font-style: italic; font-size: 9px;" th:utext="${loge.throwableAsHtml}" /></td>
											</tr>
										</th:block>
									</table>
								</td>
							</tr>
						</th:block>
						<tr th:if="${log.name==null and log.logEventList.size() > 0}">
							<td colspan="3">
								<table width="100%" border="0" cellspacing="0" cellpadding="0">
									<th:block th:each="loge : ${log.logEventList}">
										<tr>
											<td valign="top" width="20px"><img th:attr="src=@{${imagesUrl+'/status-'+loge.level+'16.png'}}" th:title="${loge.level}" th:alt="${loge.level}"
												border="0" align="absmiddle" style="padding: 0 2px 2px 0;" /></td>
											<td th:utext="${loge.messageAsHtml}" />
										</tr>
										<tr th:if="${loge.throwable!=null}">
											<td>&#160;</td>
											<td><span style="font-style: italic; font-size: 9px;" th:utext="${loge.throwableAsHtml}" /></td>
										</tr>
									</th:block>
								</table>
							</td>
						</tr>
					</th:block>
				</th:block>
				<th:block th:if="${report.splittedLogs==null or report.splittedLogs.size() &lt; 2}">
					<th:block th:each="loge : ${report.logs.logEventList}">
						<tr>
							<td valign="top" width="20px"><img th:attr="src=@{${imagesUrl+'/status-'+loge.level+'16.png'}}" th:title="${loge.level}" th:alt="${loge.level}"
								border="0" align="absmiddle" style="padding: 0 2px 2px 0;" /></td>
							<td th:utext="${loge.messageAsHtml}" />
							<td nowrap="nowrap" align="right" valign="top"><i th:text="${loge.formattedDate}" /></td>
						</tr>
						<tr th:if="${loge.throwable!=null}">
							<td>&#160;</td>
							<td><span style="font-style: italic; font-size: 9px;" th:utext="${loge.throwableAsHtml}" /></td>
						</tr>
					</th:block>
				</th:block>
			</table>
		</div>
		<div id="contentblock" th:if="${report.documents!=null and report.documents.size() > 0}" style="margin-bottom: 35px;">
			<h2 style="margin: 15px 0px 10px 0px; font-size: 120%; color: #006699;">Documents trait&#233;s :</h2>
			<table style="border-bottom: 1px solid #C9C2B7; font-size: 12px; margin-top: 2px; margin-left: auto; margin-right: auto;" border="0" cellpadding="2"
				cellspacing="0" width="95%">
				<thead>
					<tr>
						<th style="background-color: #666666; color: #FFFFFF; font-weight: bold;">&#160;</th>
						<th style="background-color: #666666; color: #FFFFFF; font-weight: bold;">R&#233;f&#233;rence</th>
						<th style="background-color: #666666; color: #FFFFFF; font-weight: bold;">Type</th>
						<th style="background-color: #666666; color: #FFFFFF; font-weight: bold;">Date</th>
						<th style="background-color: #666666; color: #FFFFFF; font-weight: bold;">Emetteur</th>
						<th style="background-color: #666666; color: #FFFFFF; font-weight: bold;">Destinataire</th>
					</tr>
				</thead>
				<tbody>
					<th:block th:each="doc : ${report.documents}">
						<tr class="row">
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="center"><img th:attr="src=@{${imagesUrl+'/document-'+doc.status+'16.gif'}}"
								th:title="${doc.status}" th:alt="${doc.status}" border="0" /></td>
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="left" nowrap="nowrap"><a
								th:if="${documentViewerUrl!=null and documentViewerUrl.trim().length()>0}" th:href="@{${baseUrl}+${documentViewerUrl}+${doc.getId()}}" target="_top"
								th:text="${doc.reference}"> <span th:if="${showDocumentsUrls}"> [[[${baseUrl}]][[${documentViewerUrl}]][[${doc.id}]]]</span>
							</a> <span th:if="${documentViewerUrl==null and documentViewerUrl.trim().length()==0}" th:text="${doc.reference}" /></td>
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="center" nowrap="nowrap" th:text="${doc.type}" />
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="left" nowrap="nowrap" th:text="${#dates.format(doc.creationDate, 'dd/MM/yyyy HH:mm')}" />
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="left" nowrap="nowrap" th:text="${doc.from}">&#160;</td>
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="left" nowrap="nowrap" th:text="${doc.to}">&#160;</td>
						</tr>
						<tr class="row" th:if="${doc.children!=null and doc.children.size()>0}" th:each="child : ${doc.children}">
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="center"><img th:attr="src=@{${imagesUrl+'/document-'+child.status+'16.gif'}}"
								th:title="${child.status}" th:alt="${child.status}" border="0" /></td>
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="left" nowrap="nowrap"><a
								th:if="${documentViewerUrl!=null and documentViewerUrl.trim().length()>0}" th:href="@{${baseUrl}+${documentViewerUrl}+${child.getId()}}" target="_top"
								th:text="${child.reference}"> <span th:if="${showDocumentsUrls}"> [[[${baseUrl}]][[${documentViewerUrl}]][[${child.id}]]]</span>
							</a> <span th:if="${documentViewerUrl==null and documentViewerUrl.trim().length()==0}" th:text="${child.reference}" /></td>
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="center" nowrap="nowrap" th:text="${child.type}" />
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="left" nowrap="nowrap"
								th:text="${#dates.format(child.creationDate, 'dd/MM/yyyy HH:mm')}" />
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="left" nowrap="nowrap" th:text="${child.from}">&#160;</td>
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="left" nowrap="nowrap" th:text="${child.to}">&#160;</td>
						</tr>
					</th:block>
				</tbody>
			</table>
		</div>
		<div id="contentblock" th:if="${report.deads!=null and report.deads.size()>0}" style="margin-bottom: 35px;">
			<h2 style="margin: 15px 0px 10px 0px; font-size: 120%; color: #006699;">Dead Documents :</h2>
			<table style="border-bottom: 1px solid #C9C2B7; font-size: 12px; margin-top: 2px; margin-left: auto; margin-right: auto;" border="0" cellpadding="2"
				cellspacing="0" width="95%">
				<thead>
					<tr>
						<th style="background-color: #666666; color: #FFFFFF; font-weight: bold;">&#160;</th>
						<th style="background-color: #666666; color: #FFFFFF; font-weight: bold;">R&#233;f&#233;rence</th>
						<th style="background-color: #666666; color: #FFFFFF; font-weight: bold;">Type</th>
						<th style="background-color: #666666; color: #FFFFFF; font-weight: bold;">Date</th>
						<th style="background-color: #666666; color: #FFFFFF; font-weight: bold;">Emetteur</th>
						<th style="background-color: #666666; color: #FFFFFF; font-weight: bold;">Destinataire</th>
					</tr>
				</thead>
				<tbody>
					<th:block th:each="doc : ${report.deads}">
						<tr class="row">
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="center"><img th:attr="src=@{${imagesUrl+'/document-'+doc.status+'16.gif'}}"
								th:title="${doc.status}" th:alt="${doc.status}" border="0" /></td>
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="left" nowrap="nowrap" th:text="${doc.reference}" />
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="center" nowrap="nowrap" th:text="${doc.type}" />
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="left" nowrap="nowrap" th:text="${#dates.format(doc.creationDate, 'dd/MM/yyyy HH:mm')}" />
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="left" nowrap="nowrap" th:text="${doc.from}">&#160;</td>
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="left" nowrap="nowrap" th:text="${doc.to}">&#160;</td>
						</tr>
						<tr class="row" th:if="${doc.children!=null and doc.children.size()>0}" th:each="child : ${doc.children}">
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="center"><img th:attr="src=@{${imagesUrl+'/document-'+child.status+'16.gif'}}"
								th:title="${child.status}" th:alt="${child.status}" border="0" /></td>
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="left" nowrap="nowrap" th:text="${child.reference}" />
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="center" nowrap="nowrap" th:text="${child.type}" />
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="left" nowrap="nowrap"
								th:text="${#dates.format(child.creationDate, 'dd/MM/yyyy HH:mm')}" />
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="left" nowrap="nowrap" th:text="${child.from}">&#160;</td>
							<td style="padding: 1px; border-bottom: 1px solid #C9C2B7;" align="left" nowrap="nowrap" th:text="${child.to}">&#160;</td>
						</tr>
					</th:block>
				</tbody>
			</table>
		</div>
		<div id="contentblock" th:if="${report.variables and report.variables.size()>0}" style="margin-bottom: 35px;">
			<h2 style="margin: 15px 0px 10px 0px; font-size: 120%; color: #006699;">Contexte :</h2>
			<table border="0" cellspacing="0" cellpadding="0" style="width: 95%;table-layout: fixed;">
				<tr th:each="var : ${report.variables.entrySet()}">
					<th valign="top" style="text-overflow:ellipsis;white-space:nowrap;overflow:hidden;width:28%;max-width:28%;"align="left" class="row" nowrap="nowrap"
						th:text="${var.key}" />
					<th valign="top" class="row" width="10px" align="center">:</th>
					<td valign="top" th:if="${var.value==null}">&#160;</td>
					<td valign="top" style="text-overflow:ellipsis;white-space:nowrap;overflow:hidden;width:70%;max-width:70%;"
						th:if="${var.value!=null and not var.key.contains('password')}"
						th:text="${var.value!=null and not var.key.contains('password')} ? ${var.value.toString()} : '********'" />
				</tr>
			</table>
		</div>
	</div>
</div>
