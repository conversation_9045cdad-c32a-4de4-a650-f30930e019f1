package com.byzaneo.xtrade.process.reprocess;

import lombok.Getter;

import java.io.*;
import java.util.List;

@Getter
public class Reprocess implements Serializable {

  /**
   * 
   */
  private static final long serialVersionUID = -964713024175104812L;

  private final ReprocessType reprocessType;

  private final String processDeadQueue;

  private final String processInputBackupDir;

  private final String processAttachBackupDir;

  private final String processBackupId;

  private final String s3DownloadDir;

  private final List<File> files;

  public Reprocess(ReprocessType reprocessType, String processDeadQueue, String processInputBackupDir, String processAttachBackupDir,
      List<File> files, String processBackupId, String s3DownloadDir) {
    this.reprocessType = reprocessType;
    this.processDeadQueue = processDeadQueue;
    this.processInputBackupDir = processInputBackupDir;
    this.processAttachBackupDir = processAttachBackupDir;
    this.files = files;
    this.processBackupId = processBackupId;
    this.s3DownloadDir = s3DownloadDir;
  }

  @Override
  public String toString() {
    return "Reprocess [reprocessType=" + reprocessType + ", processDeadQueue=" + processDeadQueue + ", processInputBackupDir=" +
        processInputBackupDir + "]";
  }

}
