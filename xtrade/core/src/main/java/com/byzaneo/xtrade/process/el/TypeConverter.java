package com.byzaneo.xtrade.process.el;

import static com.byzaneo.commons.util.I18NHelper.toLocale;
import static com.byzaneo.xtrade.process.VariableHelper.createExpression;
import static java.lang.String.format;

import java.util.Locale;

import javax.el.ELException;

import org.activiti.engine.delegate.Expression;
import org.activiti.engine.impl.el.FixedValue;

import de.odysseus.el.misc.TypeConverterImpl;

/**
 * JUEL based type converter. {@link Expression} type conversion has been added compared to the default JUEL implementation.
 *
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Jun 20, 2013
 * @since 6.0
 */
public class TypeConverter extends TypeConverterImpl {
  private static final long serialVersionUID = -3806684443501569707L;

  /** @see de.odysseus.el.misc.TypeConverterImpl#coerceToType(java.lang.Object, java.lang.Class) */
  @Override
  protected Object coerceToType(Object value, Class<?> type) {

    // -- EXPRESSION --
    if (Expression.class.isAssignableFrom(type)) {
      if (value == null)
        return null;
      if (value instanceof Expression)
        return value;
      return createExpression(value.toString());
    }

    if (value instanceof FixedValue) {
      return resolveFixedValue(value, type);
    }

    // -- LOCALE --
    if (Locale.class.isAssignableFrom(type)) {
      if (value == null)
        return null;
      if (value instanceof String)
        return toLocale((String) value);
      if (value instanceof Locale)
        return (Locale) value;
    }

    // -- FALL-BACK --
    return super.coerceToType(value, type);
  }

  public static Object resolveFixedValue(final Object value, final Class<?> type) {
    Object fixedValue = ((FixedValue) value).getValue(null);
    if (fixedValue == null)
      return null;
    if (type == Object.class || Expression.class.isAssignableFrom(type))
      return value;
    if (type == String.class)
      return fixedValue.toString();
    if (type == Boolean.class || type == boolean.class)
      return Boolean.valueOf(fixedValue.toString());
    if (type == Integer.class || type == int.class)
      return Integer.valueOf(fixedValue.toString());
    if (type == Long.class || type == long.class)
      return Long.valueOf(fixedValue.toString());
    throw new ELException(format("Fixed value conversion not supported for value '%s' with type '%s' to requested type '%s'",
        value, value.getClass(), type));
  }

}
