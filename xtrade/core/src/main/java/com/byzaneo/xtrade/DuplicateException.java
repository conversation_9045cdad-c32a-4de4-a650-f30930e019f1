package com.byzaneo.xtrade;

import com.byzaneo.commons.service.ServiceException;

/**
 * Duplicate variable exception.
 * 
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Aug 14, 2014
 * @since 7.0 XTD-417
 */
public class DuplicateException extends ServiceException {
  private static final long serialVersionUID = 8816973840294255401L;

  public DuplicateException(String message, Object... args) {
    super(message, args);
  }

  public DuplicateException(Throwable cause, String message, Object... args) {
    super(cause, message, args);
  }

  public DuplicateException(String message, Throwable cause) {
    super(message, cause);
  }

  public DuplicateException(String messageKey, String defaultMessage, Throwable cause, Object... args) {
    super(messageKey, defaultMessage, cause, args);
  }

  public DuplicateException(Throwable cause) {
    super(cause);
  }

}
