package com.byzaneo.xtrade.api;

import java.io.OutputStream;

import com.byzaneo.commons.bean.FileType;

/**
 * Producer representation dedicated to generate the acknowledgement of a {@link Report}.
 * 
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Mar 25, 2015
 * @since 8.0 XTD-429
 */
public interface ReportAcknowledgementProducer {

  /**
   * @param acknowledgement to produce
   * @param output where the acknowledgement of the report will be produced
   */
  void produce(ReportAcknowledgement acknowledgement, OutputStream output);

  /**
   * @return the file type of the acknowledgement of the report
   */
  FileType getFileType();
}
