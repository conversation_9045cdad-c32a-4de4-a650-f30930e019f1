package com.byzaneo.xtrade.dao.hibernate;

import static java.lang.Integer.MAX_VALUE;
import static org.springframework.data.domain.Sort.Direction.ASC;
import static org.springframework.data.domain.Sort.Direction.DESC;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import javax.persistence.criteria.Predicate;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.MatchMode;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.byzaneo.commons.dao.DataAccessException;
import com.byzaneo.commons.dao.hibernate.GenericJpaDAO;
import com.byzaneo.xtrade.bean.IOFile;
import com.byzaneo.xtrade.bean.IOFile_;
import com.byzaneo.xtrade.dao.FileDAO;

/**
 * <AUTHOR>
 * @company Byzaneo
 * @version CVS $Revision: 1.6 $ $Date: 2008-06-01 14:35:17 $
 */
@Repository(FileDAO.DAO_NAME)
public class FileDAOImpl extends GenericJpaDAO<IOFile, Long>
    implements FileDAO {

  /** @see com.byzaneo.xtrade.dao.FileDAO#findUniqueByName(java.lang.String) */
  @Override
  @Transactional(readOnly = true)
  public IOFile findUniqueByName(final String name) throws DataAccessException {
    return this.getRepository()
        .findOne((root, query, cb) -> cb.equal(root.get(IOFile_.name), name))
        .orElse(null);
  }

  /** @see com.byzaneo.xtrade.dao.FileDAO#findByNameLike(java.lang.String) */
  @Override
  @Transactional(readOnly = true)
  public List<IOFile> findByNameLike(String likename) throws DataAccessException {
    return this.findByNameLike(likename, null, null, null, true, -1);
  }

  /** @see com.byzaneo.xtrade.dao.FileDAO#findByNameOnTimePeriod(java.lang.String, java.util.Date, java.util.Date) */
  @Override
  @Transactional(readOnly = true)
  public List<IOFile> findByNameOnTimePeriod(String likename, Date from, Date to) throws DataAccessException {
    return this.findByNameLike(likename, from, to, null, true, -1);
  }

  /** @see com.byzaneo.xtrade.dao.FileDAO#findByNameLike(java.lang.String, java.lang.String, boolean, int) */
  @Override
  @Transactional(readOnly = true)
  public List<IOFile> findByNameLike(String likename, String orderBy, boolean ascending, int maxNumber) throws DataAccessException {
    return this.findByNameLike(likename, null, null, orderBy, ascending, maxNumber);
  }

  /**
   * @see com.byzaneo.xtrade.dao.FileDAO#findByNameLike(java.lang.String, java.util.Date, java.util.Date, java.lang.String, boolean, int)
   */
  @Override
  @Transactional(readOnly = true)
  public List<IOFile> findByNameLike(final String likename, final Date from, final Date to, final String orderBy, final boolean ascending,
      final int maxNumber) throws DataAccessException {
    return StringUtils.isBlank(likename)
        ? Collections.emptyList()
        : this.getRepository()
            .findAll((root, query, cb) ->
        {
              List<Predicate> predicates = new ArrayList<>(3);

              // name
              predicates.add(cb.like(root.get(IOFile_.name), MatchMode.ANYWHERE.toMatchString(likename)));

              // period
              if (from != null)
                predicates.add(cb.greaterThanOrEqualTo(root.get(IOFile_.creationDate), from));
              if (to != null)
                predicates.add(cb.lessThanOrEqualTo(root.get(IOFile_.creationDate), to));

              return cb.and(predicates.toArray(new Predicate[predicates.size()]));
            },
                PageRequest.of(0,
                    maxNumber > 0 ? maxNumber : MAX_VALUE,
                    ascending ? ASC : DESC,
                    orderBy == null ? IOFile_.name.getName() : orderBy))
            .getContent();
  }
}
