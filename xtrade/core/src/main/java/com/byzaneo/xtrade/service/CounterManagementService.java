package com.byzaneo.xtrade.service;

import java.util.List;

import com.byzaneo.commons.dao.DataAccessException;
import com.byzaneo.xtrade.bean.CounterManagement;

public interface CounterManagementService {

  public static final String SERVICE_NAME = "gnxCounterManagementService";

  CounterManagement findCounterManagementById(Long id);

  List<CounterManagement> getCouterManagementsWithOwnConfiguration(String owner, String currentPageId);

  void saveCounterManagement(CounterManagement counterManagement);

  CounterManagement findByPageId(String owner, String pageId);

  boolean removeCounterManagement(Long id) throws DataAccessException;

  boolean isDocumentCounterUsed(Long id);

}
