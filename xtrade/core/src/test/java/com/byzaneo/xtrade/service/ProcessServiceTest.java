package com.byzaneo.xtrade.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.ByteArrayInputStream;
import java.util.*;
import java.util.concurrent.Future;

import org.activiti.engine.RepositoryService;
import org.activiti.engine.repository.*;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import com.byzaneo.commons.bean.Job;
import com.byzaneo.commons.service.ExecutorService;
import com.byzaneo.commons.util.LRUMap;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.service.ProcessServiceImpl.ProcessExecution;

class ProcessServiceTest {

  @Spy
  @InjectMocks
  private ProcessServiceImpl service;

  @Mock
  private RepositoryService repositoryService;

  @Mock
  private ExecutorService executorService;

  @Mock
  private LRUMap<String, List<IOFile>> processResources;

  @Mock
  private LRUMap<String, List<ProcessExecution>> executions;

  @Mock
  private LRUMap<String, Future<?>> processQueue;

  @BeforeEach
  void before() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  void removePreviousProcessWhenExceptionThrow() {
    ProcessDefinition pd = getProcessDefinition();
    Job job = Mockito.mock(Job.class);
    Mockito.when(service.getJob(pd.getId()))
        .thenReturn(job);
    Mockito.doThrow(new RuntimeException())
        .when(repositoryService)
        .deleteDeployment(Mockito.anyString(), Mockito.anyBoolean());

    ((ProcessServiceImpl) service).removePreviousProcess(pd);

    Mockito.verify(repositoryService, Mockito.times(1))
        .deleteDeployment("DEPLOYMENT-ID-XXXXX", true);
    Mockito.verify(executorService, Mockito.times(0))
        .suppress(job, true);
  }

  @Test
  void removePreviousProcess() {
    ProcessDefinition pd = getProcessDefinition();
    Job job = Mockito.mock(Job.class);
    Mockito.when(service.getJob("ID-XXXXX"))
        .thenReturn(job);
    Mockito.when(service.getJob("ID-XXXXX"))
        .thenReturn(job);
    Mockito.doNothing()
        .when(repositoryService)
        .deleteDeployment(Mockito.anyString(), Mockito.anyBoolean());
    Mockito.doNothing()
        .when(executorService)
        .suppress(Mockito.any(Job.class), Mockito.anyBoolean());
    ((ProcessServiceImpl) service).removePreviousProcess(pd);

    Mockito.verify(repositoryService, Mockito.times(1))
        .deleteDeployment("DEPLOYMENT-ID-XXXXX", true);
    Mockito.verify(executorService, Mockito.times(1))
        .suppress(job, true);
  }

  @Test
  void getProcessResourcesWhenKeyIsEmpty() {
    assertTrue(CollectionUtils.isEmpty(service.getProcessResources(null, null)));
  }

  @Test
  void getProcessResourcesWhenResourcesIsCached() {
    List<IOFile> files = new ArrayList<>();
    IOFile file = Mockito.mock(IOFile.class);
    files.add(file);
    Mockito.doReturn(Boolean.TRUE)
        .when(processResources)
        .containsKey(Mockito.anyString());
    Mockito.doReturn(files)
        .when(processResources)
        .get(Mockito.anyString());

    assertEquals(file, service.getProcessResources("key", null)
        .get(0));
  }

  @Test
  void getProcessResourcesWhenResourcesIsNotCached() {
    ProcessDefinition pd = getProcessDefinition();
    List<IOFile> files = new ArrayList<>();
    IOFile file = Mockito.mock(IOFile.class);
    files.add(file);
    Mockito.doReturn(Boolean.FALSE)
        .when(processResources)
        .containsKey(Mockito.anyString());
    Mockito.doReturn(pd)
        .when(service)
        .getProcessDefinition(Mockito.anyString());
    Mockito.doReturn(Arrays.asList("resource 1"))
        .when(repositoryService)
        .getDeploymentResourceNames(Mockito.anyString());
    Mockito.doReturn(file)
        .when(service)
        .getFileByResourceName(Mockito.anyString(), Mockito.anyString(), Mockito.anyString());

    assertEquals(1, service.getProcessResources("Key", null)
        .size());
    Mockito.verify(processResources)
        .put("Key", files);
  }

  @Test
  void cleanProcessResourcesByKeyWhenResourceIsCached() {
    Mockito.doReturn(Boolean.TRUE)
        .when(processResources)
        .containsKey(Mockito.anyString());
    DeploymentDescriptor dd = getDeploymentDescriptor();
    service.cleanProcessResourcesByKey(dd);

    Mockito.verify(processResources)
        .remove("Key");
  }

  @Test
  void cleanProcessResourcesByKeyWhenResourceIsNotCached() {
    Mockito.doReturn(Boolean.FALSE)
        .when(processResources)
        .containsKey(Mockito.anyString());
    DeploymentDescriptor dd = getDeploymentDescriptor();
    service.cleanProcessResourcesByKey(dd);

    Mockito.verify(processResources, Mockito.never())
        .remove("Key");
  }

  @Test
  void deployAndCleanProcessResourcesByKey() {
    Deployment deployment = Mockito.mock(Deployment.class);
    ProcessDefinition pd = getProcessDefinition();
    Mockito.doReturn("id")
        .when(deployment)
        .getId();
    Mockito.doReturn(deployment)
        .when(service)
        .deployStream(Mockito.any(ByteArrayInputStream.class), Mockito.anyString());
    ProcessDefinitionQuery pdquery = Mockito.mock(ProcessDefinitionQuery.class);
    Mockito.doReturn(pdquery)
        .when(repositoryService)
        .createProcessDefinitionQuery();
    Mockito.doReturn(pdquery)
        .when(pdquery)
        .deploymentId(Mockito.anyString());
    Mockito.doReturn(pd)
        .when(pdquery)
        .singleResult();
    Mockito.doReturn(pd)
        .when(service)
        .getProcessDefinition(Mockito.anyString(), Mockito.anyBoolean(), Mockito.anyBoolean());
    service.deploy("source".getBytes(), "process", null);

    Mockito.verify(service)
        .cleanProcessResourcesByKey(Mockito.any(DeploymentDescriptor.class));
  }

  @Test
  void suspendProcessCurrentlyRunning() {
    Mockito.doReturn(new ArrayList<ProcessExecution>())
        .when(this.executions)
        .get("ID");
    service.cancelProcessByDeploymentDescriptor(getDeploymentDescriptor().getDefinition(), true);
    Mockito.verify(service, Mockito.times(1))
        .cancelProcess(Mockito.any(String.class), Mockito.any(String.class), Mockito.any(Boolean.class), Mockito.any(Boolean.class));
  }

  /*
   * Utils
   */

  private DeploymentDescriptor getDeploymentDescriptor() {
    DeploymentDescriptor dd = Mockito.mock(DeploymentDescriptor.class);
    DefinitionDescriptor definitionDescriptor = Mockito.mock(DefinitionDescriptor.class);
    definitionDescriptor.setId("ID");
    definitionDescriptor.setKey("Key");
    Mockito.doReturn("Key")
        .when(dd)
        .getDefinitionKey();
    Mockito.doReturn("ID")
        .when(dd)
        .getDefinitionId();
    Mockito.doReturn(definitionDescriptor)
        .when(dd)
        .getDefinition();
    Mockito.doReturn("ID")
        .when(definitionDescriptor)
        .getId();
    Mockito.doReturn("Key")
        .when(definitionDescriptor)
        .getKey();
    return dd;
  }

  private ProcessDefinition getProcessDefinition() {
    ProcessDefinition pd = Mockito.mock(ProcessDefinition.class);
    Mockito.when(pd.getId())
        .thenReturn("ID-XXXXX");
    Mockito.when(pd.getKey())
        .thenReturn("KEY-XXXXX");
    Mockito.when(pd.getDeploymentId())
        .thenReturn("DEPLOYMENT-ID-XXXXX");
    return pd;
  }
}
