package com.byzaneo.xtrade.dao;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.util.Date;

import com.byzaneo.xtrade.api.DocumentStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.*;
import org.springframework.test.context.ContextConfiguration;

import com.byzaneo.commons.test.*;
import com.byzaneo.xtrade.bean.*;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(locations = { "classpath:/xtrade-test.beans.xml" }, loader = SystemPropertyContextLoader.class)
public class WorkflowStepDAOITCase extends CrudDAOTest<WorkflowStepDAO, WorkflowStep, WorkflowStepId> {
  @Autowired
  @Qualifier(WorkflowStepDAO.DAO_NAME)
  private WorkflowStepDAO workflowStepDao;

  @Autowired
  @Qualifier(WorkflowDAO.DAO_NAME)
  private WorkflowDAO workflowDao;

  private Workflow workflow;

  @BeforeEach
  public void before(@Autowired WorkflowStepDAO workflowStepDao, @Autowired WorkflowDAO workflowDao) {
    workflowStepDao.removeAll();
    Workflow myWorkflow = new Workflow("nameTest", "DescTest", 0, "CollectionTest", "owner", "bqFilterTest", new Date());
    myWorkflow.setStatusEnd(new DocumentStatusEntity(DocumentStatus.ACCEPTED));
    myWorkflow.setStatusInit(new DocumentStatusEntity(DocumentStatus.SENT));
    myWorkflow.setStatusRefused(new DocumentStatusEntity(DocumentStatus.NONE));
    workflow = workflowDao
        .store(workflowDao.merge(myWorkflow));
    WorkflowStep ws = new WorkflowStep(workflow.getId(), 4, true, true, true, true);
    workflow.addWorkflowStep(ws);
    idEntity = workflowStepDao.merge(ws)
        .getId();
  }

  @Override
  @Test
  public void create() {
    WorkflowStep ws = new WorkflowStep(workflow.getId(), 4, true, true, true, true);
    workflow.addWorkflowStep(ws);
    WorkflowStep wsStored = workflowStepDao.merge(ws);

    assertNotNull(wsStored);
    assertNotNull(wsStored.getId());
  }

  @Override
  @Test
  public void retrieve() {
    super.retrieve();
  }

  @Override
  @Test
  public void update() {
    WorkflowStep ws = workflowStepDao.findById(idEntity);
    updateEntity(ws);
    WorkflowStep updatedEntity = workflowStepDao.merge(ws);

    assertNotNull(updatedEntity);
    assertEquals(getUpdatedField(updatedEntity), getUpdatedValue());
  }

  @Override
  @Test
  public void delete() {
    super.delete();
  }

  @Override
  public WorkflowStepDAO getDao() {
    return workflowStepDao;
  }

  @Override
  public WorkflowStep createNewEntity() {
    return null;
  }

  @Override
  public void updateEntity(WorkflowStep workflowStep) {
    workflowStep.setRefuse(false);
  }

  @Override
  public Object getUpdatedField(WorkflowStep workflowStep) {
    return workflowStep.getRefuse();
  }

  @Override
  public Object getUpdatedValue() {
    return false;
  }
}
