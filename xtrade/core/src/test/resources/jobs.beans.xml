<?xml version="1.0" encoding="ISO-8859-1"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:util="http://www.springframework.org/schema/util"
	xmlns:context="http://www.springframework.org/schema/context" xmlns:jee="http://www.springframework.org/schema/jee" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task"
	xsi:schemaLocation="
			http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
			http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd
			http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
			http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee.xsd
			http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
			http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd">
	<!-- HELLO JOB -->
	<bean id="xtdTestProcessJob" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean" p:targetObject-ref="xtdProcessService"
		p:targetMethod="start" p:concurrent="false">
		<property name="arguments">
			<list><value type="java.lang.String">hello</value></list>
		</property>
	</bean>
	<bean id="xtdTestTrigger" class="org.springframework.scheduling.quartz.CronTriggerBean" p:jobDetail-ref="xtdTestProcessJob" p:cronExpression="0 0/5 * ? * *" />
	<bean id="xtdSchedulerFactory" class="org.springframework.scheduling.quartz.SchedulerFactoryBean" p:schedulerName="XTD_SCHEDULER" p:autoStartup="true">
		<property name="triggers">
			<list><ref bean="xtdTestTrigger" /></list>
		</property>
	</bean>
</beans>