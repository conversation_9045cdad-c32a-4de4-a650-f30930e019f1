# -----------------------------------------------------------------------------
#  C I   P R O P E R T I E S
# -----------------------------------------------------------------------------

# -- D A T A B A S E --
database.flyway.disable = true
#database.driver		= com.mysql.jdbc.Driver
#database.url		= ***********************************************************************************************
#database.username	= ci
#database.password	= ci
#database.target		= com.byzaneo.commons.dao.hibernate.support.MySQL5Dialect
database.schema=gnx
database.type=postgres
database.driver=org.postgresql.Driver
database.url=****************************************************
database.username=cipg
database.password=cipg
database.target=org.hibernate.dialect.PostgreSQLDialect
database.datasource = pooledDataSource
database.showSql	= false
# - DDL -
# validate | update | create | create-drop
database.generateDdl		= true
database.generateDdl.mode	= create-drop
database.process.generateDdl = drop-create
database.generateDdl.imports= /META-INF/data/security.${database.type}.sql,/data/import.${database.type}.sql

# -- I N D E X --

index.mongo.uri=mongodb://ci:ci@ci-mongo/ci
#--DATA BASE RETRY--
database.query.retry.attemps=2
# Milliseconds
database.query.retry.delay=3000