//
// Ce fichier a été généré par l'implémentation de référence JavaTM Architecture for XML Binding (JAXB), v2.2.6 
// Voir <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Toute modification apportée à ce fichier sera perdue lors de la recompilation du schéma source. 
// Généré le : 2013.08.12 à 05:00:38 PM CEST 
//

package com.byzaneo.xtrade.ipm.xml.spring;

import java.util.ArrayList;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAnyElement;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementRefs;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Classe Java pour anonymous complex type.
 * <p>
 * Le fragment de schéma suivant indique le contenu attendu figurant dans cette classe.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;group ref="{http://www.springframework.org/schema/beans}collectionElements"/>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "description",
    "beanOrRefOrIdref"
})
@XmlRootElement(name = "key")
public class Key {

  protected Description description;
  @XmlElementRefs({
      @XmlElementRef(name = "map", namespace = "http://www.springframework.org/schema/beans", type = Map.class),
      @XmlElementRef(name = "null", namespace = "http://www.springframework.org/schema/beans", type = Null.class),
      @XmlElementRef(name = "set", namespace = "http://www.springframework.org/schema/beans", type = Set.class),
      @XmlElementRef(name = "ref", namespace = "http://www.springframework.org/schema/beans", type = Ref.class),
      @XmlElementRef(name = "value", namespace = "http://www.springframework.org/schema/beans", type = Value.class),
      @XmlElementRef(name = "list", namespace = "http://www.springframework.org/schema/beans", type = com.byzaneo.xtrade.ipm.xml.spring.List.class),
      @XmlElementRef(name = "bean", namespace = "http://www.springframework.org/schema/beans", type = Bean.class),
      @XmlElementRef(name = "array", namespace = "http://www.springframework.org/schema/beans", type = Array.class),
      @XmlElementRef(name = "idref", namespace = "http://www.springframework.org/schema/beans", type = Idref.class),
      @XmlElementRef(name = "props", namespace = "http://www.springframework.org/schema/beans", type = Props.class)
  })
  @XmlAnyElement(lax = true)
  protected java.util.List<Object> beanOrRefOrIdref;

  /**
   * Obtient la valeur de la propriété description.
   * 
   * @return possible object is {@link Description }
   */
  public Description getDescription() {
    return description;
  }

  /**
   * Définit la valeur de la propriété description.
   * 
   * @param value allowed object is {@link Description }
   */
  public void setDescription(Description value) {
    this.description = value;
  }

  /**
   * Gets the value of the beanOrRefOrIdref property.
   * <p>
   * This accessor method returns a reference to the live list, not a snapshot. Therefore any modification you make to the returned list
   * will be present inside the JAXB object. This is why there is not a <CODE>set</CODE> method for the beanOrRefOrIdref property.
   * <p>
   * For example, to add a new item, do as follows:
   * 
   * <pre>
   * getBeanOrRefOrIdref().add(newItem);
   * </pre>
   * <p>
   * Objects of the following type(s) are allowed in the list {@link Null } {@link Map } {@link Ref } {@link Set } {@link Value }
   * {@link Bean } {@link com.byzaneo.xtrade.ipm.xml.spring.List } {@link Object } {@link Array } {@link Idref } {@link Props }
   */
  public java.util.List<Object> getBeanOrRefOrIdref() {
    if (beanOrRefOrIdref == null) {
      beanOrRefOrIdref = new ArrayList<Object>();
    }
    return this.beanOrRefOrIdref;
  }

}
