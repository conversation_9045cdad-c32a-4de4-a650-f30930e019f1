//
// Ce fichier a été généré par l'implémentation de référence JavaTM Architecture for XML Binding (JAXB), v2.2.6 
// Voir <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Toute modification apportée à ce fichier sera perdue lors de la recompilation du schéma source. 
// Généré le : 2013.08.12 à 05:00:38 PM CEST 
//

package com.byzaneo.xtrade.ipm.xml.spring;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAnyAttribute;
import javax.xml.bind.annotation.XmlAnyElement;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementRefs;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.CollapsedStringAdapter;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import javax.xml.namespace.QName;

/**
 * <p>
 * Classe Java pour anonymous complex type.
 * <p>
 * Le fragment de schéma suivant indique le contenu attendu figurant dans cette classe.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;extension base="{http://www.springframework.org/schema/beans}identifiedType">
 *       &lt;group ref="{http://www.springframework.org/schema/beans}beanElements"/>
 *       &lt;attGroup ref="{http://www.springframework.org/schema/beans}beanAttributes"/>
 *       &lt;anyAttribute processContents='lax' namespace='##other'/>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "description",
    "metaOrConstructorArgOrProperty"
})
@XmlRootElement(name = "bean")
public class Bean
    extends IdentifiedType {

  protected Description description;
  @XmlElementRefs({
      @XmlElementRef(name = "qualifier", namespace = "http://www.springframework.org/schema/beans", type = Qualifier.class),
      @XmlElementRef(name = "property", namespace = "http://www.springframework.org/schema/beans", type = JAXBElement.class),
      @XmlElementRef(name = "meta", namespace = "http://www.springframework.org/schema/beans", type = JAXBElement.class),
      @XmlElementRef(name = "constructor-arg", namespace = "http://www.springframework.org/schema/beans", type = ConstructorArg.class),
      @XmlElementRef(name = "lookup-method", namespace = "http://www.springframework.org/schema/beans", type = LookupMethod.class),
      @XmlElementRef(name = "replaced-method", namespace = "http://www.springframework.org/schema/beans", type = ReplacedMethod.class)
  })
  @XmlAnyElement(lax = true)
  protected List<Object> metaOrConstructorArgOrProperty;
  @XmlAttribute(name = "name")
  protected String name;
  @XmlAttribute(name = "class")
  protected String clazz;
  @XmlAttribute(name = "parent")
  protected String parent;
  @XmlAttribute(name = "scope")
  protected String scope;
  @XmlAttribute(name = "abstract")
  protected Boolean _abstract;
  @XmlAttribute(name = "lazy-init")
  protected DefaultableBoolean lazyInit;
  @XmlAttribute(name = "autowire")
  @XmlJavaTypeAdapter(CollapsedStringAdapter.class)
  protected String autowire;
  @XmlAttribute(name = "depends-on")
  protected String dependsOn;
  @XmlAttribute(name = "autowire-candidate")
  protected DefaultableBoolean autowireCandidate;
  @XmlAttribute(name = "primary")
  protected Boolean primary;
  @XmlAttribute(name = "init-method")
  protected String initMethod;
  @XmlAttribute(name = "destroy-method")
  protected String destroyMethod;
  @XmlAttribute(name = "factory-method")
  protected String factoryMethod;
  @XmlAttribute(name = "factory-bean")
  protected String factoryBean;
  @XmlAnyAttribute
  private Map<QName, String> otherAttributes = new HashMap<QName, String>();

  /**
   * Obtient la valeur de la propriété description.
   * 
   * @return possible object is {@link Description }
   */
  public Description getDescription() {
    return description;
  }

  /**
   * Définit la valeur de la propriété description.
   * 
   * @param value allowed object is {@link Description }
   */
  public void setDescription(Description value) {
    this.description = value;
  }

  /**
   * Gets the value of the metaOrConstructorArgOrProperty property.
   * <p>
   * This accessor method returns a reference to the live list, not a snapshot. Therefore any modification you make to the returned list
   * will be present inside the JAXB object. This is why there is not a <CODE>set</CODE> method for the metaOrConstructorArgOrProperty
   * property.
   * <p>
   * For example, to add a new item, do as follows:
   * 
   * <pre>
   * getMetaOrConstructorArgOrProperty().add(newItem);
   * </pre>
   * <p>
   * Objects of the following type(s) are allowed in the list {@link JAXBElement }{@code <}{@link MetaType }{@code >} {@link Qualifier }
   * {@link ConstructorArg } {@link LookupMethod } {@link Object } {@link ReplacedMethod } {@link JAXBElement }{@code <}{@link PropertyType
   * }{@code >}
   */
  public List<Object> getMetaOrConstructorArgOrProperty() {
    if (metaOrConstructorArgOrProperty == null) {
      metaOrConstructorArgOrProperty = new ArrayList<Object>();
    }
    return this.metaOrConstructorArgOrProperty;
  }

  /**
   * Obtient la valeur de la propriété name.
   * 
   * @return possible object is {@link String }
   */
  public String getName() {
    return name;
  }

  /**
   * Définit la valeur de la propriété name.
   * 
   * @param value allowed object is {@link String }
   */
  public void setName(String value) {
    this.name = value;
  }

  /**
   * Obtient la valeur de la propriété clazz.
   * 
   * @return possible object is {@link String }
   */
  public String getClazz() {
    return clazz;
  }

  /**
   * Définit la valeur de la propriété clazz.
   * 
   * @param value allowed object is {@link String }
   */
  public void setClazz(String value) {
    this.clazz = value;
  }

  /**
   * Obtient la valeur de la propriété parent.
   * 
   * @return possible object is {@link String }
   */
  public String getParent() {
    return parent;
  }

  /**
   * Définit la valeur de la propriété parent.
   * 
   * @param value allowed object is {@link String }
   */
  public void setParent(String value) {
    this.parent = value;
  }

  /**
   * Obtient la valeur de la propriété scope.
   * 
   * @return possible object is {@link String }
   */
  public String getScope() {
    return scope;
  }

  /**
   * Définit la valeur de la propriété scope.
   * 
   * @param value allowed object is {@link String }
   */
  public void setScope(String value) {
    this.scope = value;
  }

  /**
   * Obtient la valeur de la propriété abstract.
   * 
   * @return possible object is {@link Boolean }
   */
  public Boolean isAbstract() {
    return _abstract;
  }

  /**
   * Définit la valeur de la propriété abstract.
   * 
   * @param value allowed object is {@link Boolean }
   */
  public void setAbstract(Boolean value) {
    this._abstract = value;
  }

  /**
   * Obtient la valeur de la propriété lazyInit.
   * 
   * @return possible object is {@link DefaultableBoolean }
   */
  public DefaultableBoolean getLazyInit() {
    if (lazyInit == null) {
      return DefaultableBoolean.DEFAULT;
    }
    else {
      return lazyInit;
    }
  }

  /**
   * Définit la valeur de la propriété lazyInit.
   * 
   * @param value allowed object is {@link DefaultableBoolean }
   */
  public void setLazyInit(DefaultableBoolean value) {
    this.lazyInit = value;
  }

  /**
   * Obtient la valeur de la propriété autowire.
   * 
   * @return possible object is {@link String }
   */
  public String getAutowire() {
    if (autowire == null) {
      return "default";
    }
    else {
      return autowire;
    }
  }

  /**
   * Définit la valeur de la propriété autowire.
   * 
   * @param value allowed object is {@link String }
   */
  public void setAutowire(String value) {
    this.autowire = value;
  }

  /**
   * Obtient la valeur de la propriété dependsOn.
   * 
   * @return possible object is {@link String }
   */
  public String getDependsOn() {
    return dependsOn;
  }

  /**
   * Définit la valeur de la propriété dependsOn.
   * 
   * @param value allowed object is {@link String }
   */
  public void setDependsOn(String value) {
    this.dependsOn = value;
  }

  /**
   * Obtient la valeur de la propriété autowireCandidate.
   * 
   * @return possible object is {@link DefaultableBoolean }
   */
  public DefaultableBoolean getAutowireCandidate() {
    if (autowireCandidate == null) {
      return DefaultableBoolean.DEFAULT;
    }
    else {
      return autowireCandidate;
    }
  }

  /**
   * Définit la valeur de la propriété autowireCandidate.
   * 
   * @param value allowed object is {@link DefaultableBoolean }
   */
  public void setAutowireCandidate(DefaultableBoolean value) {
    this.autowireCandidate = value;
  }

  /**
   * Obtient la valeur de la propriété primary.
   * 
   * @return possible object is {@link Boolean }
   */
  public Boolean isPrimary() {
    return primary;
  }

  /**
   * Définit la valeur de la propriété primary.
   * 
   * @param value allowed object is {@link Boolean }
   */
  public void setPrimary(Boolean value) {
    this.primary = value;
  }

  /**
   * Obtient la valeur de la propriété initMethod.
   * 
   * @return possible object is {@link String }
   */
  public String getInitMethod() {
    return initMethod;
  }

  /**
   * Définit la valeur de la propriété initMethod.
   * 
   * @param value allowed object is {@link String }
   */
  public void setInitMethod(String value) {
    this.initMethod = value;
  }

  /**
   * Obtient la valeur de la propriété destroyMethod.
   * 
   * @return possible object is {@link String }
   */
  public String getDestroyMethod() {
    return destroyMethod;
  }

  /**
   * Définit la valeur de la propriété destroyMethod.
   * 
   * @param value allowed object is {@link String }
   */
  public void setDestroyMethod(String value) {
    this.destroyMethod = value;
  }

  /**
   * Obtient la valeur de la propriété factoryMethod.
   * 
   * @return possible object is {@link String }
   */
  public String getFactoryMethod() {
    return factoryMethod;
  }

  /**
   * Définit la valeur de la propriété factoryMethod.
   * 
   * @param value allowed object is {@link String }
   */
  public void setFactoryMethod(String value) {
    this.factoryMethod = value;
  }

  /**
   * Obtient la valeur de la propriété factoryBean.
   * 
   * @return possible object is {@link String }
   */
  public String getFactoryBean() {
    return factoryBean;
  }

  /**
   * Définit la valeur de la propriété factoryBean.
   * 
   * @param value allowed object is {@link String }
   */
  public void setFactoryBean(String value) {
    this.factoryBean = value;
  }

  /**
   * Gets a map that contains attributes that aren't bound to any typed property on this class.
   * <p>
   * the map is keyed by the name of the attribute and the value is the string value of the attribute. the map returned by this method is
   * live, and you can add new attribute by updating the map directly. Because of this design, there's no setter.
   * 
   * @return always non-null
   */
  public Map<QName, String> getOtherAttributes() {
    return otherAttributes;
  }

}
