<PaymentRequest xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd"  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd ../../schema/org/xcbl/path_delim/schemas/xcbl/v4_0/financial/v1_0/financial.xsd">
    <PaymentRequestHeader>
        <PaymentRequestID>PaymentRequest/PaymentRequestHeader/PaymentRequestID</PaymentRequestID>
        <PaymentRequestIssueDate>2003-01-01T00:00:01</PaymentRequestIssueDate>
        <PayerParty>
            <core:PartyID>
                <core:Ident>PaymentRequest/PaymentRequestHeader/PayerParty/core:PartyID/core:Ident</core:Ident>
            </core:PartyID>
        </PayerParty>
        <FinancialServicesParty>
            <core:PartyID>
                <core:Ident>PaymentRequest/PaymentRequestHeader/FinancialServicesParty/core:PartyID/core:Ident</core:Ident>
            </core:PartyID>
        </FinancialServicesParty>
        <Language>
            <core:LanguageCoded>aa</core:LanguageCoded>
        </Language>
    </PaymentRequestHeader>
    <ListOfPaymentRequestDetail>
        <PaymentRequestDetail>
            <PaymentDocumentID>
                <core:RefNum>PaymentRequest/ListOfPaymentRequestDetail/PaymentRequestDetail/PaymentDocumentID/core:RefNum</core:RefNum>
            </PaymentDocumentID>
            <FinancialInstitutionDetail>
                <core:OriginatingFinancialInstitution>
                    <core:AccountDetail>
                        <core:AccountID>PaymentRequest/ListOfPaymentRequestDetail/PaymentRequestDetail/FinancialInstitutionDetail/core:OriginatingFinancialInstitution/core:AccountDetail/core:AccountID</core:AccountID>
                        <core:AccountTypeCoded>NewBusinessAccount</core:AccountTypeCoded>
                        <core:AccountName1>PaymentRequest/ListOfPaymentRequestDetail/PaymentRequestDetail/FinancialInstitutionDetail/core:OriginatingFinancialInstitution/core:AccountDetail/core:AccountName1</core:AccountName1>
                    </core:AccountDetail>
                    <core:FinancialInstitution>
                        <core:FinancialInstitutionID>PaymentRequest/ListOfPaymentRequestDetail/PaymentRequestDetail/FinancialInstitutionDetail/core:OriginatingFinancialInstitution/core:FinancialInstitution/core:FinancialInstitutionID</core:FinancialInstitutionID>
                        <core:FinancialInstitutionName>PaymentRequest/ListOfPaymentRequestDetail/PaymentRequestDetail/FinancialInstitutionDetail/core:OriginatingFinancialInstitution/core:FinancialInstitution/core:FinancialInstitutionName</core:FinancialInstitutionName>
                    </core:FinancialInstitution>
                </core:OriginatingFinancialInstitution>
                <core:ReceivingFinancialInstitution>
                    <core:AccountDetail>
                        <core:AccountID>PaymentRequest/ListOfPaymentRequestDetail/PaymentRequestDetail/FinancialInstitutionDetail/core:ReceivingFinancialInstitution/core:AccountDetail/core:AccountID</core:AccountID>
                        <core:AccountTypeCoded>NewBusinessAccount</core:AccountTypeCoded>
                        <core:AccountName1>PaymentRequest/ListOfPaymentRequestDetail/PaymentRequestDetail/FinancialInstitutionDetail/core:ReceivingFinancialInstitution/core:AccountDetail/core:AccountName1</core:AccountName1>
                    </core:AccountDetail>
                    <core:FinancialInstitution>
                        <core:FinancialInstitutionID>PaymentRequest/ListOfPaymentRequestDetail/PaymentRequestDetail/FinancialInstitutionDetail/core:ReceivingFinancialInstitution/core:FinancialInstitution/core:FinancialInstitutionID</core:FinancialInstitutionID>
                        <core:FinancialInstitutionName>PaymentRequest/ListOfPaymentRequestDetail/PaymentRequestDetail/FinancialInstitutionDetail/core:ReceivingFinancialInstitution/core:FinancialInstitution/core:FinancialInstitutionName</core:FinancialInstitutionName>
                    </core:FinancialInstitution>
                </core:ReceivingFinancialInstitution>
            </FinancialInstitutionDetail>
            <SettlementAmount>1001</SettlementAmount>
            <SettlementCurrency>
                <core:CurrencyCoded>AFA</core:CurrencyCoded>
            </SettlementCurrency>
            <PaymentRequestParty>
                <PayeeParty>
                    <core:PartyID>
                        <core:Ident>PaymentRequest/ListOfPaymentRequestDetail/PaymentRequestDetail/PaymentRequestParty/PayeeParty/core:PartyID/core:Ident</core:Ident>
                    </core:PartyID>
                </PayeeParty>
            </PaymentRequestParty>
            <PaymentMeanCoded>1035Exchange</PaymentMeanCoded>
        </PaymentRequestDetail>
    </ListOfPaymentRequestDetail>
</PaymentRequest>