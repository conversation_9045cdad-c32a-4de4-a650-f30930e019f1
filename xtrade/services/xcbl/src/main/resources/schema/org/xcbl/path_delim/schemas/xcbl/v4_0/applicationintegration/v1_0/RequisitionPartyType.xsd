<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/applicationintegration/v1_0/applicationintegration.xsd" xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns:dgs="http://www.w3.org/2000/09/xmldsig#" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/applicationintegration/v1_0/applicationintegration.xsd" elementFormDefault="qualified">
<xsd:import namespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" schemaLocation="../../core/core.xsd"/>

    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType name="RequisitionPartyType">
        <xsd:annotation>
            <xsd:documentation>holds all party information related to the requisition transaction.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
	    <xsd:element name="RequisitionerParty" type="core:PartyType">
                <xsd:annotation>
                    <xsd:documentation>contains the information for the party creating the requisition.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="BuyerParty" type="core:PartyType">
                <xsd:annotation>
                    <xsd:documentation>contains the information for the party purchasing the goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="SellerParty" type="core:PartyType">
                <xsd:annotation>
                    <xsd:documentation>identifies the party selling the goods.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ShipToParty" type="core:PartyType">
                <xsd:annotation>
                    <xsd:documentation>contains the information for the party which the items are to be shipped.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="BillToParty" type="core:PartyType">
                <xsd:annotation>
                    <xsd:documentation>contains the information for the party that will receive the bill for the goods..</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="RemitToParty" type="core:PartyType">
                <xsd:annotation>
                    <xsd:documentation>contains the information for the party to be paid.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ShipFromParty" type="core:PartyType">
                <xsd:annotation>
                    <xsd:documentation>contains party information identifying the location from which
        the items are to be shipped.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="SoldToParty" type="core:PartyType">
                <xsd:annotation>
                    <xsd:documentation>contains party information identifying the location where the
        items were sold.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ListOfPartyCoded" type="core:ListOfPartyCodedType">
                <xsd:annotation>
                    <xsd:documentation>is a collection of all other party information not explicitly
        stated as the content of another element.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
