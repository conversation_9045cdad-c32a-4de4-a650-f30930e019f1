<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/materialsmanagement/v1_0/materialsmanagement.xsd" xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns:dgs="http://www.w3.org/2000/09/xmldsig#" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/materialsmanagement/v1_0/materialsmanagement.xsd" elementFormDefault="qualified">
<xsd:import namespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" schemaLocation="../../core/core.xsd"/>


    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType name="MaterialGroupedPlanningResponseType">
        <xsd:annotation>
            <xsd:documentation>contains all the schedule-line-level data
        grouped by the material/product for the <!--code-->PlanningSchedule<!--/code--> and the
        <!--code-->PlanningScheduleResponse<!--/code--> document.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="DetailResponseCoded" type="core:DetailResponseCodeType">
                <xsd:annotation>
                    <xsd:documentation>is a code to identify the response for the line item
        detail.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="DetailResponseCodedOther" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>is used to provide a non-standard <!--code-->DetailResponseCode<!--/code-->. This element is mandatory if the value of <!--code-->DetailResponseCoded<!--/code--> is
    'Other'. These codes should not contain white space unless absolutely necessary.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="MaterialGroupedPlanningDetail" type="MaterialGroupedPlanningDetailType">
                <xsd:annotation>
                    <xsd:documentation>is used to echo back the detail that is being responded to, with any changes made by
                    the responding party.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="LineItemNote" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>describes any free-form text pertinent to the line item described by the
    PlanningScheduleResponse. This element may contain notes or any other similar information that
    is not contained explicitly in another structure.  You should not assume that the receiving application
     is capable of doing more than storing and/or displaying this information. </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ListOfStructuredNote" type="core:ListOfStructuredNoteType">
                <xsd:annotation>
                    <xsd:documentation>contains one or more structured notes that allow you to provide notes that are more than a simple free-text field. such notes may include the message text, or this text may be referenced with an external identifier or a URL. An agency may be specified, and is needed in the case where an ID has been provided for a note that is either included in-line or referenced. This field is often used to include references to centrally stored clauses in contracts that are required to appear within business documents.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ListOfAttachment" type="core:ListOfAttachmentType">
                <xsd:annotation>
                    <xsd:documentation>holds a list of attachments to a document that contain
        information relating to the document.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="ListOfMaterialGroupedPlanningResponseType">
        <xsd:annotation>
            <xsd:documentation>contains the line level detail information for the <!--code-->Planning
        schedule<!--/code-->.  The detail is grouped by the material. This is one of two
        options for grouping forecasting detail in the <!--code-->PlanningScheduleResponse<!--/code-->
        document.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element maxOccurs="unbounded" name="MaterialGroupedPlanningResponse" type="MaterialGroupedPlanningResponseType">
                <xsd:annotation>
                    <xsd:documentation>contains all the schedule-line-level data
        grouped by the material/product for the <!--code-->PlanningSchedule<!--/code--> and the
        <!--code-->PlanningScheduleResponse<!--/code--> document.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
