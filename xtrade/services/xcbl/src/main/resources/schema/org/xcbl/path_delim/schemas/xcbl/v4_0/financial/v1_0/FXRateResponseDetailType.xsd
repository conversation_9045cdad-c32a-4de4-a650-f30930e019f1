<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd" xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns:dgs="http://www.w3.org/2000/09/xmldsig#" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd" elementFormDefault="qualified">
<xsd:import namespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" schemaLocation="../../core/core.xsd"/>


    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType name="FXRateResponseDetailType">
        <xsd:annotation>
            <xsd:documentation>contains detailed information pertaining to the <!--code-->FXRateResponse<!--/code-->.  This includes payment document information containing payment-related information at the line item level.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element minOccurs="0" name="SequenceNumber" type="xsd:int">
                <xsd:annotation>
                    <xsd:documentation>tracks the payment document associated with the <!--code-->FXRateResponse<!--/code--> in the case where the payment request contains more than one payment document.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="RateQuoteID" type="core:ReferenceType">
                <xsd:annotation>
                    <xsd:documentation>identifies the unique user identifier for the <!--code-->FXRateRequest<!--/code-->.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="ReferenceCurrency" type="core:CurrencyType">
                <xsd:annotation>
                    <xsd:documentation>contains the reference currency of the rate of exchange (from currency).</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="TargetCurrency" type="core:CurrencyType">
                <xsd:annotation>
                    <xsd:documentation>contains the reference currency of the rate of exchange (to currency).</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ComputationalMethodCoded" type="ComputationalMethodCodeType">
                <xsd:annotation>
                    <xsd:documentation>contains the rules for the conversion.  </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ComputationalMethodCodedOther" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>is used to provide a non-standard <!--code-->ComputationalMethodCode<!--/code-->. This element is mandatory if the value of <!--code-->ComputationalMethodCoded<!--/code--> is 'Other'. These codes should not contain white space unless absolutely necessary.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="FXRate" type="core:Decimal19_9Type">
                <xsd:annotation>
                    <xsd:documentation>contains the FX rate quoted (Direct/Multiply).</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="InverseFXRate" type="core:Decimal19_9Type">
                <xsd:annotation>
                    <xsd:documentation>contains the FX rate quoted (Indirect/Divide).</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="PayerParty" type="PayerPartyType">
                <xsd:annotation>
                    <xsd:documentation>describes who is responsible for payment.  This is usually assumed to be the sender of the request document.  The <!--code-->PayerParty<!--/code--> includes an optional <!--code-->CertificateAuthority<!--/code--> field to hold the payer's digital signature or certificate of authority.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:choice>
                <xsd:element name="IndicativeRateDetail" type="IndicativeRateDetailType">
                    <xsd:annotation>
                        <xsd:documentation>contains fields and elements specific to the indicate rate quote.  This structure is provided when the <!--code-->IndicativeIndicator<!--/code--> is true.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
                <xsd:element name="SpotRateDetail" type="SpotRateDetailType">
                    <xsd:annotation>
                        <xsd:documentation>contains fields and elements specific to the spot rate quote.  This structure is provided when the <!--code-->IndicativeIndicator<!--/code--> is false.</xsd:documentation>
                    </xsd:annotation>
                </xsd:element>
            </xsd:choice>
            <xsd:element minOccurs="0" name="ListOfPaymentReference" type="core:ListOfReferenceType">
                <xsd:annotation>
                    <xsd:documentation>contains payment related reference detail such as invoice numbers, purchase order numbers, dates etc.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ListOfOtherPaymentInfo" type="core:ListOfNameValuePairType">
                <xsd:annotation>
                    <xsd:documentation>contains a list of additional customer providing payment information.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ListOfPaymentException" type="ListOfPaymentExceptionType">
                <xsd:annotation>
                    <xsd:documentation>contains a list of exception codes relating to the FXRate request document. </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="CertificateAuthority" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>specifies an element to contain the digital signature of the financial institution.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="FXRateResponseNote" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>contains any free-form text pertinent to the entire <!--code-->FXRateResponse<!--/code-->. This element may contain notes or any other similar information that is not contained explicitly in another structure. You should not assume that the receiving application is capable of doing more than storing and/or displaying this information.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:complexType name="ListOfFXRateResponseDetailType">
        <xsd:annotation>
            <xsd:documentation>contains a list of <!--code-->FXRateRequestDetail<!--/code--> for a FX rate quote.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element maxOccurs="unbounded" name="FXRateResponseDetail" type="FXRateResponseDetailType">
                <xsd:annotation>
                    <xsd:documentation>contains detailed information pertaining to the <!--code-->FXRateResponse<!--/code-->.  This includes payment document information containing payment-related information at the line item level.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
