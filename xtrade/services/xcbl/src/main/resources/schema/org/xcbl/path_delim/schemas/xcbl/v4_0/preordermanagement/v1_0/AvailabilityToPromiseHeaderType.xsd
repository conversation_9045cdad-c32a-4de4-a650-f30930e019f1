<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/preordermanagement/v1_0/preordermanagement.xsd" xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns:dgs="http://www.w3.org/2000/09/xmldsig#" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/preordermanagement/v1_0/preordermanagement.xsd" elementFormDefault="qualified">
<xsd:import namespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" schemaLocation="../../core/core.xsd"/>


    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType name="AvailabilityToPromiseHeaderType">
        <xsd:annotation>
            <xsd:documentation>holds all AvailabilitytoPromise header-level information. This
            element occurs once within the document.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="AvailabilityID" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>contains a reference number to identify the <!--code-->AvailabilityToPromise<!--/code--> document.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="AvailabilityIssueDate" type="xsd:dateTime">
                <xsd:annotation>
                    <xsd:documentation>indicates the date the <!--code-->AvailabilitytoPromise<!--/code--> request is
                    transmitted.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ListOfReferenceCoded" type="core:ListOfReferenceCodedType">
                <xsd:annotation>
                    <xsd:documentation>contains one or more elements containing information that can be
                    used to find further information elsewhere that is not explicitly stated in
                    another element.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="AvailabilityToPromisePurpose" type="AvailabilityToPromisePurposeType">
                <xsd:annotation>
                    <xsd:documentation>is a container for the <!--code-->AvailabilitytoPromise<!--/code--> purpose
                    details.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="AvailabilityDeliveryOption" type="AvailabilityDeliveryOptionType">
                <xsd:annotation>
                    <xsd:documentation>contains the type of delivery for the <!--code-->AvailabilityToPromise<!--/code-->
                    document.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ATPCheckType" type="ATPCheckTypeType">
                <xsd:annotation>
                    <xsd:documentation>specifies the requested criteria for checking the availability of the items specified by
                    the <!--code-->AvailabilityToPromise<!--/code-->
                    document.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ATPResponse" type="ATPResponseType">
                <xsd:annotation>
                    <xsd:documentation>is used to communicate the status of the response (if one exists) for the
                    <!--code-->AvailabilityToPromise<!--/code--> document.  The initiating party can use this code to accept or reject
                    an existing response from the responding party.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="InitiatingParty" type="core:PartyType">
                <xsd:annotation>
                    <xsd:documentation>holds the information to identify the initiator of the <!--code-->AvailabilityToPromise<!--/code-->
                    document.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="RespondingParty" type="core:PartyType">
                <xsd:annotation>
                    <xsd:documentation>holds information to identify the entity that is responding to the original <!--code-->AvailabilityToPromise<!--/code-->
                    document.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="AvailabilityShipToParty" type="core:PartyType">
                <xsd:annotation>
                    <xsd:documentation>holds the address details of the delivery party. If different
                    delivery parties are involved in this request then they will be specified at
                    the line item level.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="AvailabilityToPromiseHeaderTransport" type="core:TransportRoutingType">
                <xsd:annotation>
                    <xsd:documentation>identifies the transport details that apply to the entire
                    <!--code-->AvailabilityToPromise<!--/code--> document.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="AvailabilityToPromiseHeaderNote" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>is used to provide a note in text form if desired.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ListOfStructuredNote" type="core:ListOfStructuredNoteType">
                <xsd:annotation>
                    <xsd:documentation>contains one or more structured notes that allow you to provide
                    notes that are more than a simple free-text field. such notes may include the
                    message text, or this text may be referenced with an external identifier or a
                    URL. An agency may be specified, and is needed in the case where an ID has been
                    provided for a note that is either included in-line or referenced. This field
                    is often used to include references to centrally stored clauses in contracts
                    that are required to appear within business documents.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="AvailabilityToPromiseListOfAttachment" type="core:ListOfAttachmentType">
                <xsd:annotation>
                    <xsd:documentation>contains attachments for the <!--code-->AvailabilityToPromise<!--/code-->.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
