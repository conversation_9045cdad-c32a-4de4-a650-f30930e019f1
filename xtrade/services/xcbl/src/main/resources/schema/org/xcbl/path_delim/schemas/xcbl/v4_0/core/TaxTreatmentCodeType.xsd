<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" elementFormDefault="qualified">
    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:simpleType name="TaxTreatmentCodeType">
        <xsd:annotation>
            <xsd:documentation>This code indicates the level of tax deduction.</xsd:documentation>
        </xsd:annotation>
        <xsd:restriction base="xsd:NMTOKEN">
            <xsd:enumeration value="Other">
                <xsd:annotation>
                    <xsd:documentation>Custom Code</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NoTaxApplies">
                <xsd:annotation>
                    <xsd:documentation>Tax does not apply to this invoice</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NetInvoiceLevel">
                <xsd:annotation>
                    <xsd:documentation>Line item amounts are net amounts, and tax is calculated at invoice level in the Invoice Summary. Tax amounts must not be present on the InvoiceDetail</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GrossInvoiceLevel">
                <xsd:annotation>
                    <xsd:documentation>Line item amounts are gross amounts, and tax is calculated at invoice level in the Invoice Summary. Tax amounts must not be present on the InvoiceDetail</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="NetLineLevel">
                <xsd:annotation>
                    <xsd:documentation>Line item amounts are net amounts, and tax amounts are calculated at the line level</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
            <xsd:enumeration value="GrossLineLevel">
                <xsd:annotation>
                    <xsd:documentation>Line item amounts are gross amounts, and tax amounts are calculated at the line level</xsd:documentation>
                </xsd:annotation>
            </xsd:enumeration>
        </xsd:restriction>
    </xsd:simpleType>
</xsd:schema>
