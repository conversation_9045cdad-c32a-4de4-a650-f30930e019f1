<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/materialsmanagement/v1_0/materialsmanagement.xsd" xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns:dgs="http://www.w3.org/2000/09/xmldsig#" targetNamespace="rrn:org.xcbl:schemas/xcbl/v4_0/materialsmanagement/v1_0/materialsmanagement.xsd" elementFormDefault="qualified">
<xsd:import namespace="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" schemaLocation="../../core/core.xsd"/>


    <xsd:annotation>
        <xsd:documentation xml:lang="en">
            XML Common Business Library 4.0
            Copyright 2002 Commerce One, Inc.
            Permission is granted to use, copy, modify and distribute the
            DTD's, schemas and modules in the Commerce One XML Common Business
            Library Version 4.0 subject to the terms and conditions specified
            at http://www.xcbl.org/license.html
        </xsd:documentation>
    </xsd:annotation>
    <xsd:complexType name="ASNHeaderType">
        <xsd:annotation>
            <xsd:documentation>contains the header information of the ASN.</xsd:documentation>
        </xsd:annotation>
        <xsd:sequence>
            <xsd:element name="ASNNumber" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>is the unique number that uniquely identifies the ASN, typically according to the system that generated the ASN.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="ASNIssueDate" type="xsd:dateTime">
                <xsd:annotation>
                    <xsd:documentation>holds a digital time stamp provided by the application that created
                    the ASN document.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element maxOccurs="unbounded" name="ASNOrderNumber" type="ASNOrderNumberType">
                <xsd:annotation>
                    <xsd:documentation>indicates the unique number assigned to identify a purchase order that the ASN is referencing. </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ShippingReferences" type="ShippingReferencesType">
                <xsd:annotation>
                    <xsd:documentation>defines the references for the shipment.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="ASNPurpose" type="ASNPurposeType">
                <xsd:annotation>
                    <xsd:documentation>defines the purpose of the ASN.  This element should be used to identify whether the ASN is a new
                    ASN (Original), a replacement ASN (Replacement), or a cancellation of an existing ASN (Cancellation).  In the
                    case that the ASN is a replacement, the <!--code-->ASNNumber<!--/code--> should not change.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ASNType" type="ASNTypeType">
                <xsd:annotation>
                    <xsd:documentation>identifies the type of ASN being transmitted. </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ASNStatus" type="ASNStatusType">
                <xsd:annotation>
                    <xsd:documentation>identifies the status of the order/orders that the ASN is referencing and transmitting. </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ASNCurrency" type="core:CurrencyType">
                <xsd:annotation>
                    <xsd:documentation>identifies the default currency of the ASN, which when used at the header
                    level may be overwritten at a lower level within the structure (line item level). </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ASNLanguage" type="core:LanguageType">
                <xsd:annotation>
                    <xsd:documentation>identifies the language of the ASN.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="ASNDates" type="ASNDatesType">
                <xsd:annotation>
                    <xsd:documentation>indicates the pertinent dates for the ASN. These dates are
                    global and apply to the entire ASN, except where overridden by dates provided at the line item or package level.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="ASNParty" type="ASNPartyType">
                <xsd:annotation>
                    <xsd:documentation>holds information on the parties to the ASN, including unique ID's
                    and address information.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ListOfTransportRouting" type="core:ListOfTransportRoutingType">
                <xsd:annotation>
                    <xsd:documentation>defines the information on the movement of the goods, including
                    the carrier, equipment, locations and dates contained in the ASN.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ASNTermsOfDelivery" type="core:TermsOfDeliveryType">
                <xsd:annotation>
                    <xsd:documentation>describes the terms of delivery for the shipment described by the ASN.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ASNPaymentInstructions" type="core:PaymentInstructionsType">
                <xsd:annotation>
                    <xsd:documentation>describes the payment instructions for the ASN, this can include cost of the goods and the
                    cost of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ASNAllowancesOrCharges" type="core:ListOfAllowOrChargeType">
                <xsd:annotation>
                    <xsd:documentation>defines the allowances or charges for the ASN, this can include cost of the goods and the
                    cost of transport.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ASNHeaderNote" type="xsd:string">
                <xsd:annotation>
                    <xsd:documentation>contains any free-form text pertinent to the entire shipment described by the ASN or to the ASN message
                    itself. This element may contain notes or any other similar information that is not contained explicitly in
                    another structure.  You should not assume that the receiving application is capable of doing more than storing
                    and/or displaying this information.  </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="SpecialHandling" type="core:SpecialHandlingType">
                <xsd:annotation>
                    <xsd:documentation>descibes any special handling conditions that apply to the enitre ship notice.
                    </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ListOfStructuredNote" type="core:ListOfStructuredNoteType">
                <xsd:annotation>
                    <xsd:documentation>contains one or more structured notes that allow you to provide
                    notes that are more than a simple free-text field. such notes may include the
                    message text, or this text may be referenced with an external identifier or a
                    URL. An agency may be specified, and is needed in the case where an ID has been
                    provided for a note that is either included in-line or referenced. This field
                    is often used to include references to centrally stored clauses in contracts
                    that are required to appear within business documents.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ListOfNameValueSet" type="core:ListOfNameValueSetType">
                <xsd:annotation>
                    <xsd:documentation>is used to hold a collection of values qualified by names.  The values contianed
                    in this element should be agreed upon by trading partners.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element minOccurs="0" name="ASNHeaderAttachments" type="core:ListOfAttachmentType">
                <xsd:annotation>
                    <xsd:documentation>contains a list of attachments applicable to the entire ASN.  The information is not specific to a
                    particular line item or package, unless specifically noted.  </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:sequence>
    </xsd:complexType>
</xsd:schema>
