//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.catalog.v1_0.catalog;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for BuyerSupplierCodeType.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * <p>
 * 
 * <pre>
 * &lt;simpleType name="BuyerSupplierCodeType">
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}NMTOKEN">
 *     &lt;enumeration value="Other"/>
 *     &lt;enumeration value="Buyer"/>
 *     &lt;enumeration value="Supplier"/>
 *   &lt;/restriction>
 * &lt;/simpleType>
 * </pre>
 */
@XmlType(name = "BuyerSupplierCodeType")
@XmlEnum
public enum BuyerSupplierCodeType {

  /**
   * Custom Code
   */
  @XmlEnumValue("Other")
  OTHER("Other"),
  @XmlEnumValue("Buyer")
  BUYER("Buyer"),
  @XmlEnumValue("Supplier")
  SUPPLIER("Supplier");
  private final String value;

  BuyerSupplierCodeType(String v) {
    value = v;
  }

  public String value() {
    return value;
  }

  public static BuyerSupplierCodeType fromValue(String v) {
    for (BuyerSupplierCodeType c : BuyerSupplierCodeType.values()) {
      if (c.value.equals(v)) {
        return c;
      }
    }
    throw new IllegalArgumentException(v);
  }

}
