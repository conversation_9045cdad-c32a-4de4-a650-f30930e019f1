//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.materialsmanagement.v1_0.materialsmanagement;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfStructuredNoteType;

/**
 * contains all the schedule-line-level data grouped by the material/product.
 * <p>
 * Java class for MaterialGroupedPlanningDetailType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="MaterialGroupedPlanningDetailType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="BasePlanningDetail" type="{rrn:org.xcbl:schemas/xcbl/v4_0/materialsmanagement/v1_0/materialsmanagement.xsd}ScheduleBaseItemDetailType"/>
 *         &lt;element name="ListOfLocationSchedule" type="{rrn:org.xcbl:schemas/xcbl/v4_0/materialsmanagement/v1_0/materialsmanagement.xsd}ListOfLocationScheduleType"/>
 *         &lt;element name="LineItemNote" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ListOfStructuredNote" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ListOfStructuredNoteType" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MaterialGroupedPlanningDetailType", propOrder = {
    "basePlanningDetail",
    "listOfLocationSchedule",
    "lineItemNote",
    "listOfStructuredNote"
})
public class MaterialGroupedPlanningDetailType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "BasePlanningDetail", required = true)
  protected ScheduleBaseItemDetailType basePlanningDetail;
  @XmlElement(name = "ListOfLocationSchedule", required = true)
  protected ListOfLocationScheduleType listOfLocationSchedule;
  @XmlElement(name = "LineItemNote")
  protected String lineItemNote;
  @XmlElement(name = "ListOfStructuredNote")
  protected ListOfStructuredNoteType listOfStructuredNote;

  /**
   * Gets the value of the basePlanningDetail property.
   * 
   * @return possible object is {@link ScheduleBaseItemDetailType }
   */
  public ScheduleBaseItemDetailType getBasePlanningDetail() {
    return basePlanningDetail;
  }

  /**
   * Sets the value of the basePlanningDetail property.
   * 
   * @param value allowed object is {@link ScheduleBaseItemDetailType }
   */
  public void setBasePlanningDetail(ScheduleBaseItemDetailType value) {
    this.basePlanningDetail = value;
  }

  /**
   * Gets the value of the listOfLocationSchedule property.
   * 
   * @return possible object is {@link ListOfLocationScheduleType }
   */
  public ListOfLocationScheduleType getListOfLocationSchedule() {
    return listOfLocationSchedule;
  }

  /**
   * Sets the value of the listOfLocationSchedule property.
   * 
   * @param value allowed object is {@link ListOfLocationScheduleType }
   */
  public void setListOfLocationSchedule(ListOfLocationScheduleType value) {
    this.listOfLocationSchedule = value;
  }

  /**
   * Gets the value of the lineItemNote property.
   * 
   * @return possible object is {@link String }
   */
  public String getLineItemNote() {
    return lineItemNote;
  }

  /**
   * Sets the value of the lineItemNote property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setLineItemNote(String value) {
    this.lineItemNote = value;
  }

  /**
   * Gets the value of the listOfStructuredNote property.
   * 
   * @return possible object is {@link ListOfStructuredNoteType }
   */
  public ListOfStructuredNoteType getListOfStructuredNote() {
    return listOfStructuredNote;
  }

  /**
   * Sets the value of the listOfStructuredNote property.
   * 
   * @param value allowed object is {@link ListOfStructuredNoteType }
   */
  public void setListOfStructuredNote(ListOfStructuredNoteType value) {
    this.listOfStructuredNote = value;
  }

}
