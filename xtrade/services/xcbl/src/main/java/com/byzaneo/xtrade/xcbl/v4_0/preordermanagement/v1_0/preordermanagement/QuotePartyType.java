//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.preordermanagement.v1_0.preordermanagement;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfPartyCodedType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PartyType;

/**
 * holds all party information related to ordering goods.
 * <p>
 * Java class for QuotePartyType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="QuotePartyType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="BuyerParty" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}PartyType"/>
 *         &lt;element name="SellerParty" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}PartyType"/>
 *         &lt;element name="ShipToParty" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}PartyType" minOccurs="0"/>
 *         &lt;element name="BillToParty" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}PartyType" minOccurs="0"/>
 *         &lt;element name="RemitToParty" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}PartyType" minOccurs="0"/>
 *         &lt;element name="ShipFromParty" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}PartyType" minOccurs="0"/>
 *         &lt;element name="WarehouseParty" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}PartyType" minOccurs="0"/>
 *         &lt;element name="SoldToParty" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}PartyType" minOccurs="0"/>
 *         &lt;element name="ManufacturingParty" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}PartyType" minOccurs="0"/>
 *         &lt;element name="MaterialIssuer" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}PartyType" minOccurs="0"/>
 *         &lt;element name="ListOfPartyCoded" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ListOfPartyCodedType" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "QuotePartyType", propOrder = {
    "buyerParty",
    "sellerParty",
    "shipToParty",
    "billToParty",
    "remitToParty",
    "shipFromParty",
    "warehouseParty",
    "soldToParty",
    "manufacturingParty",
    "materialIssuer",
    "listOfPartyCoded"
})
public class QuotePartyType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "BuyerParty", required = true)
  protected PartyType buyerParty;
  @XmlElement(name = "SellerParty", required = true)
  protected PartyType sellerParty;
  @XmlElement(name = "ShipToParty")
  protected PartyType shipToParty;
  @XmlElement(name = "BillToParty")
  protected PartyType billToParty;
  @XmlElement(name = "RemitToParty")
  protected PartyType remitToParty;
  @XmlElement(name = "ShipFromParty")
  protected PartyType shipFromParty;
  @XmlElement(name = "WarehouseParty")
  protected PartyType warehouseParty;
  @XmlElement(name = "SoldToParty")
  protected PartyType soldToParty;
  @XmlElement(name = "ManufacturingParty")
  protected PartyType manufacturingParty;
  @XmlElement(name = "MaterialIssuer")
  protected PartyType materialIssuer;
  @XmlElement(name = "ListOfPartyCoded")
  protected ListOfPartyCodedType listOfPartyCoded;

  /**
   * Gets the value of the buyerParty property.
   * 
   * @return possible object is {@link PartyType }
   */
  public PartyType getBuyerParty() {
    return buyerParty;
  }

  /**
   * Sets the value of the buyerParty property.
   * 
   * @param value allowed object is {@link PartyType }
   */
  public void setBuyerParty(PartyType value) {
    this.buyerParty = value;
  }

  /**
   * Gets the value of the sellerParty property.
   * 
   * @return possible object is {@link PartyType }
   */
  public PartyType getSellerParty() {
    return sellerParty;
  }

  /**
   * Sets the value of the sellerParty property.
   * 
   * @param value allowed object is {@link PartyType }
   */
  public void setSellerParty(PartyType value) {
    this.sellerParty = value;
  }

  /**
   * Gets the value of the shipToParty property.
   * 
   * @return possible object is {@link PartyType }
   */
  public PartyType getShipToParty() {
    return shipToParty;
  }

  /**
   * Sets the value of the shipToParty property.
   * 
   * @param value allowed object is {@link PartyType }
   */
  public void setShipToParty(PartyType value) {
    this.shipToParty = value;
  }

  /**
   * Gets the value of the billToParty property.
   * 
   * @return possible object is {@link PartyType }
   */
  public PartyType getBillToParty() {
    return billToParty;
  }

  /**
   * Sets the value of the billToParty property.
   * 
   * @param value allowed object is {@link PartyType }
   */
  public void setBillToParty(PartyType value) {
    this.billToParty = value;
  }

  /**
   * Gets the value of the remitToParty property.
   * 
   * @return possible object is {@link PartyType }
   */
  public PartyType getRemitToParty() {
    return remitToParty;
  }

  /**
   * Sets the value of the remitToParty property.
   * 
   * @param value allowed object is {@link PartyType }
   */
  public void setRemitToParty(PartyType value) {
    this.remitToParty = value;
  }

  /**
   * Gets the value of the shipFromParty property.
   * 
   * @return possible object is {@link PartyType }
   */
  public PartyType getShipFromParty() {
    return shipFromParty;
  }

  /**
   * Sets the value of the shipFromParty property.
   * 
   * @param value allowed object is {@link PartyType }
   */
  public void setShipFromParty(PartyType value) {
    this.shipFromParty = value;
  }

  /**
   * Gets the value of the warehouseParty property.
   * 
   * @return possible object is {@link PartyType }
   */
  public PartyType getWarehouseParty() {
    return warehouseParty;
  }

  /**
   * Sets the value of the warehouseParty property.
   * 
   * @param value allowed object is {@link PartyType }
   */
  public void setWarehouseParty(PartyType value) {
    this.warehouseParty = value;
  }

  /**
   * Gets the value of the soldToParty property.
   * 
   * @return possible object is {@link PartyType }
   */
  public PartyType getSoldToParty() {
    return soldToParty;
  }

  /**
   * Sets the value of the soldToParty property.
   * 
   * @param value allowed object is {@link PartyType }
   */
  public void setSoldToParty(PartyType value) {
    this.soldToParty = value;
  }

  /**
   * Gets the value of the manufacturingParty property.
   * 
   * @return possible object is {@link PartyType }
   */
  public PartyType getManufacturingParty() {
    return manufacturingParty;
  }

  /**
   * Sets the value of the manufacturingParty property.
   * 
   * @param value allowed object is {@link PartyType }
   */
  public void setManufacturingParty(PartyType value) {
    this.manufacturingParty = value;
  }

  /**
   * Gets the value of the materialIssuer property.
   * 
   * @return possible object is {@link PartyType }
   */
  public PartyType getMaterialIssuer() {
    return materialIssuer;
  }

  /**
   * Sets the value of the materialIssuer property.
   * 
   * @param value allowed object is {@link PartyType }
   */
  public void setMaterialIssuer(PartyType value) {
    this.materialIssuer = value;
  }

  /**
   * Gets the value of the listOfPartyCoded property.
   * 
   * @return possible object is {@link ListOfPartyCodedType }
   */
  public ListOfPartyCodedType getListOfPartyCoded() {
    return listOfPartyCoded;
  }

  /**
   * Sets the value of the listOfPartyCoded property.
   * 
   * @param value allowed object is {@link ListOfPartyCodedType }
   */
  public void setListOfPartyCoded(ListOfPartyCodedType value) {
    this.listOfPartyCoded = value;
  }

}
