package com.byzaneo.xtrade.xcbl.transmission;

import java.util.Date;

import javax.xml.bind.annotation.*;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.CurrencyCodeType;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceTypeCodeType;

import lombok.*;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TransactionInvoiceType", propOrder = {
    "id",
    "issueDate",
    "invoiceTypeCoded",
    "currencyCoded",
    "taxCurrencyCoded",
    "taxDueDateTypeCoded",
    "specifiedTradePayments",
    "includedNote",
    "businessProcess",
    "referencedDocument",
    "delivery",
    "invoicePeriod",
    "allowOrCharge",
    "monetaryTotal",
    "taxSubTotal"
})
@Getter
@Setter
public class TransactionInvoiceType implements java.io.Serializable {

  private static final long serialVersionUID = 1L;

  @XmlElement(name = "Id")
  protected String id;
  @XmlElement(name = "IssueDate")
  protected Date issueDate;
  @XmlElement(name = "InvoiceTypeCoded", required = true)
  protected InvoiceTypeCodeType invoiceTypeCoded;
  @XmlElement(name = "CurrencyCoded", required = true)
  protected CurrencyCodeType currencyCoded;
  @XmlElement(name = "TaxCurrencyCoded", required = true)
  protected CurrencyCodeType taxCurrencyCoded;
  @XmlElement(name = "TaxDueDateTypeCoded", required = true)
  protected TaxDueDateTypeCodeType taxDueDateTypeCoded;
  @XmlElement(name = "SpecifiedTradePayments")
  protected String specifiedTradePayments;
  @XmlElement(name = "IncludedNote")
  protected IncludedNoteType includedNote;
  @XmlElement(name = "BusinessProcess")
  protected BusinessProcessType businessProcess;
  @XmlElement(name = "ReferencedDocument")
  protected ReferencedDocumentType referencedDocument;
  // buyer and seller to ask Julien
  @XmlElement(name = "Delivery")
  protected DeliveryType delivery;
  @XmlElement(name = "InvoicePeriod")
  protected InvoicePeriodType invoicePeriod;
  @XmlElement(name = "AllowOrCharge")
  protected TransmissionAllowOrChargeType allowOrCharge;
  @XmlElement(name = "MonetaryTotal")
  protected MonetaryTotalType monetaryTotal;
  @XmlElement(name = "TaxSubTotal")
  protected TaxSubTotalType taxSubTotal;

}
