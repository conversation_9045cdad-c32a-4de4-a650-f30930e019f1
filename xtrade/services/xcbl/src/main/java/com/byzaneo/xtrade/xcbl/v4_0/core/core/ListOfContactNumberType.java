//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.core.core;

import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * lists one or more communication points.
 * <p>
 * Java class for ListOfContactNumberType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ListOfContactNumberType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ContactNumber" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ContactNumberType" maxOccurs="unbounded"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ListOfContactNumberType", propOrder = {
    "contactNumber"
})
public class ListOfContactNumberType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "ContactNumber", required = true)
  protected List<ContactNumberType> contactNumber;

  /**
   * Gets the value of the contactNumber property.
   * <p>
   * This accessor method returns a reference to the live list, not a snapshot. Therefore any modification you make to the returned list
   * will be present inside the JAXB object. This is why there is not a <CODE>set</CODE> method for the contactNumber property.
   * <p>
   * For example, to add a new item, do as follows:
   * 
   * <pre>
   * getContactNumber().add(newItem);
   * </pre>
   * <p>
   * Objects of the following type(s) are allowed in the list {@link ContactNumberType }
   */
  public List<ContactNumberType> getContactNumber() {
    if (contactNumber == null) {
      contactNumber = new ArrayList<ContactNumberType>();
    }
    return this.contactNumber;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + ((contactNumber == null) ? 0 : contactNumber.hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj)
      return true;
    if (obj == null)
      return false;
    if (getClass() != obj.getClass())
      return false;
    ListOfContactNumberType other = (ListOfContactNumberType) obj;
    if (contactNumber == null) {
      if (other.contactNumber != null)
        return false;
    }
    else if (!contactNumber.equals(other.contactNumber))
      return false;
    return true;
  }

}
