//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for ListOfPaymentDocumentDetailType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ListOfPaymentDocumentDetailType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="PaymentDocumentDetail" type="{rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd}RemittanceAdviceDetailType" maxOccurs="unbounded"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ListOfPaymentDocumentDetailType", propOrder = {
    "paymentDocumentDetail"
})
public class ListOfPaymentDocumentDetailType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "PaymentDocumentDetail", required = true)
  protected List<RemittanceAdviceDetailType> paymentDocumentDetail;

  /**
   * Gets the value of the paymentDocumentDetail property.
   * <p>
   * This accessor method returns a reference to the live list, not a snapshot. Therefore any modification you make to the returned list
   * will be present inside the JAXB object. This is why there is not a <CODE>set</CODE> method for the paymentDocumentDetail property.
   * <p>
   * For example, to add a new item, do as follows:
   * 
   * <pre>
   * getPaymentDocumentDetail().add(newItem);
   * </pre>
   * <p>
   * Objects of the following type(s) are allowed in the list {@link RemittanceAdviceDetailType }
   */
  public List<RemittanceAdviceDetailType> getPaymentDocumentDetail() {
    if (paymentDocumentDetail == null) {
      paymentDocumentDetail = new ArrayList<RemittanceAdviceDetailType>();
    }
    return this.paymentDocumentDetail;
  }

}
