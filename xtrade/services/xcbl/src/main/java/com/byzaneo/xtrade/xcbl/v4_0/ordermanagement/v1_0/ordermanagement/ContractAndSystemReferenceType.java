//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.IdentifierType;

/**
 * is a reference including contract and system.
 * <p>
 * Java class for ContractAndSystemReferenceType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ContractAndSystemReferenceType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ContractID" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}IdentifierType"/>
 *         &lt;element name="ContractItemID" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SystemID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ContractAndSystemReferenceType", propOrder = {
    "contractID",
    "contractItemID",
    "systemID"
})
public class ContractAndSystemReferenceType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "ContractID", required = true)
  protected IdentifierType contractID;
  @XmlElement(name = "ContractItemID", required = true)
  protected String contractItemID;
  @XmlElement(name = "SystemID")
  protected String systemID;

  /**
   * Gets the value of the contractID property.
   * 
   * @return possible object is {@link IdentifierType }
   */
  public IdentifierType getContractID() {
    return contractID;
  }

  /**
   * Sets the value of the contractID property.
   * 
   * @param value allowed object is {@link IdentifierType }
   */
  public void setContractID(IdentifierType value) {
    this.contractID = value;
  }

  /**
   * Gets the value of the contractItemID property.
   * 
   * @return possible object is {@link String }
   */
  public String getContractItemID() {
    return contractItemID;
  }

  /**
   * Sets the value of the contractItemID property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setContractItemID(String value) {
    this.contractItemID = value;
  }

  /**
   * Gets the value of the systemID property.
   * 
   * @return possible object is {@link String }
   */
  public String getSystemID() {
    return systemID;
  }

  /**
   * Sets the value of the systemID property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setSystemID(String value) {
    this.systemID = value;
  }

}
