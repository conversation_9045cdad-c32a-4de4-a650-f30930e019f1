//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.preordermanagement.v1_0.preordermanagement;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import java.util.Date;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfAttachmentType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfReferenceCodedType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ListOfStructuredNoteType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.PartyType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.ReferenceType;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.TransportRoutingType;

/**
 * holds all AvailabilityToPromiseResponse header-level information. This element occurs once within the document.
 * <p>
 * Java class for AvailabilityToPromiseResponseHeaderType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="AvailabilityToPromiseResponseHeaderType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="AvailabilityResponseID" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="AvailabilityResponseIssueDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="AvailabilityToPromiseRefernece" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ReferenceType"/>
 *         &lt;element name="ATPResponse" type="{rrn:org.xcbl:schemas/xcbl/v4_0/preordermanagement/v1_0/preordermanagement.xsd}ATPResponseType"/>
 *         &lt;element name="ListOfReferenceCoded" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ListOfReferenceCodedType" minOccurs="0"/>
 *         &lt;element name="AvailabilityResponseDeliveryOption" type="{rrn:org.xcbl:schemas/xcbl/v4_0/preordermanagement/v1_0/preordermanagement.xsd}AvailabilityDeliveryOptionType"/>
 *         &lt;element name="ATPCheckType" type="{rrn:org.xcbl:schemas/xcbl/v4_0/preordermanagement/v1_0/preordermanagement.xsd}ATPCheckTypeType" minOccurs="0"/>
 *         &lt;element name="InitiatingParty" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}PartyType"/>
 *         &lt;element name="RespondingParty" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}PartyType" minOccurs="0"/>
 *         &lt;element name="AvailabilityShipToParty" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}PartyType" minOccurs="0"/>
 *         &lt;element name="AvailabilityResponseHeaderTransport" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}TransportRoutingType" minOccurs="0"/>
 *         &lt;element name="AvailabilityResponseHeaderNote" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ListOfStructuredNote" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ListOfStructuredNoteType" minOccurs="0"/>
 *         &lt;element name="AvailabilityResponseListOfAttachment" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}ListOfAttachmentType" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AvailabilityToPromiseResponseHeaderType", propOrder = {
    "availabilityResponseID",
    "availabilityResponseIssueDate",
    "availabilityToPromiseRefernece",
    "atpResponse",
    "listOfReferenceCoded",
    "availabilityResponseDeliveryOption",
    "atpCheckType",
    "initiatingParty",
    "respondingParty",
    "availabilityShipToParty",
    "availabilityResponseHeaderTransport",
    "availabilityResponseHeaderNote",
    "listOfStructuredNote",
    "availabilityResponseListOfAttachment"
})
public class AvailabilityToPromiseResponseHeaderType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "AvailabilityResponseID", required = true)
  protected String availabilityResponseID;
  @XmlElement(name = "AvailabilityResponseIssueDate", required = true)
  @XmlSchemaType(name = "dateTime")
  protected Date availabilityResponseIssueDate;
  @XmlElement(name = "AvailabilityToPromiseRefernece", required = true)
  protected ReferenceType availabilityToPromiseRefernece;
  @XmlElement(name = "ATPResponse", required = true)
  protected ATPResponseType atpResponse;
  @XmlElement(name = "ListOfReferenceCoded")
  protected ListOfReferenceCodedType listOfReferenceCoded;
  @XmlElement(name = "AvailabilityResponseDeliveryOption", required = true)
  protected AvailabilityDeliveryOptionType availabilityResponseDeliveryOption;
  @XmlElement(name = "ATPCheckType")
  protected ATPCheckTypeType atpCheckType;
  @XmlElement(name = "InitiatingParty", required = true)
  protected PartyType initiatingParty;
  @XmlElement(name = "RespondingParty")
  protected PartyType respondingParty;
  @XmlElement(name = "AvailabilityShipToParty")
  protected PartyType availabilityShipToParty;
  @XmlElement(name = "AvailabilityResponseHeaderTransport")
  protected TransportRoutingType availabilityResponseHeaderTransport;
  @XmlElement(name = "AvailabilityResponseHeaderNote")
  protected String availabilityResponseHeaderNote;
  @XmlElement(name = "ListOfStructuredNote")
  protected ListOfStructuredNoteType listOfStructuredNote;
  @XmlElement(name = "AvailabilityResponseListOfAttachment")
  protected ListOfAttachmentType availabilityResponseListOfAttachment;

  /**
   * Gets the value of the availabilityResponseID property.
   * 
   * @return possible object is {@link String }
   */
  public String getAvailabilityResponseID() {
    return availabilityResponseID;
  }

  /**
   * Sets the value of the availabilityResponseID property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setAvailabilityResponseID(String value) {
    this.availabilityResponseID = value;
  }

  /**
   * Gets the value of the availabilityResponseIssueDate property.
   * 
   * @return possible object is {@link Date }
   */
  public Date getAvailabilityResponseIssueDate() {
    return availabilityResponseIssueDate;
  }

  /**
   * Sets the value of the availabilityResponseIssueDate property.
   * 
   * @param value allowed object is {@link Date }
   */
  public void setAvailabilityResponseIssueDate(Date value) {
    this.availabilityResponseIssueDate = value;
  }

  /**
   * Gets the value of the availabilityToPromiseRefernece property.
   * 
   * @return possible object is {@link ReferenceType }
   */
  public ReferenceType getAvailabilityToPromiseRefernece() {
    return availabilityToPromiseRefernece;
  }

  /**
   * Sets the value of the availabilityToPromiseRefernece property.
   * 
   * @param value allowed object is {@link ReferenceType }
   */
  public void setAvailabilityToPromiseRefernece(ReferenceType value) {
    this.availabilityToPromiseRefernece = value;
  }

  /**
   * Gets the value of the atpResponse property.
   * 
   * @return possible object is {@link ATPResponseType }
   */
  public ATPResponseType getATPResponse() {
    return atpResponse;
  }

  /**
   * Sets the value of the atpResponse property.
   * 
   * @param value allowed object is {@link ATPResponseType }
   */
  public void setATPResponse(ATPResponseType value) {
    this.atpResponse = value;
  }

  /**
   * Gets the value of the listOfReferenceCoded property.
   * 
   * @return possible object is {@link ListOfReferenceCodedType }
   */
  public ListOfReferenceCodedType getListOfReferenceCoded() {
    return listOfReferenceCoded;
  }

  /**
   * Sets the value of the listOfReferenceCoded property.
   * 
   * @param value allowed object is {@link ListOfReferenceCodedType }
   */
  public void setListOfReferenceCoded(ListOfReferenceCodedType value) {
    this.listOfReferenceCoded = value;
  }

  /**
   * Gets the value of the availabilityResponseDeliveryOption property.
   * 
   * @return possible object is {@link AvailabilityDeliveryOptionType }
   */
  public AvailabilityDeliveryOptionType getAvailabilityResponseDeliveryOption() {
    return availabilityResponseDeliveryOption;
  }

  /**
   * Sets the value of the availabilityResponseDeliveryOption property.
   * 
   * @param value allowed object is {@link AvailabilityDeliveryOptionType }
   */
  public void setAvailabilityResponseDeliveryOption(AvailabilityDeliveryOptionType value) {
    this.availabilityResponseDeliveryOption = value;
  }

  /**
   * Gets the value of the atpCheckType property.
   * 
   * @return possible object is {@link ATPCheckTypeType }
   */
  public ATPCheckTypeType getATPCheckType() {
    return atpCheckType;
  }

  /**
   * Sets the value of the atpCheckType property.
   * 
   * @param value allowed object is {@link ATPCheckTypeType }
   */
  public void setATPCheckType(ATPCheckTypeType value) {
    this.atpCheckType = value;
  }

  /**
   * Gets the value of the initiatingParty property.
   * 
   * @return possible object is {@link PartyType }
   */
  public PartyType getInitiatingParty() {
    return initiatingParty;
  }

  /**
   * Sets the value of the initiatingParty property.
   * 
   * @param value allowed object is {@link PartyType }
   */
  public void setInitiatingParty(PartyType value) {
    this.initiatingParty = value;
  }

  /**
   * Gets the value of the respondingParty property.
   * 
   * @return possible object is {@link PartyType }
   */
  public PartyType getRespondingParty() {
    return respondingParty;
  }

  /**
   * Sets the value of the respondingParty property.
   * 
   * @param value allowed object is {@link PartyType }
   */
  public void setRespondingParty(PartyType value) {
    this.respondingParty = value;
  }

  /**
   * Gets the value of the availabilityShipToParty property.
   * 
   * @return possible object is {@link PartyType }
   */
  public PartyType getAvailabilityShipToParty() {
    return availabilityShipToParty;
  }

  /**
   * Sets the value of the availabilityShipToParty property.
   * 
   * @param value allowed object is {@link PartyType }
   */
  public void setAvailabilityShipToParty(PartyType value) {
    this.availabilityShipToParty = value;
  }

  /**
   * Gets the value of the availabilityResponseHeaderTransport property.
   * 
   * @return possible object is {@link TransportRoutingType }
   */
  public TransportRoutingType getAvailabilityResponseHeaderTransport() {
    return availabilityResponseHeaderTransport;
  }

  /**
   * Sets the value of the availabilityResponseHeaderTransport property.
   * 
   * @param value allowed object is {@link TransportRoutingType }
   */
  public void setAvailabilityResponseHeaderTransport(TransportRoutingType value) {
    this.availabilityResponseHeaderTransport = value;
  }

  /**
   * Gets the value of the availabilityResponseHeaderNote property.
   * 
   * @return possible object is {@link String }
   */
  public String getAvailabilityResponseHeaderNote() {
    return availabilityResponseHeaderNote;
  }

  /**
   * Sets the value of the availabilityResponseHeaderNote property.
   * 
   * @param value allowed object is {@link String }
   */
  public void setAvailabilityResponseHeaderNote(String value) {
    this.availabilityResponseHeaderNote = value;
  }

  /**
   * Gets the value of the listOfStructuredNote property.
   * 
   * @return possible object is {@link ListOfStructuredNoteType }
   */
  public ListOfStructuredNoteType getListOfStructuredNote() {
    return listOfStructuredNote;
  }

  /**
   * Sets the value of the listOfStructuredNote property.
   * 
   * @param value allowed object is {@link ListOfStructuredNoteType }
   */
  public void setListOfStructuredNote(ListOfStructuredNoteType value) {
    this.listOfStructuredNote = value;
  }

  /**
   * Gets the value of the availabilityResponseListOfAttachment property.
   * 
   * @return possible object is {@link ListOfAttachmentType }
   */
  public ListOfAttachmentType getAvailabilityResponseListOfAttachment() {
    return availabilityResponseListOfAttachment;
  }

  /**
   * Sets the value of the availabilityResponseListOfAttachment property.
   * 
   * @param value allowed object is {@link ListOfAttachmentType }
   */
  public void setAvailabilityResponseListOfAttachment(ListOfAttachmentType value) {
    this.availabilityResponseListOfAttachment = value;
  }

}
