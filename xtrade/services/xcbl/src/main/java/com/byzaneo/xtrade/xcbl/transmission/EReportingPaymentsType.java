package com.byzaneo.xtrade.xcbl.transmission;

import javax.xml.bind.annotation.*;

import lombok.*;

@XmlRootElement(name = "EReportingPayment")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "EReportingPaymentsType", propOrder = {
    "payments"
})
@Getter
@Setter
public class EReportingPaymentsType implements java.io.Serializable {

  private static final long serialVersionUID = 1L;

  @XmlElement(name = "Payments")
  private EReportingPaymentType payments;

}
