//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * contains a list of OrderConfirmationItemDetails.
 * <p>
 * Java class for ListOfOrderConfirmationItemDetailType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ListOfOrderConfirmationItemDetailType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="OrderConfirmationItemDetail" type="{rrn:org.xcbl:schemas/xcbl/v4_0/ordermanagement/v1_0/ordermanagement.xsd}OrderConfirmationItemDetailType" maxOccurs="unbounded"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ListOfOrderConfirmationItemDetailType", propOrder = {
    "orderConfirmationItemDetail"
})
public class ListOfOrderConfirmationItemDetailType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "OrderConfirmationItemDetail", required = true)
  protected List<OrderConfirmationItemDetailType> orderConfirmationItemDetail;

  /**
   * Gets the value of the orderConfirmationItemDetail property.
   * <p>
   * This accessor method returns a reference to the live list, not a snapshot. Therefore any modification you make to the returned list
   * will be present inside the JAXB object. This is why there is not a <CODE>set</CODE> method for the orderConfirmationItemDetail
   * property.
   * <p>
   * For example, to add a new item, do as follows:
   * 
   * <pre>
   * getOrderConfirmationItemDetail().add(newItem);
   * </pre>
   * <p>
   * Objects of the following type(s) are allowed in the list {@link OrderConfirmationItemDetailType }
   */
  public List<OrderConfirmationItemDetailType> getOrderConfirmationItemDetail() {
    if (orderConfirmationItemDetail == null) {
      orderConfirmationItemDetail = new ArrayList<OrderConfirmationItemDetailType>();
    }
    return this.orderConfirmationItemDetail;
  }

}
