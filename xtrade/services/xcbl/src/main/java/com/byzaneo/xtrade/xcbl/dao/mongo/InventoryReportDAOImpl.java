package com.byzaneo.xtrade.xcbl.dao.mongo;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.*;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.byzaneo.xtrade.dao.CachingStatusDAO;
import com.byzaneo.xtrade.dao.mongo.MongoIndexableDAO;
import com.byzaneo.xtrade.xcbl.bean.InventoryReport;
import com.byzaneo.xtrade.xcbl.dao.InventoryReportDAO;

/**
 * <AUTHOR> Nicolas <<EMAIL>>
 * @company Generix
 * @date Nov 13, 2015
 */

@Repository(InventoryReportDAO.DAO_NAME)
public class InventoryReportDAOImpl extends MongoIndexableDAO<InventoryReport> implements InventoryReportDAO {

  @Autowired
  private CachingStatusDAO cachingStatusDAO;

  @Override
  @Transactional(readOnly = true)
  public List<String> findAllStatus(String owner) {
    return cachingStatusDAO.findByIdxType(InventoryReport.class, owner);
  }

  @Override
  @Transactional(readOnly = true)
  public List<InventoryReport> findByFromAndTo(String from, String to) {
    Query query = new Query();
    query.addCriteria(Criteria.where("from")
        .is(from)
        .and("to")
        .is(to));
    return this.template.find(query, InventoryReport.class);
  }

}
