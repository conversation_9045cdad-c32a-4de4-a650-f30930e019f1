package com.byzaneo.xtrade.xcbl.service;

import static com.byzaneo.commons.util.GsonHelper.getGson;
import static com.byzaneo.commons.util.JAXBHelper.marshal;
import static com.byzaneo.commons.util.JAXBHelper.unmarshal;
import static com.byzaneo.query.builder.Clauses.equal;
import static com.byzaneo.query.builder.Clauses.gte;
import static com.byzaneo.query.builder.Clauses.lte;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static com.byzaneo.xtrade.xcbl.util.XcblReferenceHelper.getASNOnLinesReferences;
import static com.byzaneo.xtrade.xcbl.util.XcblReferenceHelper.getASNReference;
import static com.byzaneo.xtrade.xcbl.util.XcblReferenceHelper.getASNReferences;
import static com.byzaneo.xtrade.xcbl.util.XcblReferenceHelper.getBlanketOrderReferences;
import static com.byzaneo.xtrade.xcbl.util.XcblReferenceHelper.getOrderChangeReference;
import static com.byzaneo.xtrade.xcbl.util.XcblReferenceHelper.getOrderOnLinesReferences;
import static com.byzaneo.xtrade.xcbl.util.XcblReferenceHelper.getOrderReference;
import static com.byzaneo.xtrade.xcbl.util.XcblReferenceHelper.getOrderReferences;
import static com.byzaneo.xtrade.xcbl.util.XcblReferenceHelper.getProformaInvoiceReferences;
import static com.byzaneo.xtrade.xcbl.util.XcblReferenceHelper.getRelatedInvoiceReferences;
import static java.lang.System.currentTimeMillis;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static java.util.Collections.emptySet;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.commons.lang3.time.DateUtils.setHours;
import static org.apache.commons.lang3.time.DateUtils.setMinutes;
import static org.apache.commons.lang3.time.DateUtils.setSeconds;
import static org.apache.commons.lang3.time.DateUtils.truncate;
import static org.slf4j.LoggerFactory.getLogger;

import java.io.*;
import java.util.*;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.stereotype.Service;

import com.byzaneo.commons.service.ServiceException;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.QueryBuilder;
import com.byzaneo.query.clause.*;
import com.byzaneo.xtrade.xcbl.api.XcblDocument;
import com.byzaneo.xtrade.xcbl.bean.*;
import com.byzaneo.xtrade.xcbl.dao.XcblDAO;
import com.byzaneo.xtrade.xcbl.util.XcblReferenceHelper.Reference;

/**
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date Mar 21, 2014
 * @since 7.0 XTD-394
 */
@Service(XcblService.SERVICE_NAME)
public class XcblServiceImpl implements XcblService {
  private static final Logger log = getLogger(XcblServiceImpl.class);

  // - REFERENCE QUERIES -
  // 0:doc.owners, 1:doc.from, 2:doc.to, 3:ref.number, 4:ref.date(truncate), 5:ref.date(ceiling), 6:ref.type
  private static final Query BQ_REFERENCE_BASE = createBuilder()
      .and(equal(XcblDocument.FIELD_OWNERS, "(0)"))
      .or(equal(XcblDocument.FIELD_FROM, "(1)"), equal(XcblDocument.FIELD_TO, "(1)"))
      .or(equal(XcblDocument.FIELD_FROM, "(2)"), equal(XcblDocument.FIELD_TO, "(2)"))
      .query();

  private enum Xcbl {
    Order(
        OrderIndex.class,
        createBuilder(BQ_REFERENCE_BASE).and(
            equal("buyerOrderNumber", "(3)"),
            gte("issueDate", "(4)"),
            lte("issueDate", "(5)")),
        equal("orderType", "(6)")),
    OrderChange(
        OrderChange.class,
        createBuilder(BQ_REFERENCE_BASE).and(
            equal("changeOrderHeader.changeOrderNumber.buyerChangeOrderNumber", "(3)"),
            gte("changeOrderHeader.changeOrderIssueDate", "(4)"),
            lte("changeOrderHeader.changeOrderIssueDate", "(5)")),
        new OrClause(
            equal("changeOrderHeader.changeType.changeTypeCoded", "(6)"),
            equal("changeOrderHeader.changeType.changeTypeCodedOther", "(6)"))),
    OrderResponse(
        OrderResponse.class,
        createBuilder(BQ_REFERENCE_BASE).and(
            equal("orderResponseHeader.orderResponseNumber.buyerOrderResponseNumber", "(3)"),
            gte("orderResponseHeader.orderResponseIssueDate", "(4)"),
            lte("orderResponseHeader.orderResponseIssueDate", "(5)")),
        new OrClause(
            equal("orderResponseHeader.orderResponseDocTypeCoded", "(6)"),
            equal("orderResponseHeader.orderResponseDocTypeCodedOther", "(6)"))),
    AdvanceShipmentNotice(
        AdvanceShipmentNotice.class,
        createBuilder(BQ_REFERENCE_BASE).and(
            equal("asnHeader.asnNumber", "(3)"),
            gte("asnHeader.asnIssueDate", "(4)"),
            lte("asnHeader.asnIssueDate", "(5)")),
        new OrClause(
            equal("asnHeader.asnType.asnTypeCoded", "(6)"),
            equal("asnHeader.asnType.asnTypeCodedOther", "(6)"))),
    Invoice(
        com.byzaneo.xtrade.xcbl.bean.InvoiceIndex.class,
        createBuilder(BQ_REFERENCE_BASE).and(
            equal("invoiceNumber", "(3)"),
            gte("invoiceIssueDate", "(4)"),
            lte("invoiceIssueDate", "(5)")),
        new OrClause(
            equal("invoiceTypeCoded", "(6)"),
            equal("invoiceTypeCodedOther", "(6)"))),
    Penalty(
        com.byzaneo.xtrade.xcbl.bean.Penalty.class,
        createBuilder(BQ_REFERENCE_BASE).and(
            equal("goodsReceiptHeader.goodsReceiptID", "(3)"),
            gte("goodsReceiptHeader.goodsReceiptIssueDate", "(4)"),
            lte("goodsReceiptHeader.goodsReceiptIssueDate", "(5)")),
        new OrClause(
            equal("goodsReceiptHeader.goodsReceiptTypeCoded", "(6)"),
            equal("goodsReceiptHeader.goodsReceiptTypeOther", "(6)"))),
    Reception(
        com.byzaneo.xtrade.xcbl.bean.ReceptionIndex.class,
        createBuilder(BQ_REFERENCE_BASE).and(
            equal("receptionNumber", "(3)"),
            gte("receptionProcessDateTime", "(4)"),
            lte("receptionProcessDateTime", "(5)")),
        new OrClause(
            equal("receptionType", "(6)"),
            equal("receptionTypeOther", "(6)")));

    private final Class<? extends XcblDocument> type;
    private final QueryBuilder referenceQueryBuilder;
    private final Clause typeClause;

    @SuppressWarnings("unchecked")
    private Xcbl(Class<?> type, QueryBuilder referenceQueryBuilder, Clause typeClause) {
      this.type = (Class<? extends XcblDocument>) type;
      this.referenceQueryBuilder = referenceQueryBuilder;
      this.typeClause = typeClause;
    }

    public Query toReferenceQuery(Reference reference, String type, XcblDocument referer, boolean exactTime) {
      if (reference == null || !reference.isValid())
        return null;

      // parameters
      List<Object> params = new ArrayList<>(asList(
          referer.getOwners(),
          referer.getFrom(),
          referer.getTo(),
          reference.getNumber()));

      if (exactTime) {
        params.add(reference.getDate());
        params.add(reference.getDate());
      }
      else {
        params.add(truncate(reference.getDate(), Calendar.DAY_OF_MONTH));
        params.add(setSeconds(setMinutes(setHours(truncate(reference.getDate(), Calendar.DAY_OF_MONTH), 23), 59), 59));
      }

      // typed query
      if (reference.isTyped() || isNotBlank(type)) {
        params.add(isNotBlank(type) ? type : reference.getType());
        return createBuilder(this.referenceQueryBuilder.query())
            .and(this.typeClause)
            .query(params, false);
      }

      // un-typed query
      return this.referenceQueryBuilder.query(params, false);
    }

    public static Xcbl valueOf(XcblDocument document) {
      if (document == null)
        return null;
      for (Xcbl xcbl : values()) {
        if (xcbl.type.isInstance(document))
          return xcbl;
      }
      return null;
    }

  }

  // - DAO -
  // document storage
  @Autowired
  private List<XcblDAO<? extends XcblDocument>> daos;

  /** XCBL DAO per DAO's managed entity class */
  private Map<Class<? extends XcblDocument>, XcblDAO<? extends XcblDocument>> daoByEntityClass;

  /** @see com.byzaneo.commons.service.Initializable#init() */
  @Override
  @PostConstruct
  public void init() throws Exception {
    final long start = currentTimeMillis();
    log.info("STARTING XCBL SERVICE...");

    if (isNotEmpty(daos)) {
      log.info("\t- XCBL repositories");
      this.daoByEntityClass = new HashMap<>(daos.size());
      for (XcblDAO<?> dao : daos) {
        log.debug("\t\t. {} ({})", dao.getEntityName(), dao.getEntityClass());
        this.daoByEntityClass.put(dao.getEntityClass(), dao);
      }
    }
    else {
      log.info("\t- XCBL persistency *DISABLED*");
      this.daoByEntityClass = emptyMap();
    }

    log.info("XCBL SERVICE STARTED in {}ms.", currentTimeMillis() - start);
  }

  /* -- XML -- */

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#fromXml(java.lang.Class, java.io.InputStream) */
  @Override
  public <T extends XcblDocument> T fromXml(Class<T> type, InputStream stream) {
    try {
      return unmarshal(type, stream);
    }
    catch (Exception e) {
      log.error("Error unmarshalling XCBL type '{}': {}", type, e.getMessage());
      return null;
    }
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#toXml(XcblDocument, OutputStream) */
  @Override
  public void toXml(XcblDocument document, OutputStream output) {
    try {
      marshal(document, output);
    }
    catch (Exception e) {
      log.error("Error marshalling XCBL document: {} ({})", document, e.getMessage());
    }
  }

  /* -- JSON -- */

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#fromJson(java.lang.Class, Reader) */
  @Override
  public <T extends XcblDocument> T fromJson(Class<T> type, Reader reader) {
    return getGson().fromJson(reader, type);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#toJson(XcblDocument, Appendable) */
  @Override
  public void toJson(XcblDocument document, Appendable writer) {
    getGson().toJson(document, writer);
  }

  /* -- DOCUMENT STORAGE -- */

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#get(java.lang.Class, java.lang.String) */
  @Override
  public <T extends XcblDocument> T get(Class<T> type, String identifier) {
    if (type == null || identifier == null)
      return null;
    return this.getDao(type)
        .findById(identifier);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#save(XcblDocument)) */
  @Override
  public <T extends XcblDocument> T save(T document) {
    return document == null ? null
        : this.getDao(document)
            .store(document);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#searchOne(java.lang.Class, java.lang.String) */
  @Override
  public <T extends XcblDocument> T searchOne(Class<T> type, String jsonQuery) {
    return this.getDao(type)
        .findOneByJson(jsonQuery);
  }

  /**
   * @see com.byzaneo.xtrade.xcbl.service.XcblService#search(java.lang.Class, java.lang.String, org.springframework.data.domain.Pageable)
   */
  @Override
  public <T extends XcblDocument> Page<T> search(Class<T> type, String jsonQuery, Pageable pageable) {
    return this.getDao(type)
        .findByJson(jsonQuery, pageable);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#remove(XcblDocument)) */
  @Override
  public void remove(XcblDocument document) {
    if (document == null)
      return;
    this.getDao(document)
        .remove(document);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#searchDocumentStatus(java.lang.Class) */
  @Override
  public <T extends XcblDocument> List<String> searchDocumentStatus(Class<T> type, String owner) {
    return this.getDao(type)
        .findAllStatus(owner);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#searchFieldDistinct(Class, String, Query) */
  @Override
  public <T extends XcblDocument> List<String> searchFieldDistinct(Class<T> type, String key, Query query) {
    return this.getDao(type)
        .distinct(type, key, query);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#searchFieldDistinct(Class, String, Query) */
  @Override
  public <T extends XcblDocument, R> List<R> aggregate(Class<T> inputType, Class<R> outputType, Query match,
      AggregationOperation... operations) {
    return this.getDao(inputType)
        .aggregate(inputType, outputType, match, operations);
  }

  /**
   * @see com.byzaneo.xtrade.xcbl.service.XcblService#addFieldIndex(java.lang.Class, java.lang.String, java.lang.String,
   *      org.springframework.data.domain.Sort.Direction)
   */
  @Override
  public <T extends XcblDocument> void addFieldIndex(Class<T> type, String field, String indexName, Direction sort) {
    this.getDao(type)
        .createIndex(type, field, indexName, sort);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#removeFieldIndex(java.lang.Class, java.lang.String) */
  @Override
  public <T extends XcblDocument> void removeFieldIndex(Class<T> type, String indexName) {
    this.getDao(type)
        .deleteIndex(type, indexName);
  }

  /*
   * -- REFERENCE --
   */

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#getReferencedBlanketOrders(com.byzaneo.xtrade.xcbl.bean.Order) */
  @Override
  public List<OrderIndex> getReferencedBlanketOrders(OrderIndex order) {
    return this.resolveReferencedEntities(
        getBlanketOrderReferences(order),
        Xcbl.Order,
        ORDER_BLANKET,
        order,
        true);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#getReferencedOrders(com.byzaneo.xtrade.xcbl.bean.OrderChange) */
  @Override
  public List<OrderIndex> getReferencedOrders(OrderChange orderChange) {
    return this.resolveReferencedEntities(
        getOrderReference(orderChange),
        Xcbl.Order,
        orderChange,
        true);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#getReferencedOrders(com.byzaneo.xtrade.xcbl.bean.OrderResponse) */
  @Override
  public List<OrderIndex> getReferencedOrders(OrderResponse orderResponse) {
    return this.resolveReferencedEntities(
        getOrderReference(orderResponse),
        Xcbl.Order,
        orderResponse,
        true);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#getReferencedOrderChanges(com.byzaneo.xtrade.xcbl.bean.OrderResponse) */
  @Override
  public List<OrderChange> getReferencedOrderChanges(OrderResponse orderResponse) {
    return this.resolveReferencedEntities(
        getOrderChangeReference(orderResponse),
        Xcbl.OrderChange,
        orderResponse,
        true);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#getReferencedOrders(com.byzaneo.xtrade.xcbl.bean.AdvanceShipmentNotice) */
  @Override
  public List<OrderIndex> getReferencedOrders(AdvanceShipmentNotice asn) {
    return this.getReferencedOrders(asn, true);
  }

  private List<OrderIndex> getReferencedOrders(AdvanceShipmentNotice asn, boolean exactTime) {
    return this.resolveReferencedEntities(
        getOrderReferences(asn),
        Xcbl.Order,
        asn,
        exactTime);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#getReferencedOrders(com.byzaneo.xtrade.xcbl.bean.Invoice) */
  @Override
  public List<OrderIndex> getReferencedOrders(InvoiceIndex invoice) {
    return this.getReferencedOrders(invoice, true);
  }

  private List<OrderIndex> getReferencedOrders(InvoiceIndex invoice, boolean exactTime) {
    return this.resolveReferencedEntities(
        getOrderReferences(invoice),
        Xcbl.Order,
        invoice,
        exactTime);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#getReferencedOrdersOnLines(com.byzaneo.xtrade.xcbl.bean.AdvanceShipmentNotice) */
  @Override
  public List<OrderIndex> getReferencedOrdersOnLines(AdvanceShipmentNotice asn) {
    return this.getReferencedOrdersOnLines(asn, true);
  }

  private List<OrderIndex> getReferencedOrdersOnLines(AdvanceShipmentNotice asn, boolean exactTime) {
    return this.resolveReferencedEntities(
        getOrderOnLinesReferences(asn),
        Xcbl.Order,
        asn,
        exactTime);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#getReferencedProformaInvoices(com.byzaneo.xtrade.xcbl.bean.Invoice) */
  @Override
  public List<InvoiceIndex> getReferencedProformaInvoices(InvoiceIndex invoice) {
    return this.getReferencedProformaInvoices(invoice, true);
  }

  private List<InvoiceIndex> getReferencedProformaInvoices(InvoiceIndex invoice, boolean exactTime) {
    return this.resolveReferencedEntities(
        getProformaInvoiceReferences(invoice),
        Xcbl.Invoice,
        INVOICE_PROFORMA,
        invoice,
        exactTime);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#getReferencedAdvanceShipmentNoticesOnLines(com.byzaneo.xtrade.xcbl.bean.Invoice) */
  @Override
  public List<AdvanceShipmentNotice> getReferencedAdvanceShipmentNoticesOnLines(Invoice invoice) {
    return this.getReferencedAdvanceShipmentNoticesOnLines(invoice, true);
  }

  private List<AdvanceShipmentNotice> getReferencedAdvanceShipmentNoticesOnLines(Invoice invoice, boolean exactTime) {
    return this.resolveReferencedEntities(
        getASNOnLinesReferences(invoice),
        Xcbl.AdvanceShipmentNotice,
        invoice,
        exactTime);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#getReferencedRelatedInvoices(com.byzaneo.xtrade.xcbl.bean.Invoice) */
  @Override
  public List<InvoiceIndex> getReferencedRelatedInvoices(InvoiceIndex invoice) {
    return this.getReferencedRelatedInvoices(invoice, true);
  }

  private List<InvoiceIndex> getReferencedRelatedInvoices(InvoiceIndex invoice, boolean exactTime) {
    return this.resolveReferencedEntities(
        getRelatedInvoiceReferences(invoice),
        Xcbl.Invoice,
        invoice,
        exactTime);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#getReferencedAdvanceShipmentNotices(com.byzaneo.xtrade.xcbl.bean.Invoice) */
  @Override
  public List<AdvanceShipmentNotice> getReferencedAdvanceShipmentNotices(InvoiceIndex invoice) {
    return this.getReferencedAdvanceShipmentNotices(invoice, true);
  }

  /**
   * @param invoice holding the ASN references
   * @param exactTime if search reference with the exact date time of the reference
   * @return the AdvanceShipmentNotice list referenced in the header of given invoice
   * @return
   * @see com.byzaneo.xtrade.xcbl.service.XcblService#getReferencedAdvanceShipmentNotices(com.byzaneo.xtrade.xcbl.bean.Invoice)
   */
  private List<AdvanceShipmentNotice> getReferencedAdvanceShipmentNotices(InvoiceIndex invoice, boolean exactTime) {
    return this.resolveReferencedEntities(
        getASNReferences(invoice),
        Xcbl.AdvanceShipmentNotice,
        invoice,
        exactTime);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#getReferencedAdvanceShipmentNotices(com.byzaneo.xtrade.xcbl.bean.Penalty) */
  @Override
  public List<AdvanceShipmentNotice> getReferencedAdvanceShipmentNotices(Penalty penalty) {
    return this.resolveReferencedEntities(
        getASNReference(penalty),
        Xcbl.AdvanceShipmentNotice,
        penalty,
        true);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#getReferencedOrders(com.byzaneo.xtrade.xcbl.bean.Penalty) */
  @Override
  public List<OrderIndex> getReferencedOrders(Penalty penalty) {
    return this.resolveReferencedEntities(
        getOrderReferences(penalty),
        Xcbl.Order,
        penalty,
        true);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#getReferencedAdvanceShipmentNoticesOnLines(com.byzaneo.xtrade.xcbl.bean.Penalty) */
  @Override
  public List<AdvanceShipmentNotice> getReferencedAdvanceShipmentNoticesOnLines(Penalty penalty) {
    return this.resolveReferencedEntities(
        getASNOnLinesReferences(penalty),
        Xcbl.AdvanceShipmentNotice,
        penalty,
        true);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#getReferencedOrdersOnLines(com.byzaneo.xtrade.xcbl.bean.Penalty) */
  @Override
  public List<OrderIndex> getReferencedOrdersOnLines(Penalty penalty) {
    return this.resolveReferencedEntities(
        getOrderOnLinesReferences(penalty),
        Xcbl.Order,
        penalty,
        true);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#getReferences(com.byzaneo.xtrade.xcbl.api.XcblDocument) */
  @Override
  public Collection<XcblDocument> getReferences(XcblDocument document) {
    return this.getReferences(document, true);
  }

  /** @see com.byzaneo.xtrade.xcbl.service.XcblService#getReferences(com.byzaneo.xtrade.xcbl.api.XcblDocument, boolean exactTime) */
  @Override
  public Collection<XcblDocument> getReferences(XcblDocument document, boolean exactTime) {
    final Xcbl xcbl = Xcbl.valueOf(document);
    if (xcbl == null)
      return emptySet();

    final Set<XcblDocument> refs = new LinkedHashSet<>();
    switch (xcbl) {
    case OrderChange:
      // order
      refs.addAll(this.getReferencedOrders((OrderChange) document));
      break;
    case AdvanceShipmentNotice:
      AdvanceShipmentNotice asn = (AdvanceShipmentNotice) document;
      // order
      refs.addAll(this.getReferencedOrders(asn, exactTime));
      refs.addAll(this.getReferencedOrdersOnLines(asn, exactTime));
      break;
    case Invoice:
      InvoiceIndex invoice = (InvoiceIndex) document;
      // order
      refs.addAll(this.getReferencedOrders(invoice, exactTime));
      // asn
      refs.addAll(this.getReferencedAdvanceShipmentNotices(invoice, exactTime));
      // related invoice
      refs.addAll(this.getReferencedRelatedInvoices(invoice, exactTime));
      // proforma invoice
      refs.addAll(this.getReferencedProformaInvoices(invoice, exactTime));
      break;
    case Order:
      // blanket order
      refs.addAll(this.getReferencedBlanketOrders((OrderIndex) document));
      break;
    case OrderResponse:
      OrderResponse orderResponse = (OrderResponse) document;
      // order
      refs.addAll(this.getReferencedOrders(orderResponse));
      // order change
      refs.addAll(this.getReferencedOrderChanges(orderResponse));
      break;
    case Penalty:
      Penalty penalty = (Penalty) document;
      // order
      refs.addAll(this.getReferencedOrders(penalty));
      refs.addAll(this.getReferencedOrdersOnLines(penalty));
      // asn
      refs.addAll(this.getReferencedAdvanceShipmentNotices(penalty));
      refs.addAll(this.getReferencedAdvanceShipmentNoticesOnLines(penalty));
      break;
    default:
      break;
    }
    return refs;
  }

  /*
   * -- PRIVATE --
   */

  private <T extends XcblDocument> List<T> resolveReferencedEntities(
      Optional<Reference> reference, Xcbl xcbl,
      XcblDocument referer, boolean exactTime) {
    return reference.isPresent()
        ? resolveReferencedEntities(asList(reference.get()), xcbl, null, referer, exactTime)
        : emptyList();
  }

  private <T extends XcblDocument> List<T> resolveReferencedEntities(
      List<Reference> references, Xcbl xcbl,
      XcblDocument referer, boolean exactTime) {
    return this.resolveReferencedEntities(references, xcbl, null, referer, exactTime);
  }

  private <T extends XcblDocument> List<T> resolveReferencedEntities(
      List<Reference> references, Xcbl xcbl, String type,
      XcblDocument referer, boolean exactTime) {
    // - sanity -
    // references
    if (isEmpty(references))
      return emptyList();
    // repository
    XcblDAO<T> dao = this.getDao(xcbl);
    if (dao == null) {
      log.warn("Repository not found for: {}", xcbl);
      return emptyList();
    }
    // security
    if (referer.getOwners() == null ||
        referer.getFrom() == null ||
        referer.getTo() == null) {
      log.warn("Missing security properties (owners, from and/or to) on: {}", referer);
      return emptyList();
    }

    // - retrieves entities -
    final List<T> entities = references.stream()
        .map(ref -> xcbl.toReferenceQuery(ref, type, referer, exactTime))
        .flatMap(qry -> dao.search(qry)
            .stream())
        .collect(toList());

    return entities;
  }

  @SuppressWarnings("unchecked")
  private <T extends XcblDocument> XcblDAO<T> getDao(T entity) {
    return entity == null ? null : this.getDao((Class<T>) entity.getClass());
  }

  @SuppressWarnings("unchecked")
  private <T extends XcblDocument> XcblDAO<T> getDao(Xcbl xcbl) {
    return xcbl == null ? null : this.getDao((Class<T>) xcbl.type);
  }

  @SuppressWarnings("unchecked")
  private <T extends XcblDocument> XcblDAO<T> getDao(Class<T> entityType) {
    if (!this.daoByEntityClass.containsKey(entityType))
      throw new ServiceException("No data repository found for entity type: %s", entityType);
    return (XcblDAO<T>) this.daoByEntityClass.get(entityType);
  }

}
