//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.6 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2014.03.21 at 09:11:30 AM CET 
//

package com.byzaneo.xtrade.xcbl.v4_0.core.core;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * defines a range which a measurement may fall in by a minimum and maximum value.
 * <p>
 * Java class for MeasurementRangeType complex type.
 * <p>
 * The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="MeasurementRangeType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="MinimumValue" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}MinimumValueType"/>
 *         &lt;element name="MaximumValue" type="{rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd}MaximumValueType"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "MeasurementRangeType", propOrder = {
    "minimumValue",
    "maximumValue"
})
public class MeasurementRangeType implements java.io.Serializable {
  private static final long serialVersionUID = 1L;

  @XmlElement(name = "MinimumValue", required = true)
  protected MinimumValueType minimumValue;
  @XmlElement(name = "MaximumValue", required = true)
  protected MaximumValueType maximumValue;

  /**
   * Gets the value of the minimumValue property.
   * 
   * @return possible object is {@link MinimumValueType }
   */
  public MinimumValueType getMinimumValue() {
    return minimumValue;
  }

  /**
   * Sets the value of the minimumValue property.
   * 
   * @param value allowed object is {@link MinimumValueType }
   */
  public void setMinimumValue(MinimumValueType value) {
    this.minimumValue = value;
  }

  /**
   * Gets the value of the maximumValue property.
   * 
   * @return possible object is {@link MaximumValueType }
   */
  public MaximumValueType getMaximumValue() {
    return maximumValue;
  }

  /**
   * Sets the value of the maximumValue property.
   * 
   * @param value allowed object is {@link MaximumValueType }
   */
  public void setMaximumValue(MaximumValueType value) {
    this.maximumValue = value;
  }

}
