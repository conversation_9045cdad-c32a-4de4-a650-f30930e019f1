<?xml version="1.0" encoding="ISO-8859-1"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:p="http://www.springframework.org/schema/p"
	xsi:schemaLocation="
			http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

	<!-- E X T E R N A L P R O P E R T I E S -->
	<bean id="comPropertyConfigurer" class="com.byzaneo.commons.util.PropertyPlaceholderConfigurer">
		<property name="locations">
			<list>
				<value>classpath:/commons.properties</value>
				<value>classpath:/location.properties</value>
				<value>classpath:/xtrade.properties</value>
				<value>classpath:/dev/dev.properties</value>
			</list>
		</property>
	</bean>

	<!-- X T R A D E -->
	<import resource="classpath:/xtrade.beans.xml" />
</beans>
