/**
 * Byzaneo Query Field More
 *
 * <AUTHOR> LEMESLE
 */

Byzaneo.Search.Field.MoreField = Byzaneo.Search.Field.MultiselectField.extend({

	init : function(bqlFieldMore, that) {
		this._super(bqlFieldMore, that);
		this.createApplyButton();
	},

    attachMultiselectItem : function() {
        /* Html Component */
        this.bqlFieldName.component.setAttribute("multiple", "multiple");

	    this._super();
	},

	applyFilters: function(){
		var that = this;
		var index =that.config.currentIndex
		 var bqlFieldName;
         for(var i=0; i<that.widgetInstance.instance.bqlFieldNames.length; i++) {
             if(that.widgetInstance.instance.bqlFieldNames[i].hasToBeModified) {
                 that.updateField(i,that.widgetInstance.options.minDisplayedFields, index++)
                 that.config.currentIndex=index;
             }
             //make sure that this property is reset
             that.widgetInstance.instance.bqlFieldNames[i].hasToBeModified=false
         }
         
         $("#apply-button").hide();
         
         that.rebuild();
         $(that.htmlContent).find('button.multiselect').click();
	},
	
	updateField: function(itemIndex, minDisplayedFields, componentIndex){
		bqlFieldName = this.widgetInstance.instance.bqlFieldNames[itemIndex];
        if(bqlFieldName.hasToAdded==true){
       	 /* Add field */
       	 Byzaneo.Search.Component.prototype.createComponent(bqlFieldName, this.widgetInstance.mode.basicMode,componentIndex);
        }
        else{
       	 /* Delete field */
       	 this.removeField(bqlFieldName, this);
       	
       	 //if this bqlfield could not be removed, it is kept selected in dropdown list
       	 if( bqlFieldName.selected==true){
       		this.updateFieldInList(this.id,itemIndex, minDisplayedFields,true);
       	 }
        	 }
   	 bqlFieldName.hasToBeModified=false;
	},
	
	cancelSelectedFilters: function(that){
		 var itemId = that.id +  '-more';
		 for(var i=0; i<that.instance.bqlFieldNames.length; i++) {
			if(that.instance.bqlFieldNames[i].hasToBeModified==true){
				if(that.instance.bqlFieldNames[i].hasToAdded==true){
					this.updateFieldInList(itemId,i,that.options.minDisplayedFields,false);	
					that.instance.bqlFieldNames[i].hasToBeModified=false;
				}
				else{
					this.updateFieldInList(itemId,i,that.options.minDisplayedFields,true);
					that.instance.bqlFieldNames[i].hasToBeModified=false;
				}
			}
			//make sure that this property is reset
			that.instance.bqlFieldNames[i].hasToBeModified=false
		 }
      //hide apply-button
      $("#apply-button").hide();
	},
	
	updateFieldInList: function(itemId,itemIndex, minDisplayedFields,checked){
		$('#' +itemId).siblings(".btn-group").find("ul > li > a > label > input")[itemIndex - minDisplayedFields].checked = checked
		var element = $($('#' + itemId).siblings(".btn-group").find("ul > li")[itemIndex + 1 - minDisplayedFields]);
		if(checked){
		  element.addClass('active');
		}
		else{
		  element.removeClass('active');
		 }
	},
	
    setConfig : function () {
        this._super();
        this.config.currentIndex=this.widgetInstance.instance.bqlFieldNames.length;
        this.config.maxHeight = 260;
        this.config.buttonText = function(options, select) {
            return this.more;
        };
        this.config.onDropdownShow = function(event) {
            (this.$ul).css('max-width', '200px');
        };

        var that = this;
        var index = that.widgetInstance.instance.bqlFieldNames.length;
        this.config.onChange = function(option, checked) {
            var bqlFieldName;
            for(var i=0; i<that.widgetInstance.instance.bqlFieldNames.length; i++) {
                if(option.val() == that.widgetInstance.instance.bqlFieldNames[i].displayName) {
                    bqlFieldName = that.widgetInstance.instance.bqlFieldNames[i];
                    break;
                }
            }
            bqlFieldName.hasToBeModified = !bqlFieldName.hasToBeModified;
            if(checked) {
                /* Add field */
                bqlFieldName.selected = true;
                bqlFieldName.hasToAdded = true
            }
            else {
            	 /* Delete field */
                bqlFieldName.hasToAdded = false

            }
             _.some(that.widgetInstance.instance.bqlFieldNames,'hasToBeModified') ?  $("#apply-button").show() :  $("#apply-button").show();
     
            that.rebuild();
            $(that.htmlContent).find('button.multiselect').click();
        };
    },

	/**
	 * Builds the option of this field
	 */
	buildOption : function() {
        this._super();

		for(var i = 0; i<this.widgetInstance.instance.bqlFieldNames.length; i++) {
            if(this.widgetInstance.instance.bqlFieldNames[i].keepDisplay)
                continue;
			var value = this.widgetInstance.instance.bqlFieldNames[i].displayName;
			var option = Byzaneo.Search.ComponentHelper.prototype.createHtmlOption(
					value, value);
			if(this.widgetInstance.instance.bqlFieldNames[i].selected) {
					option.setAttribute("selected", "selected");
			}
            $(this.bqlFieldName.component).append(option);
		}
		
	},

	createApplyButton : function(){
		var that=this;
		var buttonValue = JSON.parse(this.labels.multiselect.labelSearch)['apply'];
		var ulElement = $('#' + this.widgetInstance.id + '-content-footer').find("ul");
		//wrap with a new div
		$(ulElement).wrap("<div class='multiselect-container dropdown-menu'></div>")
		//add apply button
		$(ulElement).parent().append('<input type="button" id="apply-button" value=' + buttonValue + ' class="btn btn-default btn-secondary" style="'+
				' width: 100%; border-radius: 0;">')
				
	   // change style 
		$(ulElement).css("position","static");
		
		//attach event to the button
		  $("#apply-button").on({
              "click": function (e) {
            that.applyFilters();
              }
          });
	  // is hidden until at least a criteria is checked or unchecked
		  $("#apply-button").hide();
		
	},
	
    removeField : function(bqlFieldName, button) {
		if(!bqlFieldName.keepDisplay) {
            if(bqlFieldName.valueField.length>0 && bqlFieldName.valueField[0].length >0) {
                console.log("A value is present");
            }
            else {
                $(bqlFieldName.htmlComponent.htmlContent).remove();
                bqlFieldName.selected = false;
            }
		}
		else {
            console.log("Keep this field");
		}
	}
});