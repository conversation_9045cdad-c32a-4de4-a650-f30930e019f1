Byzaneo.Bql.AutoComplete = Class.extend({
	/**
	 * Initialises autocomplete by setting options, and assigning event handler
	 * to input field.
	 * 
	 * @method {public} init
	 * @param {Object} options
	 *
	 */
	init : function(options) {
		var that = this;
		var inFocus;
		/* Desactive l'utilisation de certaines touches clavier.
		 * ex : 9 -> Tabulation
		 */
		this.INVALID_KEYS = {
	              9: true,
	             13: true,
	             14: true,
	             25: true,
	             27: true,
	             38: true,
	             40: true,
	            224: true
	        };
		this.set(options);
		this.field = this.field || jQuery("#" + this.fieldID);
		// turn off browser default autocomplete
		this.field.attr("autocomplete", "off")
		.keyup(function(e) {
			if (!that.disabled) {
				that.keyUpHandler(e); 
			}
		})
		.keydown(function(e) {
			var ESC_KEY = 27;
			// do not clear field in IE
			if (e.keyCode === ESC_KEY && that.responseContainer && that.responseContainer.is(":visible")) {
				e.preventDefault(); 
			}
		})
		// this will stop the dropdown with the suggestions hiding whenever you
		// click the field
		.click(function(e) {
			if (inFocus === that) {
				e.stopPropagation();
			}
		})
		.blur(function() {
			// we don't want the request to come back and show suggestions if we
			// have already moved away from field
			if (that.pendingRequest) {
				that.pendingRequest.abort();
			}
		});

		this.addDropdownAdvice();

		if (options.delimChar) {
			this.addMultiSelectAdvice(options.delimChar);
		}
	},

	set : function(options) {
		for ( var name in options) {
			// safeguard to stop looping up the inheritance chain
			if (options.hasOwnProperty(name)) {
				this[name] = options[name];
			}
		}
	},

	/**
	 * Adds and manages state of dropdown control
	 * 
	 * @method {Public} addDropdownAdvice
	 */
	addDropdownAdvice : function() {
		var that = this;

		// add dropdown functionality to response container
		jQuery.aop.after({
			target : this,
			method : "buildResponseContainer"
		}, function(args) {
			this.dropdownController = new Byzaneo.Bql.Dropdown.AutoComplete({
				target : this,
				method : "renderSuggestions"
			}, this.responseContainer);

			jQuery.aop.after({
				target : this.dropdownController,
				method : "hideDropdown"
			}, function() {
				this.dropdown.removeClass("dropdown-ready");
			});

			return args;
		});

		// display dropdown afer suggestions are updated
		jQuery.aop.after({
			target : this,
			method : "renderSuggestions"
		}, function(args) {
			if(typeof this.dropdownController !== 'undefined') {
				if (args && args.length > 0) {
					this.dropdownController.displayDropdown();
	
					if (this.maxHeight && this.dropdownController.dropdown.prop("scrollHeight") > this.maxHeight) {
						this.dropdownController.dropdown.css({
							height : this.maxHeight,
							overflowX : "visible",
							overflowY : "scroll"
						})
					} else if (this.maxHeight) {
						this.dropdownController.dropdown.css({
							height : "",
							overflowX : "",
							overflowY : ""
						});
					}
					this.dropdownController.dropdown.addClass("dropdown-ready");
				} else {
					this.dropdownController.hideDropdown();
				}
				return args;
			}
		});

		// hide dropdown after suggestion value is applied to field
		jQuery.aop.after({
			target : this,
			method : "completeField"
		}, function(args) {
			this.dropdownController.hideDropdown();
			return args;
		});

		jQuery.aop.after({
			target : this,
			method : "keyUpHandler"
		}, function(e) {
			// only initialises once the field length is past set length
			if ((!(this.field.val().length >= this.minQueryLength) || e.keyCode === 27) && this.dropdownController && this.dropdownController.displayed) {
				this.dropdownController.hideDropdown();
				if (e.keyCode === 27) {
					e.stopPropagation();
				}
			}
			return e;
		});
	},

	/**
	 * Validates the keypress by making sure the field value is beyond the set
	 * threshold and the key was either an up or down arrow
	 * 
	 * @method {public} keyUpHandler
	 * @param {Object}
	 *            e - event object
	 */
	keyUpHandler: (function () {
        function callback() {
            if (!this.responseContainer) {
                this.buildResponseContainer();
            }
            // send value to dispatcher to check if we have already got the response or if we need to go
            // back to the server
            this.dispatcher(this.field.val());
        }

        return function (e) {
            // only initialises once the field length is past set length
            if (this.field.val().length >= this.minQueryLength) {
                // don't do anything if the key pressed is "enter" or "down" or "up" or "right" "left"
                if (!(e.keyCode in this.INVALID_KEYS) || (this.responseContainer && !this.responseContainer.is(":visible") && (e.keyCode == 38 || e.keyCode == 40))) {
                    callback.call(this);
                }
            }
            return e;
        };
    })(),

	/**
	 * Builds HTML container for suggestions. Positions container top position
	 * to be that of the field height
	 * 
	 * @method {public} buildResponseContainer
	 */
	buildResponseContainer : function() {
		var inputParent = this.field.parent().addClass('byzaneo-autocomplete');
		this.responseContainer = jQuery(document.createElement("div"));
		this.responseContainer.addClass("suggestions")
		this.positionResponseContainer();
		this.responseContainer.appendTo(inputParent);
	},

	positionResponseContainer : function() {
		this.responseContainer.css({
			top : this.field.outerHeight()
		});
	},

	/**
	 * Allows users to navigate/select suggestions using the keyboard
	 * @method {public} addSuggestionControls
	 */
	addSuggestionControls: function(suggestionNodes) {

		// reference to this for closures
		var that = this;

		/**
		 * Make sure the index is within the threshold
		 * Looks ugly! Has to be a better way.
		 * @method {private} evaluateIndex
		 * @param {Integer} idx
		 * @param {Integer} max
		 * @return {Integer} valid threshold
		 */
		var evaluateIndex = function(idx, max) {
			var minBoundary = (that.autoSelectFirst === false) ? -1 : 0;
			if (that.allowArrowCarousel) {
				if (idx > max) {
					return minBoundary;
				} else if (idx < minBoundary) {
					return max;
				} else {
					return idx;
				}
			}
			else {
				if (idx > max) {
					return max;
				} else if (idx < minBoundary) {
					that.responseContainer.scrollTop(0);
					return minBoundary;
				} else {
					return idx;
				}
			}
		};

		/**
		 * Highlights focused node and removes highlight from previous.
		 * Actual highlight styles to come from css, adding and removing classes here.
		 * @method {private} setActive
		 * @param {Integer} idx - Index of node to be highlighted
		 */
		var setActive = function(idx) {

			// if nothing is selected, select the first suggestion
			if (that.selectedIndex !== undefined && that.selectedIndex > -1) {
				that.suggestionNodes[that.selectedIndex][0].removeClass("active");
			}
			that.selectedIndex = evaluateIndex(idx, that.suggestionNodes.length-1);
			if (that.selectedIndex > -1) {
				that.suggestionNodes[that.selectedIndex][0].addClass("active");
			}
		};

		/**
		 * Checks to see if there is actually a suggestion in focus before attempting to use it
		 * @method {private} evaluateIfActive
		 * @returns {boolean}
		 */
		var evaluateIfActive = function() {
			return that.suggestionNodes && that.suggestionNodes[that.selectedIndex] &&
			that.suggestionNodes[that.selectedIndex][0].hasClass("active");
		};


		/**
		 * When the responseContainer (dropdown) is visible listen for keyboard events
		 * that represent focus or selection.
		 * @method {private} keyPressHandler
		 * @param {Object} e - event object
		 */
		var keyPressHandler = function(e) {
			// only use keyboard events if dropdown is visible
			if (that.responseContainer.is(":visible")) {
				// if enter key is pressed check that there is a node selected, then hide dropdown and complete field
				if (e.keyCode === 13) {
					// TODO : a revoir
					if (evaluateIfActive() && !that.pendingRequest /*&& e.isPropagationStopped()*/) {
						that.completeField(that.suggestionNodes[that.selectedIndex][1]);
					}
					e.preventDefault();
					// hack - stop propagation to prevent dialog from submitting. Looking for eg JIRA.Dropdown.current doesn't work.
					e.stopPropagation();
//					e.stopImmediatePropagation();
				}
			}
		};

		/**
		 * sets focus on suggestion nodes using the "up" and "down" arrows
		 * These events need to be fired on mouseup as modifier keys don't register on keypress
		 * @method {private} keyUpHandler
		 * @param {Object} e - event object
		 */
		var keyboardNavigateHandler = function(e) {

			// only use keyboard events if dropdown is visible
			if (that.responseContainer.is(":visible")) {

				// keep cursor inside input field
				if (that.field[0] !== document.activeElement){
					that.field.focus();
				}
				// move selection down when down arrow is pressed
				if (e.keyCode === 40) {
					setActive(that.selectedIndex + 1);
					if (that.selectedIndex >= 0) {
						// move selection up when up arrow is pressed
						var containerHeight = that.responseContainer.height();
						var bottom = that.suggestionNodes[that.selectedIndex][0].position().top + that.suggestionNodes[that.selectedIndex][0].outerHeight() ;

						if (bottom - containerHeight > 0){
							that.responseContainer.scrollTop(that.responseContainer.scrollTop() + bottom - containerHeight + 2);
						}
					} else {
						that.responseContainer.scrollTop(0);
					}
					e.preventDefault();
				} else if (e.keyCode === 38) {
					setActive(that.selectedIndex-1);
					if (that.selectedIndex >= 0) {
						// if tab key is pressed check that there is a node selected, then hide dropdown and complete field
						var top = that.suggestionNodes[that.selectedIndex][0].position().top;
						if (top < 0){
							that.responseContainer.scrollTop(that.responseContainer.scrollTop() + top - 2);
						}
					}
					e.preventDefault();
				} else if (e.keyCode === 9) {
					if (evaluateIfActive()) {
						that.completeField(that.suggestionNodes[that.selectedIndex][1]);
						e.preventDefault();
					} else {
						that.dropdownController.hideDropdown();
					}
				}
			}
		};

		if (suggestionNodes.length) {

			this.selectedIndex = 0;
			this.suggestionNodes = suggestionNodes;

			for (var i=0; i < that.suggestionNodes.length; i++) {
				var eventData = { instance: this, index: i };
				this.suggestionNodes[i][0]
				.bind("mouseover", eventData, activate)
				.bind("mouseout", eventData, deactivate)
				.bind("click", eventData, complete);
			}

			// make sure we don't bind more than once
			if (!this.keyboardHandlerBinded) {
				jQuery(this.field).keypress(keyPressHandler);
				jQuery(this.field).keydown(keyboardNavigateHandler);
				this.keyboardHandlerBinded = true;
			}

			// automatically select the first in the list
			if(that.autoSelectFirst === false) {
				setActive(-1);
			} else {
				setActive(0);
			}

			// sets the autocomplete singleton infocus var to this instance
			// is used to toggle event propagation. In short, the instance that it is set to will not hide the
			// dropdown each time you click the input field
			inFocus = this;
		}

		function activate(event) {
			if (that.dropdownController.displayed) {
				setActive(event.data.index);
			}
		}
		function deactivate(event) {
			if (event.data.index === 0) {
				that.selectedIndex = -1;
			}
			jQuery(this).removeClass("active");
		}
		function complete(event) {
			that.completeField(that.suggestionNodes[event.data.index][1]);
		}
	},
	
	/**
     * Disables autocomplete. Useful for shared inputs.
     * i.e The selection of a radio button may disable the instance
     * @method {Public} disable
     */
    disable: function() {
        this.disabled = true;
    },
    
    /**
     * Enables autocomplete. Useful for shared inputs.
     * i.e The selection of a radio button may disable the instance
     * @method {Public} enable
     */
    enable: function() {
        this.disabled = false;
    },
    
    /**
     * Adds value to input field
     * @method {public} completeField
     * @param {String} value
     */
    completeField: function(value) {
        if (value) {
            this.field.val(value).focus();
            this.field.trigger("change");
        }
    },
    
    /**
     * Returns the text from the start of the field up to the end of
     * the position where suggestions are generated from.
     */
    textToSuggestionCursorPosition: function () {
        return this.field.val();
    },

    /**
     * An ajax request filter that only allows one request at a time. If there is another it will abort then issue
     * the new request.
     *
     * @param options - jQuery formatted ajax options
     */
    _makeRequest: function (options) {
    	// lazy get parent form
		if (!this.form) {
			this.form = this.options.field.closest("form");
			this.formId = this.form[0].id;
		}
		var that = this;
		// start callback
		if (this.options.onstart) {
			this.options.onstart.call(this, value);
		}
		var opts = {
			source : this.options.id,
			update : this.options.id,
			formId : this.formId,
			onsuccess : options.success
		};
		// complete callback
		if (this.options.oncomplete) {
			opts.oncomplete = this.options.oncomplete;
		}
		// error callback
		if (this.options.onerror) {
			opts.onerror = this.options.onerror;
		}
		// process
		opts.process = this.options.process ? this.options.id + ' ' + this.options.process : this.options.id;
		if (this.options.global === false) {
			opts.global = false;
		}
		var fieldName = options.data.fieldName !== undefined ? options.data.fieldName : null;
		opts.params = [ {
			name : this.id + '_fieldname',
			value : encodeURIComponent(fieldName)
		}];
		if(options.data.fieldValue !== undefined)
			opts.params.push({
				name : this.id + '_fieldvalue',
				value : encodeURIComponent(options.data.fieldValue)
			});
		if(options.data.predicateName !== undefined)
			opts.params.push({
				name : this.id + '_predicate',
				value : encodeURIComponent(options.data.predicateName)
			});
		if ( fieldName!=null && this.bqlFieldNames!==undefined ) {
			var bqlField = null;
			for (var i = 0; i < this.bqlFieldNames.length; i++) {
				var curField = this.bqlFieldNames[i];
				if ( curField!=null && curField.value==fieldName ) {
					bqlField = curField;
					break;
				}
			}
			if ( bqlField!=null ) {
				/* Add by Romuald Lemesle for break circular structure */
				var bqlFieldCopy = {};
				bqlFieldCopy.auto = bqlField.auto;
				bqlFieldCopy.displayName = bqlField.displayName;
				bqlFieldCopy.operators = bqlField.operators;
				bqlFieldCopy.orderable = bqlField.orderable;
				bqlFieldCopy.rendered = bqlField.rendered;
				bqlFieldCopy.searchable = bqlField.searchable;
				bqlFieldCopy.selectItems = bqlField.selectItems;
				bqlFieldCopy.types = bqlField.types;
				bqlFieldCopy.value = bqlField.value
				opts.params.push({
					name : this.id + '_field',
					value : encodeURIComponent(JSON.stringify(bqlFieldCopy))
				});
			}
		}
		PrimeFaces.ajax.AjaxRequest(opts);
    },
    
    /**
     * Uses jquery empty command, this is VERY important as it unassigns handlers
     * used for mouseover, click events which expose an opportunity for memory leaks
     * @method {public} clearResponseContainer
     */
    clearResponseContainer: function() {
        this.responseContainer.empty();
        this.suggestionNodes = undefined;
    },
    
    /**
     * Adds in methods via AOP to handle multiple selections
     * @method {Public} addMultiSelectAdvice
     */
    addMultiSelectAdvice: function(delim) {

        // reference to this for closures
        var that = this;

        /**
         * Alerts user if value already exists
         * @method {private} alertUserValueAlreadyExists
         * @param {String} val - value that already exists, will be displayed in message to user.
         */
        var alertUserValueAlreadyExists = function(val) {

            // check if there is an existing alert before adding another
            if (!alertUserValueAlreadyExists.isAlerting) {

                alertUserValueAlreadyExists.isAlerting = true;

                // create alert node and append it to the input field's parent, fade it in then out with a short
                // delay in between.
                //TO DO: JRA-1800 - Needs i18n!
                var userAlert = jQuery(document.createElement("div"))
                .css({"float": "left", display: "none"})
                .addClass("warningBox")
                .html("Oops! You have already entered the value <em>" + val + "</em>" )
                .appendTo(that.field.parent())
                .show("fast", function(){
                    // display message for 4 seconds before fading out
                        userAlert.hide("fast",function(){
                            // removes element from dom
                            userAlert.remove();
                            alertUserValueAlreadyExists.isAlerting = false;
                        });
                });
            }
        };

      // rather than request the entire field return the last comma seperated value
        jQuery.aop.before({target: this, method: "dispatcher"}, function(innvocation){
            // matches everything after last comma
            var val = this.field.val();
            innvocation[0] = jQuery.trim(val.selectionRange(val.lastIndexOf(delim) + 1));
            return innvocation;
        });

        // rather than replacing this field just append the new value
        jQuery.aop.before({target: this, method: "completeField"}, function(args){
            var valueToAdd = args[0],
            // create array of values
            untrimmedVals = this.field.val().split(delim);
            // trim the values in the array so we avoid extra spaces being appended to the usernames - see JRA-20657
            var trimmedVals = jQuery(untrimmedVals).map(function() {
                    return jQuery.trim(this);
               }).get();
            // check if the value to append already exists. If it does then call alert to to tell user and sets
            // the last value to "". The value to add will either appear:
            // 1) at the start of the string
            // 2) after some whitespace; or
            // 3) directly after the delimiter
            // It is assumed that the value is delimited by the delimiter character surrounded by any number of spaces.
            if (!this.allowDuplicates && new RegExp("(?:^|[\\s" + delim + "])" + valueToAdd + "\\s*" + delim).test(this.field.val())) {
                alertUserValueAlreadyExists(valueToAdd);
                trimmedVals[trimmedVals.length-1] = "";
            } else {
                // add the new value to the end of the array and then an empty value so we
                // can get an extra delimiter at the end of the joined string
                trimmedVals[trimmedVals.length-1] = valueToAdd;
                trimmedVals[trimmedVals.length] = "";
            }

            // join the array of values with the delimiter plus an extra space to make the list of values readable
            args[0] = trimmedVals.join(delim.replace(/([^\s]$)/,"$1 "));

            return args;
        });
    }
});