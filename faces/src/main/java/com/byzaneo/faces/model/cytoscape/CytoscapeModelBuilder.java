package com.byzaneo.faces.model.cytoscape;

import static com.google.common.base.Preconditions.checkNotNull;
import static org.springframework.util.Assert.isTrue;
import static org.springframework.util.Assert.notNull;

import java.io.Serializable;
import java.util.Map;

import com.byzaneo.faces.model.cytoscape.Style.LabelAlignment;
import com.byzaneo.faces.model.cytoscape.Style.Selector;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;

/**
 * Builds instances of type {@link com.byzaneo.faces.model.cytoscape.CytoscapeModel CytoscapeModel}. Initialize attributes and then invoke
 * the {@link #build()} method to create an immutable instance.
 * <p>
 * <em>{@code CytoscapeModelBuilder} is not thread-safe and generally should not be stored in a field or collection, but instead used
 * immediately to create instances.</em>
 */
public final class CytoscapeModelBuilder {
  ImmutableList.Builder<Node> nodesBuilder = ImmutableList.builder();
  ImmutableList.Builder<Edge> edgesBuilder = ImmutableList.builder();
  ImmutableList.Builder<Style> styleBuilder = ImmutableList.builder();
  Layout layout;
  Double zoom;
  Position pan;
  Double minZoom;
  Double maxZoom;
  Boolean zoomingEnabled;
  Boolean userZoomingEnabled;
  Boolean panningEnabled;
  Boolean userPanningEnabled;
  Boolean boxSelectionEnabled;
  String selectionType;
  Integer touchTapThreshold;
  Integer desktopTapThreshold;
  Boolean autolock;
  Boolean autoungrabify;
  Boolean autounselectify;
  Boolean headless;
  Boolean styleEnabled;
  Boolean hideEdgesOnViewport;
  Boolean hideLabelsOnViewport;
  Boolean textureOnViewport;
  Boolean motionBlur;
  Double motionBlurOpacity;
  Double wheelSensitivity;
  Double pixelRatio;

  /**
   * @return builder instance
   */
  public static final CytoscapeModelBuilder builder() {
    return new CytoscapeModelBuilder();
  }

  /**
   * Adds one element to {@link Elements#getNodes() nodes} list.
   * 
   * @param element A nodes element
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder addNode(Node element) {
    nodesBuilder.add(element);
    return this;
  }

  /**
   * Adds elements to {@link Elements#getNodes() nodes} list.
   * 
   * @param elements An array of nodes elements
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder addNodes(Node... elements) {
    nodesBuilder.add(elements);
    return this;
  }

  /**
   * Adds elements to {@link Elements#getNodes() nodes} list.
   * 
   * @param elements An iterable of nodes elements
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder addAllNodes(Iterable<? extends Node> elements) {
    nodesBuilder.addAll(elements);
    return this;
  }

  /**
   * Adds one element to {@link Elements#getEdges() edges} list.
   * 
   * @param element A edges element
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder addEdge(Edge element) {
    edgesBuilder.add(element);
    return this;
  }

  /**
   * Adds elements to {@link Elements#getEdges() edges} list.
   * 
   * @param elements An array of edges elements
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder addEdges(Edge... elements) {
    edgesBuilder.add(elements);
    return this;
  }

  /**
   * Adds elements to {@link Elements#getEdges() edges} list.
   * 
   * @param elements An iterable of edges elements
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder addAllEdges(Iterable<? extends Edge> elements) {
    edgesBuilder.addAll(elements);
    return this;
  }

  /**
   * Sets the alignment of the node's labels
   * 
   * @param labelVerticalAlign vertical alignment
   * @param labelHorizontalAlign horizontal alignment
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder nodeLabelSytle(LabelAlignment labelVerticalAlign, LabelAlignment labelHorizontalAlign) {
    styleBuilder.add(new BaseStyle(labelVerticalAlign, labelHorizontalAlign));
    return this;
  }

  /**
   * Enables auto-rotate on the edge's label
   * 
   * @param autorotate <code>true</code> enables auto-rotate
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder edgeLabelAutorotate(boolean autorotate) {
    styleBuilder.add(new BaseStyle(autorotate));
    return this;
  }

  /**
   * Adds one element to {@link CytoscapeModel#getStyle() style} list.
   *
   * @param element A style element
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder addStyle(Style element) {
    styleBuilder.add(element);
    return this;
  }

  /**
   * Adds elements to {@link CytoscapeModel#getStyle() style} list.
   *
   * @param elements An array of style elements
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder addStyle(Style... elements) {
    styleBuilder.add(elements);
    return this;
  }

  /**
   * Adds elements to {@link CytoscapeModel#getStyle() style} list.
   *
   * @param elements An iterable of style elements
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder addAllStyle(Iterable<? extends Style> elements) {
    styleBuilder.addAll(elements);
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getLayout() layout} attribute.
   *
   * @param layout The value for layout
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder layout(Layout layout) {
    this.layout = checkNotNull(layout, "layout");
    return this;
  }

  /**
   * The null layout puts all nodes at (0, 0). It's useful for debugging purposes.
   */
  public final CytoscapeModelBuilder layoutNull() {
    this.layout = new BaseLayout.NoneLayout();
    return this;
  }

  /**
   * The random layout puts nodes in random positions within the viewport.
   */
  public final CytoscapeModelBuilder layoutRandom() {
    this.layout = new BaseLayout.RandomLayout();
    return this;
  }

  /**
   * The preset layout puts nodes in the positions you specify manually (using {@link Node#getPosition()}).
   */
  public final CytoscapeModelBuilder layoutPreset() {
    this.layout = new BaseLayout.PresetLayout();
    return this;
  }

  /**
   * The grid layout puts nodes in a well-spaced grid.
   */
  public final CytoscapeModelBuilder layoutGrid() {
    this.layout = new BaseLayout.GridLayout();
    return this;
  }

  /**
   * The circle layout puts nodes in a circle.
   */
  public final CytoscapeModelBuilder layoutCircle() {
    this.layout = new BaseLayout.CircleLayout();
    return this;
  }

  /**
   * The concentric layout positions nodes in concentric circles, based on a metric that you specify to segregate the nodes into levels.
   * This layout sets the concentric layout value based on your metric, which can be used with mapLayoutData().
   */
  public final CytoscapeModelBuilder layoutConcentric() {
    this.layout = new BaseLayout.ConcentricLayout();
    return this;
  }

  /**
   * The breadthfirst layout puts nodes in a hierarchy, based on a breadthfirst traversal of the graph.
   */
  public final CytoscapeModelBuilder layoutBreadthfirst() {
    this.layout = new BaseLayout.BreadthfirstLayout();
    return this;
  }

  /**
   * The cose (Compound Spring Embedder) layout uses a force-directed simulation to lay out graphs. It works well with noncompound graphs
   * and it has additional logic to support compound graphs well. It was implemented by Gerardo Huck as part of Google Summer of Code 2013
   * (Mentors: Max Franz, Christian Lopes, Anders Riutta, Ugur Dogrusoz). Based on the article "A layout algorithm for undirected compound
   * graphs" by Ugur Dogrusoz, Erhan Giral, Ahmet Cetintas, Ali Civril and Emek Demir.
   */
  public final CytoscapeModelBuilder layoutCose() {
    this.layout = new BaseLayout.CoseLayout();
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getZoom() zoom} attribute.
   *
   * @param zoom The value for zoom
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder zoom(Double zoom) {
    this.zoom = zoom;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getPan() pan} attribute.
   *
   * @param pan The value for pan
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder pan(Position pan) {
    this.pan = pan;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getPan() pan} attribute.
   *
   * @param pan The value for pan
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder pan(int x, int y) {
    this.pan = new BasePosition(x, y);
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getMinZoom() minZoom} attribute.
   *
   * @param minZoom The value for minZoom
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder minZoom(Double minZoom) {
    this.minZoom = minZoom;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getMaxZoom() maxZoom} attribute.
   *
   * @param maxZoom The value for maxZoom
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder maxZoom(Double maxZoom) {
    this.maxZoom = maxZoom;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getZoomingEnabled() zoomingEnabled} attribute.
   *
   * @param zoomingEnabled The value for zoomingEnabled
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder zoomingEnabled(Boolean zoomingEnabled) {
    this.zoomingEnabled = zoomingEnabled;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getUserZoomingEnabled() userZoomingEnabled} attribute.
   *
   * @param userZoomingEnabled The value for userZoomingEnabled
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder userZoomingEnabled(Boolean userZoomingEnabled) {
    this.userZoomingEnabled = userZoomingEnabled;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getPanningEnabled() panningEnabled} attribute.
   *
   * @param panningEnabled The value for panningEnabled
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder panningEnabled(Boolean panningEnabled) {
    this.panningEnabled = panningEnabled;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getUserPanningEnabled() userPanningEnabled} attribute.
   *
   * @param userPanningEnabled The value for userPanningEnabled
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder userPanningEnabled(Boolean userPanningEnabled) {
    this.userPanningEnabled = userPanningEnabled;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getBoxSelectionEnabled() boxSelectionEnabled} attribute.
   *
   * @param boxSelectionEnabled The value for boxSelectionEnabled
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder boxSelectionEnabled(Boolean boxSelectionEnabled) {
    this.boxSelectionEnabled = boxSelectionEnabled;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getSelectionType() selectionType} attribute.
   *
   * @param selectionType The value for selectionType
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder selectionType(String selectionType) {
    this.selectionType = selectionType;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getTouchTapThreshold() touchTapThreshold} attribute.
   *
   * @param touchTapThreshold The value for touchTapThreshold
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder touchTapThreshold(Integer touchTapThreshold) {
    this.touchTapThreshold = touchTapThreshold;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getDesktopTapThreshold() desktopTapThreshold} attribute.
   *
   * @param desktopTapThreshold The value for desktopTapThreshold
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder desktopTapThreshold(Integer desktopTapThreshold) {
    this.desktopTapThreshold = desktopTapThreshold;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getAutolock() autolock} attribute.
   *
   * @param autolock The value for autolock
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder autolock(Boolean autolock) {
    this.autolock = autolock;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getAutoungrabify() autoungrabify} attribute.
   *
   * @param autoungrabify The value for autoungrabify
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder autoungrabify(Boolean autoungrabify) {
    this.autoungrabify = autoungrabify;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getAutounselectify() autounselectify} attribute.
   *
   * @param autounselectify The value for autounselectify
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder autounselectify(Boolean autounselectify) {
    this.autounselectify = autounselectify;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getHeadless() headless} attribute.
   *
   * @param headless The value for headless
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder headless(Boolean headless) {
    this.headless = headless;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getStyleEnabled() styleEnabled} attribute.
   *
   * @param styleEnabled The value for styleEnabled
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder styleEnabled(Boolean styleEnabled) {
    this.styleEnabled = styleEnabled;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getHideEdgesOnViewport() hideEdgesOnViewport} attribute.
   *
   * @param hideEdgesOnViewport The value for hideEdgesOnViewport
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder hideEdgesOnViewport(Boolean hideEdgesOnViewport) {
    this.hideEdgesOnViewport = hideEdgesOnViewport;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getHideLabelsOnViewport() hideLabelsOnViewport} attribute.
   *
   * @param hideLabelsOnViewport The value for hideLabelsOnViewport
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder hideLabelsOnViewport(Boolean hideLabelsOnViewport) {
    this.hideLabelsOnViewport = hideLabelsOnViewport;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getTextureOnViewport() textureOnViewport} attribute.
   *
   * @param textureOnViewport The value for textureOnViewport
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder textureOnViewport(Boolean textureOnViewport) {
    this.textureOnViewport = textureOnViewport;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getMotionBlur() motionBlur} attribute.
   *
   * @param motionBlur The value for motionBlur
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder motionBlur(Boolean motionBlur) {
    this.motionBlur = motionBlur;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getMotionBlurOpacity() motionBlurOpacity} attribute.
   *
   * @param motionBlurOpacity The value for motionBlurOpacity
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder motionBlurOpacity(Double motionBlurOpacity) {
    this.motionBlurOpacity = motionBlurOpacity;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getWheelSensitivity() wheelSensitivity} attribute.
   *
   * @param wheelSensitivity The value for wheelSensitivity
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder wheelSensitivity(Double wheelSensitivity) {
    this.wheelSensitivity = wheelSensitivity;
    return this;
  }

  /**
   * Initializes the value for the {@link CytoscapeModel#getPixelRatio() pixelRatio} attribute.
   *
   * @param pixelRatio The value for pixelRatio
   * @return {@code this} builder for use in a chained invocation
   */
  public final CytoscapeModelBuilder pixelRatio(Double pixelRatio) {
    this.pixelRatio = pixelRatio;
    return this;
  }

  /**
   * Builds a new {@link com.byzaneo.faces.model.cytoscape.CytoscapeModel CytoscapeModel}.
   *
   * @return An immutable instance of CytoscapeModel
   * @throws java.lang.IllegalStateException if any required attributes are missing
   */
  @SuppressWarnings("unchecked")
  public <C extends CytoscapeModel> C build()
      throws java.lang.IllegalStateException {
    if (this.zoom != null && this.zoom != 1d && this.layout != null) {
      this.layout.setFit(false);
    }
    return (C) new BaseCytoscapeModel(this);
  }

  /**
   * Builds instances of type {@link com.byzaneo.faces.model.cytoscape.Edge Edge}. Initialize attributes and then invoke the
   * {@link #build()} method to create an immutable instance.
   * <p>
   * <em>{@code EdgeBuilder} is not thread-safe and generally should not be stored in a field or collection, but instead used immediately to
   * create instances.</em>
   */
  public final static class EdgeBuilder {
    EdgeData data;
    Serializable source;
    Serializable target;
    Serializable id;
    String label;
    String tooltip;

    Position position;
    Position renderedPosition;
    Boolean selected;
    Boolean selectable;
    Boolean locked;
    Boolean grabbable;
    String classes;
    ImmutableMap.Builder<String, Serializable> style = ImmutableMap.builder();

    public static final EdgeBuilder builder() {
      return new EdgeBuilder();
    }

    /**
     * Initializes the value for the {@link Edge#getData() data} attribute.
     *
     * @param data The value for data
     * @return {@code this} builder for use in a chained invocation
     */
    public final EdgeBuilder data(EdgeData data) {
      this.data = checkNotNull(data, "data");
      return this;
    }

    /**
     * Initializes the value for the {@link EdgeData#getSource() source} attribute.
     *
     * @param source The value for source
     * @return {@code this} builder for use in a chained invocation
     */
    public final EdgeBuilder source(Serializable source) {
      this.source = checkNotNull(source, "source");
      return this;
    }

    /**
     * Initializes the value for the {@link EdgeData#getTarget() target} attribute.
     *
     * @param target The value for target
     * @return {@code this} builder for use in a chained invocation
     */
    public final EdgeBuilder target(Serializable target) {
      this.target = checkNotNull(target, "target");
      return this;
    }

    /**
     * Initializes the value for the {@link EdgeData#getId() id} attribute.
     *
     * @param id The value for id
     * @return {@code this} builder for use in a chained invocation
     */
    public final EdgeBuilder id(Serializable id) {
      this.id = checkNotNull(id, "id");
      return this;
    }

    /**
     * Initializes the value for the {@link EdgeData#getLabel() label} attribute.
     *
     * @param label The value for label
     * @return {@code this} builder for use in a chained invocation
     */
    public final EdgeBuilder label(String label) {
      this.label = label;
      return this;
    }

    /**
     * Initializes the value for the {@link EdgeData#getTooltip() tooltip} attribute.
     *
     * @param tooltip The value for tooltip
     * @return {@code this} builder for use in a chained invocation
     */
    public final EdgeBuilder tooltip(String tooltip) {
      this.tooltip = tooltip;
      return this;
    }

    /**
     * Initializes the value for the {@link Edge#getPosition() position} attribute.
     *
     * @param position The value for position
     * @return {@code this} builder for use in a chained invocation
     */
    public final EdgeBuilder position(Position position) {
      this.position = position;
      return this;
    }

    /**
     * Initializes the value for the {@link Edge#getRenderedPosition() renderedPosition} attribute.
     *
     * @param renderedPosition The value for renderedPosition
     * @return {@code this} builder for use in a chained invocation
     */
    public final EdgeBuilder renderedPosition(Position renderedPosition) {
      this.renderedPosition = renderedPosition;
      return this;
    }

    /**
     * Initializes the value for the {@link Edge#getSelected() selected} attribute.
     *
     * @param selected The value for selected
     * @return {@code this} builder for use in a chained invocation
     */
    public final EdgeBuilder selected(Boolean selected) {
      this.selected = selected;
      return this;
    }

    /**
     * Initializes the value for the {@link Edge#getSelectable() selectable} attribute.
     *
     * @param selectable The value for selectable
     * @return {@code this} builder for use in a chained invocation
     */
    public final EdgeBuilder selectable(Boolean selectable) {
      this.selectable = selectable;
      return this;
    }

    /**
     * Initializes the value for the {@link Edge#getLocked() locked} attribute.
     *
     * @param locked The value for locked
     * @return {@code this} builder for use in a chained invocation
     */
    public final EdgeBuilder locked(Boolean locked) {
      this.locked = locked;
      return this;
    }

    /**
     * Initializes the value for the {@link Edge#getGrabbable() grabbable} attribute.
     *
     * @param grabbable The value for grabbable
     * @return {@code this} builder for use in a chained invocation
     */
    public final EdgeBuilder grabbable(Boolean grabbable) {
      this.grabbable = grabbable;
      return this;
    }

    /**
     * Initializes the value for the {@link Edge#getClasses() classes} attribute.
     *
     * @param classes The value for classes
     * @return {@code this} builder for use in a chained invocation
     */
    public final EdgeBuilder classes(String classes) {
      this.classes = classes;
      return this;
    }

    /**
     * Put one entry to the {@link Edge#getStyle() style} map.
     *
     * @param key The key in the style map
     * @param value The associated value in the style map
     * @return {@code this} builder for use in a chained invocation
     */
    public final EdgeBuilder putStyle(String key, Serializable value) {
      style.put(key, value);
      return this;
    }

    /**
     * Put one entry to the {@link Edge#getStyle() style} map. Nulls are not permitted
     *
     * @param entry The key and value entry
     * @return {@code this} builder for use in a chained invocation
     */
    public final EdgeBuilder putStyle(Map.Entry<String, ? extends Serializable> entry) {
      style.put(entry);
      return this;
    }

    /**
     * Put all mappings from the specified map as entries to {@link Edge#getStyle() style} map. Nulls are not permitted
     *
     * @param entries The entries that will be added to the style map
     * @return {@code this} builder for use in a chained invocation
     */
    public final EdgeBuilder putAllStyle(Map<String, ? extends Serializable> entries) {
      style.putAll(entries);
      return this;
    }

    /**
     * Builds a new {@link com.byzaneo.faces.model.cytoscape.Edge Edge}.
     *
     * @return An immutable instance of Edge
     * @throws java.lang.IllegalStateException if any required attributes are missing
     */
    public Edge build()
        throws java.lang.IllegalStateException {
      checkRequiredAttributes();
      return new BaseEdge(this);
    }

    private void checkRequiredAttributes() {
      isTrue((data != null && data.getId() != null) || (id != null), "Edge identifier is required");
      isTrue((data != null && data.getSource() != null && data.getTarget() != null) || (source != null && target != null),
          "Edge data (source and target) is required");
    }
  }

  /**
   * Builds instances of type {@link com.byzaneo.faces.model.cytoscape.Node Node}. Initialize attributes and then invoke the
   * {@link #build()} method to create an immutable instance.
   * <p>
   * <em>{@code NodeBuilder} is not thread-safe and generally should not be stored in a field or collection, but instead used immediately to
   * create instances.</em>
   */
  public final static class NodeBuilder {
    NodeData data;
    Object value;
    Serializable id;
    String label;
    String tooltip;

    Position position;
    Position renderedPosition;
    Boolean selected;
    Boolean selectable;
    Boolean locked;
    Boolean grabbable;
    String classes;
    ImmutableMap.Builder<String, Serializable> style = ImmutableMap.builder();

    public static final NodeBuilder builder() {
      return new NodeBuilder();
    }

    /**
     * Initializes the value for the {@link NodeData#getValue() value} attribute.
     * 
     * @param value The value for value
     * @return {@code this} builder for use in a chained invocation
     */
    public final NodeBuilder value(Object value) {
      this.value = checkNotNull(value, "value");
      return this;
    }

    /**
     * Initializes the value for the {@link NodeData#getId() id} attribute.
     * 
     * @param id The value for id
     * @return {@code this} builder for use in a chained invocation
     */
    public final NodeBuilder id(Serializable id) {
      this.id = checkNotNull(id, "id");
      return this;
    }

    /**
     * Initializes the value for the {@link NodeData#getLabel() label} attribute.
     * 
     * @param label The value for label
     * @return {@code this} builder for use in a chained invocation
     */
    public final NodeBuilder label(String label) {
      this.label = label;
      return this;
    }

    /**
     * Initializes the value for the {@link NodeData#getTooltip() tooltip} attribute.
     * 
     * @param tooltip The value for tooltip
     * @return {@code this} builder for use in a chained invocation
     */
    public final NodeBuilder tooltip(String tooltip) {
      this.tooltip = tooltip;
      return this;
    }

    /**
     * Initializes the value for the {@link Node#getData() data} attribute.
     *
     * @param data The value for data
     * @return {@code this} builder for use in a chained invocation
     */
    public final NodeBuilder data(NodeData data) {
      this.data = checkNotNull(data, "data");
      return this;
    }

    /**
     * Initializes the value for the {@link Node#getPosition() position} attribute.
     *
     * @param position The value for position
     * @return {@code this} builder for use in a chained invocation
     */
    public final NodeBuilder position(Position position) {
      this.position = position;
      return this;
    }

    /**
     * Initializes the value for the {@link Node#getRenderedPosition() renderedPosition} attribute.
     *
     * @param renderedPosition The value for renderedPosition
     * @return {@code this} builder for use in a chained invocation
     */
    public final NodeBuilder renderedPosition(Position renderedPosition) {
      this.renderedPosition = renderedPosition;
      return this;
    }

    /**
     * Initializes the value for the {@link Node#getSelected() selected} attribute.
     *
     * @param selected The value for selected
     * @return {@code this} builder for use in a chained invocation
     */
    public final NodeBuilder selected(Boolean selected) {
      this.selected = selected;
      return this;
    }

    /**
     * Initializes the value for the {@link Node#getSelectable() selectable} attribute.
     *
     * @param selectable The value for selectable
     * @return {@code this} builder for use in a chained invocation
     */
    public final NodeBuilder selectable(Boolean selectable) {
      this.selectable = selectable;
      return this;
    }

    /**
     * Initializes the value for the {@link Node#getLocked() locked} attribute.
     *
     * @param locked The value for locked
     * @return {@code this} builder for use in a chained invocation
     */
    public final NodeBuilder locked(Boolean locked) {
      this.locked = locked;
      return this;
    }

    /**
     * Initializes the value for the {@link Node#getGrabbable() grabbable} attribute.
     *
     * @param grabbable The value for grabbable
     * @return {@code this} builder for use in a chained invocation
     */
    public final NodeBuilder grabbable(Boolean grabbable) {
      this.grabbable = grabbable;
      return this;
    }

    /**
     * Initializes the value for the {@link Node#getClasses() classes} attribute.
     *
     * @param classes The value for classes
     * @return {@code this} builder for use in a chained invocation
     */
    public final NodeBuilder classes(String classes) {
      this.classes = classes;
      return this;
    }

    /**
     * Put one entry to the {@link Node#getStyle() style} map.
     *
     * @param key The key in the style map
     * @param value The associated value in the style map
     * @return {@code this} builder for use in a chained invocation
     */
    public final NodeBuilder putStyle(String key, Serializable value) {
      style.put(key, value);
      return this;
    }

    /**
     * Put one entry to the {@link Node#getStyle() style} map. Nulls are not permitted
     *
     * @param entry The key and value entry
     * @return {@code this} builder for use in a chained invocation
     */
    public final NodeBuilder putStyle(Map.Entry<String, ? extends Serializable> entry) {
      style.put(entry);
      return this;
    }

    /**
     * Put all mappings from the specified map as entries to {@link Node#getStyle() style} map. Nulls are not permitted
     *
     * @param entries The entries that will be added to the style map
     * @return {@code this} builder for use in a chained invocation
     */
    public final NodeBuilder putAllStyle(Map<String, ? extends Serializable> entries) {
      style.putAll(entries);
      return this;
    }

    /**
     * Builds a new {@link com.byzaneo.faces.model.cytoscape.Node Node}.
     *
     * @return An immutable instance of Node
     * @throws java.lang.IllegalStateException if any required attributes are missing
     */
    public Node build()
        throws java.lang.IllegalStateException {
      checkRequiredAttributes();
      return new BaseNode(this);
    }

    private void checkRequiredAttributes() throws java.lang.IllegalStateException {
      isTrue((data != null && data.getId() != null) || (id != null), "Node identifier is required");
    }
  }

  /**
   * Builds instances of type {@link com.byzaneo.faces.model.cytoscape.Style Style}. Initialize attributes and then invoke the
   * {@link #build()} method to create an immutable instance.
   * <p>
   * <em>{@code StyleBuilder} is not thread-safe and generally should not be stored in a field or collection, but instead used immediately
   * to create instances.</em>
   */
  public final static class StyleBuilder {
    String selector;
    ImmutableMap.Builder<String, Serializable> style = ImmutableMap.builder();

    public static final StyleBuilder builder() {
      return new StyleBuilder();
    }

    /**
     * Initializes the value for the {@link Style#getSelector() selector} attribute.
     * 
     * @param selector The value for selector
     * @return {@code this} builder for use in a chained invocation
     */
    public final StyleBuilder selector(String selector) {
      this.selector = checkNotNull(selector, "selector");
      return this;
    }

    /**
     * Initializes the value for the {@link Style#getSelector() selector} attribute.
     * 
     * @param selector The value for selector
     * @return {@code this} builder for use in a chained invocation
     */
    public final StyleBuilder selector(Selector selector) {
      this.selector = checkNotNull(selector, "selector").toSelector();
      return this;
    }

    /**
     * Put one entry to the {@link Style#getStyle() style} map.
     * 
     * @param key The key in the style map
     * @param value The associated value in the style map
     * @return {@code this} builder for use in a chained invocation
     */
    public final StyleBuilder putStyle(String key, Serializable value) {
      style.put(key, value);
      return this;
    }

    /**
     * Put one entry to the {@link Style#getStyle() style} map. Nulls are not permitted
     * 
     * @param entry The key and value entry
     * @return {@code this} builder for use in a chained invocation
     */
    public final StyleBuilder putStyle(Map.Entry<String, ? extends Serializable> entry) {
      style.put(entry);
      return this;
    }

    /**
     * Put all mappings from the specified map as entries to {@link Style#getStyle() style} map. Nulls are not permitted
     * 
     * @param entries The entries that will be added to the style map
     * @return {@code this} builder for use in a chained invocation
     */
    public final StyleBuilder putAllStyle(Map<String, ? extends Serializable> entries) {
      style.putAll(entries);
      return this;
    }

    /**
     * Builds a new {@link com.byzaneo.faces.model.cytoscape.Style Style}.
     * 
     * @return An immutable instance of Style
     * @throws java.lang.IllegalStateException if any required attributes are missing
     */
    public Style build() throws java.lang.IllegalStateException {
      checkRequiredAttributes();
      return new BaseStyle(this);
    }

    private void checkRequiredAttributes() throws java.lang.IllegalStateException {
      notNull(selector, "Style selector is required");
    }
  }
}
