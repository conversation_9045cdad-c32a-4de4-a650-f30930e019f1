package com.generix.legalreferential.service.dto.custom.filter;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.generix.legalreferential.domain.enumeration.*;
import com.generix.legalreferential.service.dto.validation.ValidEnum;

import javax.validation.Valid;
import java.io.Serializable;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class FilterSiretRequestDTO implements Serializable {

    private static final long serialVersionUID = 4406980138269842988L;

    private FilterRequestDTO siret;
    private FilterRequestDTO siren;

    @Valid
    @ValidEnum(enumClass = EstablishmentType.class)
    private FilterRequestDTO typeEtablissement;
    private FilterRequestDTO denomination;
    private FilterRequestDTO lignesAdresse;
    private FilterRequestDTO codePostal;
    private FilterRequestDTO subDivisionPays;
    private FilterRequestDTO localite;
    @Valid
    @ValidEnum(enumClass = Statut.class)
    private FilterRequestDTO etatAdministratif;

    public FilterRequestDTO getSiret() {
        return siret;
    }

    public void setSiret(FilterRequestDTO siret) {
        this.siret = siret;
    }

    public FilterRequestDTO getDenomination() {
        return denomination;
    }

    public void setDenomination(FilterRequestDTO denomination) {
        this.denomination = denomination;
    }

    public FilterRequestDTO getTypeEtablissement() {
        return typeEtablissement;
    }

    public void setTypeEtablissement(FilterRequestDTO typeEtablissement) {
        this.typeEtablissement = typeEtablissement;
    }

    public FilterRequestDTO getEtatAdministratif() {
        return etatAdministratif;
    }

    public void setEtatAdministratif(FilterRequestDTO etatAdministratif) {
        this.etatAdministratif = etatAdministratif;
    }

    public FilterRequestDTO getLignesAdresse() {
        return lignesAdresse;
    }

    public void setLignesAdresse(FilterRequestDTO lignesAdresse) {
        this.lignesAdresse = lignesAdresse;
    }

    public FilterRequestDTO getCodePostal() {
        return codePostal;
    }

    public void setCodePostal(FilterRequestDTO codePostal) {
        this.codePostal = codePostal;
    }

    public FilterRequestDTO getLocalite() {
        return localite;
    }

    public void setLocalite(FilterRequestDTO localite) {
        this.localite = localite;
    }

    public FilterRequestDTO getSiren() {
        return siren;
    }

    public void setSiren(FilterRequestDTO siren) {
        this.siren = siren;
    }

    public FilterRequestDTO getSubDivisionPays() {
        return subDivisionPays;
    }

    public void setSubDivisionPays(FilterRequestDTO subDivisionPays) {
        this.subDivisionPays = subDivisionPays;
    }
}
