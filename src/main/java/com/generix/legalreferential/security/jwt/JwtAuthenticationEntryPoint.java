package com.generix.legalreferential.security.jwt;

import com.fasterxml.jackson.databind.ObjectMapper;
// import com.generix.tokenvalidator.check.error.TokenValidatorException;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;

public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint {

    /**
     * Commences an authentication scheme.
     * <p>
     * <code>ExceptionTranslationFilter</code> will populate the <code>HttpSession</code>
     * attribute named
     * <code>AbstractAuthenticationProcessingFilter.SPRING_SECURITY_SAVED_REQUEST_KEY</code>
     * with the requested target URL before calling this method.
     * <p>
     * Implementations should modify the headers on the <code>ServletResponse</code> as necessary to
     * commence the authentication process.
     *
     * @param request       that resulted in an <code>AuthenticationException</code>
     * @param response      so that the user agent can begin authentication
     * @param authException that caused the invocation
     */
    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException)
        throws IOException {
        // Temporarily commented out due to missing tokenvalidator dependency
        if (false) { // authException instanceof TokenValidatorException
            Map<String, Object> responseBody = new HashMap<>();
            responseBody.put("type", request.getRequestURI());
            responseBody.put("titre", "401, You are not authorized to access this resource");
            responseBody.put("statut", HttpStatus.UNAUTHORIZED.value());
            responseBody.put("detail", authException.getMessage());
            responseBody.put("instance", request.getRequestURI() + "#TokenValidator");

            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setContentType("application/json");

            PrintWriter out = response.getWriter();
            ObjectMapper mapper = new ObjectMapper();
            mapper.writeValue(out, responseBody);
            out.flush();
        } else {
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, authException.getMessage());
        }
    }
}
