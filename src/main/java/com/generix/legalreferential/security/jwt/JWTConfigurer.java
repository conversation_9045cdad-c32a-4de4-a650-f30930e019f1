package com.generix.legalreferential.security.jwt;

// import com.generix.tokenvalidator.check.config.TokenValidatorFilter;
import org.springframework.security.config.annotation.SecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.DefaultSecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

public class JWTConfigurer extends SecurityConfigurerAdapter<DefaultSecurityFilterChain, HttpSecurity> {

    private final TokenProvider tokenProvider;
    private final String urlBaseKeycloak;
    private final int delayCachetoken;
    private final int delayCacheCertsKeys;

    public JWTConfigurer(TokenProvider tokenProvider, String urlBaseKeycloak, int delayCachetoken, int delayCacheCertsKeys) {
        this.tokenProvider = tokenProvider;
        this.urlBaseKeycloak = urlBaseKeycloak;
        this.delayCachetoken = delayCachetoken;
        this.delayCacheCertsKeys = delayCacheCertsKeys;
    }

    @Override
    public void configure(HttpSecurity http) {
        // Temporarily commented out due to missing tokenvalidator dependency
        /*
        TokenValidatorFilter tokenValidatorFilter = new TokenValidatorFilter(
            new JwtAuthenticationEntryPoint(),
            urlBaseKeycloak,
            delayCachetoken,
            delayCacheCertsKeys
        );
        http.addFilterBefore(tokenValidatorFilter, UsernamePasswordAuthenticationFilter.class);
        */

        JWTFilter customFilter = new JWTFilter(tokenProvider);
        http.addFilterBefore(customFilter, UsernamePasswordAuthenticationFilter.class);
    }
}
