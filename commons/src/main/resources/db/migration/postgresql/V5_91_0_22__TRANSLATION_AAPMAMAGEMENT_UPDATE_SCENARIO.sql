
-- general
delete from gnx_i18n_translation
WHERE code IN (
               'general.error.required',
               'general.error.max',
               'general.error.min',
               'general.error.minlength',
               'general.error.maxLength',
               'general.info.success_save'
    );


INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.error.required', 'Required Field', 'en', NOW(), 1);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.error.required', 'Champ obligatoire', 'fr', NOW(), 1);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.error.max', 'Max value is {{ max }}', 'en', NOW(), 1);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.error.max', 'La valeur maximale est {{ max }}', 'fr', NOW(), 1);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.error.min', 'Min value is {{ min }}', 'en', NOW(), 1);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.error.min', 'La valeur minimale est {{ min }}', 'fr', NOW(), 1);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.error.minlength', 'Min value length {{ requiredLength }}', 'en', NOW(), 1);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.error.minlength', 'La longeur minimale de la valeur doit être {{ requiredLength }}', 'fr', NOW(), 1);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.error.maxLength', 'Max value length {{ requiredLength }}', 'en', NOW(), 1);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.error.maxLength', 'La longeur maximale de la valeur doit être {{ requiredLength }}', 'fr', NOW(), 1);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.info.success_save', 'Saved successfully', 'en', NOW(), 1);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.info.success_save', 'Sauvegardé avec succès', 'fr', NOW(), 1);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.action.back', 'Back', 'en', NOW(), 1);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.action.back', 'Retour', 'fr', NOW(), 1);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.action.cancel', 'Cancel', 'en', NOW(), 1);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.action.cancel', 'Annuler', 'fr', NOW(), 1);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.action.save', 'Save', 'en', NOW(), 1);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.action.save', 'Enregister', 'fr', NOW(), 1);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.action.edit', 'Edit', 'en', NOW(), 1);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.action.edit', 'Modifier', 'fr', NOW(), 1);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.action.view', 'View', 'en', NOW(), 1);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.action.view', 'Voir', 'fr', NOW(), 1);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.all', 'All', 'en', NOW(), 1);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.all', 'Tous', 'fr', NOW(), 1);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.selected', 'Selected', 'en', NOW(), 1);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.selected', 'Séléctionné(s)', 'fr', NOW(), 1);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.error.invalid_form', 'Invalid Form', 'en', NOW(), 1);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.error.invalid_form', 'Formulaire invalide', 'fr', NOW(), 1);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.error.server_error', 'Server Error', 'en', NOW(), 1);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('general.error.server_error', 'Erreur interne du système', 'fr', NOW(), 1);

-- aap-management
-- Section 1
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.section-1.header', 'Informations', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.section-1.header', 'Informations', 'fr', NOW(), 10);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.name', 'Name', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.name', 'Nom', 'fr', NOW(), 10);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.enable', 'Active', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.enable', 'Actif', 'fr', NOW(), 10);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.description', 'Description', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.description', 'Description', 'fr', NOW(), 10);

-- Section 2
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.section-2.header', 'Selection of invoices on which to apply the imputation', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.section-2.header', 'Sélection des factures sur les quelles appliquer l''imputation', 'fr', NOW(), 10);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.section-2.tab-1.header', 'Filter by field', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.section-2.tab-1.header', 'Filtrer par champ', 'fr', NOW(), 10);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.section-2.tab-1.more-filters', 'More filters', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.section-2.tab-1.more-filters', 'Plus de filtres', 'fr', NOW(), 10);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.section-2.tab-2.header', 'Filter by query', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.section-2.tab-2.header', 'Filtrer par requête', 'fr', NOW(), 10);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.bqlFilterQuery', 'Selection query', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.bqlFilterQuery', 'Requête de sélection', 'fr', NOW(), 10);

-- Section 3
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.section-3.header', 'Automatic pre-imputation settings', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.section-3.header', 'Paramétrage de la pré-imputation automatique', 'fr', NOW(), 10);

-- Section 3 / accountingPostingSenarioParameter
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.accPosSenPar', 'Do the automatic pre-imputation between the invoice and : ', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.accPosSenPar', 'Effectuer la pré-imputation automatique entre la facture et : ', 'fr', NOW(), 10);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.accPosSenPar.option-1', 'Order only', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.accPosSenPar.option-1', 'La commande uniquement', 'fr', NOW(), 10);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.accPosSenPar.option-2', 'Receiving and ordering', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.accPosSenPar.option-2', 'La réception et la commande', 'fr', NOW(), 10);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.accPosSenPar.option-3', 'Not applicable', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.accPosSenPar.option-3', 'Sans objet', 'fr', NOW(), 10);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.section-3.subheader-1', 'Exclusion of documents of a special status from automatic pre-imputation : ', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.section-3.subheader-1', 'Exclusion des documents d''un statut particulier de la pré-imputation automatique : ', 'fr', NOW(), 10);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.excludedorders', 'Excluded orders', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.excludedorders', 'Commandes exclues', 'fr', NOW(), 10);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.excludedreceptions', 'Excluded receptions', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.excludedreceptions', 'Réceptions exclues', 'fr', NOW(), 10);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.excludedPreInvoices', 'Excluded previous invoices', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.excludedPreInvoices', 'Factures précédentes exclues', 'fr', NOW(), 10);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.completeWithAccRef', 'Complete using the accounting referential', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.completeWithAccRef', 'Compléter à l’aide du référentiel comptable', 'fr', NOW(), 10);


-- Section 4
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.section-4.header', 'Invoice status management', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.section-4.header', 'Gestion des statuts de la facture', 'fr', NOW(), 10);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.missingOrderStatus', 'In case of missing order', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.missingOrderStatus', 'En cas de commande inexistante', 'fr', NOW(), 10);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.missingRecepStatus', 'In case of missing reception', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.missingRecepStatus', 'En cas de réception inexistante ', 'fr', NOW(), 10);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.missingAccRef', 'In case of missing accounting referential', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.missingAccRef', 'En cas de référentiel comptable inexistant', 'fr', NOW(), 10);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.partialImpStatus', 'In case of partial imputation', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.partialImpStatus', 'En cas d’imputation partielle', 'fr', NOW(), 10);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.completedImpStatus', 'In case of completed imputation', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.completedImpStatus', 'En cas d’imputation complète', 'fr', NOW(), 10);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.impInManualModeStat', 'In case of imputation in manual mode', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.controls.impInManualModeStat', 'En cas d’imputation en mode manuel', 'fr', NOW(), 10);

INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.errors.dynamicFormRequired', 'You must complete at least one filter', 'en', NOW(), 10);
INSERT INTO gnx_i18n_translation (code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES ('aap-management.update-view-scenario.errors.dynamicFormRequired', 'Vous devez remplir au moins un filtre', 'fr', NOW(), 10);
