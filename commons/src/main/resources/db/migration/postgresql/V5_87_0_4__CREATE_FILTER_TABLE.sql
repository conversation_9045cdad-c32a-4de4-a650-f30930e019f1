CREATE TABLE IF NOT EXISTS gnx_bql_filter
(
    id BIGSERIAL NOT NULL,
    name character varying(255) NOT NULL,
    creation_date timestamp with time zone,
    username character varying(255),
    attributes text,
    CONSTRAINT gnx_bql_filter_pkey PRIMARY KEY (id),
    CONSTRAINT gnx_bql_filter_unique unique (name,username),
    CONSTRAINT username FOREIGN KEY (username)
        REFERENCES sec_user (user_id)
);
