DELETE FROM sec_technical_user;

ALTER TABLE sec_technical_user DROP COLUMN primary_group, DROP CONSTRAINT sec_technical_user_pkey CASCADE, ADD COLUMN IF NOT EXISTS technical_user_id varchar(100),
    ADD PRIMARY KEY(technical_user_id);

CREATE TABLE sec_technical_user_perimeter
(
    technical_user_id character varying(100) NOT NULL,
    perimeter_id bigint NOT NULL,
    CONSTRAINT sec_technical_user_perimeter_pkey PRIMARY KEY (technical_user_id, perimeter_id),
    CONSTRAINT sec_tech_user_perim_user_id_fk FOREIGN KEY (technical_user_id)
        REFERENCES sec_technical_user(technical_user_id),
    CONSTRAINT sec_tech_user_perim_id_fk FOREIGN KEY (perimeter_id)
        REFERENCES sec_perimeter(perimeter_id)
);

CREATE TABLE sec_technical_user_group
(
    technical_user_id character varying(100) NOT NULL,
    group_id character varying(100) NOT NULL,
    CONSTRAINT sec_technical_user_group_pkey PRIMARY KEY (technical_user_id, group_id),
    CONSTRAINT sec_tech_user_group_user_id_fk FOREIGN KEY (technical_user_id)
        REFERENCES sec_technical_user (technical_user_id),
    CONSTRAINT sec_tech_user_group_gr_id_fk FOREIGN KEY (group_id)
        REFERENCES sec_group (group_id)
);
