INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'or_text', 'ou', 'fr', SYSDATE, 6);
INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'or_text', 'or', 'en', SYSDATE, 6);

INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'addressing_code_update', 'Code ligne d''adressage', 'fr', SYSDATE, 6);
INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'addressing_code_update', 'Addressing line ID', 'en', SYSDATE, 6);

INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'enter_code', 'Entrez le code', 'fr', SYSDATE, 6);
INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'enter_code', 'Enter the code', 'en', SYSDATE, 6);

INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'enter_number', 'Entrez le numéro', 'fr', SYSDATE, 6);
INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'enter_number', 'Enter the number', 'en', SYSDATE, 6);

INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'enter_company', 'Entrez la raison sociale', 'fr', SYSDATE, 6);
INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'enter_company', 'Enter the company name', 'en', SYSDATE, 6);

INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'siren_number_update', 'N° de SIREN', 'fr', SYSDATE, 6);
INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'siren_number_update', 'SIREN number', 'en', SYSDATE, 6);

INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'siret_number_update', 'N° de SIRET', 'fr', SYSDATE, 6);
INSERT INTO gnx_i18n_translation (id, code, default_value, locale, default_value_changed_at, i18n_module_id)
VALUES (GNX_I18N_TRANSLATION_SEQ.nextval, 'siret_number_update', 'SIRET number', 'en', SYSDATE, 6);





