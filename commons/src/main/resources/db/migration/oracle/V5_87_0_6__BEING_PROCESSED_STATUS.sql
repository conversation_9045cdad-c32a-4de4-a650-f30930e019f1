DECLARE
    STATUS_ID NUMBER(19,0);
    COUNTER NUMBER;
BEGIN
    SELECT COUNT(STATUS_CODE) INTO COUNTER FROM DOCUMENT_STATUS WHERE STATUS_CODE = 'BEING_PROCESSED';
    IF COUNTER = 0 THEN
        INSERT INTO DOCUMENT_STATUS(STATUS_CODE) VALUES ('BEING_PROCESSED');
        FOR ENV IN (SELECT DISTINCT(ENV_CODE) AS CODE FROM DOC_STS_VALUES) LOOP
            SELECT DOC_VALUE INTO STATUS_ID FROM (SELECT * FROM DOC_STATUS_VALUES_MAPPING ORDER BY DOC_VALUE DESC) WHERE ROWNUM = 1;

            STATUS_ID := STATUS_ID + 1;

            INSERT INTO DOC_STS_VALUES(ID, ENV_CODE, STYLE) VALUES (STATUS_ID, ENV.CODE, 'status_orange');
            INSERT INTO DOC_STATUS_VALUES_MAPPING(DOC_STATUS, DOC_VALUE) VALUES ('BEING_PROCESSED',STATUS_ID);
            INSERT INTO DOC_STATUS_LABELS(LABELS_ID, LABELS, NAME) VALUES (STATUS_ID,'Being processed','en');
            INSERT INTO DOC_STATUS_LABELS(LABELS_ID, LABELS, NAME) VALUES (STATUS_ID,'En cours de traitement','fr');
        END LOOP;
    END IF;
END;