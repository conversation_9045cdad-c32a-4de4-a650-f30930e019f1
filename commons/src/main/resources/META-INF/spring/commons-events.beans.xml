<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xmlns:context="http://www.springframework.org/schema/context"
          xmlns:mongo="http://www.springframework.org/schema/data/mongo"
          xsi:schemaLocation=
          "http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
          http://www.springframework.org/schema/data/mongo http://www.springframework.org/schema/data/mongo/spring-mongo-3.3.xsd
          http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

	<!-- SERVER -->
	<mongo:mongo-client id="comEventMongoClient"  connection-string="${index.mongo.uri}">
	<mongo:client-settings
			cluster-server-selection-timeout="${index.mongo.socketTimeout:45000}"
			application-name="${event.mongo.description:Mongo Event Operations Client}"
			connection-pool-max-size="${event.mongo.connectionsPerHost:10}"
			connection-pool-min-size="${event.mongo.minConnectionsPerHost:0}"
			connection-pool-max-connection-idle-time="${event.mongo.maxConnectionIdleTime:0}"
			connection-pool-max-connection-life-time="${event.mongo.maxConnectionLifeTime:0}"
			socket-connect-timeout="${event.mongo.connectTimeout:10000}"
			connection-pool-max-wait-time="${event.mongo.maxWaitTime:180000}"
			read-preference="${event.mongo.readPreference:PRIMARY_PREFERRED}"
			server-heartbeat-frequency="${event.mongo.heartbeatFrequency:10000}"
			server-min-heartbeat-frequency="${event.mongo.minHeartbeatFrequency:500}"
			ssl-enabled="${event.mongo.ssl:false}"
		/>
	</mongo:mongo-client>

	<!-- FACTORY -->
	<bean id="mongoDbName"
		  class="com.byzaneo.commons.util.DataSourceMongo"
		  factory-method="extractMongoDbName">
		<constructor-arg value="${index.mongo.uri}" />
	</bean>
	<mongo:db-factory id="comEventMongoDbFactory" dbname="#{mongoDbName}" mongo-client-ref="comEventMongoClient" />
	
	<!-- CONVERTERS -->
	<mongo:mapping-converter id="comEventMongoMappingConverter" db-factory-ref="comEventMongoDbFactory">
		<mongo:custom-converters>
			<mongo:converter><bean class="com.byzaneo.commons.dao.mongo.ClassWriteConverter" /></mongo:converter>
			<mongo:converter><bean class="com.byzaneo.commons.dao.mongo.ClassReadConverter" /></mongo:converter>
		</mongo:custom-converters>
	</mongo:mapping-converter>
	
	<!-- OPERATIONS -->
	<bean id="comEventOperations" class="com.byzaneo.commons.dao.mongo.EventMongoOperatons">
		<constructor-arg name="mongoDbFactory" ref="comEventMongoDbFactory" />
		<constructor-arg name="mongoConverter" ref="comEventMongoMappingConverter" />
	</bean>

	<!-- EXCEPTIONS -->
	<bean class="org.springframework.data.mongodb.core.MongoExceptionTranslator" />
 </beans>