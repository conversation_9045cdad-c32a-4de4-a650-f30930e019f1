package com.byzaneo.commons.util;

import static com.byzaneo.commons.bean.FileType.HTML;
import static com.byzaneo.commons.bean.FileType.TXT;
import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.WEBAPP_DIR;
import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static java.util.Arrays.stream;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.io.IOUtils.writeLines;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.commons.lang3.StringUtils.startsWith;
import static org.apache.commons.lang3.SystemUtils.LINE_SEPARATOR;
import static org.thymeleaf.templatemode.TemplateMode.TEXT;
import java.io.*;

import com.byzaneo.commons.service.ConfigurationService;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.IContext;
import org.thymeleaf.templateresolver.ClassLoaderTemplateResolver;

/**
 * <a href="http://www.thymeleaf.org/" target="_blank">Thymeleaf</a> template helper.
 *
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date Aug 29, 2016
 * @since 9.0.0
 */
public class TemplateHelper {

  private static final String TEMPLATE_FOLDER = "/resources/template/";;
  private static final String ANGULAR_TEMPLATE_FOLDER = "/resources/generix_angular_templates/";;
  /** HTML template engine */
  private static TemplateEngine htmlEngine;

  /** TEXT template engine */
  private static TemplateEngine textEngine;

  private TemplateHelper() {
    // should never be instantiate...
  }

  /* -- ENGINES -- */

  /**
   * @return the {@link TemplateEngine} initialized with <code>HTML</code> resolver with <code>/template/</code> prefix and
   *         <code>.html</code> suffix.
   */
  public static final TemplateEngine getHtmlEngine() {
    if (htmlEngine == null) {
      htmlEngine = new TemplateEngine();
      final ClassLoaderTemplateResolver htmlResolver = new ClassLoaderTemplateResolver();
      htmlResolver.setName("ByzaneoTemplateHtmlResolver");
      htmlResolver.setPrefix("/template/");
      htmlResolver.setSuffix(HTML.getExtension());
      htmlEngine.setTemplateResolver(htmlResolver);
    }
    return htmlEngine;
  }

  /**
   * @return the {@link TemplateEngine} initialized with <code>TEXT</code> resolver with <code>/template/</code> prefix and
   *         <code>.txt</code> suffix.
   */
  public static TemplateEngine getTextEngine() {
    if (textEngine == null) {
      textEngine = new TemplateEngine();
      final ClassLoaderTemplateResolver textResolver = new ClassLoaderTemplateResolver();
      textResolver.setName("ByzaneoTemplateTextResolver");
      textResolver.setPrefix("/template/");
      textResolver.setSuffix(TXT.getExtension());
      textResolver.setTemplateMode(TEXT);
      textEngine.setTemplateResolver(textResolver);
    }
    return textEngine;
  }

  /* -- PROCESS -- */

  /**
   * Writes the output text result in the given writer. A post-process is done to remove line starting with <code>--</code>.
   *
   * @param template to process
   * @param context of the execution
   * @param writer output
   */
  public static void processText(final String template, final IContext context, final Writer writer) {
    if (isNotBlank(template) && context != null && writer != null) {
      try (StringWriter textWriter = new StringWriter()) {
        getTextEngine().process(
            template,
            context,
            textWriter);
        // removes lines starting with "--" as (for now)
        // I don't know how to avoid template engine
        // to write empty lines when line only contains
        // thymeleaf instructions (if, foreach...)
        writeLines(
            stream(textWriter
                .toString()
                .split(LINE_SEPARATOR))
                    .filter(l -> !startsWith(l, "--"))
                    .collect(toList()),
            LINE_SEPARATOR,
            writer);
      }
      catch (IOException ioe) {
        throw new UncheckedIOException(ioe);
      }
    }
  }

  /**
   * Writes the output HTML result in the given writer.
   *
   * @param template to process
   * @param context of the execution
   * @param writer output
   */
  public static void processHtml(final String template, final IContext context, final Writer writer) {
    if (isNotBlank(template) && context != null && writer != null) {
      getHtmlEngine().process(
          template,
          context,
          writer);
    }
  }

  public static File resolveTemplateDirectory() {
    return new File(getFile(WEBAPP_DIR), TEMPLATE_FOLDER);
  }

  public static File getFile(ConfigurationService.ConfigurationKey key) {
    ConfigurationService configurationService = getBean(ConfigurationService.class, ConfigurationService.SERVICE_NAME);
    return configurationService.getFile(key, null);
  }

  public static File resolveAngularTemplateDirectory() {
    return new File(getFile(WEBAPP_DIR), ANGULAR_TEMPLATE_FOLDER);
  }

}
