package com.byzaneo.commons.event.spi;

import static com.byzaneo.commons.service.EventService.DEFAULT_TOPIC;
import static java.util.Arrays.asList;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;
import static org.slf4j.LoggerFactory.getLogger;

import java.util.List;

import org.slf4j.Logger;

import com.byzaneo.commons.api.Event;
import com.byzaneo.commons.event.bus.EventBus;
import com.byzaneo.commons.service.EventService;
import com.byzaneo.commons.util.Assert;
import com.byzaneo.commons.util.RegexMap;

/**
 * Default {@link EventService} implementation using the Guava library.
 *
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date 19 October 2013
 */
public class GuavaEventHandler implements EventSubscriber, EventPublisher {
  private static final Logger log = getLogger(GuavaEventHandler.class);

  public static final String EVENT_HANDLER_NAME = "comGuavaEventsHandler";

  private RegexMap<EventBus> eventBuses;

  /*
   * -- CONSTRUCTOR --
   */

  public GuavaEventHandler() {
    super();
    this.eventBuses = new RegexMap<>();
    // Set up default topic for 'dead' events
    addNewEventBusTopic(DEFAULT_TOPIC);
  }

  /*
   * -- LISTENER --
   */

  /** @see com.byzaneo.commons.service.EventService#subscribe(Object, String...) */
  @Override
  public void subscribe(Object listenerClass, String... topics) {
    for (String topic : topics) {
      log.debug("Subscribing {} on Guava topic {}", listenerClass, topic);
      getEventBuses(topic)
          .forEach(bus -> bus.register(listenerClass, topic));
    }
  }

  /** @see com.byzaneo.commons.service.EventService#unsubscribe(Object, String...) */
  @Override
  public void unsubscribe(Object listenerClass, String... listenerTopics) {
    for (String topic : listenerTopics) {
      getEventBuses(topic)
          .forEach(bus -> bus.unregister(listenerClass, topic));
      this.eventBuses.remove(topic);
    }
  }

  /*
   * -- PUBLISHER --
   */

  /** @see com.byzaneo.commons.event.spi.EventPublisher#publish(com.byzaneo.commons.api.Event) */
  @Override
  public void publish(final Event event) {
    Assert.notNull(event, "Event cannot be null");

    log.debug("Publishing {}", event);
    getEventBuses(event.getTopic())
        .forEach(bus -> bus.post(event));
  }

  /*
   * -- ACCESSORS --
   */

  public EventBus addNewEventBusTopic(final String topic) {
    Assert.notNull(topic, "Topic cannot be null");
    Assert.hasLength(topic, "Topic has to have name");

    final EventBus eventBus = new EventBus(topic);
    this.eventBuses.put(topic, eventBus);
    return eventBus;
  }

  /*
   * -- PRIVATES --
   */

  private List<EventBus> getEventBuses(final String topic) {
    final List<EventBus> existingEventBus = this.eventBuses.gets(topic);
    return isNotEmpty(existingEventBus)
        ? existingEventBus
        : asList(addNewEventBusTopic(topic));
  }

  /*
   * -- OVERRIDE --
   */

  @Override
  public String toString() {
    return EVENT_HANDLER_NAME;
  }
}
