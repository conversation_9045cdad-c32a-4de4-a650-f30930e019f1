package com.byzaneo.commons.job;

import static java.util.Arrays.asList;
import static org.springframework.util.Assert.notNull;

import java.util.AbstractMap.SimpleEntry;
import java.util.List;
import java.util.Map.Entry;

/**
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Oct 28, 2013
 * @since 3.1 COM-127
 */
public class EventTrigger extends AbstractTrigger {

  private static final long serialVersionUID = -3535636383185655846L;

  protected String topic;

  /* -- CONSTRUCTORS -- */

  public EventTrigger() {
    super();
  }

  public EventTrigger(String topic) {
    super();
    this.setTopic(topic);
  }

  /* -- CONFIGURATION -- */

  /** @see com.byzaneo.commons.job.Trigger#getConfiguration() */
  @Override
  public List<? extends Entry<String, String>> getConfiguration() {
    return asList(new SimpleEntry<String, String>("topic", getTopic()));
  }

  /* -- IMPL. -- */

  /** @see com.byzaneo.commons.job.Trigger#validate() */
  @Override
  public void validate() {
    notNull(this.topic, "Topic is required");
  }

  /* -- ACCESSORS -- */

  public String getTopic() {
    return topic;
  }

  public void setTopic(String topic) {
    this.topic = topic;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = super.hashCode();
    result = prime * result + ((topic == null) ? 0 : topic.hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (obj == null)
      return false;
    if (this == obj)
      return true;
    if (!super.equals(obj))
      return false;
    if (getClass() != obj.getClass())
      return false;
    EventTrigger other = (EventTrigger) obj;
    if (topic == null) {
      if (other.topic != null)
        return false;
    }
    else if (!topic.equals(other.topic))
      return false;
    return true;
  }

}
