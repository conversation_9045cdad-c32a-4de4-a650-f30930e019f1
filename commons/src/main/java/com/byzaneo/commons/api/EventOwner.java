package com.byzaneo.commons.api;

import java.io.Serializable;
import java.security.Principal;

/**
 * Application event ownership representation
 * 
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Jul 1, 2015
 * @since 5.0
 */
public interface EventOwner extends Serializable {

  // fields names
  public static final String FIELD_TYPE = "type";
  public static final String FIELD_IDENTIFIER = "id";
  public static final String FIELD_NAME = "name";
  public static final String FIELD_PRIMARY_GROUP_NAME = "primary";

  /**
   * @return this event principal type
   */
  Class<?> getType();

  /**
   * @return this event principal identification
   */
  String getIdentifier();

  /**
   * @return this event principal name
   */
  String getName();

  /**
   * @return this event principal primary group name
   */
  String getPrimaryGroupName();

  /**
   * @return the transient event principal
   */
  <P extends Principal> P getPrincipal();

  void setType(Class<?> type);

  void setIdentifier(String identifier);

  void setName(String name);

  void setPrimaryGroupName(String primaryGroupName);

  void setPrincipal(Principal principal);

}