package com.byzaneo.commons.exception;

/** 
* The UndefinedPathException is raised by createIndexable method when a bean descriptor path value is missing
*/  
public class UndefinedPathException extends Exception{  

	private static final long serialVersionUID = -8061800201880378866L;

	public UndefinedPathException() {}  

	public UndefinedPathException(String message) {  
		super(message); 
	}

	public UndefinedPathException(Throwable cause) {  
		super(cause); 
	}  
	
	public UndefinedPathException(String message, Throwable cause) {  
		super(message, cause); 
	} 
}