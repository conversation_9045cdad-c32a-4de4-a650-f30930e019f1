package com.byzaneo.commons.service;

import com.byzaneo.commons.bean.Job;
import com.byzaneo.commons.job.*;
import com.byzaneo.commons.util.DatePeriodHelper;
import com.byzaneo.commons.util.SelfSchedulerHelper;
import lombok.Getter;
import lombok.Setter;
import org.quartz.impl.calendar.DailyCalendar;
import org.quartz.impl.calendar.WeeklyCalendar;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.*;

import static com.byzaneo.commons.job.JobExecutionBuilder.createJobExecutionBuilder;
import static com.byzaneo.commons.job.SelfRelaunchableTrigger.PROCESS_RELAUNCH_TOPIC_VARIABLE_NAME;
import static com.byzaneo.commons.service.ExecutorService.ConfigurationKey.MinimumPoolSize;
import static com.byzaneo.commons.util.DatePeriodHelper.SENTINEL_DATE;
import static com.byzaneo.commons.util.SelfSchedulerHelper.getDelay;
import static com.byzaneo.commons.util.SelfSchedulerHelper.logSelfScheduledExecution;

@Service(SelfSchedulerService.SERVICE_NAME)
public class SelfSchedulerServiceImpl implements SchedulerService, SelfSchedulerService {

  private static final Logger log = LoggerFactory.getLogger(SelfSchedulerServiceImpl.class);

  // SERVICES
  @Autowired
  @Qualifier(ConfigurationService.SERVICE_NAME)
  private ConfigurationService configService;

  @Autowired
  @Qualifier(ExecutorService.SERVICE_NAME)
  private ExecutorService executorService;

  @Autowired
  @Qualifier(MultiNodeService.SERVICE_NAME)
  private transient MultiNodeService multiNodeService;

  private boolean disabled = false;

  // used for single node
  private Map<String, ScheduledFutureWithDate> localScheduledFutureMap = new HashMap<>();

  private Map<String, List<org.quartz.Calendar>> localAuthorizedPeriodsMap = new HashMap<>();

  private Set<String> localSelfRelaunchableActiveTriggerIds = new HashSet<>();

  private ScheduledExecutorService localScheduledExecutor;

  @Override
  @PostConstruct
  public void init() throws Exception {
    if (!isMultiNode()) {
      // the maximum number of threads to allow in the pool; negative means not bound, should use executor.min.pool.size for this
      Integer maximumPoolSize = configService.getInteger(MinimumPoolSize, -1);

      localScheduledExecutor = Executors
          .newScheduledThreadPool(maximumPoolSize < 0 ? Integer.MAX_VALUE : maximumPoolSize, r ->
          {
            final Thread result = new Thread(r);
            result.setName("selfScheduler");
            result.setDaemon(true);
            return result;
          });
    }
  }

  @Override
  public String getName() {
    return SelfSchedulerService.SERVICE_NAME;
  }

  public boolean isMultiNode() {
    return multiNodeService.isMultiNodeEnabled();
  }

  @Override
  public boolean accept(JobTrigger jobTrigger) {
    return jobTrigger != null &&
        jobTrigger.getTrigger() != null &&
        accept(jobTrigger.getTrigger()
            .getClass());
  }

  @Override
  public boolean accept(Class<? extends Trigger> triggerType) {
    return triggerType != null && SelfRelaunchableTrigger.class.isAssignableFrom(triggerType);
  }

  @Override
  public boolean manage(JobTrigger jobTrigger) throws JobException {
    return accept(jobTrigger);
  }

  @Override
  public boolean process(JobTrigger jobTrigger, boolean store) throws JobException {
    if (!accept(jobTrigger) || jobTrigger.getJob() == null)
      return false;

    revoke(jobTrigger);

    final SelfRelaunchableTrigger trigger = jobTrigger.<SelfRelaunchableTrigger> getTrigger();
    if (jobTrigger.isDisabled()) {
      disableTrigger(trigger.getId());
      log.info("Job trigger is disabled: {}", jobTrigger);
      return false;
    }

    if (trigger.getEndTime() != null && trigger.getEndTime()
        .before(new Date())) {
      return false;
    }

    if (trigger.getJob() == null) {
      trigger.setJob(jobTrigger.getJob());
    }

    addActiveTrigger(trigger);
    addAuthorizedPeriods(trigger);
    scheduleWithDelay(trigger, trigger.getPeriodicityInMin() /* delayInMinutes */, true);
    return true;
  }

  @Override
  public boolean revoke(JobTrigger jobTrigger) throws JobException {
    if (!accept(jobTrigger) || jobTrigger.getJob() == null)
      return false;

    boolean unscheduled = false;
    final SelfRelaunchableTrigger trigger = jobTrigger.<SelfRelaunchableTrigger> getTrigger();

    if (!isMultiNode()) {
      unscheduled = unscheduleLocalScheduledExecution(trigger, true);
    }
    else {
      unscheduled = SelfSchedulerHelper.unscheduleDistributedScheduledExecution(trigger,
          multiNodeService.getDistributedScheduledFutureMap(),
          multiNodeService.getDistributedScheduledFutureDateMap(),
          multiNodeService.getDistributedScheduledExecutor(), true);
    }

    if (unscheduled) {
      log.debug("Next execution of self relaunchable trigger {} ({}) was unscheduled.", trigger.getName(), trigger.getId());
      log.debug("Scheduled periodic trigger revoked: {}", trigger);
    }

    return unscheduled;
  }

  @Override
  public Date nextExecutionTime(JobTrigger jobTrigger) {
    return nextExecutionTime(null, jobTrigger);
  }

  @Override
  public Date nextExecutionTime(Date fromDate, JobTrigger jobTrigger) {
    if (!accept(jobTrigger) ||
        jobTrigger.getJob() == null || jobTrigger.getJob()
        .isDisabled())
      return null;

    final SelfRelaunchableTrigger trigger = jobTrigger.<SelfRelaunchableTrigger> getTrigger();

    if (!isMultiNode()) {
      ScheduledFutureWithDate scheduledFutureWithDate = localScheduledFutureMap.get(trigger.getId());
      if (scheduledFutureWithDate != null)
        return scheduledFutureWithDate.getNextExecutionDate();
    }
    else {
      Date nextExecutionDate = multiNodeService.getDistributedScheduledFutureDateMap()
          .get(trigger.getId());
      if (nextExecutionDate != null)
        return nextExecutionDate;
    }

    List<org.quartz.Calendar> authorizedPeriodsCalendarList = isMultiNode() ? multiNodeService.getDistributedTriggerAuthorizedPeriodsMap()
        .get(trigger.getId()) : localAuthorizedPeriodsMap.get(trigger.getId());
    // check if the current time is in a valid period
    long delayInSec = getDelay(trigger, authorizedPeriodsCalendarList, 0, false);
    if (delayInSec < 0) {
      // no execution time to display if endTime has passed
      return null;
    }
    return SENTINEL_DATE;
  }

  @Override
  public void start() throws JobExecutorException, UnsupportedOperationException {
    this.disabled = false;
  }

  @Override
  public void stop() throws JobExecutorException, UnsupportedOperationException {
    this.disabled = true;
    if (!isMultiNode()) {
      clearAllLocalScheduledExecutions();
    }
    // for multi node: if a node stops, the scheduled executions will be rescheduled on an active node
  }

  @Override
  public void pause() throws JobExecutorException, UnsupportedOperationException {
    this.stop();
  }

  @Override
  public void resume() throws JobExecutorException, UnsupportedOperationException {
    this.start();
  }

  @Override
  public void shutdown(boolean force) throws JobExecutorException, UnsupportedOperationException {
    this.stop();
  }

  @Override
  public boolean isPaused() throws JobExecutorException, UnsupportedOperationException {
    return disabled;
  }

  @Override
  public boolean isStarted() throws JobExecutorException, UnsupportedOperationException {
    return !disabled;
  }

  @Override
  public boolean isShutdown() throws JobExecutorException, UnsupportedOperationException {
    return !disabled;
  }

  @Override
  public void scheduleWithDelay(Trigger trigger, long delayInMinutes, boolean hasTopicWithPeriod) {
    if (!(trigger instanceof SelfRelaunchableTrigger)) {
      return;
    }
    SelfRelaunchableTrigger selfRelaunchableTrigger = (SelfRelaunchableTrigger) trigger;

    if (!isMultiNode()) {
      localScheduleWithDelay(selfRelaunchableTrigger, delayInMinutes, hasTopicWithPeriod);
    }
    else {
      Job job = (Job) selfRelaunchableTrigger.getJob();
      // because the Job will be serialized on a distributed map
      // we need to call prePresist at this point
      job.prePresist();
      multiNodeService.getDistributedScheduledJobMap()
          .put(selfRelaunchableTrigger.getId(), job);
      SelfSchedulerHelper.distributedScheduleWithDelay(selfRelaunchableTrigger, multiNodeService.getDistributedScheduledFutureMap(),
          multiNodeService.getDistributedScheduledFutureDateMap(), multiNodeService.getDistributedTriggerAuthorizedPeriodsMap(),
          multiNodeService.getDistributedScheduledExecutor(), multiNodeService.getDistributedExecutor(), delayInMinutes, hasTopicWithPeriod);
    }
  }

  @Override
  public boolean isTriggerEnabled(Trigger trigger) {
    if (!(trigger instanceof SelfRelaunchableTrigger)) {
      return false;
    }
    if (!isMultiNode()) {
      return localSelfRelaunchableActiveTriggerIds.contains(trigger.getId());
    }
    return multiNodeService.getJobTriggers(getName())
        .containsKey(trigger.getId());
  }

  private void disableTrigger(String triggerId) {
    if (!isMultiNode()) {
      localSelfRelaunchableActiveTriggerIds.remove(triggerId);
    }
    else {
      multiNodeService.getJobTriggers(getName())
          .remove(triggerId);
    }
  }

  private void addActiveTrigger(SelfRelaunchableTrigger trigger) {
    if (!isMultiNode()) {
      localSelfRelaunchableActiveTriggerIds.add(trigger.getId());
    }
    else {
      multiNodeService.getJobTriggers(getName())
          .put(trigger.getId(), trigger);
    }
  }

  private void addAuthorizedPeriods(SelfRelaunchableTrigger trigger) {
    Map<String, List<org.quartz.Calendar>> authorizedPeriodsMap = isMultiNode()
        ? multiNodeService.getDistributedTriggerAuthorizedPeriodsMap()
        : localAuthorizedPeriodsMap;
    List<SelfRelaunchableTriggerAuthorizedPeriod> triggerAuthorizedPeriods = trigger.getAuthorizedPeriods();
    if (triggerAuthorizedPeriods.isEmpty()) {
      authorizedPeriodsMap.remove(trigger.getId());
      return;
    }
    List<org.quartz.Calendar> authorizedPeriodsList = new ArrayList<>();
    for (SelfRelaunchableTriggerAuthorizedPeriod authorizedPeriod : triggerAuthorizedPeriods) {
      DailyCalendar calendar = getAuthorizedPeriodCalendar(authorizedPeriod);
      authorizedPeriodsList.add(calendar);
    }
    authorizedPeriodsMap.put(trigger.getId(), authorizedPeriodsList);
  }

  private DailyCalendar getAuthorizedPeriodCalendar(SelfRelaunchableTriggerAuthorizedPeriod authorizedPeriod) {
    long endTime = authorizedPeriod.getEndTime() == 0 ? 0 : authorizedPeriod.getEndTime() - 1;
    // DailyCalendar hour must be in the range 0 to 23
    // ex. 0h -> 24h -> 00:00:00 -> 23:59:59
    // ex. 9h -> 13h -> 09:00:00 -> 12:59:59
    // ex. 0h -> 1h -> 00:00:00 -> 00:59:59
    DailyCalendar calendar = new DailyCalendar(String.format("%02d:00:00", authorizedPeriod.getStartTime()),
        String.format("%02d:59:59", endTime));
    calendar.setInvertTimeRange(true); // include time between startTime and endTime
    WeeklyCalendar weeklyCalendar = getWeeklyCalendar(authorizedPeriod);
    calendar.setBaseCalendar(weeklyCalendar);
    return calendar;
  }

  private static WeeklyCalendar getWeeklyCalendar(SelfRelaunchableTriggerAuthorizedPeriod authorizedPeriod) {
    WeeklyCalendar weeklyCalendar = new WeeklyCalendar();
    weeklyCalendar.setDayExcluded(Calendar.MONDAY, !authorizedPeriod.getDays()
        .contains(Calendar.MONDAY));
    weeklyCalendar.setDayExcluded(Calendar.TUESDAY, !authorizedPeriod.getDays()
        .contains(Calendar.TUESDAY));
    weeklyCalendar.setDayExcluded(Calendar.WEDNESDAY, !authorizedPeriod.getDays()
        .contains(Calendar.WEDNESDAY));
    weeklyCalendar.setDayExcluded(Calendar.THURSDAY, !authorizedPeriod.getDays()
        .contains(Calendar.THURSDAY));
    weeklyCalendar.setDayExcluded(Calendar.FRIDAY, !authorizedPeriod.getDays()
        .contains(Calendar.FRIDAY));
    weeklyCalendar.setDayExcluded(Calendar.SATURDAY, !authorizedPeriod.getDays()
        .contains(Calendar.SATURDAY));
    weeklyCalendar.setDayExcluded(Calendar.SUNDAY, !authorizedPeriod.getDays()
        .contains(Calendar.SUNDAY));
    return weeklyCalendar;
  }

  private void localScheduleWithDelay(SelfRelaunchableTrigger trigger, long delayInMinutes, boolean hasTopicWithPeriod) {
    CompletableFuture
        .supplyAsync(() ->
        {
          try {
            if (hasTopicWithPeriod) {
              // Unschedule scheduled self relaunchable trigger periodical execution
              boolean isNextPeriodicalExecutionUnscheduled = unscheduleLocalScheduledExecution(trigger, true);
              if (isNextPeriodicalExecutionUnscheduled) {
                log.debug("Next periodical execution of self relaunchable trigger {} ({}) was unscheduled.", trigger.getName(), trigger.getId());
              }
            }

            // if at least one authorized period was added, do not execute the process if we are not in an authorized period
            List<org.quartz.Calendar> authorizedPeriodsCalendarList = localAuthorizedPeriodsMap.get(trigger.getId());
            if (authorizedPeriodsCalendarList != null && !authorizedPeriodsCalendarList.isEmpty()) {
              boolean isCurrentDateIncluded = false;
              for (org.quartz.Calendar authorizedPeriodCalendar : authorizedPeriodsCalendarList) {
                if (authorizedPeriodCalendar.isTimeIncluded((new Date()).getTime())) {
                  isCurrentDateIncluded = true;
                  break;
                }
              }
              if (!isCurrentDateIncluded) {
                // try to reschedule after the given periodicity
                localScheduleWithDelay(trigger, 0, true);
                return null;
              }
            }

            // execute process
            return trigger.getJob()
                .call(createJobExecutionBuilder(trigger, PROCESS_RELAUNCH_TOPIC_VARIABLE_NAME,
                    trigger.getTopic())
                    .build());
          }
          catch (Exception ex) {
            log.error("Job execution failed: " + trigger.getJob(), ex);
            return null;
          }
        }, this.delayedExecutor(trigger, delayInMinutes * 60, hasTopicWithPeriod));
  }

  private Executor delayedExecutor(SelfRelaunchableTrigger trigger, long delay, boolean hasTopicWithPeriod) {
    return r -> {
      long delayInSec = delay;
      if (hasTopicWithPeriod && delayInSec == 0) {
        delayInSec = trigger.getPeriodicityInMin() * 60;
      }

      // do not schedule next process execution if trigger was disabled
      if (!isTriggerEnabled(trigger)) {
        return;
      }

      List<org.quartz.Calendar> authorizedPeriodCalendar = localAuthorizedPeriodsMap.get(trigger.getId());
      delayInSec = getDelay(trigger, authorizedPeriodCalendar, delayInSec, true);
      if (delayInSec < 0) {
        // do not schedule next process execution if endTime has passed
        return;
      }

      ScheduledFuture<Void> scheduledFuture = localScheduledExecutor.schedule(() -> {
        executorService.getExecutor()
              .execute(r);
        return null;
      }, delayInSec, TimeUnit.SECONDS);

      // reschedule next periodical execution after the given periodicity and in an authorized period
      // if a topic with period was received OR if an authorized period just ended
      if (hasTopicWithPeriod || (delay == 0 && delayInSec > 0)) {
        long delayInSecToNextPeriodicalExecution = delayInSec == 0
            ? DatePeriodHelper.getDurationInSeconds(new Date(),
            SelfSchedulerHelper.getNextPossibleAuthorizedExecutionDate(trigger, authorizedPeriodCalendar,
                new Date(System.currentTimeMillis() + TimeUnit.SECONDS.toMillis(trigger.getPeriodicityInMin() * 60)), true))
            : delayInSec;
        Date nextExecution = new Date(System.currentTimeMillis() + TimeUnit.SECONDS.toMillis(delayInSecToNextPeriodicalExecution));
        logSelfScheduledExecution(trigger, delayInSec, nextExecution);
        localScheduledFutureMap.put(trigger.getId(), new ScheduledFutureWithDate(scheduledFuture, nextExecution));
      }
    };
  }

  /**
   * Unschedule given scheduled self relaunchable trigger execution, on single node
   *
   * @param trigger
   * @param removeExecution
   * @return true if the execution was successfully unscheduled
   */
  private boolean unscheduleLocalScheduledExecution(SelfRelaunchableTrigger trigger, boolean removeExecution) {
    try {
      final String tid = trigger.getId();
      ScheduledFutureWithDate scheduledFutureWithDate = localScheduledFutureMap.get(tid);
      if (scheduledFutureWithDate != null && scheduledFutureWithDate.getScheduledFuture() != null) {
        scheduledFutureWithDate.getScheduledFuture()
            .cancel(false);
        if (removeExecution) {
          localScheduledFutureMap.remove(tid);
        }
        return true;
      }
    }
    catch (Exception e) {
      throw new JobException("Error revoking self relaunchable trigger: " + trigger, e);
    }

    return false;
  }

  /**
   * Unschedule all scheduled self relaunchable trigger executions, on single node
   */
  private void clearAllLocalScheduledExecutions() {
    localScheduledFutureMap.values()
        .forEach(scheduledFutureWithDate -> scheduledFutureWithDate.getScheduledFuture()
            .cancel(false));
    localScheduledFutureMap.clear();
    log.debug("All scheduled self relaunchable trigger executions were unscheduled.");
  }

  @Override
  public void destroy() throws Exception {
  }

  public class ScheduledFutureWithDate {
    @Getter
    @Setter
    private ScheduledFuture<Void> scheduledFuture;

    @Getter
    @Setter
    private Date nextExecutionDate;

    public ScheduledFutureWithDate(ScheduledFuture<Void> scheduledFuture, Date nextExecutionDate) {
      this.scheduledFuture = scheduledFuture;
      this.nextExecutionDate = nextExecutionDate;
    }
  }
}