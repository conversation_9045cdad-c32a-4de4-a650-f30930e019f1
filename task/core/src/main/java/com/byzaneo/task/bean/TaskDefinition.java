package com.byzaneo.task.bean;

import static com.byzaneo.task.api.TaskDefinition.State.Enabled;
import static javax.persistence.CascadeType.REMOVE;
import static javax.persistence.EnumType.STRING;
import static javax.persistence.TemporalType.TIMESTAMP;
import static org.hibernate.annotations.SourceType.DB;

import java.util.*;

import javax.persistence.*;

import org.hibernate.annotations.Source;

import com.byzaneo.commons.bean.AbstractPersitentLongId;
import com.google.gson.annotations.Until;

/**
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Apr 27, 2012
 * @since 1.0
 */
@Entity
@Table(name = "TSK_TASK_DEFINITION")
public class TaskDefinition extends AbstractPersitentLongId implements com.byzaneo.task.api.TaskDefinition {
  private static final long serialVersionUID = 647145767251041169L;

  @Column(name = "TASK_UUID")
  private String uuid;

  @Column(name = "TASK_TITLE", nullable = false, length = 1024)
  private String title;

  @Column(name = "TASK_TYPE", nullable = false)
  private String type;

  @Column(name = "TASK_OWNER", nullable = false)
  private String owner;

  @Column(name = "TASK_STATE", nullable = false, length = 32)
  @Enumerated(STRING)
  private State state = Enabled;

  @Column(name = "TASK_DATE_CREATION", updatable = false)
  private Date creationDate = new Date();

  @Version
  @Column(name = "TASK_DATE_MODIFICATION", updatable = false, columnDefinition = "TIMESTAMP")
  @Source(DB)
  @Temporal(TIMESTAMP)
  private Date modificationDate = new Date();

  @Column(name = "TASK_DESCRIPTION", length = 4096)
  private String description;

  @Column(name = "TASK_LOCKED")
  private boolean locked;

  @Lob
  @Column(name = "TASK_DEFINITION", length = 65535)
  private String definition;

  @ManyToOne
  @JoinColumn(name = "TASK_PARENT_ID")
  private TaskDefinition parent;

  @Column(name = "TASK_SHOW_TITLE")
  private Boolean showTitle;

  @Column(name = "TASK_SHOW_DESCRIPTION")
  private Boolean showDescription;

  /** Used only for cascade deletion */
  @Until(0.0)
  @OneToMany(mappedBy = "parent", cascade = REMOVE)
  private List<TaskDefinition> children;

  /*
   * CONSTRUCTOR
   */

  TaskDefinition() {
    super();
  }

  public TaskDefinition(String type) {
    this();
    this.type = type;
  }

  /*
   * ACCESSORS
   */

  @Override
  public String getType() {
    return type;
  }

  @Override
  public String getUuid() {
    return uuid;
  }

  @Override
  public void setUuid(String uuid) {
    this.uuid = uuid;
  }

  @Override
  public String getTitle() {
    return title;
  }

  @Override
  public void setTitle(String title) {
    this.title = title;
  }

  @Override
  public String getOwner() {
    return owner;
  }

  @Override
  public void setOwner(String owner) {
    this.owner = owner;
  }

  @Override
  public State getState() {
    return state;
  }

  @Override
  public void setState(State state) {
    this.state = state;
  }

  @Override
  public String getDescription() {
    return description;
  }

  @Override
  public void setDescription(String description) {
    this.description = description;
  }

  @Override
  public String getDefinition() {
    return definition;
  }

  @Override
  public void setDefinition(String definition) {
    this.definition = definition;
  }

  @Override
  public boolean isLocked() {
    return locked;
  }

  @Override
  public void setLocked(boolean locked) {
    this.locked = locked;
  }

  @Override
  public TaskDefinition getParent() {
    return parent;
  }

  public void setParent(TaskDefinition parent) {
    this.parent = parent;
  }

  public Date getCreationDate() {
    return creationDate;
  }

  public Date getModificationDate() {
    return modificationDate;
  }

  @Override
  public boolean isShowTitle() {
    return showTitle == null || showTitle;
  }

  @Override
  public void setShowTitle(boolean showTitle) {
    this.showTitle = showTitle;
  }

  @Override
  public boolean isShowDescription() {
    return showDescription == null || showDescription;
  }

  @Override
  public void setShowDescription(boolean showDescription) {
    this.showDescription = showDescription;
  }

  @Override
  @SuppressWarnings("unchecked")
  public <D extends com.byzaneo.task.api.TaskDefinition> List<D> getChildren() {
    return (List<D>) children;
  }

  /*
   * OVERRIDE
   */

  @Override
  public String toString() {
    return new StringBuilder()
        .append("Task@")
        .append(id)
        .append(" [")
        .append(type)
        .append(":")
        .append(uuid)
        .append(":")
        .append(title)
        .append(":")
        .append(state)
        .append(":")
        .append(locked ? "locked" : "unlocked")
        .append(":")
        .append(parent == null ? "root" : parent.getId())
        .append(",%s]")
        .toString();
  }

  /** @see com.byzaneo.commons.bean.AbstractPersitentLongId#hashCode() */
  @Override
  public int hashCode() {
    final int prime = 31;
    int result = super.hashCode();
    result = prime * result + ((definition == null) ? 0 : definition.hashCode());
    result = prime * result + ((description == null) ? 0 : description.hashCode());
    result = prime * result + ((title == null) ? 0 : title.hashCode());
    result = prime * result + ((type == null) ? 0 : type.hashCode());
    return result;
  }

  /** @see com.byzaneo.commons.bean.AbstractPersitentLongId#equals(java.lang.Object) */
  @Override
  public boolean equals(Object obj) {
    if (obj == null)
      return false;
    if (this == obj)
      return true;
    // should we remove this condition when the
    // bean is proxified
    if (getClass() != obj.getClass())
      return false;
    TaskDefinition other = (TaskDefinition) obj;
    // first, checks equality on IDs
    if (this.id != null && this.id.equals(other.id))
      return true;
    // then, checks on definition, description, title and type
    if (definition == null) {
      if (other.definition != null)
        return false;
    }
    else if (!definition.equals(other.definition))
      return false;
    if (description == null) {
      if (other.description != null)
        return false;
    }
    else if (!description.equals(other.description))
      return false;
    if (uuid == null) {
      if (other.uuid != null)
        return false;
    }
    else if (!uuid.equals(other.uuid))
      return false;
    if (uuid == null) {
      if (other.uuid != null)
        return false;
    }
    else if (!uuid.equals(other.uuid))
      return false;
    if (title == null) {
      if (other.title != null)
        return false;
    }
    else if (!title.equals(other.title))
      return false;
    if (type == null) {
      if (other.type != null)
        return false;
    }
    else if (!type.equals(other.type))
      return false;
    return true;
  }
}
