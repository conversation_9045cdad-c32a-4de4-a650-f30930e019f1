package com.byzaneo.task.ui.convert;

import static com.byzaneo.commons.ui.util.JSFHelper.getSpringBean;
import static org.apache.commons.lang3.StringUtils.isBlank;

import java.io.Serializable;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import com.byzaneo.task.api.TaskType;
import com.byzaneo.task.service.TaskService;

/**
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Oct 25, 2012
 * @since 1.0
 */
@FacesConverter(value = TaskTypeFacesConverter.CONVERTER_ID)
public class TaskTypeFacesConverter implements Converter, Serializable {
  private static final long serialVersionUID = -1424471521602393931L;
  public static final String CONVERTER_ID = "taskTypeConverter";

  private static TaskService service;

  /** @see javax.faces.convert.Converter#getAsString(FacesContext, UIComponent, Object) */
  @Override
  public String getAsString(FacesContext context, UIComponent component, Object value) {
    if (value == null)
      return "";
    if (value instanceof String)
      return (String) value;
    return ((TaskType) value).getId();
  }

  /** @see javax.faces.convert.Converter#getAsObject(FacesContext, UIComponent, String) */
  @Override
  public Object getAsObject(FacesContext context, UIComponent component, String value) {
    return isBlank(value) ? null : this.getService()
        .getTaskType(value);
  }

  private TaskService getService() {
    if (service == null)
      service = getSpringBean(TaskService.class, TaskService.SERVICE_NAME);
    return service;
  }
}
