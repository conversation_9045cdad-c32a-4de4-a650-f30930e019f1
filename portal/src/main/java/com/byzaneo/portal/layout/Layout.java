package com.byzaneo.portal.layout;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

import com.byzaneo.portal.bean.Portlet;

/**
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Oct 27, 2012
 * @since 1.0
 */
public abstract class Layout implements Serializable {
  private static final long serialVersionUID = -538338793653242605L;

  protected String name;

  protected Layout(String name) {
    super();
    this.name = name;
  }

  public String getName() {
    return name;
  }

  /** @see java.lang.Object#hashCode() */
  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + ((name == null) ? 0 : name.hashCode());
    return result;
  }

  /** @see java.lang.Object#equals(java.lang.Object) */
  @Override
  public boolean equals(Object obj) {
    if (this == obj)
      return true;
    if (obj == null)
      return false;
    if (getClass() != obj.getClass())
      return false;
    Layout other = (Layout) obj;
    if (name == null) {
      if (other.name != null)
        return false;
    }
    else if (!name.equals(other.name))
      return false;
    return true;
  }

  /** @see java.lang.Object#toString() */
  @Override
  public String toString() {
    return String.format("Layout [name=%s]", name);
  }

  /**
   * @return the portlets contained in this layout
   */
  public abstract List<Portlet> getPortlets();

  /**
   * @param portlets to remove
   * @return <code>true</code> if at least one of the given portlet has been removed
   */
  public abstract boolean removePortlets(Collection<Portlet> portlets);

  /**
   * @param portlets to add
   * @return the portlets <b>NOT</b> added
   */
  public abstract Collection<Portlet> addPortlets(Collection<Portlet> portlets);
}
