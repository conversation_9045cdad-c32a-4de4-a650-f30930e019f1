package com.byzaneo.portal.layout.columns;

import static com.byzaneo.portal.layout.columns.Column.Device.md;
import static com.byzaneo.portal.layout.columns.ColumnsLayout.Direction.east;
import static com.byzaneo.portal.layout.columns.ColumnsLayout.Direction.north;
import static java.lang.String.format;
import static java.util.Collections.emptyList;
import static java.util.Collections.swap;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.lang3.ArrayUtils.isNotEmpty;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import com.byzaneo.portal.bean.Portlet;
import com.byzaneo.portal.layout.Layout;
import com.byzaneo.portal.layout.columns.Column.Device;

/**
 * Columns layout based on the Bootstrap 12 columns grid system: http://getbootstrap.com/css/#grid
 *
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Oct 13, 2014
 * @since 1.3 PTL-10
 */
public class ColumnsLayout extends Layout {
  private static final long serialVersionUID = -1218588495447893337L;

  public static final String LAYOUT_NAME = "Columns";

  public enum Direction {
    /** Cardinal direction */
    north,
    south,
    east,
    west,
    /** from columns to header */
    header,
    /** from header to columns */
    columns;
  }

  /** Header columns (full width layout top column) */
  private Column header;

  /** Columns definition */
  private List<Column> columns;

  /* -- CONSTRUCTORS -- */

  public ColumnsLayout() {
    super(LAYOUT_NAME);
    this.columns = new ArrayList<>();
  }

  public ColumnsLayout(Device device, int... widths) {
    this();
    if (isNotEmpty(widths)) {
      for (int width : widths) {
        addColumn(device, width);
      }
    }
  }

  /* -- IMPLEMENTATION -- */

  /** @see com.byzaneo.portal.layout.Layout#getPortlets() */
  @Override
  public List<Portlet> getPortlets() {
    final List<Portlet> r = new ArrayList<>();
    // header
    if (header != null)
      r.addAll(header.getPortlets());
    // columns
    for (Column col : columns)
      r.addAll(col.getPortlets());
    return r;
  }

  /** @see com.byzaneo.portal.layout.Layout#removePortlets(java.util.Collection) */
  @Override
  public boolean removePortlets(Collection<Portlet> portlets) {
    if (isEmpty(portlets))
      return true;
    boolean r = false;
    // header
    if (header != null)
      r = header.getPortlets()
          .removeAll(portlets);
    // columns
    for (Column col : columns) {
      r = r || col.getPortlets()
          .removeAll(portlets);
    }
    return r;
  }

  /** @see com.byzaneo.portal.layout.Layout#addPortlets(java.util.Collection) */
  @Override
  public Collection<Portlet> addPortlets(Collection<Portlet> portlets) {
    if (portlets == null || portlets.isEmpty())
      return emptyList();
    if (columns.isEmpty()) {
      columns.add(new Column(md, 12));
    }
    Column column = columns.get(0);
    // adds the portlets to the first column
    for (Portlet portlet : portlets) {
      if (portlet != null) {
        column.getPortlets()
            .add(portlet);
      }
    }
    return emptyList();
  }

  /* -- ACCESSORS -- */

  public Column getHeader() {
    return header == null ? (header = new Column(md, 12)) : header;
  }

  public void setHeader(Column header) {
    this.header = header;
  }

  public boolean isHeaded() {
    return header != null && !header.isEmpty();
  }

  public List<Column> getColumns() {
    return columns;
  }

  public void setColumns(List<Column> columns) {
    this.columns = columns;
  }

  public int getColumnCount() {
    return columns.size();
  }

  /* -- UTILS -- */

  public void addColumn(Device md, int width) {
    if (md != null && width > 0)
      columns.add(new Column(md, width));
  }

  /**
   * @param colIndex the column index where to add the portlet If less than <code>0</code>, added to the header column
   * @param rowIndex the row index where to insert the portlet. If less than <code>0</code> the portlet is added to the end.
   * @param above if <code>true</code> inserted above the given positive index. Otherwise, inserted below.
   */
  public void addPortlet(int colIndex, int rowIndex, boolean above) {
    final Column col = colIndex < 0 ? getHeader() : getColumns().get(colIndex);
    if (rowIndex < 0)
      col.getPortlets()
          .add(new Portlet());
    else if (above)
      col.getPortlets()
          .add(rowIndex, new Portlet());
    else
      col.getPortlets()
          .add(rowIndex + 1, new Portlet());
  }

  /**
   * @param colIndex the column index where the portlet to remove is located. If less than <code>0</code>, removed in the header column
   * @param rowIndex the row index of the portlet to remove.
   * @return the removed portlet
   * @throws IndexOutOfBoundsException if the indexes are out of range (<tt>index &lt; 0 || index &gt;= size()</tt>)
   */
  public Portlet removePortlet(int colIndex, int rowIndex) {
    final Column col = colIndex < 0 ? getHeader() : getColumns().get(colIndex);
    return col.getPortlets()
        .remove(rowIndex);
  }

  /**
   * @param colIndex where is located the portlet to move. If less than <code>0</code>, move will be applied in the header column.
   * @param rowIndex where is located the portlet to move
   * @param direction in which to move the portlet. Note, if header column, only {@link Direction#north} and {@link Direction#south} are
   *          supported.
   */
  public void movePortlet(int rowIndex, int colIndex, Direction direction) {
    if (direction == null)
      return;

    // columns
    switch (direction) {
    case columns:
      getColumns().get(0)
          .getPortlets()
          .add(0, getHeader().getPortlets()
              .remove(rowIndex));
      break;
    case header:
      getHeader().getPortlets()
          .add(getColumns().get(colIndex)
              .getPortlets()
              .remove(rowIndex));
      break;
    case north:
    case south:
      // header column?
      Column column = colIndex < 0 ? getHeader() : getColumns().get(colIndex);
      swap(column.getPortlets(), rowIndex,
          north == direction ? rowIndex - 1 : rowIndex + 1);
      break;
    case east:
    case west:
      // not header column
      if (colIndex > -1) {
        Column colSource = this.getColumns()
            .get(colIndex);
        Column colTarget = this.getColumns()
            .get(east == direction ? colIndex + 1 : colIndex - 1);
        colTarget.getPortlets()
            .add(colSource.getPortlets()
                .remove(rowIndex));
      }
      break;
    default:
      throw new IllegalArgumentException("Wrong direction: " + direction);
    }
  }

  /* -- OVERRIDE -- */

  /** @see com.byzaneo.portal.layout.Layout#toString() */
  @Override
  public String toString() {
    return format("%s [header=%s, columns=%s]", getName(), isHeaded(), columns);
  }
}
