<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml" xmlns:f="http://xmlns.jcp.org/jsf/core" xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:cc="http://xmlns.jcp.org/jsf/composite" xmlns:pt="http://xmlns.jcp.org/jsf/passthrough" xmlns:p="http://primefaces.org/ui"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets" xmlns:gnx="http://xmlns.jcp.org/jsf/composite/components/gnx"
	xmlns:b="http://byzaneo.com/ui">
	<!-- INTERFACE -->
	<cc:interface name="perimeters" >
		<cc:attribute name="handler" default="#{gnxCompanyHandler}" required="true" />
	</cc:interface>
		
	<!-- IMPLEMENTATION -->
	<cc:implementation>
		<p:dataTable id="cPerimeterTable" var="perimeter" value="#{cc.attrs.handler.perimeters}" rows="10" paginator="true" paginatorPosition="bottom" paginatorAlwaysVisible="true"
				rowKey="#{perimeter.id}" selection="#{cc.attrs.handler.perimeter}" selectionMode="single" sortBy="#{perimeter.code}" sortOrder="ascending" filteredValue="#{cc.attrs.handler.filteredPerimeters}"
				widgetVar="wPerimeterTable" styleClass="datatable-hide-filters psCPerimeterPanel" emptyMessage="#{labels.no_records_found}" rendered="#{gnxCompanyHandler.logs==null}">
				<p:ajax event="rowSelect" process="@this" update="@none" listener="#{cc.attrs.handler.onSelectPerimeter}"  oncomplete="jQuery('.jqsOpenUserDialogPerimeters').click()" />
				<f:facet name="header">
					<h:panelGrid columns="2" columnClasses="left,right" width="100%">
						<h:outputText value="#{labels.perimeters}" styleClass="title1" />
						<h:panelGroup>
							<p:inputText id="globalFilter" onkeyup="PF('wPerimeterTable').filter()" style="width:150px" />
							<p:defaultCommand target="globalFilter" />
							<p:watermark for="globalFilter" value="#{labels.search}" />
							<p:commandButton icon="ui-icon-plusthick" value="#{comlbls.add}" actionListener="#{cc.attrs.handler.onAddPerimeter}" process="@this" update="@(.psCPerimeterEdit)" oncomplete="PF('wCPerimeterEdit').show()" styleClass="mls" />
						</h:panelGroup>
					</h:panelGrid>
				</f:facet>
				<p:column headerText="#{labels.code}" sortBy="#{perimeter.code}" filterBy="#{perimeter.code}" style="width:20%;"><h:outputText value="#{perimeter.code}" /></p:column>
				<p:column headerText="#{labels.name}" sortBy="#{perimeter.name}" filterBy="#{perimeter.name}"><h:outputText value="#{perimeter.name}" /></p:column>
				<p:column style="width:200px;" styleClass="right">
					<p:commandButton icon="ui-icon-copy"
										title="#{labels.duplicate}"
										process="@this"
										update="cPerimeterTable"
										actionListener="#{cc.attrs.handler.onDuplicatePerimeter(perimeter)}"/>
					<p:commandButton icon="ui-icon-pencil"
                                     title="#{labels.security}"
                                     immediate="true"
                                     process="@this"
                                     action="#{cc.attrs.handler.setPerimeter(perimeter)}"
                                     update="@widgetVar(wCPerimeterEdit)"
                                     oncomplete="PF('wCPerimeterEdit').show()"/>
					<p:commandButton icon="ui-icon-trash" title="#{comlbls.delete}" 
						actionListener="#{cc.attrs.handler.onRemovePerimeter(perimeter)}" process="@this" update="-messages cPerimeterTable"
						onclick="if ( !confirm('#{comlbls.confirm_delete}') ) { return false; }" styleClass="mls"/>
				</p:column>
			</p:dataTable>
			<p:dialog header="#{labels.perimeter}" modal="true" widgetVar="wCPerimeterEdit" width="500" dynamic="true">
				<p:outputPanel id="cPerimeterEdit" styleClass="psCPerimeterEdit" rendered="#{cc.attrs.handler.perimeter!=null}">
                    <p:spacer height="17px" width="100%"/>
                    
                 	<p:fieldset legend="#{labels.name}" toggleable="true">  
                 		<p:inputText id="cPerimeterName"
                                 value="#{cc.attrs.handler.perimeter.name}"
                                 required="true"
                                 pt:placeholder="#{labels.name}"
                                 styleClass="form-control"
                                 validatorMessage="#{labels.alphanumeric_underscore}">
                        	<f:validateRegex pattern="[a-zA-Z0-9_]{1,32}"/>
                    	</p:inputText>
                 	</p:fieldset>
                    
					<h:panelGroup layout="block" styleClass="right" style="margin: 10px 0;">
						<p:commandButton icon="ui-icon-disk" value="#{labels.save}" 
							actionListener="#{cc.attrs.handler.onSavePerimeter}" process="@this cPerimeterEdit" update="-messages cPerimeterEdit @(.psCPerimeterPanel)" 
							oncomplete="handleCloseDialog(xhr, status, args, PF('wCPerimeterEdit'))"/>
						<p:commandButton icon="ui-icon-cancel" value="#{comlbls.cancel}" type="button" onclick="PF('wCPerimeterEdit').hide()" styleClass="mls" />
					</h:panelGroup>
				</p:outputPanel>
			</p:dialog>
			<h:panelGroup layout="block" style="width:1px;height:1px;visibility:hidden;">
				<p:commandButton value=" " immediate="true" process="@this" update="cPerimeterTable" actionListener="#{cc.attrs.handler.onOpenPerimeterDialog}"
					styleClass="jqsOpenUserDialogPerimeters">
					<p:ajax event="dialogReturn" listener="#{cc.attrs.handler.onSavedPerimeter}" process="@this" update="-messages cPerimeterTable" />
				</p:commandButton>
			</h:panelGroup>
	</cc:implementation>
</ui:component>