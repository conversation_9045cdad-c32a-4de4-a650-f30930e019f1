<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml" 
	xmlns:f="http://xmlns.jcp.org/jsf/core" xmlns:h="http://xmlns.jcp.org/jsf/html" 
	xmlns:cc="http://xmlns.jcp.org/jsf/composite" xmlns:p="http://primefaces.org/ui"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets">

	<!-- INTERFACE -->
	<cc:interface name="instanceTitle">
		<cc:attribute name="instance" type="com.byzaneo.generix.bean.Instance" required="true" />
		<cc:attribute name="service" type="java.lang.String" required="true" />
		<cc:attribute name="serviceLink" type="java.lang.String" required="false" />
	</cc:interface>

	<!-- IMPLEMENATION -->
	<cc:implementation>
		<ui:param name="hasBreadcrumb" value="#{cc.getFacet('breadcrumb')!=null}" />
		<ul class="breadcrumb">
			<!-- Instance List -->
			<li>
				<i class="icon-instance icon-instance-#{cc.attrs.service}" title="#{labels[cc.attrs.service]}" style="color:#4c8fbd;" />
				<h:outputLink value="#{request.contextPath}/ui/admin/instances.jsf">#{labels.instances}</h:outputLink>
			</li>
			<!-- Instance --> 
			<li>
				<h:outputLink value="#{request.contextPath}/ui/admin/instance/general.jsf?iid=#{cc.attrs.instance.id}" rendered="#{cc.attrs.instance.id!=null and cc.attrs.service!='general'}">
					<h:outputText value=" #{gnxHandler.label(cc.attrs.instance.name, gnxSessionHandler.locale, gnxInstanceISHandler.getDefaultLanguage())}" title="#{cc.attrs.instance.code}" />
				</h:outputLink>
				<h:outputText value=" #{gnxHandler.label(gnxInstanceISHandler.instance.name, gnxSessionHandler.locale, gnxInstanceISHandler.getDefaultLanguage())}" title="#{cc.attrs.instance.code}" rendered="#{cc.attrs.instance.id!=null and cc.attrs.service=='general'}" />
				<h:outputText value=" #{labels.instance}" rendered="#{cc.attrs.instance.id==null}" />
			</li>
			<!-- Service -->
			<li style="display:#{cc.attrs.instance.id==null or cc.attrs.service=='general' ? 'none' : 'inherit'};">
				<h:outputLink value="#{request.contextPath}/ui/admin/instance/#{cc.attrs.serviceLink!=null ? cc.attrs.serviceLink : cc.attrs.service}.jsf?iid=#{cc.attrs.instance.id}" rendered="#{hasBreadcrumb}">
					<h:outputText value=" #{labels[cc.attrs.service]}" />
				</h:outputLink>
				<h:outputText value="#{labels['imenu_'.concat(cc.attrs.service)]}" rendered="#{not hasBreadcrumb}" />
			</li>
			<!-- ... -->
			<cc:renderFacet name="breadcrumb" />
		</ul>
 	</cc:implementation>
</ui:component>
