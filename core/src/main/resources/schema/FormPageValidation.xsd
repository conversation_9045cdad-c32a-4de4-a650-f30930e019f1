<!-- édité avec XMLSpy v2020 sp1 (x64) (http://www.altova.com) par GENERIX SA (GENERIX GROUP FRANCE) -->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:element name="Page">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="Group" minOccurs="0" maxOccurs="unbounded">
					<xs:complexType>
						<xs:choice minOccurs="0" maxOccurs="unbounded">
							<xs:element name="Textfield">
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="name" type="xs:string" use="optional"/>
											<xs:attribute name="label" type="xs:string" use="optional"/>
											<xs:attribute name="sizemax" type="xs:byte" use="optional"/>
											<xs:attribute name="mandatory" type="xs:boolean" use="optional"/>
											<xs:attribute name="timeline" type="xs:boolean"/>
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="Textarea">
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="name" type="xs:string" use="optional"/>
											<xs:attribute name="label" type="xs:string" use="optional"/>
											<xs:attribute name="sizemax" type="xs:byte" use="optional"/>
											<xs:attribute name="mandatory" type="xs:boolean" use="optional"/>
											<xs:attribute name="height"/>
											<xs:attribute name="width"/>
											<xs:attribute name="timeline" type="xs:boolean"/>
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="CheckBox">
								<xs:complexType>
									<xs:simpleContent>
										<xs:extension base="xs:string">
											<xs:attribute name="name" type="xs:string" use="optional"/>
											<xs:attribute name="label" type="xs:string" use="optional"/>
											<xs:attribute name="mandatory" type="xs:boolean" use="optional"/>
											<xs:attribute name="timeline" type="xs:boolean"/>
										</xs:extension>
									</xs:simpleContent>
								</xs:complexType>
							</xs:element>
							<xs:element name="ComboBox">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="Item" minOccurs="0" maxOccurs="unbounded">
											<xs:complexType>
												<xs:simpleContent>
													<xs:extension base="xs:string">
														<xs:attribute name="value" type="xs:string" use="optional"/>
														<xs:attribute name="label" type="xs:string" use="optional"/>
														<xs:attribute name="bydefault" type="xs:boolean" use="optional"/>
													</xs:extension>
												</xs:simpleContent>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
									<xs:attribute name="name" type="xs:string" use="optional"/>
									<xs:attribute name="label" type="xs:string" use="optional"/>
									<xs:attribute name="mandatory" type="xs:boolean" use="optional"/>
									<xs:attribute name="timeline" type="xs:boolean"/>
									<xs:attribute name="feedwith" type="xs:string" use="optional"/>
									<xs:attribute name="strict" type="xs:boolean" use="optional"/>
								</xs:complexType>
							</xs:element>
						</xs:choice>
						<xs:attribute name="label" type="xs:string" use="optional"/>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="label" type="xs:string"/>
		</xs:complexType>
	</xs:element>
</xs:schema>
