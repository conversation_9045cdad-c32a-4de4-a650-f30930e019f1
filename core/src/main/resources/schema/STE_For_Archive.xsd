<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
	<!-- ELEMENTS ROOT -->
	<xs:element name="STE">
		<xs:annotation>
			<xs:documentation>
	        	Stratégie technique d'empaquetage
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element name="Code" type="xs:string"/>
				<xs:element name="Version" type="xs:string"/>
				<xs:element name="Hash" type="xs:string"/>
				<xs:element name="Document" type="DocumentType"/>
			</xs:sequence>
			<xs:attribute name="strict"/>
			<xs:attribute name="timeline"/>
			<xs:attribute name="errors"/>
		</xs:complexType>
	</xs:element>
	<!-- COMPLEXE TYPES -->
	<xs:complexType name="DocumentType">
		<xs:annotation>
			<xs:documentation>
	        	Description du document à archiver
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Kind" type="xs:string"/>
			<xs:element name="Norm" type="xs:string" minOccurs="0"/>
			<xs:element name="File" type="FileType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="FileType">
		<xs:annotation>
			<xs:documentation>
	        	Description du type de fichier
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="FileType" type="FileTypeType"/>
			<xs:element name="FileSubType" type="xs:string" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="Encoding" type="xs:NMTOKEN" use="optional"/>
		<xs:attribute ref="ParentFile" use="optional"/>
		<xs:attribute ref="Required" use="required"/>
	</xs:complexType>
	<!-- SIMPLE TYPES -->
	<xs:simpleType name="FileTypeType">
		<xs:restriction base="xs:string">
			<xs:minLength value="2"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:attribute name="ParentFile" type="xs:boolean">
		<xs:annotation>
			<xs:documentation>
				Fichier parent
			</xs:documentation>
		</xs:annotation>
	</xs:attribute>
	<xs:attribute name="Required" type="xs:boolean">
		<xs:annotation>
			<xs:documentation>
				Fichier requis
			</xs:documentation>
		</xs:annotation>
	</xs:attribute>
</xs:schema>
