package com.byzaneo.generix.api;

import org.springframework.stereotype.Component;

@Component("gnxPartnerUser")
public class PartnerUserDescriptorWrapper extends UserDescriptorWrapper {// Wrapper for user descriptor where there is only partners and
                                                                         // users

  /** @see com.byzaneo.security.io.UserDescriptorWrapper#getDescriptorResource() */
  @Override
  protected String getDescriptorResource() {
    return "com/byzaneo/generix/bean/partnerUserDescriptor.xml";
  }

}
