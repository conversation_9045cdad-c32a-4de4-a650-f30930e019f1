package com.byzaneo.generix.service;

import static com.byzaneo.commons.bean.FileType.HTML;
import static com.byzaneo.commons.bean.FileType.RPTDESIGN;
import static com.byzaneo.commons.bean.FileType.XML;
import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.DATABASE_DRIVER;
import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.DATABASE_PASSWORD;
import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.DATABASE_URL;
import static com.byzaneo.commons.service.ConfigurationService.ConfigurationKey.DATABASE_USERNAME;
import static com.byzaneo.commons.util.Assert.notNull;
import static com.byzaneo.commons.util.ClassPathScanningHelper.findResources;
import static com.byzaneo.commons.util.FileHelper.deleteFile;
import static com.byzaneo.generix.api.Variable.toVariables;
import static com.byzaneo.generix.service.TransformService.Transform.RTE;
import static com.byzaneo.generix.service.TransformService.Transform.SCHEMATRON;
import static com.byzaneo.generix.service.TransformService.Transform.ThymeLeaf;
import static com.byzaneo.generix.service.TransformService.Transform.fromURI;
import static com.byzaneo.generix.transform.translator.TranslatorEngine.ConfigKey.TRANSLATOR_ENGINE_DIR;
import static com.byzaneo.generix.transform.translator.TranslatorEngine.ParamKey.PARAMETER;
import static com.byzaneo.generix.transform.translator.TranslatorEngine.ParamKey.SCENARIO;
import static com.byzaneo.generix.transform.translator.TranslatorEngine.ParamKey.SCENARIO_ZIP;
import static com.byzaneo.generix.util.DocumentCompoundTypeHelper.getKindOrType;
import static com.byzaneo.generix.util.TransformHelper.createBirtConfig;
import static com.byzaneo.generix.util.TransformHelper.newFileWithSuffix;
import static com.byzaneo.generix.util.TransformHelper.setExecutable;
import static com.byzaneo.transform.birt.engine.BIRTRuntimeEngine.BIRT_IMAGE_DIRECTORY;
import static com.byzaneo.xtrade.ipm.util.ProcessTemplateHelper.importTemplate;
import static com.byzaneo.xtrade.util.DocumentHelper.findFileByType;
import static java.lang.Boolean.FALSE;
import static java.lang.System.currentTimeMillis;
import static java.util.Arrays.stream;
import static java.util.Collections.emptyList;
import static java.util.Collections.sort;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;
import static org.apache.commons.collections4.MapUtils.isNotEmpty;
import static org.apache.commons.io.FileUtils.getTempDirectory;
import static org.apache.commons.io.FileUtils.getTempDirectoryPath;
import static org.apache.commons.io.FileUtils.getUserDirectory;
import static org.apache.commons.io.FileUtils.getUserDirectoryPath;
import static org.apache.commons.io.FileUtils.listFiles;
import static org.apache.commons.io.FilenameUtils.getBaseName;
import static org.apache.commons.io.FilenameUtils.removeExtension;
import static org.apache.commons.io.IOCase.INSENSITIVE;
import static org.apache.commons.lang3.ArrayUtils.add;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.eclipse.birt.report.engine.api.IEngineConfig.RESOURCE_PATH;
import static org.slf4j.LoggerFactory.getLogger;

import java.io.*;
import java.util.*;
import java.util.function.Supplier;

import javax.annotation.PostConstruct;

import com.byzaneo.commons.config.HazelcastConfiguration;
import com.byzaneo.commons.ui.util.JSFHelper;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.filefilter.*;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.commons.service.*;
import com.byzaneo.commons.service.ConfigurationService.ConfigurationKey;
import com.byzaneo.generix.api.AbstractConfigurableService;
import com.byzaneo.generix.bean.*;
import com.byzaneo.generix.transform.formpage.FormPageEngine;
import com.byzaneo.generix.util.TransformHelper;
import com.byzaneo.security.bean.*;
import com.byzaneo.transform.TransformException;
import com.byzaneo.transform.birt.engine.*;
import com.byzaneo.transform.birt.engine.BIRTEngine.InvocationPlace;
import com.byzaneo.transform.command.engine.CommandEngine.Param;
import com.byzaneo.transform.engine.*;
import com.byzaneo.transform.thymeleaf.engine.ThymeLeafEngine.*;
import com.byzaneo.xtrade.DuplicateException;
import com.byzaneo.xtrade.api.DocumentFile;
import com.byzaneo.xtrade.api.DocumentType;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.index.MongoIndexOperations;
import com.google.common.collect.ImmutableMap;

/**
 * Messaging Service implementation.
 *
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date Feb 20, 2013
 * @version 2.1 GNX-276
 */
@Service(TransformService.SERVICE_NAME)
public class TransformServiceImpl extends AbstractConfigurableService<TransformConfiguration> implements TransformService {

  private static final Logger LOG = getLogger(TransformServiceImpl.class);
  public static final String IS_ANGULAR = "isAngular";

  // - SERVICES -
  // Templates
  @Autowired
  @Qualifier(ConfigurationService.SERVICE_NAME)
  private ConfigurationService configService;

  // Engine's parameters
  @Autowired(required = false)
  @Qualifier("xtdIndexOperations")
  private MongoIndexOperations mongoIndexOperations;

  // - PROPERTIES -
  /** Templates' folder */
  private File templatesFolder;

  /*
   * -- LIFE CYCLE --
   */

  /** @see com.byzaneo.commons.service.Initializable#init() */
  @Override
  @PostConstruct
  public void init() {
    final long start = currentTimeMillis();
    LOG.info("STARTING TRANSFORM SERVICE...");

    // - template's folder -
    this.templatesFolder = new File(this.configService.getFile(
        ConfigurationKey.DATA_DIR, getTempDirectory()), "templates");
    this.templatesFolder.mkdirs();
    // TODO add templates from classpath or a webapp directory (provided) to
    // the template folder
    LOG.info("\t- Templates directory: {}", this.templatesFolder);

    // [user.home]/.counters directory */
    LOG.debug("\t- RTE counter dir created: {}",
        new File(getUserDirectory(), ".counters").mkdirs());

    if (HazelcastConfiguration.getDoNotDeployFiles()) {
      LOG.info("\t- Global templates already deployed, there is no update needed");
    }
    else {
      // - global templates -
      for (Transform type : Transform.values()) {
        final Resource[] resources = findResources("templates/" + type.toString()
            .toLowerCase(), ".*\\.zip");
        LOG.info("\t- Installing global {} templates: {}", type, resources.length);
        for (Resource resource : resources) {
          try {
            List<Template> templates = this.addTemplates(GLOBAL_TEMPLATES_OWNER, type,
                resource.getFilename(), resource.getInputStream());
            if (LOG.isDebugEnabled() && !isEmpty(templates)) {
              if (templates.size() == 1) {
                LOG.debug("\t\t. {} -> {}", resource, templates.get(0));
              }
              else {
                LOG.debug("\t\t. {}:", resource);
                templates.forEach(template -> LOG.debug("\t\t\t~ {}", template));
              }
            }
          }
          catch (Exception e) {
            LOG.error("\t\t. Error installing global {} template: {} ({})",
                type, resource, e.getMessage());
          }
        }
      }
    }

    LOG.info("TRANSFORM SERVICE STARTED in {}ms.",
        System.currentTimeMillis() - start);
  }

  /*
   * -- TRANSFORM --
   */

  /** @see com.byzaneo.generix.service.TransformService#transform(String, Locale, Map, FileType, OutputStream) */
  @Override
  public boolean transform(String templateUri, Locale language, Map<String, Object> parameters, FileType outputType, OutputStream output) {
    return this.transform(null, getTemplate(templateUri), null, outputType, output, language, null, null, null, parameters);
  }

  /** @see com.byzaneo.generix.service.TransformService#transform(Template, FileType, OutputStream, Locale, Instance, Group, User, Map) */
  @Override
  public boolean transform(Template template, FileType outputType, OutputStream output,
      Locale language, Instance instance, Group group, User user,
      Map<String, Object> parameters) {
    return this.transform(null, template, null, outputType, output, language, instance, group, user, parameters);
  }

  /**
   * @see com.byzaneo.generix.service.TransformService#transform(com.byzaneo.generix.bean.Template,
   *      com.byzaneo.transform.engine.InputSource, com.byzaneo.commons.bean.FileType, java.io.OutputStream, java.util.Locale,
   *      com.byzaneo.generix.bean.Instance, com.byzaneo.security.bean.Group, com.byzaneo.security.bean.User, java.util.Map)
   */
  @Override
  public boolean transform(Template template,
      InputSource input, FileType outputType, OutputStream output,
      Locale language, Instance instance, Group group, User user,
      Map<String, Object> parameters) {
    return this.transform(null, template, input, outputType, output, language, instance, group, user, parameters);
  }

  /**
   * @see com.byzaneo.generix.service.TransformService#transform(com.byzaneo.transform.engine.Engine, com.byzaneo.generix.bean.Template,
   *      InputSource, com.byzaneo.commons.bean.FileType, java.io.OutputStream, java.util.Locale, com.byzaneo.generix.bean.Instance,
   *      com.byzaneo.security.bean.Group, com.byzaneo.security.bean.User, java.util.Map)
   */
  @SuppressWarnings("squid:S1226")
  @Override
  public boolean transform(TransformEngineBuilder engine,
      Template template, InputSource input, FileType outputType,
      OutputStream output, Locale language, Instance instance,
      Group group, User user, Map<String, Object> parameters) {
    // - template -
    if (template == null || template.getFile() == null || !template.getFile()
        .isFile()) {
      return false;
    }
    Transform type = template.getType();

    // - engine -
    if (engine == null) {
      engine = this.getEngine(type, null);
    }

    if (engine == null) {
      LOG.warn("Transform engine not found for type: {}", type);
      return false;
    }

    // builds engine's parameters
    Map<String, Object> params = this.buildEngineParameters(template, language, instance, group, user, parameters);
    engine.addParameters(params);

    // Transforms...
    try (final OutputStream out = new BufferedOutputStream(output)) {
      final OutputResult result = new OutputResult(
          out, outputType == null ? type.getOutput() : outputType);

      switch (type) {
      case ThymeLeaf:
        engine.transform(new InputSource(HTML, template.getUri()), result);
        break;
      case BIRT:
        engine.config(BIRTEngine.CALLED_FROM, InvocationPlace.PORTAL);
        engine.withStylesheet(template.getFile(), RPTDESIGN);
        engine.transform(null, result);
        break;
      case RTE:
        engine.transform(null, result);
        break;
      case RTE_SOURCE:
        engine.withTemplate(template.getFile(), FileType.RTE);
        engine.transform(input, result);
        break;
      case Translator:
        if (input == null) {
          LOG.debug("Translator engine needs input source to process transformation");
          return false;
        }
        engine.transform(input, result);
        break;
      case SmartPDF:
        if (input == null) {
          LOG.debug("SmartPDF engine needs PDF input source to process transformation");
          return false;
        }
        engine.withTemplate(template.getFile(), FileType.XML);
        engine.transform(input, result);
        break;
      case FormPage:
        if (template.validate(null))
          if(parameters != null && parameters.containsKey(IS_ANGULAR) && parameters.get(IS_ANGULAR).equals(true)){
            engine.transform(new InputSource(FileType.XML, template.getUri()), result, false, parameters, true);
          }else {
            engine.transform(new InputSource(FileType.XML, template.getUri()), result);
          }
        break;
      default:
        throw new AssertionError(type);
      }
      return true;
    }
    catch (IOException | TransformException e) {
      throw new ServiceException(e.getMessage(), e);
    }
  }

  /*
   * -- BIRT --
   */

  /**
   * @see com.byzaneo.generix.service.TransformService#executeBirt(com.byzaneo.xtrade.bean.Document, com.byzaneo.generix.bean.Instance,
   *      java.util.Locale, com.byzaneo.commons.bean.FileType)
   */
  @Override
  public File executeBirt(final Document document, final Instance instance, final Locale language, final FileType outputType,
      DocumentType documentType) {
    if (document == null || instance == null)
      return null;

    // transform...
    return this.executeBirt(
        document,
        this.getTemplate(instance.getGroup(), document, outputType),
        instance,
        language,
        outputType, documentType);
  }

  /**
   * @see com.byzaneo.generix.service.TransformService#executeBirt(com.byzaneo.xtrade.bean.Document, com.byzaneo.generix.bean.Template,
   *      com.byzaneo.generix.bean.Instance, java.util.Locale, com.byzaneo.commons.bean.FileType)
   */
  @Override
  public File executeBirt(final Document document, final Template template,
      final Instance instance, final Locale language,
      final FileType outputType, final DocumentType documentType) {
    if (document == null || instance == null)
      return null;

    // source file resolution
    // assumes the source is the first document's XML file
    final DocumentFile dofSource = findFileByType(document, XML);

    // transform...
    return this.executeBirt(
        document,
        template,
        createBirtConfig(
            instance,
            dofSource != null ? dofSource.getFile() : null),
        language,
        getTempDirectory(),
        outputType,
        false, documentType);
  }

  /**
   * @see com.byzaneo.generix.service.TransformService#executeBirt(com.byzaneo.xtrade.bean.Document, com.byzaneo.generix.bean.Template,
   *      com.byzaneo.generix.bean.Instance, java.io.File, java.util.Locale, java.io.File, com.byzaneo.commons.bean.FileType, boolean)
   */
  @Override
  public File executeBirt(final Document document, final Template template,
      final Instance instance, final File source,
      final Locale language,
      final File outputDirectory, final FileType outputType,
      final boolean force, final DocumentType documentType) {
    return this.executeBirt(document, template,
        createBirtConfig(instance, source),
        language,
        outputDirectory, outputType, force, documentType);
  }

  /**
   * @see com.byzaneo.generix.service.TransformService#executeBirt(com.byzaneo.xtrade.bean.Document, com.byzaneo.generix.bean.Template,
   *      java.util.Map, java.util.Locale, java.io.File, com.byzaneo.commons.bean.FileType, boolean)
   */
  @Override
  public File executeBirt(final Document document, final Template template,
      final Map<String, Object> config,
      final Locale language,
      final File outputDirectory, final FileType outputType,
      final boolean force, final DocumentType documentType) {
    // sanity
    if (document == null ||
        template == null ||
        outputType == null)
      return null;

    // output file
    final File file = newFileWithSuffix(outputDirectory, document, outputType, language, documentType, template.getName());
    if (file == null)
      return null;

    // existing output file (cache)
    if (file.isFile() && !force) {
      return file;
    }
    else {
      deleteFile(file);
    }

    // transform...
    try (final OutputStream output = new FileOutputStream(file)) {
      this.transform(
          template,
          outputType,
          output,
          language,
          null, null, null,
          config);
    }
    catch (ServiceException se) {
      throw se;
    }
    catch (Exception e) {
      throw new ServiceException(e, "Error transforming to %s the document: %s (%s)", outputType, document, template);
    }
    return file;
  }

  /**
   * @see com.byzaneo.generix.service.TransformService#executeBirt(com.byzaneo.xtrade.bean.Document, com.byzaneo.generix.bean.Template,
   *      java.util.Map, java.util.Locale, java.io.File, com.byzaneo.commons.bean.FileType, boolean)
   */
  @Override
  public File executeBirt(final Instance instance, User user, final Template template,
      final Map<String, Object> config,
      final Locale language,
      final File outputDirectory, final FileType outputType,
      final String reportPreffixName,
      final boolean force) {
    // sanity
    if (template == null ||
        outputType == null)
      return null;

    // output file
    final File file = TransformHelper.newFileWithSuffix(outputDirectory, instance.getCode(),
        outputType.name()
            .toLowerCase(),
        outputType.getExtension(), language,
        template.getName(), reportPreffixName);
    if (file == null)
      return null;

    // images directory
    final Map<String, Object> params = ImmutableMap.<String, Object> builder()
        .putAll(config)
        .put(BIRT_IMAGE_DIRECTORY, file.getParent())
        .build();

    // existing output file (cache)
    if (file.isFile() && !force) {
      return file;
    }
    else {
      deleteFile(file);
    }

    // transform...
    try (final OutputStream output = new FileOutputStream(file)) {
      this.transform(
          template,
          outputType,
          output,
          language,
          instance, instance.getGroup(), user,
          params);
    }
    catch (ServiceException se) {
      throw se;
    }
    catch (Exception e) {
      throw new ServiceException(e, "Error transforming to %s the document: %s (%s)", outputType, "doc", template);
    }
    return file;
  }

  /*
   * -- RTE --
   */

  /**
   * @see com.byzaneo.generix.service.TransformService#executeRte(com.byzaneo.transform.command.engine.CommandEngine, java.io.File,
   *      java.lang.String[])
   */
  @Override
  public RteResult executeRte(Map<String, ?> config, File rte, String... arguments) {
    notNull(rte, "labels.error_required_rte", "RTE file is required");

    // sets executable
    setExecutable(rte, true);

    // Env variable
    Map<String, String> envMap = new HashMap<>();
    envMap.put("LD_LIBRARY_PATH", rte.getParentFile()
        .getAbsolutePath());
    envMap.put("HOME", getUserDirectoryPath());

    final RteResult result = new RteResult();

    TransformEngineBuilder.engine(RTE.getEngine())
        .config(config)
        // parameters
        .addParameter(Param.Directory, rte.getParentFile())
        .addParameter(Param.Environment, envMap)
        .addParameter(Param.Command, add(arguments, 0, rte.getAbsolutePath()))
        // transforms
        .transform(null, result);

    return result;
  }

  /*
   * -- TEMPLATES --
   */

  /** @see com.byzaneo.generix.service.TransformService#getTemplate(java.lang.String) */
  @Override
  public Template getTemplate(String uri) {
    final File templateFile;
    final Transform type = fromURI(uri);
    return type == null || !(templateFile = new File(this.templatesFolder, uri.concat(type.getExtension()))).exists() ? null
        : toTemplate(templateFile);
  }

  /**
   * @see com.byzaneo.generix.service.TransformService#getTemplate(com.byzaneo.security.bean.Group, java.lang.String,
   *      com.byzaneo.commons.bean.FileType)
   */
  @Override
  public Template getTemplate(Group group, String documentType, FileType output) {
    return this.getTemplate(group,
        null,
        documentType,
        null,
        output);
  }

  /**
   * @see com.byzaneo.generix.service.TransformService#getTemplate(com.byzaneo.security.bean.Group, com.byzaneo.xtrade.bean.Document,
   *      com.byzaneo.commons.bean.FileType)
   */
  @Override
  public Template getTemplate(Group group, Document document, FileType output) {
    return document == null
        ? null
        : this.getTemplate(
            group,
            document.getIndexCollection(),
            getKindOrType(document),
            document.getSubtype(),
            output);
  }

  /**
   * @see com.byzaneo.generix.service.TransformService#getTemplate(com.byzaneo.security.bean.Group, java.lang.String, java.lang.String,
   *      java.lang.String, com.byzaneo.commons.bean.FileType)
   */
  @Override
  public Template getTemplate(Group group, String collection, String type, String subType, FileType output) {
    // sanity
    if (group == null ||
        (isBlank(type) && isBlank(collection)) ||
        output == null) {
      return null;
    }

    this.getConfiguration(group)
        .getTransformations()
        .stream()
        .collect(groupingBy(t -> ofNullable(t.getCollection())));

    Transformation tfound = findTransformation(this.getConfiguration(group)
        .getTransformations(), collection, type, subType, output);

    // generic transformation template or not found
    return tfound == null
        ? null
        : this.getTemplate(tfound.getTemplateUri());
  }

  @Override
  public Transformation findTransformation(List<Transformation> transformations, String collection, String type, String subType,
      FileType output) { // looks for the better configuration
    Transformation tfound = null;
    int tmfound = 0;
    for (Transformation t : transformations) {
      int match = t.matching(new Transformation(collection, type, subType, output, ""));
      // no match
      if (match == 0) {
        continue;
      }
      // perfect match
      if (match == 100) {
        tfound = t;
        break;
      }
      // better partial match
      if (tmfound < match) {
        tfound = t;
        tmfound = match;
      }
    }
    return tfound;
  }

  /** @see com.byzaneo.generix.service.TransformService#getTemplate(String, Transform, String, Locale) */
  @Override
  public Template getTemplate(String owner, Transform type, String name, Locale language) {
    final List<Template> templates = getTemplates(
        getTemplateFolder(owner, type, false),
        name,
        null, language);
    if (isEmpty(templates)) {
      return null;
    }
    if (templates.size() > 1) {
      LOG.warn(
          "More than one ({}) template found for owner={} and name={}: {}",
          templates.size(), owner, name, templates);
    }
    return templates.get(0);
  }

  /**
   * @see com.byzaneo.generix.service.TransformService#getTemplates(String, com.byzaneo.generix.service.TransformService.Transform,
   *      FileType)
   */
  @Override
  public List<Template> getTemplates(String owner, Transform type, FileType outputFileType) {
    return getTemplates(getTemplateFolder(owner, type, false), null, outputFileType, null);
  }

  /**
   * @see com.byzaneo.generix.service.TransformService#addTemplates(String, com.byzaneo.generix.service.TransformService.Transform, String,
   *      InputStream)
   */
  @Override
  public List<Template> addTemplates(String owner, Transform type, String name, InputStream content) {
    if (content == null) {
      return Collections.emptyList();
    }
    notNull(owner, "msglbls.exception_template_owner", "Template's owner is required");
    notNull(type, "msglbls.exception_template_type", "Template's type is required");

    File templateFolder = this.getTemplateFolder(owner, type, true);
    String generateDdlMode = this.configService.getGenerateDdlMode();
    File folder = importTemplate(type.getFileType(), name, content, templateFolder, GLOBAL_TEMPLATES_OWNER.equals(owner), false,
        "create".equals(generateDdlMode), false);

    // gets brand new templates
    final List<Template> templates = getTemplates(folder);
    if (isEmpty(templates)) {
      return emptyList();
    }

    return templates;
  }

  /** @see com.byzaneo.generix.service.TransformService#removeTemplate(com.byzaneo.generix.bean.Template) */
  @Override
  public boolean removeTemplate(Template template) {
    return template != null &&
        template.getFile() != null &&
        deleteFile(template.getFile()
            .getParentFile());
  }

  /**
   * @see com.byzaneo.generix.service.TransformService#removeTemplates(java.lang.String,
   *      com.byzaneo.generix.service.TransformService.Transform)
   */
  @Override
  public boolean removeTemplates(String owner, Transform type) {
    return deleteFile(getTemplateFolder(owner, type, false));
  }

  /*
   * -- CONFIGURABLE --
   */

  /**
   * @see com.byzaneo.generix.api.ConfigurableService#saveConfiguration(com.byzaneo.security.bean.Group,
   *      com.byzaneo.generix.api.Configuration)
   */
  @Override
  public void saveConfiguration(Group producer, TransformConfiguration configuration) throws DuplicateException {
    // sanity
    if (configuration == null || new TransformConfiguration().equals(configuration)) {
      return;
    }

    super.saveConfiguration(producer, configuration);
  }

  @Override
  protected String getVariableName() {
    return TransformConfiguration.class.getSimpleName();
  }

  @Override
  protected String getServiceName() {
    return TransformService.SERVICE_NAME;
  }

  @Override
  protected Supplier<TransformConfiguration> getConfigurationSupplier() {
    return TransformConfiguration::new;
  }

  /*
   * -- PRIVATES --
   */

  /**
   * @param type of the transformation engine to get
   * @param configuration to apply to the engine (optional)
   * @return the created or cached engine for the requested transform type
   */
  private TransformEngineBuilder getEngine(Transform type, Map<String, Object> configuration) {
    if (type == null || type.getEngine() == null) {
      return null;
    }

    // default configuration
    final Map<String, Object> config = new HashMap<>();

    // specific configuration
    if (isNotEmpty(configuration)) {
      config.putAll(configuration);
    }
    switch (type) {
    case ThymeLeaf:
      config.put(Config.templateDirectory.toString(), this.templatesFolder.getParent());
      config.put(Config.classLoaderResolver.toString(), FALSE);
      config.put(Config.cacheable.toString(), FALSE);
      config.put(Config.prefix.toString(), this.templatesFolder.getName());
      config.put(Config.suffix.toString(), ThymeLeaf.getExtension());
      break;
    case RTE:
      config.put("HOME", getUserDirectoryPath());
      config.put("TMP", getTempDirectoryPath());
      break;
    case Translator:
      config.put(TRANSLATOR_ENGINE_DIR.getKey(),
          this.configService.getFile(TRANSLATOR_ENGINE_DIR, getTempDirectory())
              .getAbsolutePath());
      break;
    case FormPage:
      config.put(Config.templateDirectory.toString(), this.templatesFolder.getParent());
      config.put(Config.classLoaderResolver.toString(), FALSE);
      config.put(Config.prefix.toString(), this.templatesFolder.getName());
      config.put(Config.suffix.toString(), com.byzaneo.generix.service.TransformService.Transform.FormPage.getExtension());
      return TransformEngineBuilder.engine(FormPageEngine.class)
          .config(config);
    default:
      break;
    }

    // returns the engine (could be cached)
    return TransformEngineBuilder.engine(type.getEngine())
        .config(config);
  }

  private Map<String, Object> buildEngineParameters(
      Template template,
      // parameters
      Locale language,
      Instance instance, Group group, User user,
      Map<String, Object> parameters) {
    Map<String, Object> params = new HashMap<>();

    // engine's specifics
    switch (template.getType()) {
    case BIRT:
      params.putAll(buildBirtParams(template, language));
      break;
    case ThymeLeaf:
      if (language != null) {
        params.put(Parameter.locale.toString(), language);
      }
      params.put("user", user);
      params.put("group", group);
      params.put("instance", instance);
      break;
    case RTE:
      // is there default parameters for RTE?
      break;
    case RTE_SOURCE:
      // is there default parameters for RTE source?
      break;
    case Translator:
      /* Properties sent to translator */
      params.put(PARAMETER.getKey(), new Properties());
      /* Standard call */
      params.put(SCENARIO.getKey(), template.getName());
      /* Call with the template file as scenario (name of the archive = name of the scenario) */
      params.put(SCENARIO_ZIP.getKey(), template.getFile());
      break;
    case SmartPDF:
      // is there default parameters for the smart pdf engine?
      break;
    case XML:
    case Text:
    case FormPage:
      if (language != null) {
        params.put(Parameter.locale.toString(), language);
      }
      params.put("user", user);
      params.put("group", group);
      params.put("instance", instance);
      break;
    default:
      // Generic template types...
    }

    Locale locale = parameters != null && parameters.containsKey(IS_ANGULAR) && parameters.get(IS_ANGULAR).equals(true) ? (Locale) parameters.get("locale"):JSFHelper.getLocale();
    // adds variables
    params.putAll(toVariables(user));
    params.putAll(toVariables(group));
    params.putAll(toVariables(instance, locale));

    // adds given parameters
    if (isNotEmpty(parameters)) {
      params.putAll(parameters);
    }

    return params;
  }

  @Override
  public Map<String, Object> buildBirtParams(Template template, Locale language) {
    Map<String, Object> params = new HashMap<>();
    if (this.mongoIndexOperations != null) {
      params.put("index_uri", this.configService.getProperties()
          .getProperty("index.mongo.uri"));
    }
    // ... for the database
    params.put(DATABASE_DRIVER.toString()
        .toLowerCase(), this.configService.getString(DATABASE_DRIVER, ""));
    params.put(DATABASE_URL.toString()
        .toLowerCase(), this.configService.getString(DATABASE_URL, ""));
    params.put(DATABASE_USERNAME.toString()
        .toLowerCase(), this.configService.getString(DATABASE_USERNAME, ""));
    params.put(DATABASE_PASSWORD.toString()
        .toLowerCase(), this.configService.getString(DATABASE_PASSWORD, ""));
    // - resources -
    params.put(RESOURCE_PATH, template.getFile()
        .getParentFile()
        .getAbsolutePath());
    // - locale -
    if (language != null) {
      params.put(BIRTRuntimeEngine.BIRT_LOCALE, language);
      params.put(BIRTViewerParameter.locale.toParam(), language);
    }
    return params;
  }

  @Override
  public File getTemplateFolder(String owner, Transform type, boolean typeRequired) {
    if (isBlank(owner) || (typeRequired && type == null)) {
      return null;
    }

    File ownerFolder = new File(templatesFolder, owner);
    if (type == null) {
      return ownerFolder;
    }

    return new File(ownerFolder, type.toString());
  }

  private List<Template> getTemplates(final File folder) {
    final List<Template> templates = this.getTemplates(folder, null, null, null);
    if (isEmpty(templates)) {
      deleteFile(folder);
      throw new ServiceException("msglbls.exception_no_template",
          "No template found in: " + folder, null, folder);
    }
    return templates;
  }

  @Override
  public List<Template> getTemplates(File folder, String name, FileType outputFileType, Locale language) {
    if (folder == null || !folder.isDirectory()) {
      return emptyList();
    }

    LOG.debug("Looking for templates in: {} (name={}, language={})", folder, name, language);

    // searching templates
    final OrFileFilter filter = new OrFileFilter();
    stream(Transform.values())
        .map(Transform::getExtension)
        .map(extension -> isNotBlank(name)
            ? new AbstractFileFilter() {
              @Override
              public boolean accept(File dir, String filename) {
                return filename.equalsIgnoreCase(name.concat(extension));
              }
            }
            : new SuffixFileFilter(extension, INSENSITIVE))
        .forEach(filter::addFileFilter);
    AbstractFileFilter directoryFilter = new AbstractFileFilter() {
      @Override
      public boolean accept(File filepath) {
        return !filepath.getPath()
            .matches(".*\\\\SCHEMATRON\\\\.*\\\\.*|.*/SCHEMATRON/.*/.*");
      }
    };
    final Collection<File> templateFiles = listFiles(folder, filter, directoryFilter);
    if (isEmpty(templateFiles)) {
      return emptyList();
    }

    // creates templates
    List<Template> templates = templateFiles.stream()
        .map(this::toTemplate)
        .filter(Objects::nonNull)
        .collect(toList());

    // filtering templates
    if (language != null || outputFileType != null) {
      // language support filter
      templates = templates.stream()
          .filter(template -> (language == null || template.supports(language)) &&
              (outputFileType == null || template.supports(outputFileType)))
          .collect(toList());
    }

    // sorts templates
    sort(templates);

    return templates;
  }

  private Template toTemplate(File file) {
    try {
      final String uri = removeExtension(FilenameUtils.normalize(file.getAbsolutePath()))
          .replace(FilenameUtils.normalize(this.templatesFolder.getAbsolutePath()), "")
          .replace('\\', '/');
      final Transform type = fromURI(uri);
      if (SCHEMATRON.equals(type) && !SCHEMATRON.supportOutput(FileType.getType(file)))
        return null;
      return type == null ? null
          : new Template(
              type,
              // - URI -
              uri,
              // - file -
              file,
              // - bundles -
              listFiles(file.getParentFile(), new AndFileFilter(
                  new PrefixFileFilter(getBaseName(file.getAbsolutePath())),
                  new SuffixFileFilter(TEMPLATE_BUNDLE_SUFFIX, INSENSITIVE)), null), // - extras -
              listFiles(file.getParentFile(), new AndFileFilter(
                  new NotFileFilter(new SuffixFileFilter(type.getExtension(), INSENSITIVE)),
                  new NotFileFilter(new SuffixFileFilter(TEMPLATE_BUNDLE_SUFFIX, INSENSITIVE))), null));
    }
    catch (RuntimeException e) {
      LOG.error("Error processing template: " + file, e);
      return null;
    }
  }

  @Override
  public String getMongoCredentials() {
    return this.configService.getProperties()
        .getProperty("index.mongo.uri");
  }

  /*
   * -- INNERS --
   */

  /**
   * RTE {@link OutputResult} specialization
   */
  public class RteResult extends OutputResult {
    public RteResult() {
      super(new ByteArrayOutputStream(), RTE.getOutput());
    }

    public String getOutput() {
      return super.getOutputStream().toString();
    }
  }

  public void setTemplatesFolder(File templatesFolder) {
    this.templatesFolder = templatesFolder;
  }
}
