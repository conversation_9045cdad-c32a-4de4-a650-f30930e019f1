package com.byzaneo.generix.service.legalReferential.dto.response;

import com.byzaneo.generix.service.legalReferential.dto.enumeration.Statut;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

public class CustAnnuaireRoutingCodeWithSirenAndSiret extends AnnuaireRoutingCodeWithSirenAndSiret {
  @Getter
  @Setter
  private Map<String,String> routingCodeState;

  public String getBootstrapStyleClass(Statut code) {
    return Statut.A.equals(code) ? "label-status_green" : "label-status_yellow";
  }
}