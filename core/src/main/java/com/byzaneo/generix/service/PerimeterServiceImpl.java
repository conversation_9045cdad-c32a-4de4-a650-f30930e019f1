package com.byzaneo.generix.service;

import static com.byzaneo.generix.util.PerimeterHelper.createJsonArrayResponse;

import java.util.*;
import java.util.stream.*;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.byzaneo.generix.bean.Scope;
import com.byzaneo.security.bean.*;

@Service(PerimeterService.SERVICE_NAME)
public class PerimeterServiceImpl implements PerimeterService {

  private static final Logger log = LoggerFactory.getLogger(PerimeterServiceImpl.class);

  @Autowired
  private transient SecurityService securityService;

  @Override
  public List<Perimeter> resolveSelectablePerimeters(List<Perimeter> perimeters, String queryFilter, boolean skipFiltering,
      List<Perimeter> selectedPerimeters) {
    List<Perimeter> selectablePerimeters = perimeters.stream()
        .filter(perimeter -> skipFiltering ? true
            : StringUtils.containsIgnoreCase(perimeter.getCode(), queryFilter) ||
                StringUtils.containsIgnoreCase(perimeter.getName(), queryFilter))
        .filter(perimeter -> skipFiltering ? true
            : perimeter != null && !selectedPerimeters.contains(perimeter))
        .collect(Collectors.toList());
    return selectablePerimeters;
  }

  @Override
  public List<Scope> resolveSelectableScopes(User user, Group primaryGroup, String query, boolean skipFiltering,
      List<Scope> selectedScopes, boolean isCompanyUser) {
    Stream<Group> distinctUserPartnersStreamFromUgaAndPerimeters = getDistinctUserPartnersStreamFromUgaAndPerimeters(user);
    if (isCompanyUser && distinctUserPartnersStreamFromUgaAndPerimeters.collect(Collectors.toList()).isEmpty())
      return selectedScopes;
    Stream<Group> partnersStream = getDistinctUserPartnersStreamFromUgaAndPerimeters(user).map(Partner.class::cast);
    List<Scope> selectableScopes = partnersStream
        .filter(group -> skipFiltering ? true
            : !group.equals(primaryGroup))
        .filter(group -> skipFiltering ? true
            : StringUtils.containsIgnoreCase(group.getFullname(), query) || StringUtils.containsIgnoreCase(group.getName(), query))
        .map(group -> new Scope(SecurityService.Resource.Partner, group))
        .filter(scope -> skipFiltering ? true : scope != null && !selectedScopes.contains(scope))
        .collect(Collectors.toList());
    return selectableScopes;
  }

  @Override
  public String getDisabledScopes(User editedUser, User sessionUser, List<Scope> selectedScopes, List<Scope> resolvedScopes) {
    if (editedUser == null)
      return createJsonArrayResponse(new ArrayList<String>());
    List<String> disabledScopes = selectedScopes.stream()
        .filter(scope -> !resolvedScopes.contains(scope))
        .map(scope -> ((Partner) scope.getResource()).getCode() + " " + scope.getName())
        .collect(Collectors.toList());
    String jsonDisabledScopes = createJsonArrayResponse(disabledScopes);
    if (log.isDebugEnabled())
      log.debug("Disabled scopes {} for user {} ", jsonDisabledScopes, editedUser);
    return jsonDisabledScopes;
  }

  @Override
  public String getDisabledPerimeters(User editedUser, User sessionUser, List<Perimeter> selectedPerimeters,
      List<Perimeter> resolvedPerimeters) {
    if (editedUser == null)
      return createJsonArrayResponse(new ArrayList<String>());
    List<String> disabledPerimeters = selectedPerimeters.stream()
        .filter(perimeter -> !resolvedPerimeters.contains(perimeter))
        .map(perimeter -> perimeter.getCode() + " " + perimeter.getName())
        .collect(Collectors.toList());
    String jsonDisabledPerimeters = createJsonArrayResponse(disabledPerimeters);
    if (log.isDebugEnabled())
      log.debug("Disabled perimeters {} for user {} ", jsonDisabledPerimeters, editedUser);
    return jsonDisabledPerimeters;
  }

  @Override
  public long getPartnersCount(User user, String companyId) {
    long nrOfPartners = getDistinctUserPartnersStreamFromUgaAndPerimeters(user)
        .count();
    if (nrOfPartners != 0)
      return nrOfPartners;
    // Visibility is on all partners if no Scope is specified for the customer user.
    return this.securityService.getGroupSizeByParentAndDescription(companyId, Partner.DESCRIPTION);
  }

  @Override
  public long getUserCount(Partner partner, String companyId, User user) {
    return partner == null ? countUsersVisibleByCompanyUser(partner, companyId, user)
        : counterUsersVisibleByPartnerUser(partner, companyId, user);
  }

  private long counterUsersVisibleByPartnerUser(Partner partner, String companyId, User user) {
    List<String> distinctPartnerIds = getDistinctUserPartnersStreamFromUgaAndPerimeters(user)
        .map(Group::getId)
        .collect(Collectors.toList());
    return securityService.getUserCount(companyId, distinctPartnerIds, true);
  }

  private long countUsersVisibleByCompanyUser(Partner partner, String companyId, User user) {
    List<String> distinctPartnerIds = getDistinctUserPartnersStreamFromUgaAndPerimeters(user)
        .map(Group::getId)
        .collect(Collectors.toList());
    long userCount = securityService.getUserCount(companyId, distinctPartnerIds, true);
    if (userCount != 0)
      // company user has scope defined, display all users for the partners defined in his scope (scope & perimeters)
      return userCount;
    // if it's company user and it has no scope, it displays all users for all the partners of his company
    return this.securityService.getUserCount(companyId, partner != null ? partner.getId() : null, true);
  }

  private Stream<Group> getDistinctUserPartnersStreamFromUgaAndPerimeters(User user) {
    Stream<Group> stream1 = user
        .getUserGroupAssociations()
        .stream()
        .map(UserGroupAssociation::getGroup)
        .filter(g -> Partner.DESCRIPTION.equals(g.getDescription()));
    Stream<Group> stream2 = getSelectedPartnersStream(user
        .getPerimeters());
    return Stream.concat(stream1, stream2)
        .distinct();
  }

  @Override
  public Stream<Group> getSelectedPartnersStream(List<Perimeter> perimeters) {
    return perimeters
        .stream()
        .map(Perimeter::getSelectedPartners)
        .map(Arrays::asList)
        .flatMap(List::stream);
  }

}
