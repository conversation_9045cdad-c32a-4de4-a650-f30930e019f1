package com.byzaneo.generix.security;

import com.byzaneo.generix.bean.Instance;
import com.byzaneo.security.bean.User;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * Authentication provider configuration. Note: it's not an interface to allow JaXB marshalling.
 *
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Feb 16, 2017
 * @since AIO-5973
 */
@XmlSeeAlso({ AccessTokenV1AuthenticationConfiguration.class })
public abstract class AuthenticationConfiguration {

  /**
   * @param instance       where the authentication occurs
   * @param authentication used by the user to authenticate
   * @return the authenticated {@link User}. The user should have at least the following properties set: login, password and its primary
   *     group (organization)
   * @throws AuthenticationException if the credentials could not be validated (generally a {@link BadCredentialsException}, an
   *                                 {@link AuthenticationServiceException} or {@link UsernameNotFoundException} or
   *                                 {@link InternalAuthenticationServiceException})
   */
  public abstract User authenticate(Instance instance, UsernamePasswordAuthenticationToken authentication);

}
