package com.byzaneo.generix.bean;

import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static com.byzaneo.security.service.SecurityService.SERVICE_NAME;

import java.security.Principal;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.Transient;

import com.byzaneo.security.bean.AccessControlEntry;
import com.byzaneo.security.bean.Company;
import com.byzaneo.security.bean.Group;
import com.byzaneo.security.service.SecurityService;
import org.springframework.security.acls.model.Acl;
import org.springframework.security.acls.model.Permission;
import org.springframework.security.acls.model.Sid;

/**
 * {@link AccessControlEntry} representing the company's partners.
 * 
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Jan 10, 2013
 * @version 1.0
 */
@Entity
@DiscriminatorValue(ACPartners.TYPE)
public class ACPartners extends AccessControlEntry {
  private static final long serialVersionUID = -1708120938626939805L;

  public static final String TYPE = "com.byzaneo.generix.Partners";

  @Transient
  private Company company;

  public ACPartners() {
    this.principalType = TYPE;
  }

  public ACPartners(Company company) {
    this();
    this.setCompany(company);
  }

  @Transient
  public Company getCompany() {
    if (this.company == null)
      this.company = getBean(SecurityService.class, SERVICE_NAME).getGroupByName(this.getPrincipalId());
    return company;
  }

  @Transient
  public boolean setCompany(Company company) {
    if (company == null)
      return false;
    this.principalId = company.getId();
    this.principalType = TYPE;
    this.company = company;
    return true;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    if (!super.equals(o)) return false;

    ACPartners that = (ACPartners) o;

    return company != null ? company.equals(that.company) : that.company == null;
  }

  @Override
  public boolean setPrincipal(Principal principal) {
    return this.setCompany((Company) Group.fromPrincipal(company));
  }

  @Override
  public int hashCode() {
    int result = super.hashCode();
    result = 31 * result + (company != null ? company.hashCode() : 0);
    return result;
  }


}
