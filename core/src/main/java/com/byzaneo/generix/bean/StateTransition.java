package com.byzaneo.generix.bean;

import static com.byzaneo.security.util.ResourceHelper.generateId;
import static java.lang.String.format;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.byzaneo.commons.bean.AbstractPersitentLongId;
import com.byzaneo.commons.bean.Persistent;
import com.byzaneo.security.bean.User;
import com.byzaneo.security.util.ResourceHelper;

/**
 * Aims to keep the history of the state transitions.
 * 
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Dec 17, 2012
 * @version 2.0 GNX-163
 */
@Entity
@Table(name = "GNX_STATE_TRANSITION")
public class StateTransition extends AbstractPersitentLongId {
  private static final long serialVersionUID = 4156393332281786662L;

  /** Resource owning the state transition (see {@link ResourceHelper}) */
  @Column(name = "TRS_RESOURCE", nullable = false, updatable = false, length = 256)
  private String resource;

  /** Source state */
  @Column(name = "TRS_SOURCE", nullable = true, updatable = false, length = 64)
  private String sourceState;

  /** Target state */
  @Column(name = "TRS_TARGET", nullable = false, updatable = false, length = 64)
  private String targetState;

  /** Manual/Auto for example */
  @Column(name = "TRS_TYPE", nullable = true, updatable = false, length = 64)
  private String type;

  /** User responsible of the transition */
  @ManyToOne
  @JoinColumn(name = "TRS_UID")
  private User user;

  /** Date of the transition */
  @Column(name = "TRS_DATE", nullable = false, updatable = false)
  private Date date;

  /** Comment */
  @Column(name = "TRS_COMMENT", nullable = true, updatable = false)
  private String comment;

  /*
   * -- CONSTRUCTORS --
   */

  public StateTransition() {
    super();
    this.date = new Date();
  }

  public StateTransition(Persistent<?> persistent, State source, State target, String type, User user, String comment) {
    this(generateId(persistent), source == null ? null : source.name(), target.name(), type, user, comment);
  }

  public StateTransition(String resource, String sourceState, String targetState, String type, User user, String comment) {
    this();
    this.resource = resource;
    this.sourceState = sourceState;
    this.targetState = targetState;
    this.type = type;
    this.user = user;
    this.comment = comment;
  }

  /*
   * -- ACCESSORS --
   */

  public String getResource() {
    return resource;
  }

  public String getSourceState() {
    return sourceState;
  }

  public String getTargetState() {
    return targetState;
  }

  public String getType() {
    return type;
  }

  public User getUser() {
    return user;
  }

  public Date getDate() {
    return date;
  }

  public String getComment() {
    return comment;
  }

  /*
   * -- OVERRIDE --
   */

  @Override
  public String toString() {
    return format("%s: %s to %s [type=%s, user=%s, date=%s, comment=%s]",
        resource, sourceState, targetState, type, user == null ? "" : user.getId(), date, comment);
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    if (!super.equals(o)) return false;

    StateTransition that = (StateTransition) o;

    if (resource != null ? !resource.equals(that.resource) : that.resource != null) return false;
    if (sourceState != null ? !sourceState.equals(that.sourceState) : that.sourceState != null) return false;
    if (targetState != null ? !targetState.equals(that.targetState) : that.targetState != null) return false;
    if (type != null ? !type.equals(that.type) : that.type != null) return false;
    return date != null ? date.equals(that.date) : that.date == null;
  }

  @Override
  public int hashCode() {
    int result = super.hashCode();
    result = 31 * result + (resource != null ? resource.hashCode() : 0);
    result = 31 * result + (sourceState != null ? sourceState.hashCode() : 0);
    result = 31 * result + (targetState != null ? targetState.hashCode() : 0);
    result = 31 * result + (type != null ? type.hashCode() : 0);
    result = 31 * result + (date != null ? date.hashCode() : 0);
    return result;
  }
}
