package com.byzaneo.generix.dao;

import static java.lang.String.format;
import static java.lang.System.currentTimeMillis;
import static java.util.Collections.emptyList;
import static java.util.Collections.emptyMap;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isEmpty;
import static org.apache.commons.lang3.time.DateUtils.MILLIS_PER_DAY;

import java.util.*;
import java.util.stream.Collectors;

import javax.persistence.TypedQuery;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.byzaneo.commons.bean.DatePeriod;
import com.byzaneo.commons.dao.hibernate.GenericJpaDAO;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.security.bean.*;
import com.byzaneo.security.util.PrincipalHelper;

/**
 * <AUTHOR> <<EMAIL>>
 * @company Byzaneo
 * @date Feb 9, 2013
 * @version 1.0
 */
@Repository(ReportingDAO.DAO_NAME)
public class ReportingDAOImpl extends GenericJpaDAO<User, String> implements ReportingDAO {
  private static final long serialVersionUID = 8594977954364159716L;

  /**
   * @see com.byzaneo.generix.dao.ReportingDAO#findLoginCountByPartners(com.byzaneo.security.bean.Company,
   *      com.byzaneo.commons.bean.DatePeriod)
   */
  @Override
  @Transactional(readOnly = true)
  public Map<Partner, Integer> findLoginCountByPartners(Company company, DatePeriod period) {
    // SQL :
    // SELECT g.GROUP_NAME, SUM(u.USER_LOGIN_COUNT) AS LOGCOUNT
    // FROM sec_user u
    // INNER JOIN SEC_USER_GROUP ug ON ug.USER_ID = u.USER_ID AND
    // ug.PRIMARY_GROUP = true
    // INNER JOIN SEC_GROUP g ON g.GROUP_ID = ug.GROUP_ID AND
    // g.GROUP_DESCRIPTION = 'PARTNER' AND g.GROUP_PARENT = 'lrd'
    // WHERE u.USER_LOGIN_COUNT > 0
    // AND u.USER_LOGIN_DATE between '2000-01-01 00:00' and '2020-01-01
    // 00:00'
    // GROUP BY g.GROUP_ID
    // ORDER BY LOGCOUNT DESC;
    DatePeriod datePeriod = period;
    if (company == null)
      return emptyMap();
    if (datePeriod == null)
      datePeriod = new DatePeriod(new Date(0l), new Date(currentTimeMillis() + MILLIS_PER_DAY));

    final List<Object[]> results = this.getEntityManager()
        .createQuery(
            "SELECT part, " +
                "(SELECT SUM(u.loginCount)  " + "FROM User u " + "	INNER JOIN u.userGroupAssociations ug " +
                "	INNER JOIN ug.id.group g " + "WHERE ug.primary = true " + "   AND g.description = 'PARTNER' AND g.parent = :company " +
                "   AND u.loginCount > 0 " +
                "	AND u.loginDate between :start and :end AND part.id = g.id GROUP BY g.id) AS logcount FROM Partner part ORDER BY logcount DESC",
            Object[].class)
        .setParameter("company", company)
        .setParameter("start", datePeriod.getStartDate())
        .setParameter("end", datePeriod.getEndDate())
        .getResultList();

    final Map<Partner, Integer> r = new LinkedHashMap<>();
    for (Object[] result : results) {
      if (result[1] != null)
        r.put((Partner) result[0], ((Number) result[1]).intValue());
    }
    return r;
  }

  /**
   * @see com.byzaneo.generix.dao.ReportingDAO#findLoginCountByPartner(com.byzaneo.security.bean.Partner,
   *      com.byzaneo.commons.bean.DatePeriod)
   */
  @Override
  @Transactional(readOnly = true)
  public Integer findLoginCountByPartner(Partner partner, DatePeriod period) {
    DatePeriod datePeriod = period;
    if (partner == null)
      return 0;
    if (period == null)
      datePeriod = new DatePeriod(new Date(0l), new Date(currentTimeMillis() + MILLIS_PER_DAY));

    final Number result = this.getEntityManager()
        .createQuery(
            "SELECT SUM(u.loginCount) " + "FROM User u " + "	INNER JOIN u.userGroupAssociations ug " + "WHERE ug.id.group = :partner " +
                "	AND u.loginDate between :start and :end ",
            Number.class)
        .setParameter("partner", partner)
        .setParameter("start", datePeriod.getStartDate())
        .setParameter("end", datePeriod.getEndDate())
        .getSingleResult();

    return result == null ? 0 : result.intValue();
  }

  /**
   * @see com.byzaneo.generix.dao.ReportingDAO#suggest(com.byzaneo.security.bean.User, com.byzaneo.generix.bean.Instance, boolean,
   *      java.lang.String)
   */
  @Override
  @Transactional(readOnly = true)
  public List<User> suggest(User user, Instance instance, boolean partnerUsersOnly, String query) {
    return suggestQuery(user, instance, partnerUsersOnly, query,
        "(ug.primary = true AND g.description = 'PARTNER' AND g.parent = :company)");
  }

  @Override
  @Transactional(readOnly = true)
  public List<User> suggestWithBackUser(User user, Instance instance, boolean partnerUsersOnly, String query) {
    return suggestQuery(user, instance, partnerUsersOnly, query,
        "(ug.id.group.id = 'ADMIN') OR (ug.primary = true AND g.description = 'PARTNER' AND g.parent = :company)");
  }

  private List<User> suggestQuery(User user, Instance instance, boolean partnerUsersOnly, String query, String userGroupClause) {
    String clause = userGroupClause;
    boolean isPartnerUser = PrincipalHelper.isPartnerUser(user);

    if (user == null || instance == null || isBlank(query))
      return emptyList();

    if (isPartnerUser) {
      /* if partner user add partner constraint */
      clause = format("((%s) AND g = :partner)", clause);
    }

    if (!partnerUsersOnly)
      clause = format("((%s) OR (ug.id.group = :company))", clause);

    TypedQuery<User> typedQuery = this.getEntityManager()
        .createQuery("SELECT u FROM User u INNER JOIN u.userGroupAssociations ug " + "   INNER JOIN ug.id.group g WHERE " + clause +
            "   AND u.id != :currenUserId " +
            "   AND (LOWER(u.login) LIKE :squery OR LOWER(u.firstname) LIKE :squery OR " +
            "   LOWER(u.lastname) LIKE :squery OR LOWER(u.email) LIKE :cquery OR" +
            "   LOWER(g.name) LIKE :cquery OR LOWER(g.fullname) LIKE :squery) " + "   ORDER BY u.fullname ", User.class)
        .setParameter("company", instance.getGroup())
        .setParameter("currenUserId", user.getId())
        .setParameter("squery", query.toLowerCase() + "%")
        .setParameter("cquery", "%" + query.toLowerCase() + "%");

    if (isPartnerUser) {
      typedQuery.setParameter("partner", user.getPrimaryGroup());
    }

    return typedQuery.setMaxResults(20)
        .getResultList();
  }

  @Override
  @Transactional(readOnly = true)
  public List<User> suggest(User user, Instance instance, String query) {
    if (instance == null || isBlank(query))
      return emptyList();
    List<Group> groups = null;
    // company clause
    String clause = "(ug.id.group = :company)";
    String partnerClause = "(ug.primary = true AND g.description = 'PARTNER' AND g.parent = :company)";
    boolean isPartnerUser = PrincipalHelper.isPartnerUser(user);
    if (user == null)
      // search for all users
      clause = format("(%s OR %s)", partnerClause, clause);
    else {
      if (isPartnerUser) {
        StringBuilder inList = new StringBuilder();
        // creates a list of all the partners the user has in access to
        groups = getUserScope(user);
        for (int i = 0; i < groups.size(); i++) {
          if (inList.length() > 0)
            inList.append(" AND ");
          // the searched users need to have access to the same partners as the user
          inList.append("(:group" + i + " IN (SELECT part FROM u.perimeters per INNER JOIN per.selectedPartners part) OR ");
          inList.append(":group" + i + " IN (SELECT g FROM u.userGroupAssociations ug INNER JOIN ug.id.group g))");
        }
        // format clause to search for partner users with the same scope or the customer users
        clause = format(
            "((%s AND %s) OR %s)", inList.toString(), partnerClause, clause);
      }
      // don't return the same user
      clause += " AND u.id != :currenUserId ";
    }
    TypedQuery<User> typedQuery = this.getEntityManager()
        .createQuery("SELECT u FROM User u INNER JOIN u.userGroupAssociations ug " + " INNER JOIN ug.id.group g" +
            " WHERE " + clause +
            "   AND (LOWER(u.login) LIKE :squery OR LOWER(u.firstname) LIKE :squery OR " +
            "   LOWER(u.lastname) LIKE :squery OR LOWER(u.email) LIKE :cquery OR" +
            "   LOWER(g.name) LIKE :cquery OR LOWER(g.fullname) LIKE :squery) " + "   ORDER BY u.fullname ", User.class)
        .setParameter("company", instance.getGroup())
        .setParameter("squery", query.toLowerCase() + "%")
        .setParameter("cquery", "%" + query.toLowerCase() + "%");

    if (user != null)
      typedQuery.setParameter("currenUserId", user.getId());

    if (isPartnerUser) {
      for (int i = 0; i < groups.size(); i++)
        typedQuery.setParameter("group" + i, groups.get(i));
    }

    return typedQuery.setMaxResults(20)
        .getResultList();
  }

  @Override
  @Transactional(readOnly = true)
  public List<User> suggest(User user, Instance instance, boolean isDedicatedPortal) {
    if (instance == null || user == null)
      return emptyList();

    List<Group> groups = getUserScope(user);
    // company clause
    String clause =  isDedicatedPortal ? "(ug.id.group = :company)" : "";
    String partnerClause = "(ug.primary = true AND g.description = 'PARTNER' AND g.parent = :company)";
    StringBuilder inList = new StringBuilder();
    // creates a list of all the partners who have in the scope at least 1 group which the user has
    for (int i = 0; i < groups.size(); i++) {
      if (inList.length() > 0)
        inList.append(" OR ");
      // the searched users need to have access to the same partners as the user
      inList.append("(:group" + i + " IN (SELECT part FROM u.perimeters per INNER JOIN per.selectedPartners part) OR ");
      inList.append(":group" + i + " IN (SELECT g FROM u.userGroupAssociations ug INNER JOIN ug.id.group g))");
    }
    // format clause to search for partner users with at least 1 group from the user in the scope or the customer users
    String baseClause = format("(%s) AND %s", inList.toString(), partnerClause);
    clause = isEmpty(clause) ? baseClause : format("(%s OR %s)", baseClause, clause);
    // don't return the same user
    clause += " AND u.id != :currenUserId ";
    TypedQuery<User> typedQuery = this.getEntityManager()
        .createQuery("SELECT u FROM User u INNER JOIN u.userGroupAssociations ug " + " INNER JOIN ug.id.group g" +
            " WHERE " + clause +
            " ORDER BY u.fullname ", User.class)
        .setParameter("company", instance.getGroup());

    typedQuery.setParameter("currenUserId", user.getId());

    for (int i = 0; i < groups.size(); i++)
      typedQuery.setParameter("group" + i, groups.get(i));

    return typedQuery.getResultList();
  }

  private List<Group> getUserScope(User user) {
    List<Group> groups = null;
    groups = user.getGroups()
        .stream()
        .filter(g -> Partner.DESCRIPTION.equals(g.getDescription()))
        .collect(Collectors.toList());
    groups.addAll(user.getPerimeters()
        .stream()
        .map(Perimeter::getSelectedPartners)
        .flatMap(Arrays::stream)
        .collect(Collectors.toList()));
    return groups;
  }

  /**
   * @see com.byzaneo.generix.dao.ReportingDAO#findUserInParentGroup(com.byzaneo.security.bean.Group, boolean)
   */
  @Override
  @Transactional(readOnly = true)
  public Collection<User> findUserInParentGroup(Group parent, boolean includeParentUser) {
    return this.getEntityManager()
        .createQuery("SELECT DISTINCT u " + " FROM User u " + "	INNER JOIN u.userGroupAssociations ug " + "	INNER JOIN ug.id.group g " +
            "	INNER JOIN g.parent p " + " WHERE (ug.primary = true AND g.description = 'PARTNER' AND p = :parent) " +
            (includeParentUser ? " OR g = :parent " : "") + " ORDER BY u.fullname ", User.class)
        .setParameter("parent", parent)
        .getResultList();
  }
}
