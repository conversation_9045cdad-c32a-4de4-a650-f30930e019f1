package com.byzaneo.generix.ui.validator;

import com.byzaneo.commons.ui.util.JSFHelper;
import com.byzaneo.generix.exception.DuplicateException;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.generix.service.SecurityServiceImpl;
import com.byzaneo.generix.ui.CompanyHandler;
import com.byzaneo.generix.ui.OrganizationDialogHandler;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.Clauses;
import com.byzaneo.query.builder.QueryBuilder;
import com.byzaneo.security.bean.Company;
import com.byzaneo.security.bean.Partner;
import com.byzaneo.security.dao.GroupDAO;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import javax.faces.application.Application;
import javax.faces.context.FacesContext;
import javax.faces.validator.ValidatorException;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.same;
import static org.mockito.Mockito.doReturn;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class CodeUnicityValidatorTest {

  private MockedStatic<JSFHelper> mockedJSFHelper;

  @Mock
  private FacesContext context;

  @Mock
  private Application application;

  @Mock
  private GroupDAO groupAO;

  @Spy
  @InjectMocks
  private SecurityService ss = new SecurityServiceImpl();

  @InjectMocks
  private CodeUnicityValidator codeUnicityValidator;

  @BeforeEach
  public void setUp() throws Exception {
    mockedJSFHelper = Mockito.mockStatic(JSFHelper.class);
    // mock jsf context
    doReturn(application).when(context)
        .getApplication();
    mockedJSFHelper.when(() -> JSFHelper.getSpringBean(same(SecurityService.class), same(SecurityService.SERVICE_NAME)))
        .thenReturn(ss);

    // Company
    // La company est unique sur toute l'application
    Mockito.when(groupAO.count(Company.class, createQueryBuilderForCompany("CodeNoDuplicate", null)))
        .thenReturn(0L);
    Mockito.when(groupAO.count(Company.class, createQueryBuilderForCompany("CodeNoDuplicate", "company_id")))
        .thenReturn(0L);

    Mockito.when(groupAO.count(Company.class, createQueryBuilderForCompany("CodeDuplicate", null)))
        .thenReturn(1L);
    // Dans la vie réelle, ce cas ne devrai jamais arriver (implique une erreur de création en amont, car la présence d'une organization en
    // base dont le code est en double n'est pas possible)
    Mockito.when(groupAO.count(Company.class, createQueryBuilderForCompany("CodeDuplicate", "company_id")))
        .thenReturn(1L);

    // Partenaire
    // Le partenaire est unique dans une société (donc la société du partenaire est utilisée...)
    Mockito.when(groupAO.count(Partner.class, createQueryBuilderForPartner("CodeNoDuplicate", null, "company_id")))
        .thenReturn(0L);
    Mockito.when(groupAO.count(Partner.class, createQueryBuilderForPartner("CodeNoDuplicate", "partner_id", "company_id")))
        .thenReturn(0L);

    Mockito.when(groupAO.count(Partner.class, createQueryBuilderForPartner("CodeDuplicate", null, "company_id")))
        .thenReturn(1L);
    // Dans la vie réelle, ce cas ne devrai jamais arriver (implique une erreur de création en amont, car la présence d'une organization en
    // base dont le code est en double n'est pas possible)
    Mockito.when(groupAO.count(Partner.class, createQueryBuilderForPartner("CodeDuplicate", "partner_id", "company_id")))
        .thenReturn(1L);
  }

  @AfterEach
  void tearDownStaticMocks() {
    mockedJSFHelper.closeOnDemand();
  }

  // Tests service
  @Test
  public void test_company_service_nouveau_code_should_not_throw_duplicate_exception() {
    ss.checkCompanyCodeUniqueness("CodeNoDuplicate", null);
  }

  @Test
  public void test_company_service_modification_code_should_not_throw_duplicate_exception() {
    ss.checkCompanyCodeUniqueness("CodeNoDuplicate", "company_id");
  }

  @Test
  public void test_company_service_nouveau_code_should_throw_duplicate_exception() {
    assertThrows(DuplicateException.class, () ->
      ss.checkCompanyCodeUniqueness("CodeDuplicate", null));
  }

  @Test
  public void test_company_service_modification_code_should_throw_duplicate_exception() {
    assertThrows(DuplicateException.class, () ->
      ss.checkCompanyCodeUniqueness("CodeDuplicate", "company_id"));
  }

  @Test
  public void test_partner_service_nouveau_code_should_not_throw_duplicate_exception() {
    ss.checkPartnerCodeUniqueness(null, "CodeNoDuplicate", "company_id");
  }

  @Test
  public void test_partner_service_modification_code_should_not_throw_duplicate_exception() {
    ss.checkPartnerCodeUniqueness("partner_id", "CodeNoDuplicate", "company_id");
  }

  @Test
  public void test_partner_service_nouveau_code_should_throw_duplicate_exception() {
    assertThrows(DuplicateException.class, () ->
      ss.checkPartnerCodeUniqueness(null, "CodeDuplicate", "company_id"));
  }

  @Test
  public void test_partner_service_modification_code_should_throw_duplicate_exception() {
    assertThrows(DuplicateException.class, () ->
      ss.checkPartnerCodeUniqueness("partner_id", "CodeDuplicate", "company_id"));
  }

  // Test company JSF validator
  @Test
  public void test_company_nouveau_code_should_not_throw_validator_exception() {
    createMockOrganization();
    createMockCompany(null);
    codeUnicityValidator.validate(context, null, "CodeNoDuplicate");

    createMockSocieteOrganization(null);
    codeUnicityValidator.validate(context, null, "CodeNoDuplicate");
  }

  @Test
  public void test_company_modification_should_not_throw_validator_exception() {
    createMockSocieteOrganization("company_id");
    codeUnicityValidator.validate(context, null, "CodeNoDuplicate");
  }

  @Test
  public void test_company_nouveau_code_should_throw_validator_exception() {
    assertThrows(ValidatorException.class, () -> {
      createMockSocieteOrganization(null);
      codeUnicityValidator.validate(context, null, "CodeDuplicate");
    });
  }

  @Test
  public void test_company_modification_code_should_throw_validator_exception() {
    assertThrows(ValidatorException.class, () -> {
      createMockSocieteOrganization("company_id");
      codeUnicityValidator.validate(context, null, "CodeDuplicate");
    });
  }

  // Test partner JSF validator
  @Test
  public void test_partner_nouveau_code_should_not_throw_validator_exception() {
    createMockPartnerOrganization(null, "company_id");
    codeUnicityValidator.validate(context, null, "CodeNoDuplicate");
  }

  @Test
  public void test_partner_modification_code_should_not_throw_validator_exception() {
    createMockPartnerOrganization("partner_id", "company_id");
    codeUnicityValidator.validate(context, null, "CodeNoDuplicate");
  }

  @Test
  public void test_partner_nouveau_code_duplicate_should_throw_validator_exception() {
    assertThrows(ValidatorException.class, () -> {
      createMockPartnerOrganization(null, "company_id");
      codeUnicityValidator.validate(context, null, "CodeDuplicate");
    });
  }

  @Test
  public void test_partner_modification_code_should_throw_validator_exception() {
    assertThrows(ValidatorException.class, () -> {
      createMockPartnerOrganization("partner_id", "company_id");
      codeUnicityValidator.validate(context, null, "CodeDuplicate");
    });
  }

  private void createMockCompany(String companyId) {
    CompanyHandler companyHandler = new CompanyHandler();
    Company company = new Company();
    company.setId(companyId);
    companyHandler.setCompany(company);
    // mock validator with company
    doReturn(companyHandler).when(application)
        .evaluateExpressionGet(same(context),
            same("#{gnxCompanyHandler}"),
            same(CompanyHandler.class));
  }

  private void createMockPartnerCompany(String companyId, String partnerId) {
    CompanyHandler companyHandler = new CompanyHandler();
    Partner partner = new Partner();
    Company company = new Company();
    partner.setId(partnerId);
    company.setId(companyId);
    companyHandler.setPartner(partner);
    companyHandler.setCompany(company);
    // mock validator with company
    doReturn(companyHandler).when(application)
        .evaluateExpressionGet(same(context),
            same("#{gnxCompanyHandler}"),
            same(CompanyHandler.class));
  }

  private void createMockOrganization() {
    OrganizationDialogHandler organizationDialogHandler = new OrganizationDialogHandler();
    // mock validator with organization
    doReturn(organizationDialogHandler).when(application)
        .evaluateExpressionGet(same(context),
            same("#{gnxOrganizationDialogHandler}"),
            same(OrganizationDialogHandler.class));
  }

  private void createMockSocieteOrganization(String companyId) {
    OrganizationDialogHandler organizationDialogHandler = new OrganizationDialogHandler();
    Company organizationIsCompany = new Company();
    organizationIsCompany.setId(companyId);
    organizationDialogHandler.setCompany(organizationIsCompany);
    // mock validator with organization
    doReturn(organizationDialogHandler).when(application)
        .evaluateExpressionGet(same(context),
            same("#{gnxOrganizationDialogHandler}"),
            same(OrganizationDialogHandler.class));
  }

  private void createMockPartnerOrganization(String partenaireId, String companyId) {
    OrganizationDialogHandler organizationDialogHandler = new OrganizationDialogHandler();
    Partner organizationIsPartner = new Partner();
    organizationIsPartner.setId(partenaireId);
    Company societeDuPartenaire = new Company();
    societeDuPartenaire.setId(companyId);
    societeDuPartenaire.setName("TST");
    organizationDialogHandler.setCompany(societeDuPartenaire);
    organizationDialogHandler.setPartner(organizationIsPartner);
    // mock validator with organization
    doReturn(organizationDialogHandler).when(application)
        .evaluateExpressionGet(same(context),
            same("#{gnxOrganizationDialogHandler}"),
            same(OrganizationDialogHandler.class));
  }

  private Query createQueryBuilderForCompany(String newCode, String companyId) {
    QueryBuilder qb = new QueryBuilder();
    qb.and(Clauses.equal("code", newCode));
    if (companyId != null) {
      qb.and(Clauses.notEqual("id", companyId));
    }
    return qb.query();
  }

  private Query createQueryBuilderForPartner(String newCode, String partenaireId, String companyId) {
    QueryBuilder qb = new QueryBuilder();
    qb.and(Clauses.equal("parent.id", companyId));
    qb.and(Clauses.equal("code", newCode));
    if (partenaireId != null) {
      qb.and(Clauses.notEqual("id", partenaireId));
    }
    return qb.query();
  }

}
