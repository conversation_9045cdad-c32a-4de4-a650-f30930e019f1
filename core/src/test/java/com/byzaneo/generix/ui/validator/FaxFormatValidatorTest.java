package com.byzaneo.generix.ui.validator;

import static org.junit.jupiter.api.Assertions.assertThrows;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import javax.faces.context.FacesContext;
import javax.faces.validator.ValidatorException;

public class FaxFormatValidatorTest {
  private FacesContext context;

  private FaxFormatValidator validator;

  @BeforeEach
  public void setUp() throws Exception {
    validator = new FaxFormatValidator();
    context = Mockito.mock(FacesContext.class, Mockito.RETURNS_DEEP_STUBS);
  }

  @Test
  public void testValidateWithEmptyFaxNumber() {
    validator.validate(context, null, "");
    Mockito.verify(context, Mockito.times(0))
        .getExternalContext();
  }

  @Test
  public void testValidate() {
    Mockito.when(context.getExternalContext()
        .getRequestParameterMap()
        .get("partnerForm-cOrgaCC-partnerInfoTabView-ccountry_input"))
        .thenReturn(null);
    validator.validate(context, null, "1234567");

    Mockito.when(context.getExternalContext()
        .getRequestParameterMap()
        .get("partnerForm-cOrgaCC-partnerInfoTabView-ccountry_input"))
        .thenReturn("FR");
    validator.validate(context, null, "001234567");
  }

  @Test
  public void testValidateShouldThrowValidatorExceptionWithFrenchFax() {
    assertThrows(ValidatorException.class, () -> {
      Mockito.when(context.getExternalContext()
          .getRequestParameterMap()
          .get("partnerForm-cOrgaCC-partnerInfoTabView-ccountry_input"))
          .thenReturn("_FR");
      validator.validate(context, null, "1234567890");
    });
  }

  @Test
  public void testValidateShouldThrowValidatorExceptionWithOtherFax() {
    assertThrows(ValidatorException.class, () -> {
      Mockito.when(context.getExternalContext()
          .getRequestParameterMap()
          .get("partnerForm-cOrgaCC-partnerInfoTabView-ccountry_input"))
          .thenReturn("_US");
      validator.validate(context, null, "1234567890");
    });
  }
}