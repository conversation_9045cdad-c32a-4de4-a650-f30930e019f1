package com.byzaneo.generix.ui.convert;

import com.byzaneo.generix.bean.MessageRecipient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import static org.junit.jupiter.api.Assertions.*;

public class RecipientConverterTest {
  private RecipientConverter converter;

  @BeforeEach
  public void setUp() throws Exception {
    converter = new RecipientConverter();
  }

  @Test
  public void testGetAsStringAndGetAsObject() {
    MessageRecipient recipient = Mockito.mock(MessageRecipient.class);

    assertNull(converter.getAsString(null, null, null));
    assertEquals("test", converter.getAsString(null, null, "test"));
    String key = converter.getAsString(null, null, recipient);
    assertNotNull(key);
    assertEquals(recipient, converter.getAsObject(null, null, key));
  }

}