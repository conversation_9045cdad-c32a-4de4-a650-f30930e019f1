package com.byzaneo.generix.api.exception;

import org.slf4j.Logger;

import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.ext.ExceptionMapper;

import static org.slf4j.LoggerFactory.getLogger;

/**
 * Generic handler for Exception, simply log it and return HTTP 500 with exception message. Avoid printing stacktrace to the client.
 *
 * <AUTHOR>
 * @date 29/06/2015
 */
public abstract class GenericHandler<E extends Throwable> implements ExceptionMapper<E> {
  protected static final Logger logger = getLogger(GenericHandler.class);

  @Override
  public Response toResponse(final E e) {
    logger.error(e.getMessage(), e);
    return Response
        .status(Response.Status.INTERNAL_SERVER_ERROR)
        .type(MediaType.TEXT_PLAIN)
        .entity(e.getMessage())
        .build();
  }
}
