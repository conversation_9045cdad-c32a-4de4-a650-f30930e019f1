package com.byzaneo.generix.api.bean.portal.action;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class TriggerActionWrapperDto {
  private String name;
  private TriggerActionId id;
  private boolean global;
  private List<String> status;

  private String formPageUrl;

  public static enum TriggerActionId {

    ACTION_1(1),
    ACTION_2(2),
    ACTION_3(3),
    ACTION_4(4),
    ACTION_5(5),
    ACTION_6(6),
    ACTION_7(7),
    ACTION_8(8),
    ACTION_9(9),
    ACTION_10(10);

    // in order to be used in an annotation the string must be constant declared at compile time // cannot use ACTION_1.toString()
    public static final String ACTION_1_STR = "ACTION_1";
    public static final String ACTION_2_STR = "ACTION_2";
    public static final String ACTION_3_STR = "ACTION_3";
    public static final String ACTION_4_STR = "ACTION_4";
    public static final String ACTION_5_STR = "ACTION_5";
    public static final String ACTION_6_STR = "ACTION_6";
    public static final String ACTION_7_STR = "ACTION_7";
    public static final String ACTION_8_STR = "ACTION_8";
    public static final String ACTION_9_STR = "ACTION_9";
    public static final String ACTION_10_STR = "ACTION_10";
    private int index;

    private TriggerActionId(int index) {
      this.index = index;
    }

    public int getIndex() {
      return index;
    }

    public TriggerActionId getNextActionId() {
      if (this.index == 10) {
        return ACTION_1;
      }
      return getByIndex(index + 1);
    }

    public TriggerActionId getByIndex(int index) {
      for (TriggerActionId actionId : values()) {
        if (actionId.getIndex() == index) {
          return actionId;
        }
      }
      return null;
    }
  }
}
