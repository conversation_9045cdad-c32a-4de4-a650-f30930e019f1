package com.byzaneo.generix.api.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class RoleListDTO {

  @Schema(description = "")
  private Integer total = null;

  @Schema(description = "")
  private List<RoleDTO> roles = null;

  /**
   * Get total
   *
   * @return total
   **/
  @JsonProperty("total")
  public Integer getTotal() {
    return total;
  }

  public void setTotal(Integer total) {
    this.total = total;
  }

  public RoleListDTO total(Integer total) {
    this.total = total;
    return this;
  }

  /**
   * Get roles
   *
   * @return roles
   **/
  @JsonProperty("roles")
  public List<RoleDTO> getRoles() {
    return roles;
  }

  public void setRoles(List<RoleDTO> roles) {
    this.roles = roles;
  }

  public RoleListDTO roles(List<RoleDTO> roles) {
    this.roles = roles;
    return this;
  }

  public RoleListDTO addRolesItem(RoleDTO rolesItem) {
    this.roles.add(rolesItem);
    return this;
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RoleList {\n");

    sb.append("    total: ")
        .append(toIndentedString(total))
        .append("\n");
    sb.append("    roles: ")
        .append(toIndentedString(roles))
        .append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private static String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString()
        .replace("\n", "\n    ");
  }
}
