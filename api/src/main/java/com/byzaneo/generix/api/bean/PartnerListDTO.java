package com.byzaneo.generix.api.bean;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;

@ApiModel(value = "PartnerList")
public class PartnerListDTO extends ReturnedListDTO {

  @Schema(description = "")
  private List<PartnerAPIDTO> partners = null;

  /**
   * Get partners
   *
   * @return partners
   **/
  @JsonProperty("partners")
  public List<PartnerAPIDTO> getPartners() {
    return partners;
  }

  public void setPartners(List<PartnerAPIDTO> partners) {
    this.partners = partners;
  }

  public PartnerListDTO partners(List<PartnerAPIDTO> partners) {
    this.partners = partners;
    return this;
  }

  public PartnerListDTO addPartnersItem(PartnerAPIDTO partnersItem) {
    this.partners.add(partnersItem);
    return this;
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PartnerList {\n");
    sb.append("    ")
        .append(toIndentedString(super.toString()))
        .append("\n");
    sb.append("    partners: ")
        .append(toIndentedString(partners))
        .append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private static String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString()
        .replace("\n", "\n    ");
  }
}
