package com.byzaneo.generix.api.bean.invoice;

import lombok.*;

import javax.xml.bind.annotation.XmlRootElement;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@XmlRootElement(name = "ValueControl")
public class ValueControlDto {
  private String listValue;

  private Map<Locale, String> labels = new HashMap<>();
  private boolean byDefault;

  public String getLabel(Locale locale) {
    if (labels.containsKey(locale))
      return labels.get(locale);
    else return labels.get(Locale.ENGLISH);
  }

}
