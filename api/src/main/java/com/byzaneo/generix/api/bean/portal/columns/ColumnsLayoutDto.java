package com.byzaneo.generix.api.bean.portal.columns;

import com.byzaneo.generix.api.bean.portal.LayoutDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.xml.bind.annotation.XmlRootElement;
import java.util.*;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@XmlRootElement( name = "ColumnsLayout")
public class ColumnsLayoutDto extends LayoutDto {
  private static final long serialVersionUID = -1218588495447893337L;

  public static final String LAYOUT_NAME = "Columns";

  public enum Direction {
    /** Cardinal direction */
    north,
    south,
    east,
    west,
    header,
    columns;
  }


  private ColumnDto header;


  private List<ColumnDto> columns;

}
