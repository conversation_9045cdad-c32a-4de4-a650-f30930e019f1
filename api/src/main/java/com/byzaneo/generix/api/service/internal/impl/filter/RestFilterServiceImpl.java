package com.byzaneo.generix.api.service.internal.impl.filter;

import com.byzaneo.generix.api.bean.BqlFilterBundleDto;
import com.byzaneo.generix.api.bean.BqlFilterDto;
import com.byzaneo.generix.api.service.internal.RestFilterService;
import com.byzaneo.generix.api.service.internal.delegators.RestFilterServiceDelegator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;

import static org.slf4j.LoggerFactory.getLogger;

@Service(RestFilterService.SERVICE_NAME)
@RequiredArgsConstructor
public class RestFilterServiceImpl implements RestFilterService {

  private final RestFilterServiceDelegator filterServiceDelegator;

  @Override
  @Transactional
  public Response getFiltersByUserId(HttpServletRequest request, String userId) {
    return filterServiceDelegator.getFiltersByUserId(request, userId);
  }

  @Override
  @Transactional
  public Response getFiltersByFilterId(HttpServletRequest request, long filterId) {
    return filterServiceDelegator.getFiltersByFilterId(request, filterId);
  }

  @Override
  public Response getAllFilters(HttpServletRequest request) {
    return filterServiceDelegator.getAllFilters(request);
  }

  @Override
  public Response getFiltersByPortletId(HttpServletRequest request, long portletId,String userId) {
    return filterServiceDelegator.getFiltersByPortletId(request, portletId, userId);
  }

  @Override
  @Transactional
  public Response saveFilter(HttpServletRequest request, BqlFilterDto filterDto) {
    return filterServiceDelegator.saveFilter(request, filterDto);
  }

  @Override
  @Transactional
  public Response bulkSaveFilters(HttpServletRequest request, BqlFilterBundleDto bqlFilterBundleDto) {
      return filterServiceDelegator.bulkSaveFilters(request, bqlFilterBundleDto);
        }


  @Override
  public Response deleteFilter(HttpServletRequest request, long filterId) {
    return filterServiceDelegator.deleteFilter(request, filterId);
  }
}
