package com.byzaneo.generix.api.service.internal.delegators;

import com.byzaneo.generix.api.bean.BillingPreferencesDTO;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;

public interface RestProfilePreferencesServiceDelegator {

  Response getProfilePreferencesInformation(HttpServletRequest request);

  Response getTvaExemptionItems(HttpServletRequest request);

  Response getPaymentChoices(HttpServletRequest request);

  Response saveInvoicePreferencesListener(HttpServletRequest request,
      BillingPreferencesDTO billingPreferencesDTO);

}
