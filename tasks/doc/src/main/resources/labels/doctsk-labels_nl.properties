docchart_recnotnull = Ontvanger niet ongeldig
docchart_title_layout = Lay-out
without_legend = Zonder legenda
docchart_tos = Naar
year = Jaar
grouping_type = Groeperingtype
docchart_lp_sw = Zuid-west
docchart_status = Status
line_stepAmount = N<PERSON><PERSON> van stap
rteParameters = RTE parameters
ORDERS = Order
model = Model
outputDirectory = Uitvoer directory
DESADV = Verzend advies
day = Dag
query1 = Query 1
FOLDER = Folder
query2 = Query 2
warn_removing_locked_doc = Verwijderen van geblokkeerd document
docchart_lp_se = Zuid-oost
filedset_ordonate = Ordonaat
query = Filteren
filter1 = Filter 1
error_removing_doc = Fout bij verwijderen van document
filter2 = Filter 2
message_no_data = Geen data om weer te geven voor de grafiek
docchart_stepiteration = Stap opsomming
legende = Legenda
message_placeholder_colors = 62656a,a5a5a5,929397,...
month = Maand
docchart_chart = Overzicht
info_removing_doc = Documend verwijderd
error_renaming_disabled = Fout hernoemen gedeactiveerd
ORDRSP = Orderantwoord
INVOIC = Factuur
docchart_lp_nw = Noord-west
collection_name = Verzameling Mongo
docchart_chartheight = Hoogte
week = Week
xpath_query1 = XPATH verzoek 1
color = Kleur
xpath_query2 = XPATH verzoek 2
configuration = Configuratie
south = Zuiden
line_step = Stap
north = Noord
outputType = Uitvoer
docchart_stepamount = Stap hoeveelheid
colors = Kleuren
docchart_lp_ne = Noord-oost
docchart_step = Stap
docchart_froms = Van
ORDCHG = Orderverandering
legend_position = Legenda positie
docchart_ownername = Eigenaar
info_renaming_doc = Document hernoemen
INVRPT = Inventarisatierapport
tooltip1 = Teller tooltip 1
tooltip2 = Teller tooltip 2
docchart_title_type = Type
docchart_lp_s = Zuid
axe_title = As-titel
display = Weergave
docchart_lp_w = West
docchart_chartwidth = Breedte
docchart_title_filters = Filters
REPORT = Rapport
docchart_title_period = Periode
docchart_lp_e = Oost
portlet_open_click = Portlet om te openen
docchart_countfield = Tel veld
docchart_doctype = Document type
docchart_docfield = Document veld
filedset_absciss = Abscis
docchart_tos_lower = naar
DELINS = Leveringsinstructies
docchart_legendposition = Legenda positie
docchart_lp_n = Noord
west = West
east = Oost
template_birt = Birttemplate
reload = Updaten
configure = Configureren
format_birt = Standaardformaat

docPercentage = van alle documenten
icon = Icon
INVOIC_JSON = Portal Facturen
percentage_legend_default = van alle documenten
date_range_week = Week
date_range_year_tooltip = Als het selectievakje niet is ingeschakeld, registreert de teller de gegevens sinds 1 januari.\nAls het selectievakje is ingeschakeld, registreert de teller de gegevens van de afgelopen 365 dagen.
last_update = Laatste update
diagram_title = Titel dashboard
date_range_with_values = Van {0} naar {1}
date_range_use = Het gebruik van datumbereik toestaan
date_range_sliding = Voortschrijdende periode
date_range_month_tooltip = Als het selectievakje niet is ingeschakeld, registreert de teller gegevens vanaf de 1e van de maand.\nAls het selectievakje is ingeschakeld, registreert de teller de gegevens van de laatste 30 dagen.
display_percentage = Percentage weergeven
date_range_week_tooltip = Als het selectievakje niet is ingeschakeld, registreert de teller de gegevens sinds maandag.\nAls het selectievakje is ingeschakeld, registreert de teller de gegevens van de afgelopen 7 dagen.
date_range_introduction = U kunt de weergave van deze teller aanpassen door een datumbereik in te stellen op de {0}
date_range_year = Jaar
date_range_month = Maand
diagram_style = Hoogte
type = Type
asnHeader.asnIssueDate = Verzenddatum
date_range_with_value = Op {0}
status = Status
refresh_counter_color = Kleur van de knop Vernieuwen
limit_documents_workflow = Alleen documenten weergeven die behoren tot de workflow van de ingelogde gebruiker
update_time = Tijd bijwerken
automatic_update = Automatisch bijwerken
date_range = Datumbereik
asnHeader.asnDates.deliveryDate = Leveringsdatum
stage = Stadium
to = Naar
counter_label = Label
from = Van
additional_date_filter = Datum waarop het extra filter moet worden uitgevoerd
invoiceProcessDateTime = Dematerialisatie datum
filter_direction = Richting factuurstroom
invoiceIssueDate = Uitgiftedatum factuur
docchart_read_portlet_information = Klik voor meer informatie over BQL op de
refresh_counter = Vernieuw de teller op verzoek van de gebruiker
creationDate = Aanmaakdatum
iconTooltip = U kunt hier een geschikt pictogram kiezen
orderRequestedDeliverByDate = Leveringsdatum
iconPlaceholder = ex: 'fa-bar-chart'
style_min_height = min-hoogte in px
orderResponseHeader.orderResponseIssueDate = Datum antwoord bestelling
at_time = op
issueDate = Datum bestelling
use_date_for_percent_calculation = Het extra filter toepassen op de percentageberekening
invoiceDueDate = Vervaldatum
percentage_legend = Percentage label
portlet_computation_mode_sum = Som
portlet_computation_applied_on_field = Het bedrag waarop de som moet worden uitgevoerd
portlet_computation_mode_label = Geef het resultaat weer als
totalTaxAmountTAC = Belastingbedrag
taxableValueTAC = Totaal (Ex. BTW)
invoiceTotalTAC = Totaal (incl. btw)
totalAmountPayableTAC = Te betalen bedrag
portlet_computation_mode_count = Teller