<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml" 
	xmlns:f="http://xmlns.jcp.org/jsf/core" xmlns:h="http://xmlns.jcp.org/jsf/html" 
	xmlns:cc="http://xmlns.jcp.org/jsf/composite" xmlns:p="http://primefaces.org/ui"
	xmlns:tsk="http://xmlns.jcp.org/jsf/composite/components/task"
	xmlns:ui="http://xmlns.jcp.org/jsf/facelets">

	<!-- INTERFACE -->
	<cc:interface name="documentChartView">
		<cc:attribute name="value" required="true" />
		<cc:attribute name="type" required="true" />
		<cc:attribute name="mode" default="view" />
	</cc:interface>

	<!-- IMPLEMENATION -->
	<cc:implementation>
		<p:outputPanel styleClass="psDocChartViewPnl task-docchart-panel">
			<ui:param name="chartData" value="#{cc.attrs.value.model}" />
			<h:panelGroup rendered="#{chartData!=null}">
				<p:chart type="bar" model="#{cc.attrs.value.model}" legendPosition="#{cc.attrs.value.legendPosition}" rendered="#{cc.attrs.value.chart=='Bar'}" animate="true" breakOnNull="false"
					style="width:#{cc.attrs.value.chartWidth==0 ? '100%' : cc.attrs.value.chartWidth}#{cc.attrs.value.chartWidth==0 ? '' : 'px'};height:#{cc.attrs.value.chartHeight}px;" />
				<p:chart type="line" model="#{cc.attrs.value.model}" legendPosition="#{cc.attrs.value.legendPosition}" rendered="#{cc.attrs.value.chart=='Line'}" animate="true" breakOnNull="false"
					style="width:#{cc.attrs.value.chartWidth==0 ? '100%' : cc.attrs.value.chartWidth}#{cc.attrs.value.chartWidth==0 ? '' : 'px'};height:#{cc.attrs.value.chartHeight}px;" />
				<p:chart type="pie" model="#{cc.attrs.value.model}" legendPosition="#{cc.attrs.value.legendPosition}" rendered="#{cc.attrs.value.chart=='Pie'}" showDataLabels="true" 
					style="width:#{cc.attrs.value.chartWidth==0 ? '100%' : cc.attrs.value.chartWidth}#{cc.attrs.value.chartWidth==0 ? '' : 'px'};height:#{cc.attrs.value.chartHeight}px;" >
					<p:ajax event="itemSelect" listener="#{cc.attrs.value.onSelect}" immediate="true" process="@this" update="@none" />
				</p:chart>
			</h:panelGroup>
			<h:panelGroup rendered="#{chartData==null}" layout="block" styleClass="center" style="padding-top:10px;height:#{cc.attrs.value.chartHeight}px;">
				<h:outputText value="#{labels.no_data}" styleClass="title2"/>
			</h:panelGroup>
		</p:outputPanel>
 	</cc:implementation>
</ui:component>
