package com.byzaneo.generix.task.portal.util;

import java.io.Serializable;
import java.util.*;

public class <PERSON><PERSON><PERSON><PERSON>rapper implements Serializable {

  private static final long serialVersionUID = 1129415364465699665L;

  private Map<String, Long> dataValues;
  private Map<String, String> dataLabels;


  private Date lastUpdate;

  public DocChartWrapper() {
  }

  public DocChartWrapper(Map<String, Long> dataValues, Map<String, String> dataLabels, Date lastUpdate) {
    this.dataValues = dataValues;
    this.dataLabels = dataLabels;
    this.lastUpdate = lastUpdate;
  }

  public Map<String, Long> getDataValues() {
    return dataValues;
  }

  public void setDataValues(Map<String, Long> dataValues) {
    this.dataValues = dataValues;
  }

  public Date getLastUpdate() {
    return lastUpdate;
  }

  public void setLastUpdate(Date lastUpdate) {
    this.lastUpdate = lastUpdate;
  }

  public Map<String, String> getDataLabels() {
    return dataLabels;
  }

  public void setDataLabels(Map<String, String> dataLabels) {
    this.dataLabels = dataLabels;
  }
}
