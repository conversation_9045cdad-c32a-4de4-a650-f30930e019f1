signature_packaging_enveloped = ENVELOPED
no = No
signature_output_dir = Output directory
signature_format_pades_baseline_t = PAdES_BASELINE_T
signature_outputoption_specific_directory = Specific directory
signature = Signature
signature_format_cades_baseline_b = CAdES_BASELINE_B
certificate = Certificate
signature_output = Signature output
signature_packaging_detached = DETACHED
title = Title
signature_documentfile_bql_verified = BQL filter for the associated file to be verified
signature_packaging_enveloping = ENVELOPING
signature_format_xades_baseline_b = XAdES_BASELINE_B
signature_hashing_sha256 = SHA-256
signature_packaging = Signature packaging
allow_self_signed_certificates = Allow self-signed certificates
digest = Digest
disabled = Disabled
signature_format_pades_baseline_b = PAdES_BASELINE_B
signature_outputoption_context = Context
signed_artefacts = Signed artefacts
signature_cdfp_xcbl_and_pdf_invoicelike = PDF and XCBL attachments (eg : workflow facture PDF)
yes = Yes
certificate_name = Certificate name
signature_documentfile_bql_description = comment = 'to_be_signed' AND type = 'XML'
signature_cdfp_xcbl_and_xml = XML and XCBL attachments
signature_format = Signature format
signature_outputdir_placeholder = default is ${process_work_dir_string}
signature_cdfp = Documents to be processed
signature_format_cades_baseline_t = CAdES_BASELINE_T
information = Information
signature_format_xades_baseline_t = XAdES_BASELINE_T
hashing_algorithm = Hashing algorithm
signature_documentfile_bql_signed = BQL filter for the associated file to be signed