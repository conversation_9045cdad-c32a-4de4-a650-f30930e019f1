<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml" xmlns:f="http://xmlns.jcp.org/jsf/core" xmlns:h="http://xmlns.jcp.org/jsf/html"
	xmlns:cc="http://xmlns.jcp.org/jsf/composite" xmlns:pt="http://xmlns.jcp.org/jsf/passthrough" xmlns:p="http://primefaces.org/ui"
	xmlns:tsk="http://xmlns.jcp.org/jsf/composite/components/task" xmlns:ui="http://xmlns.jcp.org/jsf/facelets">
	<!-- INTERFACE -->
	<cc:interface name="signatureTaskEdit">
	</cc:interface>

	<!-- IMPLEMENATION -->
	<cc:implementation>
		<p:outputPanel styleClass="form-group" rendered="true">
			<!-- Label -->
			<p:outputLabel styleClass="col-sm-2 control-label" value="#{tsksiglbls.disabled}" />
			<!-- Value -->
			<p:outputPanel styleClass="col-sm-10 cc-input">
				<p:selectBooleanCheckbox value="#{cc.attrs.value.disabled}" label="#{comApplicationHandler.label(cc.attrs.label)}" 
					required="#{cc.attrs.required}" readonly="#{cc.attrs.readonly}" disabled="#{cc.attrs.readonly}" 
					styleClass="form-control"
					rendered="true">
					<p:clientValidator disabled="#{not cc.attrs.clientValidation}" />
					<p:message for="@parent" display="icon" rendered="#{cc.attrs.clientValidation}" />
				</p:selectBooleanCheckbox>
			</p:outputPanel>
		</p:outputPanel>
		
		
		<p:outputPanel styleClass="form-group" rendered="true">
			<!-- Label -->
			<p:outputLabel styleClass="col-sm-2 control-label" value="#{tsksiglbls.use_client_key}" />
			<!-- Value -->
			<p:outputPanel styleClass="col-sm-10 cc-input">
				<p:selectBooleanCheckbox value="#{cc.attrs.value.useClientKey}" label="#{comApplicationHandler.label(cc.attrs.label)}" 
					required="#{cc.attrs.required}" readonly="#{cc.attrs.readonly}" disabled="#{cc.attrs.readonly}" 
					styleClass="form-control"
					rendered="true">
					<p:clientValidator disabled="#{not cc.attrs.clientValidation}" />
					<p:message for="@parent" display="icon" rendered="#{cc.attrs.clientValidation}" />
				</p:selectBooleanCheckbox>
			</p:outputPanel>
		</p:outputPanel>
		
		
		<!--<p:fieldset legend="#{tsksiglbls.information}" toggleable="true">
			<p:outputPanel styleClass="form-group" rendered="true">
				Label 
				<p:outputLabel styleClass="col-sm-2 control-label" value="#{tsksiglbls.nom_toto}" />
				Value 
				<p:outputPanel styleClass="col-sm-10 cc-input">
					<p:inputText value="#{cc.attrs.value.nomToto}" label="#{comApplicationHandler.label(cc.attrs.label)}" 
						required="#{cc.attrs.required}" readonly="#{cc.attrs.readonly}"  
						styleClass="form-control input-#{cc.attrs.size} jqs#{cc.attrs.name}" pt:placeholder="#{comApplicationHandler.label(cc.attrs.description)}" 
						converter="#{comApplicationHandler.getConverter(cc.attrs.converter)}"
						rendered="true">
						<p:clientValidator disabled="#{not cc.attrs.clientValidation}" />
						<p:message for="@parent" display="icon" rendered="#{cc.attrs.clientValidation}" />
					</p:inputText>
				</p:outputPanel>
			</p:outputPanel>
		</p:fieldset>-->
		<!-- CERTIFICATE -->
		<p:fieldset legend="#{tsksiglbls.certificate}" toggleable="true"
			styleClass="task-edit-fieldset #{group.getName()!='default' ? null : 'hide-legend'}">
			<p:outputPanel styleClass="form-group" rendered="true">
				<!-- Label -->
				<p:outputLabel styleClass="col-sm-2 control-label" value="#{tsksiglbls.certificate_name}" />
				<!-- Value -->
				<p:selectOneMenu value="#{cc.attrs.value.certificateNameExpr}" label="blabla" 
					required="true" readonly="#{cc.attrs.readonly}"
					editable="#{cc.attrs.editable}"
					converter="processExpressionConverter"
					rendered="true">
					<!-- <f:selectItems value="#{cc.attrs.value.certificateNameOptions}"/>-->
					<f:selectItems value="#{cc.attrs.value.obtainCertificateNameOptionsByInstance(cc.attrs.owner.project.workspace.name)}"/>
					<p:clientValidator disabled="#{not cc.attrs.clientValidation}" />
					<p:message for="@parent" display="icon" rendered="#{cc.attrs.clientValidation}" />
				</p:selectOneMenu>
			</p:outputPanel>
			<p:outputPanel styleClass="form-group" rendered="true">
				<!-- Label -->
				<p:outputLabel styleClass="col-sm-4 control-label" value="#{tsksiglbls.allow_self_signed_certificates}" />
				<!-- Value -->
				<p:outputPanel styleClass="col-sm-6 cc-input">
					<p:selectBooleanCheckbox value="#{cc.attrs.value.selfSignedCertificateAllowedExpr}" label="#{comApplicationHandler.label(cc.attrs.label)}" 
						required="#{cc.attrs.required}" readonly="#{cc.attrs.readonly}" disabled="#{cc.attrs.readonly}" 
						styleClass="form-control"
						rendered="true">
						<p:clientValidator disabled="#{not cc.attrs.clientValidation}" />
						<p:message for="@parent" display="icon" rendered="#{cc.attrs.clientValidation}" />
					</p:selectBooleanCheckbox>
				</p:outputPanel>
			</p:outputPanel>
		</p:fieldset>
		<!-- DIGEST -->
		<p:fieldset legend="#{tsksiglbls.digest}" toggleable="true"
			styleClass="task-edit-fieldset #{group.getName()!='default' ? null : 'hide-legend'}">
			<p:outputPanel styleClass="form-group" rendered="true">
				<!-- Label -->
				<p:outputLabel styleClass="col-sm-2 control-label" value="#{tsksiglbls.hashing_algorithm}" />
				<!-- Value -->
				<p:selectOneMenu value="#{cc.attrs.value.hashingAlgorithmExpr}" label="blabla" 
					required="#{cc.attrs.required}" readonly="#{cc.attrs.readonly}"
					editable="#{cc.attrs.editable}"
					converter="processExpressionConverter"
					rendered="true">
					<f:selectItems value="#{cc.attrs.value.hashingAlgorithmOptions}" var="hashingAlgorithmOption"
						itemValue="#{hashingAlgorithmOption.label}" itemLabel="#{tsksiglbls[hashingAlgorithmOption.label]}" />
					<p:clientValidator disabled="#{not cc.attrs.clientValidation}" />
					<p:message for="@parent" display="icon" rendered="#{cc.attrs.clientValidation}" />
				</p:selectOneMenu>
			</p:outputPanel>
		</p:fieldset>
		<!-- SIGNATURE -->
		<p:fieldset legend="#{tsksiglbls.signature}" toggleable="true"
			styleClass="task-edit-fieldset #{group.getName()!='default' ? null : 'hide-legend'}">
			<p:outputPanel styleClass="form-group" rendered="true">
				<!-- Label -->
				<p:outputLabel styleClass="col-sm-2 control-label" value="#{tsksiglbls.signature_format}" />
				<!-- Value -->
				<p:selectOneMenu value="#{cc.attrs.value.signatureFormatExpr}" label="blabla" 
					required="#{cc.attrs.required}" readonly="#{cc.attrs.readonly}"
					editable="#{cc.attrs.editable}"
					converter="processExpressionConverter"
					rendered="true">
					<f:selectItems value="#{cc.attrs.value.signatureFormatOptions}" var="signatureFormatOption"
						itemValue="#{signatureFormatOption.label}" itemLabel="#{tsksiglbls[signatureFormatOption.label]}" />
					<p:clientValidator disabled="#{not cc.attrs.clientValidation}" />
					<p:message for="@parent" display="icon" rendered="#{cc.attrs.clientValidation}" />
				</p:selectOneMenu>
			</p:outputPanel>
			<p:outputPanel styleClass="form-group" rendered="true">
				<!-- Label -->
				<p:outputLabel styleClass="col-sm-2 control-label" value="#{tsksiglbls.signature_packaging}" />
				<!-- Value -->
				<p:selectOneMenu value="#{cc.attrs.value.signaturePackagingExpr}" label="blabla" 
					required="#{cc.attrs.required}" readonly="#{cc.attrs.readonly}"
					editable="#{cc.attrs.editable}"
					converter="processExpressionConverter"
					rendered="true">
					<f:selectItems value="#{cc.attrs.value.signaturePackagingOptions}" var="signaturePackagingOption"
						itemValue="#{signaturePackagingOption.label}" itemLabel="#{tsksiglbls[signaturePackagingOption.label]}" />
					<p:clientValidator disabled="#{not cc.attrs.clientValidation}" />
					<p:message for="@parent" display="icon" rendered="#{cc.attrs.clientValidation}" />
				</p:selectOneMenu>
			</p:outputPanel>
		</p:fieldset>
		<!-- SIGNED ARTEFACTS -->
		<p:fieldset legend="#{tsksiglbls.signed_artefacts}" toggleable="true"
			styleClass="task-edit-fieldset #{group.getName()!='default' ? null : 'hide-legend'}">
			<p:outputPanel styleClass="form-group" rendered="true">
				<!-- Label -->
				<p:outputLabel styleClass="col-sm-2 control-label" value="#{tsksiglbls.signature_output}" 
				/>
				<!-- Value -->
				<p:selectOneMenu value="#{cc.attrs.value.signatureOutputOptionExpr}" label="blabla" 
					required="#{cc.attrs.required}" readonly="#{cc.attrs.readonly}"
					editable="#{cc.attrs.editable}"
					converter="processExpressionConverter"
					rendered="true"
					>
					<p:ajax partialSubmit="true" event="change" update="ddfdd ddfdd2"/>
					<f:selectItems value="#{cc.attrs.value.signatureOutputOptions}" var="outputOption" 
        				itemValue="#{outputOption.label}" itemLabel="#{tsksiglbls[outputOption.label]}" />
					<p:clientValidator disabled="#{not cc.attrs.clientValidation}" />
					<p:message for="@parent" display="icon" rendered="#{cc.attrs.clientValidation}" />
				</p:selectOneMenu>
			<p:outputPanel id="ddfdd2" >
				<p:outputLabel value="#{tsksiglbls.signature_outputdir_placeholder}" rendered="#{cc.attrs.value.isOutputDirectoryDisabled()}"/>
			</p:outputPanel>
			</p:outputPanel>
			<p:outputPanel>
				<!-- Label -->
				<p:outputLabel styleClass="col-sm-2 control-label" value="#{tsksiglbls.signature_output_dir}" />
				<!-- Value -->
				<p:inputText id="ddfdd" widgetVar="dd" value="#{cc.attrs.value.outputDirectoryExpr}" label="#{comApplicationHandler.label(cc.attrs.label)}"
				    validator="xssValidator" 
					required="#{cc.attrs.required}" readonly="#{cc.attrs.readonly}"  
					styleClass="form-control input-medium jqs#{cc.attrs.name}" 
					pt:placeholder="#{tsksiglbls.signature_outputdir_placeholder}" 
					converter="#{comApplicationHandler.getConverter(cc.attrs.converter)}"
					rendered="true" disabled="#{cc.attrs.value.isOutputDirectoryDisabled()}"><!-- #{cc.attrs.value.signatureOutputOptionExpr == 'CONTEXT'} -->
					<p:clientValidator disabled="#{not cc.attrs.clientValidation}" />
					<p:message for="@parent" display="icon" rendered="#{cc.attrs.clientValidation}" />
				</p:inputText>
				<tsk:fieldset group="#{taskViewModel.getGroup('insertionContext')}"
				renderFields="false">
				<!-- document-query-->
        		<tsk:field
               		value="#{cc.attrs.value}" type="#{cc.attrs.type}" mode="#{cc.attrs.mode}" owner="#{cc.attrs.owner}"
                	multilingual="#{cc.attrs.multilingual}" locale="#{cc.attrs.locale}" locales="#{cc.attrs.locales}"
               	    defaultLocale="#{cc.attrs.defaultLocale}" labelProvider="#{cc.attrs.labelProvider}"
                	 property="#{taskViewModel.getGroup('filter')['queryDocument']}"/>
			   </tsk:fieldset>
			</p:outputPanel>
		</p:fieldset>
	</cc:implementation>
</ui:component>