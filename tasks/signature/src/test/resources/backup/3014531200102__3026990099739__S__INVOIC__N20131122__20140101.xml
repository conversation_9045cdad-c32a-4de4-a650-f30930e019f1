<?xml version="1.0" encoding="iso-8859-1"?>
<Invoice xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd" xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd">
    <InvoiceHeader>
        <InvoiceNumber>N20131122</InvoiceNumber>
        <InvoiceIssueDate>2014-01-01T00:00:00</InvoiceIssueDate>
        <InvoicePurpose>
            <InvoicePurposeCoded>Original</InvoicePurposeCoded>
        </InvoicePurpose>
        <InvoiceType>
            <InvoiceTypeCoded>CommercialInvoice</InvoiceTypeCoded>
        </InvoiceType>
        <InvoiceLanguage>
            <core:LanguageCoded>en</core:LanguageCoded>
        </InvoiceLanguage>
        <InvoiceParty>
            <BuyerParty>
                <core:PartyID>
                    <core:Ident>3026990099739</core:Ident>
                </core:PartyID>
                <core:NameAddress>
                    <core:Name1>The buyer</core:Name1>
                </core:NameAddress>
            </BuyerParty>
            <SellerParty>
                <core:PartyID>
                    <core:Ident>3014531200102</core:Ident>
                </core:PartyID>
                <core:NameAddress>
                    <core:Name1>The seller</core:Name1>
                </core:NameAddress>
            </SellerParty>
            <BillToParty>
                <core:PartyID>
                    <core:Ident>3026990099739</core:Ident>
                </core:PartyID>
                <core:NameAddress>
                    <core:Name1>The invoicee</core:Name1>
                </core:NameAddress>
            </BillToParty>
        </InvoiceParty>
    </InvoiceHeader>
    <InvoiceDetail>
        <ListOfInvoiceItemDetail>
            <InvoiceItemDetail>
                <InvoiceBaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>1</core:BuyerLineItemNum>
                        <core:SellerLineItemNum>1</core:SellerLineItemNum>
                    </LineItemNum>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>GTIN</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifier>N/A</core:ProductIdentifier>
                            </core:StandardPartNumber>
                        </core:PartNumbers>
                    </ItemIdentifiers>
                    <InvoicedQuantity>
                        <core:QuantityValue>0</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>Other</core:UOMCoded>
                        </core:UnitOfMeasurement>
                    </InvoicedQuantity>
                </InvoiceBaseItemDetail>
                <InvoicePricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationNet</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>0</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                    </core:ListOfPrice>
                </InvoicePricingDetail>
            </InvoiceItemDetail>
        </ListOfInvoiceItemDetail>
    </InvoiceDetail>
</Invoice>
