package com.byzaneo.generix.campaign.task.autotest;

import static com.byzaneo.commons.bean.FileType.EXE;
import static com.byzaneo.commons.ui.util.MessageHelper.error;
import static com.byzaneo.commons.ui.util.MessageHelper.getMessage;
import static com.byzaneo.commons.util.I18NHelper.fromJsonLabelSet;
import static com.byzaneo.commons.util.SpringContextHelper.getBean;
import static com.byzaneo.generix.campaign.task.autotest.AutoTestHelper.fromXmlResult;
import static com.byzaneo.generix.commons.task.util.TaskHelper.getWorkDir;
import static com.byzaneo.generix.util.TransformHelper.createRteConfig;
import static java.lang.String.format;
import static java.lang.System.currentTimeMillis;
import static java.nio.file.Files.getPosixFilePermissions;
import static java.nio.file.Files.isExecutable;
import static java.nio.file.Files.setPosixFilePermissions;
import static java.nio.file.Paths.get;
import static java.nio.file.attribute.PosixFilePermission.OWNER_EXECUTE;
import static java.util.Optional.ofNullable;
import static java.util.concurrent.TimeUnit.MINUTES;
import static org.apache.commons.exec.OS.isFamilyWindows;
import static org.apache.commons.io.IOUtils.copy;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.slf4j.LoggerFactory.getLogger;
import static org.springframework.util.Assert.notNull;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.attribute.PosixFilePermission;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.primefaces.event.FileUploadEvent;
import org.slf4j.Logger;

import com.byzaneo.commons.ui.util.JSFHelper;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.generix.bean.InstanceProperties;
import com.byzaneo.generix.campaign.task.autotest.xml.InterchangeTestResult;
import com.byzaneo.generix.service.TransformService;
import com.byzaneo.generix.service.TransformServiceImpl.RteResult;
import com.byzaneo.security.bean.User;
import com.byzaneo.xtrade.bean.DocumentFile;

/**
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date Mar 23, 2013
 * @version 2.2 GNX-274
 */
public class TradeXpressRuntime extends Runtime {
  private static final long serialVersionUID = 3668977282386586502L;

  private static final Logger log = getLogger(TradeXpressRuntime.class);

  public static final String RT_NAME = "TradeXpress";

  public static final String RTE_FOLDER = "rte";

  // - SERVICE -
  private final transient TransformService transformService;

  /** Directory where the RTE are located */
  private static File rteDir;

  // DATA
  private String rtePath;

  private String rteName;

  private long timeOut = MINUTES.toMillis(2L);

  private transient File rte;

  /* -- CONSTRUCTOR -- */

  public TradeXpressRuntime() {
    super(RT_NAME);
    this.transformService = getBean(TransformService.class, TransformService.SERVICE_NAME);
  }

  /** @see com.byzaneo.generix.campaign.task.autotest.Runtime#init() */
  @Override
  public void init() {
    initRteDir();
    if (isNotBlank(rtePath)) {
      rte = new File(rtePath);
    }
  }

  private static void initRteDir() {
    if (rteDir == null) {
      rteDir = new File(getWorkDir("autotest"), RTE_FOLDER);
      rteDir.mkdirs();
    }
  }

  /* -- IMPLEMENTATION -- */

  /** @see com.byzaneo.generix.campaign.task.autotest.Runtime#test(User, File, Instance, List) */
  @Override
  public InterchangeTestResult test(User user, File interchange, Instance instance, List<DocumentFile> documents) throws AutoTestException {

    // Checks permissions
    final Path path = get(rtePath);
    if (!isFamilyWindows() && !isExecutable(path)) {
      // Changing the permissions
      try {
        Set<PosixFilePermission> permissions = getPosixFilePermissions(path);
        permissions.add(OWNER_EXECUTE);
        setPosixFilePermissions(path, permissions);
      }
      catch (IOException ioe) {
        throw new AutoTestException(ioe, "Wrong RTE file permissions for: %s", rtePath);
      }
    }

    // RTE execution
    final RteResult result;
    try {
      final List<String> command = new ArrayList<>();
      command.add("-f");
      command.add(interchange.getAbsolutePath());
      command.add("ENVIRONNEMENT=" + (fromJsonLabelSet(instance.getName(), instance.getConfiguration()
          .getDefaultLanguage(), JSFHelper.getLocale(), "Environment")));
      command.add("ENVIRONNEMENT_CODE=" + instance.getCode());
      command.add("COMPANY=" + instance.getGroup()
          .getFullname());
      command.add("COMPANY_CODE=" + instance.getGroup()
          .getName());
      command.add("PARTNER=" + user.getPrimaryGroup()
          .getFullname());
      command.add("PARTNER_CODE=" + user.getPrimaryGroup()
          .getName());
      command.add("CONTACT=" + user.getFullname());
      command.add("CONTACT_LOGIN=" + user.getLogin());
      command.add("LANG=" + getLang(user, instance));

      // document file parameters
      if (CollectionUtils.isNotEmpty(documents)) {
        for (DocumentFile fileConfig : documents) {
          if (fileConfig.getFile() == null ||
              !fileConfig.getFile()
                  .isFile() ||
              isBlank(fileConfig.getComment())) {
            throw new AutoTestException(
                "RTE Of configuration file=" + fileConfig + " doesn't exist for alias name=" + fileConfig.getComment());
          }
          command.add(format("%s=%s", fileConfig.getComment(), fileConfig.getFile()
              .getAbsolutePath()));
        }
      }
      log.debug("File Encoding java=" + System.getProperty("file.encoding"));

      // creates command transformation engine
      final Map<String, ?> config = createRteConfig(timeOut);

      // runs transformation
      result = transformService.executeRte(config, rte, command.toArray(new String[command.size()]));

      // has errors
      if (!isBlank(result.getError())) {
        throw new AutoTestException("RTE Internal Error %s", result.getError());
      }

      // has no result
      if (isBlank(result.getOutput())) {
        throw new AutoTestException("RTE result is null, internal problem");
      }

    }
    catch (AutoTestException ate) {
      throw ate;
    }
    catch (Exception e) {
      throw new AutoTestException(e, "RTE execution failed: %s (%s)", rtePath, getRootCauseMessage(e));
    }

    // Output validation
    final String validation = AutoTestHelper.validate(result.getOutput()
        .getBytes());
    if (validation != null) {
      throw new AutoTestException(validation);
    }

    // Output marshalling
    try {
      return fromXmlResult(result.getOutput()
          .getBytes());
    }
    catch (Exception e) {
      // @since GNX-393 Non XML RTE return is
      // returned as error message.
      throw new AutoTestException(result.getOutput());
    }
  }

  /* -- ACTIONS -- */

  public List<?> onUploadRte(FileUploadEvent event) {
    this.rte = new File(rteDir, format("RTE-%s%s",
        currentTimeMillis(),
        !isFamilyWindows() ? "" : EXE.getExtension()));
    try (final FileOutputStream out = new FileOutputStream(this.rte)) {
      this.rteName = event.getFile()
          .getFileName();
      this.rtePath = this.rte.getAbsolutePath();
      copy(event.getFile()
          .getInputstream(), out);
    }
    catch (Exception e) {
      error(e, "Error uploading RTE");
      this.rte = null;
      this.rteName = null;
      this.rtePath = null;
    }
    return Collections.emptyList();
  }

  /* -- ACCESSORS -- */

  public File getRte() {
    return rte;
  }

  public void setRte(File rte) {
    this.rte = rte;
  }

  public String getRteName() {
    return rteName;
  }

  public void setRteName(String rteName) {
    this.rteName = rteName;
  }

  public String getRtePath() {
    return rtePath;
  }

  public void setRtePath(String rtePath) {
    this.rtePath = rtePath;
  }

  public long getTimeOut() {
    return timeOut;
  }

  public void setTimeOut(long timeOut) {
    this.timeOut = timeOut;
  }

  /* -- OVERRIDE -- */

  /** @see com.byzaneo.generix.campaign.task.autotest.Runtime#validate() */
  @Override
  public void validate() {
    notNull(rte, getMessage("autotestlbls.error_RTE_missing", "RTE is missing", null));
    notNull(rteName, getMessage("autotestlbls.error_RTE_name_missing", "RTE name is missing", null));
    notNull(rtePath, getMessage("autotestlbls.error_RTE_path_missing", "RTE path is missing", null));
  }

  /** @see com.byzaneo.generix.campaign.task.autotest.Runtime#toString() */
  @Override
  public String toString() {
    return format("%s [rte=%s (%s)]", super.toString(), rteName, rte);
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    if (!super.equals(o)) return false;

    TradeXpressRuntime that = (TradeXpressRuntime) o;

    if (timeOut != that.timeOut) return false;
    if (transformService != null ? !transformService.equals(that.transformService) : that.transformService != null)
      return false;
    if (rtePath != null ? !rtePath.equals(that.rtePath) : that.rtePath != null) return false;
    if (rteName != null ? !rteName.equals(that.rteName) : that.rteName != null) return false;
    return rte != null ? rte.equals(that.rte) : that.rte == null;
  }

  @Override
  public int hashCode() {
    int result = super.hashCode();
    result = 31 * result + (transformService != null ? transformService.hashCode() : 0);
    result = 31 * result + (rtePath != null ? rtePath.hashCode() : 0);
    result = 31 * result + (rteName != null ? rteName.hashCode() : 0);
    result = 31 * result + (int) (timeOut ^ (timeOut >>> 32));
    result = 31 * result + (rte != null ? rte.hashCode() : 0);
    return result;
  }

  /**
   * @param user
   * @param instance
   * @return value for RTE param LANG which is the user's language (if missing, return the environment language)
   */
  private String getLang(User user, Instance instance) {
    return ofNullable(user)
        .map(User::getLocale)
        .map(Locale::getLanguage)
        .orElse(ofNullable(instance)
            .map(Instance::getConfiguration)
            .map(InstanceProperties::getDefaultLanguage)
            .map(Locale::getLanguage)
            .get());
  }
}
