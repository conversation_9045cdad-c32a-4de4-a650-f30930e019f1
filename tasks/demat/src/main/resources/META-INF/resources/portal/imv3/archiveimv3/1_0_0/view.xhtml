<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml"
			  xmlns:f="http://xmlns.jcp.org/jsf/core"
			  xmlns:h="http://xmlns.jcp.org/jsf/html"
			  xmlns:cc="http://xmlns.jcp.org/jsf/composite"
			  xmlns:p="http://primefaces.org/ui"
			  xmlns:gnx="http://webui.generix.com/jsf"
			  xmlns:g="http://xmlns.jcp.org/jsf/composite/components/gnx"
			  xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
			  xmlns:pe="http://primefaces.org/ui/extensions">
	<cc:interface name="invoiceManagerV3View" />
	<cc:implementation>
		<p:outputPanel id="imViewPnl" styleClass="psImViewPnl imv-view-panel">
			<script type="text/javascript">
				function start() {
					PF('statusDialog').show();
				}

				function stop() {
					PF('statusDialog').hide();
					jQuery('.psMenuExport').hide();
					jQuery('.psSearchBtn').click();
				}
			</script>
			<ui:param name="taskProperties" value="#{cc.attrs.value.descriptor.getProperties()}" />
			<ui:param name="taskBean" value="#{cc.attrs.value}" />
			<!-- * LIST * -->
			<p:outputPanel styleClass="psTaskList"
				rendered="#{cc.attrs.value.document==null and cc.attrs.value.relatedList==null}">
				<g:advancedSearch taskBean="#{taskBean}"/>
				<g:indexActions>
						<f:facet name="menuActions">
				           <p:splitButton id="splitButtonId"
				           					widgetVar="splitButtonId"
					            			value="#{comtsklbls.splitButton_choice}"
                                            global="false"
				            		>
				            	<p:menuitem id="actionMenuDownloadCsv"
						            			value="#{gnxxcblorderlbls.download_csv}"
						            			actionListener="#{cc.attrs.value.onDownloadCSVs(cc.attrs.owner)}"
						            			disabled="#{cc.attrs.value.splitMenuDesactive}"
						            			onclick="$('.psMenuExport').hide();"
						            			onstart="jQuery('.psMenuExport').hide(); PrimeFaces.monitorDownload(start,stop);"
						            			oncomplete="$('.jqsZip').click();PF('wResultTable').unselectAllRows()"
						            			update="@(.psTaskList)"/>
					            <p:menuitem id="actionMenuExportList"
					            			value="#{gnxxcblorderlbls.download_list}"
					            			widgetVar="exportButton"
                     						ajax="false">
							        <gnx:dataExporter type="xls"
							                          target="tskForm-archiveimv3View-resultTable"
							                          fileName="listIMV3"/>
					            </p:menuitem>
				    		</p:splitButton>
	                    </f:facet>
	                </g:indexActions>
				<p:dataTable id="resultTable"
					value="#{cc.attrs.value.dataModel}"
					lazy="true"
					var="doc"
					selection="#{cc.attrs.value.multiSelected}"
					scrollable="true"
					scrollWidth="100%"
					paginator="true"
					rows="15"
					paginatorPosition="bottom"
					paginatorTemplate="{CurrentPageReport}  {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
					currentPageReportTemplate="{startRecord}-{endRecord}/{totalRecords}"
					rowsPerPageTemplate="15,50,100,200,500"
					resizableColumns="true"
					rowKey="#{doc.id}"
					styleClass="im-document-table"
					emptyMessage="#{labels.no_records_found}"
					sortBy="#{doc.creationDate}"
					sortOrder="descending"
					widgetVar="wResultTable">
					<p:ajax event="rowSelect"
							update="@(.psImViewPnl)"
							listener="#{cc.attrs.value.onSelectDocument}"/>

					<p:ajax event="rowSelectCheckbox"
                    		listener="#{cc.attrs.value.onCheckSelectBox()}"
                            update="@(.psImViewPnl)"
                            global="false"/>
                    <p:ajax event="rowUnselectCheckbox"
                    	    listener="#{cc.attrs.value.onCheckSelectBox()}"
                            update="@(.psImViewPnl)"
                            global="false"/>
					<p:ajax event="toggleSelect"
                    	    listener="#{cc.attrs.value.onCheckSelectBox()}"
                            update="@(.psImViewPnl)"
                            global="false"/>

					<p:ajax event="rowToggle" immediate="true" process="@this" update="@none"/>
					<p:column style="width:35px">
						<p:rowToggler />
					</p:column>

					<p:column selectionMode="multiple" style="width:50px;text-align:center" />

 					<gnx:pcolumn property="#{taskProperties.get(0)}" />
					<gnx:pcolumn property="#{taskProperties.get(1)}" />
					<gnx:pcolumn property="#{taskProperties.get(2)}" />
					<gnx:pcolumn property="#{taskProperties.get(3)}" />
					<gnx:pcolumn property="#{taskProperties.get(4)}" />
					<gnx:pcolumn property="#{taskProperties.get(5)}" />
					<gnx:pcolumn property="#{taskProperties.get(6)}" />
					<gnx:pcolumn property="#{taskProperties.get(7)}" />
					<gnx:pcolumn property="#{taskProperties.get(8)}" />
					<gnx:pcolumn property="#{taskProperties.get(9)}" />
					<gnx:pcolumn property="#{taskProperties.get(10)}" />
					<gnx:pcolumn property="#{taskProperties.get(11)}" />
					<gnx:pcolumn property="#{taskProperties.get(12)}" />
					<gnx:pcolumn property="#{taskProperties.get(13)}" />
					<gnx:pcolumn property="#{taskProperties.get(14)}" />
					<gnx:pcolumn property="#{taskProperties.get(15)}" />
					<gnx:pcolumn property="#{taskProperties.get(16)}" />
					<gnx:pcolumn property="#{taskProperties.get(17)}" />
					<gnx:pcolumn property="#{taskProperties.get(18)}" />
					<gnx:pcolumn property="#{taskProperties.get(19)}" />
					<gnx:pcolumn property="#{taskProperties.get(20)}" />
					<gnx:pcolumn property="#{taskProperties.get(21)}" />
					<gnx:pcolumn property="#{taskProperties.get(22)}" />
					<gnx:pcolumn property="#{taskProperties.get(23)}" />
					<gnx:pcolumn property="#{taskProperties.get(24)}" />
					<gnx:pcolumn property="#{taskProperties.get(25)}" />
					<gnx:pcolumn property="#{taskProperties.get(26)}" />
					<gnx:pcolumn property="#{taskProperties.get(27)}" />
					<gnx:pcolumn property="#{taskProperties.get(28)}" />
					<gnx:pcolumn property="#{taskProperties.get(29)}" />
					<gnx:pcolumn property="#{taskProperties.get(30)}" />
					<gnx:pcolumn property="#{taskProperties.get(31)}" />
					<gnx:pcolumn property="#{taskProperties.get(32)}" />
					<gnx:pcolumn property="#{taskProperties.get(33)}" />
					<gnx:pcolumn property="#{taskProperties.get(34)}" />
					<gnx:pcolumn property="#{taskProperties.get(35)}" />
					<gnx:pcolumn property="#{taskProperties.get(36)}" />
					<gnx:pcolumn property="#{taskProperties.get(37)}" />
					<gnx:pcolumn property="#{taskProperties.get(38)}" />
					<gnx:pcolumn property="#{taskProperties.get(39)}" />
					<gnx:pcolumn property="#{taskProperties.get(40)}" />
					<gnx:pcolumn property="#{taskProperties.get(41)}" />
					<gnx:pcolumn property="#{taskProperties.get(42)}" />
					<gnx:pcolumn property="#{taskProperties.get(43)}" />

					<p:column styleClass="center" exportable="false" priority="1"
						style="#{taskBean.responsive ? '' : 'width:150px'}">
						<p:commandButton id="docInvoice"
                        	class="btn btn-default"
                            title="#{dmttsklbls.action}" icon="fa fa-bars" global="false"
                            style="height:34px;width:35px;pointer-events: all;-webkit-border-radius:4px !important;"
                            onclick="PF('wResultTable').unselectAllRows();"/>
                        <p:menu styleClass="psMenuDoc xcbltask-tieredmenu"
                            overlay="true" trigger="docInvoice" my="left top"
                            at="left bottom">
                            <!-- overflow: auto; max-height: 80px;" -->
                            <p:menuitem id="linkedDocsMenuItemId"
								value="#{dmttsklbls.im_linkeddocs}"
								process="@this resultTable"
								update="@(.psImViewPnl)"
								action="#{cc.attrs.value.onViewRelated(true)}"
								actionListener="#{cc.attrs.value.setSelected(doc)}"/>
							<p:menuitem id="attachedDocsMenuItemId"
								value="#{dmttsklbls.im_attacheddocs}"
								process="@this resultTable"
								update="@(.psImViewPnl)"
								action="#{cc.attrs.value.onViewRelated(false)}"
								actionListener="#{cc.attrs.value.setSelected(doc)}"/>
                        </p:menu>
					</p:column>

					<p:rowExpansion>
						<h:panelGrid columns="8" styleClass="im-document-detail">

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['number'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.number != null and cc.attrs.value.descriptor['number'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.number}"
								rendered="#{doc.number != null and cc.attrs.value.descriptor['number'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['date'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.date != null and cc.attrs.value.descriptor['date'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.date}"
								rendered="#{doc.date != null and cc.attrs.value.descriptor['date'].rendered}">
								<f:convertDateTime locale="#{gnxSessionHandler.locale}" type="date"
									timeZone="#{secSessionHandler.timeZone}"
									pattern="#{gnxSessionHandler.userDateFormat}" />
							</h:outputText>

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['creationDate'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.creationDate != null and cc.attrs.value.descriptor['creationDate'].rendered }" styleClass="bold" />
							<h:outputText value="#{doc.creationDate}"
								rendered="#{doc.creationDate != null and cc.attrs.value.descriptor['creationDate'].rendered}">
								<f:convertDateTime locale="#{gnxSessionHandler.locale}" type="both" timeStyle="short"
									timeZone="#{secSessionHandler.timeZone}"
									pattern="#{gnxSessionHandler.userDateFormat}" />
							</h:outputText>

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['type'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.type != null and cc.attrs.value.descriptor['type'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{taskBean.label(doc.type)}"
								rendered="#{doc.type != null and cc.attrs.value.descriptor['type'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['owners'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.owners != null and cc.attrs.value.descriptor['owners'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.owners}"
								rendered="#{doc.owners != null and cc.attrs.value.descriptor['owners'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['ownersName'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.ownersName != null and cc.attrs.value.descriptor['ownersName'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.ownersName}"
								rendered="#{doc.ownersName != null and cc.attrs.value.descriptor['ownersName'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['to'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.to != null and cc.attrs.value.descriptor['to'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.to}"
								rendered="#{doc.to != null and cc.attrs.value.descriptor['to'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['toName'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.toName != null and cc.attrs.value.descriptor['toName'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.toName}"
								rendered="#{doc.toName != null and cc.attrs.value.descriptor['toName'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['dematState'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.dematState != null and cc.attrs.value.descriptor['dematState'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{taskBean.label(doc.dematState)}" styleClass="#{doc.dematState}"
								rendered="#{doc.dematState != null and cc.attrs.value.descriptor['dematState'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['extState'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.extState != null and cc.attrs.value.descriptor['extState'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{dmttsklbls[doc.extState]}"
								rendered="#{doc.extState != null and cc.attrs.value.descriptor['extState'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['storedState'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.storedState != null and cc.attrs.value.descriptor['storedState'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{taskBean.label(doc.storedState)}"
								rendered="#{doc.storedState != null and cc.attrs.value.descriptor['storedState'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['total'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.total != null and cc.attrs.value.descriptor['total'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.total}"
								rendered="#{doc.total != null and cc.attrs.value.descriptor['total'].rendered}">
								<f:convertNumber locale="#{gnxSessionHandler.locale}" />
							</h:outputText>

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['archName'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.archName != null and cc.attrs.value.descriptor['archName'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.archName}"
								rendered="#{doc.archName != null and cc.attrs.value.descriptor['archName'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['format'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.format != null and cc.attrs.value.descriptor['format'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.format}"
								rendered="#{doc.format != null and cc.attrs.value.descriptor['format'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['version'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.version != null and cc.attrs.value.descriptor['version'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.version}"
								rendered="#{doc.version != null and cc.attrs.value.descriptor['version'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['from'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.from != null and cc.attrs.value.descriptor['from'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.from}"
								rendered="#{doc.from != null and cc.attrs.value.descriptor['from'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['fromName'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.fromName != null and cc.attrs.value.descriptor['fromName'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.fromName}"
								rendered="#{doc.fromName != null and cc.attrs.value.descriptor['fromName'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['storedDate'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.storedDate != null and cc.attrs.value.descriptor['storedDate'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.storedDate}"
								rendered="#{doc.storedDate != null and cc.attrs.value.descriptor['storedDate'].rendered}">
								<f:convertDateTime locale="#{gnxSessionHandler.locale}" type="both" timeStyle="short"
									timeZone="#{secSessionHandler.timeZone}"
									pattern="#{gnxSessionHandler.userDateFormat}" />
							</h:outputText>

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['msgRef'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}"
								rendered="#{doc.msgRef != null and cc.attrs.value.descriptor['msgRef'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.msgRef}"
								rendered="#{doc.msgRef != null and cc.attrs.value.descriptor['msgRef'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['ichgId'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}"
								rendered="#{doc.ichgId != null and cc.attrs.value.descriptor['ichgId'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.ichgId}"
								rendered="#{doc.ichgId != null and cc.attrs.value.descriptor['ichgId'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['text01'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.text01 != null and cc.attrs.value.descriptor['text01'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.text01}"
								rendered="#{doc.text01 != null and cc.attrs.value.descriptor['text01'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['text02'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.text02 != null and cc.attrs.value.descriptor['text02'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.text02}"
								rendered="#{doc.text02 != null and cc.attrs.value.descriptor['text02'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['text03'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.text03 != null and cc.attrs.value.descriptor['text03'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.text03}"
								rendered="#{doc.text03 != null and cc.attrs.value.descriptor['text03'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['text04'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.text04 != null and cc.attrs.value.descriptor['text04'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.text04}"
								rendered="#{doc.text04 != null and cc.attrs.value.descriptor['text04'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['text05'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.text05 != null and cc.attrs.value.descriptor['text05'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.text05}"
								rendered="#{doc.text05 != null and cc.attrs.value.descriptor['text05'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['text06'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.text06 != null and cc.attrs.value.descriptor['text06'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.text06}"
								rendered="#{doc.text06 != null and cc.attrs.value.descriptor['text06'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['text07'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.text07 != null and cc.attrs.value.descriptor['text07'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.text07}"
								rendered="#{doc.text07 != null and cc.attrs.value.descriptor['text07'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['text08'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.text08 != null and cc.attrs.value.descriptor['text08'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.text08}"
								rendered="#{doc.text08 != null and cc.attrs.value.descriptor['text08'].rendered}" />

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['date01'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.date01 != null and cc.attrs.value.descriptor['date01'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.date01}"
								rendered="#{doc.date01 != null and cc.attrs.value.descriptor['date01'].rendered}">
								<f:convertDateTime locale="#{gnxSessionHandler.locale}" type="both" timeStyle="medium"
									timeZone="#{secSessionHandler.timeZone}"
									pattern="#{gnxSessionHandler.userDateFormat}" />
							</h:outputText>

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['date02'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.date02 != null and cc.attrs.value.descriptor['date02'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.date02}"
								rendered="#{doc.date02 != null and cc.attrs.value.descriptor['date02'].rendered}">
								<f:convertDateTime locale="#{gnxSessionHandler.locale}" type="both" timeStyle="medium"
									timeZone="#{secSessionHandler.timeZone}"
									pattern="#{gnxSessionHandler.userDateFormat}" />
							</h:outputText>

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['date03'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.date03 != null and cc.attrs.value.descriptor['date03'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.date03}"
								rendered="#{doc.date03 != null and cc.attrs.value.descriptor['date03'].rendered}">
								<f:convertDateTime locale="#{gnxSessionHandler.locale}" type="both" timeStyle="medium"
									timeZone="#{secSessionHandler.timeZone}"
									pattern="#{gnxSessionHandler.userDateFormat}" />
							</h:outputText>

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['date04'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.date04 != null and cc.attrs.value.descriptor['date04'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.date04}"
								rendered="#{doc.date04 != null and cc.attrs.value.descriptor['date04'].rendered}">
								<f:convertDateTime locale="#{gnxSessionHandler.locale}" type="both" timeStyle="medium"
									timeZone="#{secSessionHandler.timeZone}"
									pattern="#{gnxSessionHandler.userDateFormat}" />
							</h:outputText>

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['float01'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.float01 != null and cc.attrs.value.descriptor['float01'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.float01}"
								rendered="#{doc.float01 != null and cc.attrs.value.descriptor['float01'].rendered}">
								<f:convertNumber locale="#{gnxSessionHandler.locale}" />
							</h:outputText>

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['float02'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.float02 != null and cc.attrs.value.descriptor['float02'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.float02}"
								rendered="#{doc.float02 != null and cc.attrs.value.descriptor['float02'].rendered}">
								<f:convertNumber locale="#{gnxSessionHandler.locale}" />
							</h:outputText>
							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['float03'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.float03 != null and cc.attrs.value.descriptor['float03'].rendered}" styleClass="bold" />
							<h:outputText value="#{doc.float03}"
								rendered="#{doc.float03 != null and cc.attrs.value.descriptor['float03'].rendered}">
								<f:convertNumber locale="#{gnxSessionHandler.locale}" />
							</h:outputText>

							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['changeStatusLogin'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.changeStatusLogin != null and cc.attrs.value.descriptor['changeStatusLogin'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.changeStatusLogin}"
								rendered="#{doc.changeStatusLogin != null and cc.attrs.value.descriptor['changeStatusLogin'].rendered}" />
							<h:outputText
								value="#{gnxHandler.label(cc.attrs.value.descriptor['changeStatusDate'].label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}:"
								rendered="#{doc.changeStatusDate != null and cc.attrs.value.descriptor['changeStatusDate'].rendered}" styleClass="bold" />
							<h:outputText
								value="#{doc.changeStatusDate}"
								rendered="#{doc.changeStatusDate != null and cc.attrs.value.descriptor['changeStatusDate'].rendered}">
								<f:convertDateTime locale="#{gnxSessionHandler.locale}" type="date"
									timeZone="#{secSessionHandler.timeZone}"
									pattern="#{gnxSessionHandler.userDateFormat}" />
							</h:outputText>
						</h:panelGrid>
					</p:rowExpansion>

				</p:dataTable>
			</p:outputPanel>
			<!-- * DOCUMENT ARCHIVE * -->
			<h:panelGroup rendered="#{cc.attrs.value.document!=null}">
				<p:panelGrid style="margin-bottom:10px;">
					<p:row>
						<p:column>
							<h:outputText
								value="#{taskBean.label(cc.attrs.value.document.type)} n°"
								styleClass="title2" />
							<h:outputText value="#{cc.attrs.value.indexable.number}"
								styleClass="title2" />
							<h:outputText
								rendered="#{cc.attrs.value.indexable.dematState!=''}"
								value=" (#{taskBean.label(cc.attrs.value.indexable.dematState)})"
								styleClass="#{cc.attrs.value.indexable.dematState}" />
						</p:column>
						<p:column styleClass="right">
							<p:commandButton id="exportButtonId"
								icon="ui-icon-arrowthickstop-1-s"
								value="#{comApplicationHandler.label('labels','export', gnxSessionHandler.locale)}"
								ajax="false" process="@this" immediate="true" styleClass="mls">
								<p:fileDownload value="#{cc.attrs.value.onExportArchiveFile()}" />
							</p:commandButton>
							<p:commandButton id="prevButtonId" icon="ui-icon-seek-prev"
								value="#{comApplicationHandler.label('labels','back', gnxSessionHandler.locale)}"
								process="@this" immediate="true" update="imViewPnl"
								styleClass="mls">
								<f:setPropertyActionListener target="#{cc.attrs.value.document}"
									value="#{null}" />
								<f:setPropertyActionListener target="#{cc.attrs.value.tabKeys}"
									value="#{null}" />
							</p:commandButton>
						</p:column>
					</p:row>
				</p:panelGrid>
				<p:tabView id="imDocumentTabs" dynamic="true" cache="false"
					value="#{cc.attrs.value.getTabKeys(cc.attrs.owner)}" var="tabKey">
					<p:ajax event="tabChange"
						listener="#{cc.attrs.value.onSelectedTabChange}" />
					<p:tab
						title="#{gnxHandler.label(cc.attrs.value.getTabHeader(tabKey), gnxSessionHandler.locale, cc.attrs.defaultLocale)}">
						<pe:codeMirror
							value="#{cc.attrs.value.getArchiveFileContent(tabKey)}"
							mode="xml" theme="default" lineNumbers="true"
							lineWrapping="false" readonly="true"
							rendered="#{not cc.attrs.value.isPDFArchiveFile(tabKey)}" />
						<p:media
							value="#{cc.attrs.value.getArchiveFileUrl(tabKey)}&amp;download=false"
							player="pdf" width="100%" height="1000"
							rendered="#{cc.attrs.value.isPDFArchiveFile(tabKey)}">
							<h:outputText value="Sorry, your browser can't display PDF, " />
							<h:outputLink value="#{cc.attrs.value.getArchiveFileUrl(tabKey)}">
								<h:outputText value="click" />
							</h:outputLink>
							<h:outputText value=" to download pdf instead." />
						</p:media>
					</p:tab>
				</p:tabView>
			</h:panelGroup>
			<!-- * RELATED DOCUMENTS * -->
			<h:panelGroup rendered="#{cc.attrs.value.relatedList!=null}">
				<ui:include src="view-related.xhtml">
					<ui:param name="imTask" value="#{cc.attrs.value}" />
				</ui:include>
			</h:panelGroup>
			<!-- RESTITUTION -->
			<p:dialog modal="true" widgetVar="statusDialog"
                header="#{gnxxcblcomlbls.downloading}" draggable="false"
                closable="false" resizable="false">
                <p:graphicImage name="/images/ajaxloadingbar.gif" width="100%" height="19px"/>
            </p:dialog>
			<p:commandLink ajax="false" styleClass="jqsZip"
                onclick="PrimeFaces.monitorDownload(start, stop);"
                style="visibility:hidden;">
                <p:fileDownload value="#{cc.attrs.value.getZipFile()}" />
            </p:commandLink>
            
            <!-- LOADING -->
            <g:loading />
            
		</p:outputPanel>
	</cc:implementation>
</ui:component>