package com.byzaneo.generix.demat.task;

import static com.byzaneo.commons.bean.FileType.EXCEL;
import static com.byzaneo.commons.bean.FileType.PDF;
import static com.byzaneo.commons.ui.FileResourceHandler.createResourceRequestPath;
import static com.byzaneo.commons.ui.util.EmptyPersistentDataModel.emptyDataModel;
import static com.byzaneo.commons.ui.util.JSFHelper.getManagedBean;
import static com.byzaneo.commons.ui.util.MessageHelper.error;
import static com.byzaneo.commons.ui.util.MessageHelper.getMessage;
import static com.byzaneo.commons.ui.util.MessageHelper.warn;
import static com.byzaneo.commons.util.BeanDescriptorHelper.newDescriptor;
import static com.byzaneo.commons.util.I18NHelper.createLabelSet;
import static com.byzaneo.commons.util.I18NHelper.toJsonLabelSet;
import static com.byzaneo.generix.demat.util.ArchiveHelper.getFile;
import static com.byzaneo.generix.demat.util.ArchiveHelper.hasFile;
import static com.byzaneo.generix.demat.util.ArchiveHelper.hasFileType;
import static com.byzaneo.generix.xtrade.util.DocumentPdfHelper.watermarkPDF;
import static com.byzaneo.generix.xtrade.util.IndexableTaskHelper.getFontAwesomeIcon;
import static com.byzaneo.query.builder.Clauses.gt;
import static com.byzaneo.query.builder.QueryBuilder.createBuilder;
import static com.byzaneo.task.util.TaskHelper.error;
import static com.byzaneo.xtrade.api.DocumentType.INVOIC;
import static com.byzaneo.xtrade.util.DocumentHelper.isEqualsStatusCode;
import static java.lang.System.currentTimeMillis;
import static java.nio.charset.Charset.defaultCharset;
import static java.nio.file.Files.delete;
import static java.util.Arrays.asList;
import static java.util.Arrays.stream;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isEmpty;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.slf4j.LoggerFactory.getLogger;

import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.*;

import com.byzaneo.commons.bean.*;
import com.byzaneo.commons.dao.mongo.PageRequest;
import com.byzaneo.commons.util.FileHelper;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.generix.demat.service.DematService;
import com.byzaneo.generix.edocument.SafeboxException;
import com.byzaneo.generix.edocument.bean.*;
import com.byzaneo.generix.edocument.service.SafeboxService;
import com.byzaneo.generix.service.VariablesService;
import com.byzaneo.generix.xtrade.task.AbstractIndexableDocumentTask;
import com.byzaneo.generix.xtrade.util.BirtFile;
import com.byzaneo.query.Query;
import com.byzaneo.query.Query.ContextKey;
import com.byzaneo.query.builder.QueryBuilder;
import com.byzaneo.security.ui.handler.SessionHandler;
import com.byzaneo.task.annotation.*;
import com.byzaneo.xtrade.api.*;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.ui.*;

import org.apache.commons.beanutils.PropertyUtilsBean;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.primefaces.event.*;
import org.primefaces.model.*;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;
import org.springframework.data.domain.Page;

import com.ibm.icu.util.Calendar;

/**
 * Allows to display the Invoice Manager documents.
 *
 * <AUTHOR> Rossi <<EMAIL>>
 * @company Byzaneo
 * @date Apr 10, 2014
 * @since 3.0 GNX-1112
 */
@Task(name = "archiveimv3", library = "portal/imv3", version = "1.0.0")
@TaskViewModel(labelFamily = "dmttsklbls")
public class InvoiceDematTask extends AbstractIndexableDocumentTask<Invoice> {
  private static final long serialVersionUID = -8669497610886896943L;

  private static final Logger log = getLogger(InvoiceDematTask.class);

  private static final String IM_LABELS_PREFIX = "im_";

  private static final int BUFFER_MAX_SIZE = 2048;

  protected static final String PATTERN_TIME = "HHmmss";

  // SERVICES
  @Autowired
  @Qualifier(DematService.SERVICE_NAME)
  private transient DematService dematService;

  @Autowired
  @Qualifier(SafeboxService.SERVICE_NAME)
  private transient SafeboxService safeboxService;

  @Autowired
  @Qualifier(VariablesService.SERVICE_NAME)
  private transient VariablesService variablesService;

  // -- DATA --
  private BeanDescriptor tabs;


  private Boolean duplicata;

  // -- EDIT --

  // -- VIEW --
  // archive files
  private transient File archiveDir;

  private transient File archiveFile;

  private transient Boolean read;

  // advanced search
  private transient boolean attachments;

  private transient boolean links;

  // tabs
  private transient List<String> tabKeys;

  private transient String tab;

  // related documents
  private transient boolean linked;

  private transient List<InvoiceRelated> relatedList;

  private transient InvoiceRelated related;

  // Propriétés de la classe cible (IndexableDocument) auxquelles un
  // "rendered" est évalué pour afficher ou pas le composant
  private static final List<String> RENDERED_PROPERTIES_DOC = asList("safe", "linked", "attached", "status");

  private static final List<String> SORTED_PROPERTIES = asList("creationDate", "number", "type", "owners", "ownersName", "to", "toName",
      "extState", "format", "from", "fromName");

  private static final List<String> NOT_EXPORTABLE_PROPERTIES = asList("dematState", "extState");

  private static final List<String> MULTIPLE_VALUES_PROPERTIES = asList("dematState", "extState", "storedState", "type", "status");

  private static final List<String> SAFE_BOX_PROPERTIES = asList("dematState", "extState", "storedState", "msgRef", "ichgId", "version",
      "creationDate", "storedDate", "total", "links",
      "attachments", "date01", "date02", "date03", "date04", "float01", "float02", "float03");

  protected static final List<String> PROPERTIES_TO_EXCLUDE = asList("id", "class", "entityRef", "entityClass",
      "entityId", "archiveStatus", "stage");

  private static final Map<String, List<String>> VALUES_PROPERTIES;

  private static final Map<String, String> STYLE_CLASS_VALUES;

  static {
    STYLE_CLASS_VALUES = new HashMap<>();
    STYLE_CLASS_VALUES.put("safe", "ui-icon ui-icon-suitcase");
    STYLE_CLASS_VALUES.put("linked", "ui-icon ui-icon-link");
    STYLE_CLASS_VALUES.put("attached", "ui-icon ui-icon-tag");

    VALUES_PROPERTIES = new HashMap<>();
    VALUES_PROPERTIES.put("dematState", asList("DEMAT_OK", "DEMAT_CORRECTED", "DEMAT_IN_PROGRESS", "DEMAT_REJECTED"));
    VALUES_PROPERTIES.put("extState", asList("PENDING", "VALIDATED", "CONFLICTING", "PAYING"));
    VALUES_PROPERTIES.put("storedState", asList("STORED", "CDC_ART", "CDC_ARF"));
    VALUES_PROPERTIES.put("type", asList("APERAK", "CREADV", "CREADV_AUTO", "CREADV_AUTO_DU", "CREADV_DU", "CREADV_NO", "DEBADV",
        "DEBADV_NO", "INVOIC", "INVOIC_AF", "INVOIC_AUTO",
        "INVOIC_AUTO_DU", "INVOIC_DU", "INVOIC_FI", "INVOIC_PF", "INVOIC_PF_DU"));
    // GNX-1691
    VALUES_PROPERTIES.put("status", asList("READ", "NONE"));
  }

  @Override
  protected DocumentStatus getDocumentInitialStatus() {
    return DocumentStatus.NONE;
  }

  /*
   * -- ACTIONS --
   */

  // ** EDIT **

  public void onAddTab(Instance instance) {
    if (isBlank(this.tab) || instance == null) {
      return;
    }
    if (this.tabs.containsKey(tab)) {
      warn(getMessage("labels.dmttsk-labels.im_tab_already_exist", "Tab already exists (%s)", null), tab);
      return;
    }
    this.tabs.addProperty(new PropertyDescriptor(this.tab, toJsonLabelSet(createLabelSet(null, null, this.tab, instance.getConfiguration()
        .getLanguages())), true));
    this.tab = null;
  }

  // ** VIEW **

  // - RETURN -

  // - SELECT -

  public Object onSelectDocument(SelectEvent event) {
    this.indexable = (Invoice) event.getObject();
    this.archiveFile = null;
    try {
      read = isEqualsStatusCode(indexable.getStatus(), DocumentStatus.READ);
      if (!read) {
        SessionHandler session = getManagedBean(SessionHandler.class, SessionHandler.MANAGED_BEAN_NAME);
        this.indexable.setChangeStatusLogin(session.getUserLogin());
        this.indexable.setChangeStatusDate(new Date(currentTimeMillis()));
      }
      this.document = this.dematService.getInvoiceDocument(indexable);
      this.archiveDir = this.dematService.getInvoiceArchiveDirectory(this.document);

      // check if the document was read by a partner contact and was not
      // read before now.
      updateStatus();
    }
    catch (SafeboxException se) {
      error(se, getMessage("labels.dmttsk-labels.im_safebox_get_archive_error", "Error while retrieving archive from safebox (%s)", null),
          getRootCauseMessage(se));
      log.error("Failed to select document", se);
      this.document = null;
    }
    catch (Exception e) {
      error(e, getMessage("labels.dmttsk-labels.im_load_archive_error", "Error while loading archive (%s)", null), getRootCauseMessage(e));
      log.error("Failed to select document", e);
      this.document = null;
    }
    return null;
  }

  @Override
  public Map<Object, String> getValues(String label) {
    List<String> list;
    if ("status".equals(label)) {
      list = VALUES_PROPERTIES.get("status");
      return getStatusesAsMapOfCodeObject(list);
    }
    return Collections.emptyMap();
  }

  // - SEARCH -
  @Override
  public void onAdvancedSearchModel() {

    // Search in database
    this.selected = null;
    this.filtered = null;
    try {
      Query query = super.resolveSearchQuery();
      if (this.links || this.attachments) {
        final QueryBuilder qb = QueryBuilder.createBuilder(query);
        if (this.links) {
          qb.and(gt("link", 0));
        }
        if (this.attachments) {
          qb.and(gt("attachment", 0));
        }
        query = qb.query();
      }
      this.dataModel = new InvoiceDataModel(query);
    }
    catch (Exception e) {
      error(e, getMessage("labels.dmttsk-labels.im_search_error", "Error while searching (%s)", null), getRootCauseMessage(e));
      this.dataModel = emptyDataModel();
    }
  }

  @Override
  public void onResetAdvancedSearch() {
    super.onResetAdvancedSearch();
    attachments = links = false;
  }

  // - LINKED/ATTACHED -

  public void onViewRelated(boolean linked) {
    this.indexable = (Invoice) this.getSelected();
    this.linked = linked;
    this.relatedList = new ArrayList<>(linked ? this.indexable.getLinks() : this.indexable.getAttachments());
    this.related = null;
  }

  public Object onSelectRelated(SelectEvent event) {
    this.related = (InvoiceRelated) event.getObject();
    return null;
  }

  // - EXPORT -

  public void onCheckSelectBox() {
  }

  public void onDownloadCSVs(Instance instance) {
    onDownloads(instance, INVOIC.name(), EXCEL);
  }

  @Override
  public void onDownloads(Instance instance, String docType, FileType fileType) {
    if (isItemSelected()) {
      generateMultiSelectionZipFile(instance, docType, fileType);
      birtFile = null;
    }
  }

  @Override
  public void generateMultiSelectionZipFile(Instance instance, String type, FileType fileType) {
    Calendar cal = Calendar.getInstance();
    zipFile = new File(new StringBuilder("export")
        .append("-")
        .append(StringUtils.defaultIfBlank(type, "unknown"))
        .append("-")
        .append(new SimpleDateFormat(PATTERN_DATE).format(cal.getTime()))
        .append("-")
        .append(new SimpleDateFormat(PATTERN_TIME).format(cal.getTime()))
        .append(FileType.ARCHIVE.getExtension())
        .toString());
    try (FileOutputStream fos = new FileOutputStream(zipFile);
        ZipOutputStream out = new ZipOutputStream(new BufferedOutputStream(fos))) {
      byte[] data = new byte[BUFFER_MAX_SIZE];
      for (Indexable selected : selected) {
        File file = generateDocumentFile(instance, (Invoice) selected, fileType);
        ZipEntry entry = new ZipEntry(file.getName());
        out.putNextEntry(entry);

        writeInZip(file, out, data);

        document = dematService.getInvoiceDocument((Invoice) selected);
        archiveDir = dematService.getInvoiceArchiveDirectory(document);
        // unzip
        File archive = new File(archiveDir + FileType.ARCHIVE.getExtension());
        File archiveDezip = new File(archiveDir + "-unzip");
        FileHelper.unzip(archive, archiveDezip, null);
        List<File> listFileArchive = Arrays.asList(FileHelper.getListByAscendingDate(archiveDezip));
        List<File> listFileValidArchive = new LinkedList<>();
        validatePathFileForArchive(instance, listFileArchive, listFileValidArchive);

        // zip
        File archive2 = new File(archiveDir.getPath() + "_export" + FileType.ARCHIVE.getExtension());
        try (ZipOutputStream outArch = new ZipOutputStream(new FileOutputStream(archive2))) {
          byte[] dataArch = new byte[BUFFER_MAX_SIZE];
          for (File validFile : listFileValidArchive) {
            String pathFile = validFile.getPath()
                .replace(archiveDezip.getPath() + "\\", "");
            ZipEntry entryValidFile = new ZipEntry(pathFile);
            outArch.putNextEntry(entryValidFile);

            writeInZip(validFile, outArch, dataArch);
          }
        }

        entry = new ZipEntry(archive2.getName());
        out.putNextEntry(entry);

        writeInZip(archive2, out, data);

        delete(archive2.toPath());
      }
    }
    catch (Exception e) {
      error(this, e, "xtdtsklbls.error_processing_document", this.document.getReference(), getRootCauseMessage(e));
    }
    finally {
      document = null;
      selected = null;
    }
  }

  protected void validatePathFileForArchive(Instance instance, List<File> files, List<File> listFile) {
    Iterator<File> it = files.iterator();
    while (it.hasNext()) {
      File file = it.next();
      if (file.listFiles() != null &&
          file.listFiles().length > 0)
        validatePathFileForArchive(instance, Arrays.asList(file.listFiles()), listFile);
      if (file.isDirectory())
        continue;
      String path = file.getPath()
          .replace("\\", "\\\\");
      List<String> validPath = getTabKeys(instance).stream()
          .filter(o -> (path.indexOf(o) > -1 && path.indexOf("files") > -1) || path.indexOf("files") == -1)
          .collect(Collectors.toList());
      if (!validPath.isEmpty())
        listFile.add(file);
    }
  }

  protected void writeInZip(File file, ZipOutputStream out, byte[] data) throws IOException {
    try (InputStream origin = new BufferedInputStream(new FileInputStream(file), BUFFER_MAX_SIZE)) {
      for (int count; (count = origin.read(data, 0, BUFFER_MAX_SIZE)) != -1; out.write(data, 0, count)) {
      }
    }
  }

  protected File generateDocumentFile(Instance instance, Invoice invoice, FileType type) throws Exception {
    birtFile = new BirtFile(this.dematService.getInvoiceDocument(invoice)
        .getFirstFile(), type);
    return birtFile.getFile();
  }

  // - RESTITUTION -

  @Override
  public StreamedContent getZipFile() {
    if (zipFile == null) {
      return null;
    }
    try {
      return new DefaultStreamedContent(
          new FileInputStream(zipFile),
          FileType.ARCHIVE.getDefaultMime(),
          zipFile.getName());
    }
    catch (Exception e) {
      error(this, e, "Unable to get Zip file", getRootCauseMessage(e));
      return null;
    }
    finally {
      zipFile = null;
    }
  }

  /*
   * -- UTILS --
   */

  /** @see com.byzaneo.generix.xtrade.task.AbstractIndexableTask#resolveDataModel() */
  @Override
  protected LazyDataModel<Invoice> resolveDataModel() {
    return new InvoiceDataModel(resolveBaseQuery());
  }

  /*
   * -- ACCESSORS --
   */

  // ** EDIT **

  // - COLUMNS -

  @Override
  public BeanDescriptor getDescriptor(Instance instance) {
    validate(getDescriptor().getProperties(), IM_LABELS_PREFIX, instance);
    return descriptor;
  }

  // - TABS -

  public BeanDescriptor getTabs() {
    return tabs;
  }

  public void setTabs(BeanDescriptor tabs) {
    this.tabs = tabs;
  }

  public BeanDescriptor getTabs(Instance instance) {
    if (tabs == null) {

      tabs = stream(InvoiceTab.values())
          .map(itab -> createTab(instance, itab))
          .collect(BeanDescriptor::new, BeanDescriptor::addProperty, BeanDescriptor::putAll);
    }
    validate(tabs.getProperties(), IM_LABELS_PREFIX + "tab_", instance);
    return tabs;
  }

  // - SAFE -

  public Boolean getDuplicata() {
    return duplicata == null ? false : duplicata;
  }

  public void setDuplicata(Boolean duplicata) {
    this.duplicata = duplicata;
  }

  public String getArchiveDetails(Document doc) {
    if (doc == null) {
      return null;
    }
    PropertyGroup pg = variablesService.getPropertyGroup(doc.getNotNullModel()
        .getPropertyValue("channelId"));

    StringBuilder sb = new StringBuilder();
    if (pg != null) {
      sb.append(pg.getPropertyValue("channel_type"))//
          .append(" - ");
    }
    return sb.append(doc.getReference())//
        .toString();
  }

  // ** VIEW **

  // -- ARCHIVE --

  public boolean isArchiveFileRendered(String folder) {
    return hasFile(archiveDir, folder);
  }

  public boolean isPDFArchiveFile(String folder) {
    return hasFileType(archiveDir, folder, PDF);
  }

  public String getArchiveFileContent(String folder) {
    this.archiveFile = getFile(archiveDir, folder);
    if (this.archiveFile == null) {
      return null;
    }
    try (FileInputStream fis = new FileInputStream(this.archiveFile)) {
      return IOUtils.toString(fis, defaultCharset());
    }
    catch (IOException e) {
      log.error("Failed to get archive file content", e);
      return null;
    }
  }

  public String getArchiveFileUrl(String folder) {
    try {
      this.archiveFile = getFile(archiveDir, folder);
      if ("pivotpdf".equals(tab) && read && duplicata) {
        this.archiveFile = getDuplicatePDFFile();
      }
      return createResourceRequestPath(this.archiveFile, null, null);
    }
    catch (IOException e) {
      error(e, getMessage("labels.dmttsk-labels.im_export_archive_error", "Error while exporting archive file %s : %s", null), tab,
          getRootCauseMessage(e));
      log.error("Failed to get archive url", e);
      return "";
    }
  }

  public File getArchiveFile() {
    return archiveFile;
  }

  public StreamedContent onExportArchiveFile() {
    // if selectedTab empty, we take the first tab
    try {
      if (isEmpty(tab)) {
        tab = tabKeys.get(0);
      }

      File file = getFile(archiveDir, tab);

      if (file != null && "pivotpdf".equals(tab) && read && duplicata) {
        file = getDuplicatePDFFile();
      }

      if (file == null) {
        return null;
      }

      return new DefaultStreamedContent(new FileInputStream(file), FileType.getType(file)
          .getDefaultMime(), file.getName());
    }
    catch (IOException e) {
      error(e, getMessage("labels.dmttsk-labels.im_export_archive_error", "Error while exporting archive file %s : %s", null), tab,
          getRootCauseMessage(e));
      log.error("Failed to export archive file", e);
      return null;
    }
  }

  private PropertyDescriptor createTab(Instance instance, InvoiceTab itab) {
    return new PropertyDescriptor(itab.toString(),
        getPropertyLabel(instance, IM_LABELS_PREFIX + "tab_", itab.toString(), itab.toString(), null), true);
  }

  private File getDuplicatePDFFile() throws IOException {
    File output;
    if (this.archiveFile.getName()
        .contains("duplicata_")) {
      output = new File(FileHelper.getTempDirectory()
          .getPath(), this.archiveFile.getName());
    }
    else {
      output = new File(FileHelper.getTempDirectory()
          .getPath(), "duplicata_" + this.archiveFile.getName());
      if (!output.isFile()) {
        watermarkPDF(this.archiveFile, output);
      }
    }
    output.deleteOnExit();
    return output;
  }

  // -- TABS --

  public String getTab() {
    return tab;
  }

  public void setTab(String tab) {
    this.tab = tab;
  }

  public String getTabHeader(String tab) {
    try {
      return tabs.get(tab)
          .getLabel();
    }
    catch (Exception e) {
      log.error("Failed to get tab header", e);
      return tab;
    }
  }

  public List<String> getTabKeys(Instance instance) {
    if (document != null && tabKeys == null) {
      tabKeys = getTabs(instance).getProperties()
          .stream()
          .filter(t -> t.getRendered() && isArchiveFileRendered(t.getName()))
          .map(PropertyDescriptor::getName)
          .collect(toList());
    }
    return tabKeys;
  }

  public void setTabKeys(List<String> tabKeys) {
    this.tabKeys = tabKeys;
  }

  public void onSelectedTabChange(TabChangeEvent event) {
    tab = (String) event.getData();
  }

  // -- SEARCH --

  public boolean isAttachments() {
    return attachments;
  }

  public void setAttachments(boolean attacheddoc) {
    this.attachments = attacheddoc;
  }

  public boolean isLinks() {
    return links;
  }

  public void setLinks(boolean linkeddoc) {
    this.links = linkeddoc;
  }

  // -- RELATED --

  public boolean isLinked() {
    return linked;
  }

  public List<InvoiceRelated> getRelatedList() {
    return relatedList;
  }

  public void setRelatedList(List<InvoiceRelated> relatedList) {
    this.relatedList = relatedList;
  }

  public InvoiceRelated getRelated() {
    return related;
  }

  public void setRelated(InvoiceRelated related) {
    this.related = related;
  }

  public String getRelatedURL() {
    return getRelatedURL(related);
  }

  public String getRelatedURL(InvoiceRelated related) {
    if (related == null || isBlank(related.getUri())) {
      return null;
    }
    File file = related.getFile();
    if (file != null && file.exists()) {
      return createResourceRequestPath(file, related.getReference(), null);
    }
    return related.getUri();
  }

  public String getRelatedContent() {
    return getRelatedContent(related);
  }

  public String getRelatedContent(InvoiceRelated related) {
    if (!isRelatedContentRendered(related)) {
      return null;
    }
    try (FileInputStream fis = new FileInputStream(related.getFile())) {
      return IOUtils.toString(fis, defaultCharset());
    }
    catch (Exception e) {
      log.error("Failed to get related content", e);
      return e.getLocalizedMessage();
    }
  }

  public StreamedContent getRelatedStreamContent(InvoiceRelated related) {
    if (!isRelatedContentRendered(related)) {
      return null;
    }
    try (FileInputStream fis = new FileInputStream(related.getFile())) {
      return new DefaultStreamedContent(fis);
    }
    catch (Exception e) {
      log.error("Failed to get related stream content", e);
      return null;
    }
  }

  public boolean isRelatedContentRendered() {
    return isRelatedContentRendered(related);
  }

  public boolean isRelatedContentRendered(InvoiceRelated related) {
    File file;
    return related != null && (file = related.getFile()) != null && file.isFile() &&
        asList("XML", "XSD", "XSL", "HTML", "CSV", "TXT", "EDI").contains(related.getType());
  }

  public boolean isRelatedPdfRendered() {
    File file;
    return related != null && (file = related.getFile()) != null && file.isFile() && "PDF".equals(related.getType());
  }

  public String getRelatedContentMode() {
    if (related == null || related.getFileType() == null) {
      return "txt";
    }
    switch (related.getFileType()) {
    case "XML":
    case "XSD":
    case "XSL":
      return "xml";
    case "HTML":
      return "html";
    case "CSV":
      return "csv";
    case "TXT":
    case "EDI":
      return "txt";
    default:
      return "txt";
    }
  }

  @Override
  public boolean isRendered(PropertyDescriptor col) {
    return super.isRendered(col) && (!isSafeboxSearch() || !SAFE_BOX_PROPERTIES.contains(col.getName()));
  }

  /**
   * Evalue dynamiquement la propriété "property.name" dans l'objet "Invoice". A ne pas confondre avec property.getRendered() qui permet
   * l'affichage ou pas de la colonne. (non-Javadoc)
   *
   * @see com.byzaneo.generix.xtrade.task.AbstractIndexableTask#isRendered(Indexable, PropertyDescriptor)
   */
  @Override
  public boolean isRendered(Invoice invoice, PropertyDescriptor property) {
    if (RENDERED_PROPERTIES_DOC.contains(property.getName())) {
      try {
        Object value = new PropertyUtilsBean().getProperty(invoice, property.getName());
        if (value instanceof Boolean) {
          return (boolean) value;
        }
      }
      catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException e) {
        log.error("Error occurs while invoking method property " + property.getName() + " on class " + Invoice.class, e);
      }
    }
    return true;
  }

  @Override
  public boolean isSortable(PropertyDescriptor property) {
    return SORTED_PROPERTIES.contains(property.getName());
  }

  @Override
  public boolean isExportable(PropertyDescriptor property) {

    return !NOT_EXPORTABLE_PROPERTIES.contains(property.getName());
  }

  @Override
  public boolean containsValue(PropertyDescriptor property) {
    return !RENDERED_PROPERTIES_DOC.contains(property.getName());
  }

  @Override
  public String getValueStyleClass(Invoice invoice, PropertyDescriptor property, String environmentCode) {
    if ("status".equals(property.getName())) {
      return getFontAwesomeIcon(invoice.getStatus()
          .getStatusCode());
    }
    if ("dematState".equals(property.getName())) {
      return invoice.getDematState();
    }
    return "";
  }

  @Override
  public String getColumnStyleClass(PropertyDescriptor property) {
    if (Date.class == property.getType() || Double.class == property.getType() || property.getName()
        .endsWith("State") || "status".equals(property.getName())) {
      return "center";
    }
    return super.getColumnStyleClass(property);
  }

  @Override
  public boolean isMultipleValues(PropertyDescriptor property) {
    return MULTIPLE_VALUES_PROPERTIES.contains(property.getName());
  }

  @Override
  public List<String> getValues(PropertyDescriptor property) {
    if ("status".equals(property.getName())) {
      List<DocumentStatus> values = VALUES_PROPERTIES.get(property.getName())
          .stream()
          .map(DocumentStatus::valueOf)
          .collect(toList());
      return sortLabel(values);
    }
    else {
      return VALUES_PROPERTIES.get(property.getName());
    }
  }

  @Override
  public String getLabelPrefix() {
    return IM_LABELS_PREFIX;
  }

  /*
   * -- LAZY DATA MODEL --
   */

  public final class InvoiceDataModel extends AbstractIndexableDataModel<Invoice> {
    private static final long serialVersionUID = -6729324800659701990L;

    public InvoiceDataModel(Query query) {
      super(documentService, query, getSearch());
    }

    /**
     * @see AbstractPersistentDataModel#load(PageRequest, Map)
     */
    @Override
    public Page<Invoice> load(PageRequest pageable, Map<String, Object> filters) {
      // initializes query builder with the base query
      final QueryBuilder qb = createBuilder(query);

      // adds filtering
      this.addFilterClauses(qb, filters);

      Query query = qb.query();
      query.getContext()
          .put(ContextKey.PARTNER_SCOPE, getUserCodes());
      // processes loading request
      return this.service.searchIndexables(this.type, query, pageable);
    }
  }

  @Override
  public String getPathDescriptor() {
    return pathDescriptor;
  }

  @Override
  public void setPathDescriptor(String pathDescriptor) {
    this.pathDescriptor = pathDescriptor;
  }

  @Override
  protected BeanDescriptor createDescriptor() {
    BeanDescriptor beanDesc = newDescriptor(getIndexableType(), null, true, false,
        PROPERTIES_TO_EXCLUDE.toArray(new String[PROPERTIES_TO_EXCLUDE.size()]));

    // set options expression for status property if exists
    if (beanDesc.getProperty("status") != null) {
      beanDesc.getProperty("status")
          .setOptionsExpression("#{cc.attrs.taskBean.getValues('status')}");
    }

    return beanDesc;
  }

}
