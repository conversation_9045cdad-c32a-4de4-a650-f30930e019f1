package com.byzaneo.generix.xcbl.portal.task.converter;

import com.byzaneo.generix.api.DocumentCompoundType;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

public class DocumentTypeConverterTest {
  private DocumentTypeConverter converter = new DocumentTypeConverter();

  @Test
  public void testGetAsObject() {
    assertEquals("", converter.getAsObject(null, null, null));
    assertEquals("D96A", converter.getAsObject(null, null, "{\"norm\":\"D96A\"}"));
    assertEquals("{\"type\":\"D96A\"}", converter.getAsObject(null, null, "{\"type\":\"D96A\"}"));
  }

  @Test
  public void testGetAsString() {
    assertEquals("", converter.getAsString(null, null, null));
    assertEquals("D96A", converter.getAsString(null, null, "D96A"));
    assertEquals("1", converter.getAsString(null, null, 1));
  }
}