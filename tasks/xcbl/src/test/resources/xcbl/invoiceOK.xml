<Invoice xmlns:ns4="rrn:org.xcbl:schemas/xcbl/v4_0/materialsmanagement/v1_0/materialsmanagement.xsd" xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd" xmlns:dsg="http://www.w3.org/2000/09/xmldsig#" xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <InvoiceHeader>
        <InvoiceNumber>9988</InvoiceNumber>
        <InvoiceIssueDate>2018-06-11T00:00:00+02:00</InvoiceIssueDate>
        <InvoiceReferences>
            <PurchaseOrderReference>
                <core:BuyerOrderNumber>66</core:BuyerOrderNumber>
                <core:PurchaseOrderDate>2018-06-11T00:00:00+02:00</core:PurchaseOrderDate>
            </PurchaseOrderReference>
            <ASNNumber>
                <core:RefNum>55</core:RefNum>
                <core:RefDate>2018-06-11T00:00:00+02:00</core:RefDate>
            </ASNNumber>
        </InvoiceReferences>
        <InvoicePurpose>
            <InvoicePurposeCoded>Original</InvoicePurposeCoded>
        </InvoicePurpose>
        <InvoiceType>
            <InvoiceTypeCoded>CommercialInvoice</InvoiceTypeCoded>
        </InvoiceType>
        <InvoiceCurrency>
            <core:CurrencyCoded>EUR</core:CurrencyCoded>
        </InvoiceCurrency>
        <InvoiceLanguage>
            <core:LanguageCoded>fr</core:LanguageCoded>
        </InvoiceLanguage>
        <TaxReference>
            <core:TaxCategoryCoded>ExemptFromTax</core:TaxCategoryCoded>
            <core:ReasonTaxExemptCoded>Other</core:ReasonTaxExemptCoded>
            <core:ReasonTaxExemptCodedOther>Article 262 ter-i du CGI</core:ReasonTaxExemptCodedOther>
            <core:TaxTreatmentCoded>NoTaxApplies</core:TaxTreatmentCoded>
        </TaxReference>
        <InvoiceMedium>
            <InvoiceMediumCoded>Other</InvoiceMediumCoded>
            <InvoiceMediumCodedOther>Portal</InvoiceMediumCodedOther>
        </InvoiceMedium>
        <InvoiceDates>
            <InvoiceDueDate>2018-07-11T00:00:00+02:00</InvoiceDueDate>
            <ActualDeliveryDate>2018-06-11T00:00:00+02:00</ActualDeliveryDate>
            <ListOfOtherInvoiceDates>
                <core:DateCoded>
                    <core:Date>2018-06-11T13:16:06.699+02:00</core:Date>
                    <core:DateQualifier>
                        <core:DateQualifierCoded>DocumentReceivedDateTime</core:DateQualifierCoded>
                    </core:DateQualifier>
                </core:DateCoded>
                <core:DateCoded>
                    <core:Date>2018-06-11T13:16:34.189+02:00</core:Date>
                    <core:DateQualifier>
                        <core:DateQualifierCoded>PreparationDateTimeOfDocument</core:DateQualifierCoded>
                    </core:DateQualifier>
                </core:DateCoded>
            </ListOfOtherInvoiceDates>
        </InvoiceDates>
        <InvoiceParty>
            <BuyerParty>
                <core:PartyID>
                    <core:Agency>
                        <core:AgencyCoded>EAN</core:AgencyCoded>
                        <core:CodeListIdentifierCoded>LocationCode</core:CodeListIdentifierCoded>
                    </core:Agency>
                    <core:Ident>002</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <core:AgencyCodedOther>RCS-RCM</core:AgencyCodedOther>
                            <core:AgencyDescription>French Trade and Companies Register</core:AgencyDescription>
                        </core:Agency>
                    </core:Identifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:NameAddress>
                    <core:Name1>oxana</core:Name1>
                    <core:Street>calea turzii</core:Street>
                    <core:PostalCode>123456</core:PostalCode>
                    <core:City>cluj</core:City>
                    <core:Country>
                        <core:CountryCoded>RO</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>10</core:Ident>
                    </core:TaxIdentifier>
                </core:PartyTaxInformation>
            </BuyerParty>
            <SellerParty xsi:type="core:PartyCodedType">
                <core:PartyID>
                    <core:Agency>
                        <core:AgencyCoded>EAN</core:AgencyCoded>
                        <core:CodeListIdentifierCoded>LocationCode</core:CodeListIdentifierCoded>
                    </core:Agency>
                    <core:Ident>123456</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <core:AgencyCodedOther>RCS-RCM</core:AgencyCodedOther>
                            <core:AgencyDescription>French Trade and Companies Register</core:AgencyDescription>
                        </core:Agency>
                        <core:Ident>RCS-RCS-XXX</core:Ident>
                    </core:Identifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>1</core:Ident>
                    </core:Identifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AssignedByNationalTradeAgency</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>BusinessLegalStructureType</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>test</core:Ident>
                    </core:Identifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>LegalCapital</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>200</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:NameAddress>
                    <core:Name1>Doru Ciolan</core:Name1>
                    <core:Street>Calea Turzii 74-76</core:Street>
                    <core:PostalCode>400193</core:PostalCode>
                    <core:City>Cluj-Napoca</core:City>
                    <core:Country>
                        <core:CountryCoded>RO</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>5%</core:Ident>
                    </core:TaxIdentifier>
                    <core:RegisteredName>LIDL</core:RegisteredName>
                </core:PartyTaxInformation>
            </SellerParty>
            <ShipToParty xsi:type="core:PartyCodedType">
                <core:PartyID>
                    <core:Ident>002</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <core:AgencyCodedOther>RCS-RCM</core:AgencyCodedOther>
                            <core:AgencyDescription>French Trade and Companies Register</core:AgencyDescription>
                        </core:Agency>
                    </core:Identifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:NameAddress>
                    <core:Name1>oxana</core:Name1>
                    <core:Street>calea turzii</core:Street>
                    <core:PostalCode>123456</core:PostalCode>
                    <core:City>cluj</core:City>
                    <core:Country>
                        <core:CountryCoded>RO</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
                <core:PrimaryContact/>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>10</core:Ident>
                    </core:TaxIdentifier>
                </core:PartyTaxInformation>
            </ShipToParty>
            <BillToParty xsi:type="core:PartyCodedType">
                <core:PartyID>
                    <core:Ident>001</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier/>
                <core:NameAddress>
                    <core:Name1>ox</core:Name1>
                    <core:Street>calea turzii</core:Street>
                    <core:PostalCode>400123</core:PostalCode>
                    <core:City>Cluj</core:City>
                    <core:Country>
                        <core:CountryCoded>RO</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
                <core:PrimaryContact/>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>10</core:Ident>
                    </core:TaxIdentifier>
                </core:PartyTaxInformation>
            </BillToParty>
        </InvoiceParty>
        <InvoicePaymentInstructions>
            <core:PaymentTerms>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>Discount</core:PaymentTermCoded>
                    <core:PaymentTermDescription>Pas d'escompte pour paiement anticipé</core:PaymentTermDescription>
                </core:PaymentTerm>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>LatePayment</core:PaymentTermCoded>
                    <core:PaymentTermDescription>Pénalité de 40 EUROS appliquée pour non paiement à échéance - art 441.6CC</core:PaymentTermDescription>
                </core:PaymentTerm>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>PenaltyTerms</core:PaymentTermCoded>
                    <core:PaymentTermDescription>En cas de non respect de l'échéance, des intérêts de retard pourront être facturés à un taux égal à trois fois le taux d'intérêt légal</core:PaymentTermDescription>
                </core:PaymentTerm>
            </core:PaymentTerms>
            <core:PaymentMethod>
                <core:PaymentMeanCoded>Cheque</core:PaymentMeanCoded>
            </core:PaymentMethod>
        </InvoicePaymentInstructions>
        <ListOfNameValueSet>
            <core:NameValueSet>
                <core:SetName>KPI</core:SetName>
                <core:ListOfNameValuePair>
                    <core:NameValuePair>
                        <core:Name>Format In</core:Name>
                        <core:Value>User</core:Value>
                        <core:Datatype>Text</core:Datatype>
                    </core:NameValuePair>
                    <core:NameValuePair>
                        <core:Name>Format Out</core:Name>
                        <core:Value>D96A</core:Value>
                        <core:Datatype>Text</core:Datatype>
                    </core:NameValuePair>
                    <core:NameValuePair>
                        <core:Name>Nature</core:Name>
                        <core:Value>Goods</core:Value>
                        <core:Datatype>Text</core:Datatype>
                    </core:NameValuePair>
                    <core:NameValuePair>
                        <core:Name>Sens</core:Name>
                        <core:Value>Entrant</core:Value>
                        <core:Datatype>Text</core:Datatype>
                    </core:NameValuePair>
                </core:ListOfNameValuePair>
            </core:NameValueSet>
        </ListOfNameValueSet>
    </InvoiceHeader>
    <InvoiceDetail>
        <ListOfInvoiceItemDetail>
            <InvoiceItemDetail>
                <InvoiceBaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>1</core:BuyerLineItemNum>
                        <core:SellerLineItemNum>1</core:SellerLineItemNum>
                    </LineItemNum>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:SellerPartNumber>
                                <core:PartID>44</core:PartID>
                            </core:SellerPartNumber>
                            <core:BuyerPartNumber>
                                <core:PartID>55</core:PartID>
                            </core:BuyerPartNumber>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>Other</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifierQualifierCodedOther>Reference</core:ProductIdentifierQualifierCodedOther>
                                <core:ProductIdentifier>1234565432123</core:ProductIdentifier>
                            </core:StandardPartNumber>
                        </core:PartNumbers>
                        <core:ItemDescription>ff</core:ItemDescription>
                    </ItemIdentifiers>
                    <InvoicedQuantity>
                        <core:QuantityValue>1</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>EA</core:UOMCoded>
                        </core:UnitOfMeasurement>
                    </InvoicedQuantity>
                </InvoiceBaseItemDetail>
                <InvoicePricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationGross</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>114.000000</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationNet</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>114.000000</core:UnitPriceValue>
                            </core:UnitPrice>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:LineItemTotal>
                        <core:MonetaryAmount>114.00</core:MonetaryAmount>
                    </core:LineItemTotal>
                </InvoicePricingDetail>
            </InvoiceItemDetail>
        </ListOfInvoiceItemDetail>
    </InvoiceDetail>
    <InvoiceSummary>
        <InvoiceTotals>
            <InvoiceTotal>
                <core:MonetaryAmount>114.00</core:MonetaryAmount>
            </InvoiceTotal>
            <InvoiceSubTotal>
                <core:MonetaryAmount>114.00</core:MonetaryAmount>
            </InvoiceSubTotal>
            <TaxableValue>
                <core:MonetaryAmount>0</core:MonetaryAmount>
            </TaxableValue>
            <TotalTaxAmount>
                <core:MonetaryAmount>0</core:MonetaryAmount>
            </TotalTaxAmount>
        </InvoiceTotals>
    </InvoiceSummary>
</Invoice>
