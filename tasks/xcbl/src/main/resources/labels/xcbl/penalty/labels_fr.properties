# /xcbl/goodsreceipt
# French (fr)
# GoodsReceiptTask
actions=Actions
cancellationDate=Date r\u00E9cep./annul.
companyComment=Commentaire {0}
companyResponse=R\u00E9p. {0}
confirmTitle=Attention!
confirmValidateAndSend=Confirmez-vous la validation et l\u2019envoi de vos r\u00E9ponses pour cette commande ? Vous ne pourrez ensuite plus les modifier.
contestability=Date maximale contestation
CONTESTATION_ACCEPTED=Contestation accept\u00E9e
CONTESTATION_REFUSED=Contestation refus\u00E9e
creationDate=Date Cr\u00E9ation
criteria=Crit\u00E8res
deliveredQuantity=Qt\u00E9 r\u00E9cep.
DELIVERED_TOO_LATE=Date
enteringResponse=Saisie d\u2019une r\u00E9ponse
enterResponse=Saisir une r\u00E9ponse
euro=\u20AC
exceedingTimeLimit=Le d\u00E9lai est d\u00E9pass\u00E9
from=Emetteur
goodsReceiptHeader_goodsReceiptParty_sellerParty_partyID_ident=Code fourn. p\u00E9nalis\u00E9
goodsReceiptHeader_goodsReceiptParty_sellerParty_nameAddress_name1=Nom fourn. p\u00E9nalis\u00E9
goodsReceiptHeader_goodsReceiptParty_buyerParty_partyID_ident=Code acheteur
goodsReceiptHeader_goodsReceiptParty_buyerParty_nameAddress_name1=Nom acheteur
goodsReceiptHeader_goodsReceiptReferences_listOfPurchaseOrderReference_purchaseOrderReference[0]_buyerOrderNumber=Commande
goodsReceiptHeader_goodsReceiptReferences_listOfPurchaseOrderReference_purchaseOrderReference[0]_purchaseOrderDate=Date commande
goodsReceiptHeader_goodsReceiptDates_requestedDeliveryDate=Date livraison souhait\u00E9e
goodsReceiptHeader_goodsReceiptDates_listOfDateCoded_dateCoded[1]_date=Date publication
goodsReceiptHeader_goodsReceiptDates_listOfDateCoded_dateCoded[2]_date=Date maxi. contestation
goodsReceiptHeader_goodsReceiptParty_shipFromParty_partyID_ident=Code fourn. marchandise
goodsReceiptHeader_goodsReceiptParty_shipFromParty_nameAddress_name1=Nom fourn. marchandise
goodsReceiptHeader_goodsReceiptParty_shipToParty_partyID_ident=Code entrep\u00F4t
goodsReceiptHeader_goodsReceiptParty_shipToParty_nameAddress_name1=Nom entrep\u00F4t
id=Identifiant
legend_unread=Non lu
legend_read=Lu
legend_sendToCompany=Envoy\u00E9 \u00E0 {0}
legend_answeredByCompany=R\u00E9ponse faite par {0}
legend_timeout=D\u00E9lai d\u00E9pass\u00E9
multiActions=Actions multiples
multiResponseMsg=Plusieurs p\u00E9nalit\u00E9s s\u00E9lectionn\u00E9es n\u2019ont pas actuellement les m\u00EAmes r\u00E9ponses
orderDate=Date commande
orderedQuantity=Qt\u00E9 cd\u00E9e
orderNumber=Commande
orderSequence=Reliquat
owners=Entit\u00E9 juridique
penaltyAmount=Montant p\u00E9nalit\u00E9
penaltyDetail=D\u00E9tail de commande
penaltySaveAction=Les modifications ont \u00E9t\u00E9 enregistr\u00E9es
penaltyType=Type p\u00E9nalit\u00E9
PENALTY_ACCEPTED=P\u00E9nalit\u00E9 accept\u00E9e
PENALTY_CONTESTED=P\u00E9nalit\u00E9 contest\u00E9e
product=Produit
productDescription=Libell\u00E9 produit
QUANTITY_SHORT=Quantit\u00E9
select=S\u00E9lectionner
selectTypeChoice=Merci de s\u00E9lectionner votre choix de r\u00E9ponse
sellerId=Code fournisseur p\u00E9nalis\u00E9
sellerName=Nom fournisseur p\u00E9nalis\u00E9
shipFromId=Code fournisseur marchandise
shipFromName=Nom fournisseur marchandise
shipToName=Nom entrep\u00F4t
status=Statut
supplierComment=Commentaire fournisseur
supplierResponse=R\u00E9p. four.
type=Genre
typeContestationReasons=Merci d\u2019indiquer les raisons de votre contestation
to=Destinataire
validateSend=Valider et envoyer
validationOn=Validation effectu\u00E9e le
none = Non lu
read = Lu
sent = Envoy\u00E9
timeout = D\u00E9lai d\u00E9pass\u00E9
answered = R\u00E9pondue