package com.byzaneo.generix.xcbl.portal.task.util;

import com.byzaneo.commons.bean.FileType;
import com.byzaneo.commons.ui.util.*;
import com.byzaneo.commons.util.*;
import com.byzaneo.generix.bean.ReconciliationDifferences;
import com.byzaneo.generix.bean.ReconciliationDifferences.Lines.Line;
import com.byzaneo.generix.bean.ReconciliationDifferences.Lines.Line.Difference;
import com.byzaneo.generix.edocument.bean.*;
import com.byzaneo.generix.edocument.util.StageEnum;
import com.byzaneo.generix.service.SecurityService;
import com.byzaneo.generix.util.ToolsHelper;
import com.byzaneo.generix.xcbl.portal.task.InvoiceTask;
import com.byzaneo.generix.xcbl.portal.task.bean.*;
import com.byzaneo.generix.xtrade.util.*;
import com.byzaneo.security.bean.User;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.service.DocumentService;
import com.byzaneo.xtrade.xcbl.bean.Invoice;
import com.byzaneo.xtrade.xcbl.bean.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.JAXBException;
import javax.xml.stream.XMLStreamException;
import java.io.File;
import java.io.*;
import java.math.*;
import java.text.*;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.*;

import static com.byzaneo.commons.bean.FileType.ERROR;
import static com.byzaneo.commons.bean.FileType.PDF;
import static com.byzaneo.commons.bean.FileType.XCBL;
import static com.byzaneo.generix.edocument.service.EDocumentService.OFFICIAL_INDEX;
import static com.byzaneo.generix.edocument.util.InvoiceXcblHelper.getXcblInvoiceFile;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toCollection;
import static org.apache.commons.lang.StringUtils.EMPTY;
import static org.apache.commons.lang3.StringUtils.capitalize;

public class InvoiceTaskHelper {

  public static final String APP_RSP_RTE_COMMENT = "Application Response";
  public static final String INVOICE_RTE_COMMENT = "Invoice Xcbl";

  public static final Predicate<DocumentFile> PREDICATE_PDF_DOF = dof -> PDF.equals(dof.getType());

  public static final Predicate<DocumentFile> PREDICATE_INVOICE_DOF = dof -> XCBL.equals(dof.getType()) && dof.getComment() != null &&
      dof.getComment()
          .contains(INVOICE_RTE_COMMENT);

  public static final Predicate<DocumentFile> PREDICATE_APPRSP_DOF = dof -> XCBL.equals(dof.getType()) && dof.getComment() != null &&
      dof.getComment()
          .contains(APP_RSP_RTE_COMMENT);

  public static final Predicate<DocumentFile> PREDICATE_INVOICE_XCBL = dof -> XCBL.equals(dof.getType()) &&
      OFFICIAL_INDEX.equals(dof.getDescription());

  private static final String QUANTITY = "itemInvoicedQuantityValue";
  private static final String TOTAL = "itemLineItemTotal";
  private static final String NET_UNIT_PRICE = "itemNetUnitPrice";

  /* -- FILE -- */

  // BIRT
  public static BirtFile resolveBirtFile(Document doc, BirtFile birtFile) {
    Optional<DocumentFile> dof = doc.getFiles()
        .stream()
        .filter(PREDICATE_PDF_DOF)
        .findAny();

    return dof.isPresent() ? new BirtFile(dof.get()
        .getFile(), PDF) : birtFile;
  }

  // APPLICATION RESPONSE
  public static DocumentFile resolveAppRspDof(Document doc) {
    return doc.getFiles()
        .stream()
        .filter(PREDICATE_APPRSP_DOF)
        .findAny()
        .orElse(null);
  }

  /* -- CORRECTION -- */

  /* -RECONCILIATION- */

  public static List<InvoiceDifferenceLine> buildInvoiceDifferenceLines(InvoiceIndex index,
      ReconciliationDifferences reconciliationDiferences, String invoiceItemCodeField) {
    if (index == null || CollectionUtils.isEmpty(index.getDetails()))
      return Collections.emptyList();
    List<InvoiceDifferenceLine> lines = new ArrayList<InvoiceDifferenceLine>();
    for (InvoiceDetailIndex line : index.getDetails()) {
      ReconciliationDifferences.Lines.Line differenceLine = searchDifferenceLine(line.getItemLineNumber(), reconciliationDiferences, line);

      if (!lines.stream()
          .anyMatch(l -> Objects.equals(l.getProductCode(), line.getItemBuyerPartNumber()) ||
              Objects.equals(l.getProductCode(), line.getItemProductIdentifier()) ||
              Objects.equals(l.getProductCode(), line.getItemSellerPartNumber()))) {
        if (differenceLine == null)
          lines.add(createInvoiceDifferenceLine(index, line, differenceLine, false, invoiceItemCodeField));
        else {
          lines.add(createInvoiceDifferenceLine(index, line, differenceLine, true, invoiceItemCodeField));
        }
      }

    }
    return lines;
  }

  private static InvoiceDifferenceLine createInvoiceDifferenceLine(InvoiceIndex index, InvoiceDetailIndex line, Line differenceLine,
      boolean isDifference, String invoiceItemCodeField) {
    InvoiceDifferenceLine invoiceDifference = new InvoiceDifferenceLine();
    invoiceDifference.setInvoiceCurrency(CurrencyCodeAndSign.getSignByCode(index.getInvoiceCurrencyCoded()));
    invoiceDifference.setLineNumber(line.getItemLineNumber());
    invoiceDifference.setProductDescription(line.getItemDescription());
    invoiceDifference.setProductCode(differenceLine != null ? differenceLine.getLineItemIdentifier()
        : StringUtils.isNotEmpty(invoiceItemCodeField) && getItemCodeValue(line, invoiceItemCodeField) != null
            ? getItemCodeValue(line, invoiceItemCodeField)
            : MessageHelper.getMessage(InvoiceTask.LABEL_FAMILY + ".rec_no_product_identifier", "No product identifier found",
                JSFHelper.getLocale()));
    invoiceDifference.setDifference(isDifference);
    invoiceDifference.setIntruderProduct(
        differenceLine != null && differenceLine.isIntruderProduct() != null ? differenceLine.isIntruderProduct() : false);
    if (isDifference)
      return buildInvoiceLineWhenDifference(invoiceDifference, line, differenceLine, index, invoiceItemCodeField);
    else return buildInvoiceLineWhenNoDifference(invoiceDifference, line);
  }

  private static InvoiceDifferenceLine buildInvoiceLineWhenDifference(InvoiceDifferenceLine invoiceDifference, InvoiceDetailIndex line,
      Line differenceLine, InvoiceIndex index, String invoiceItemCodeField) {
    Difference quantityDifference = getDifference(differenceLine, QUANTITY);
    Difference netUnitPriceDifference = getDifference(differenceLine, NET_UNIT_PRICE);
    Difference totalDifference = getDifference(differenceLine, TOTAL);

    // ordered
    invoiceDifference.getDifferences()
        .put(DifferenceType.ORDERED, buildOrderedDifference(quantityDifference, netUnitPriceDifference, totalDifference));
    // reception
    invoiceDifference.getDifferences()
        .put(DifferenceType.RECEPTION, buildReceptionDifference(quantityDifference));

    // invoiced (previous invoices + credit notes)
    invoiceDifference.getDifferences()
        .put(DifferenceType.BILL, buildBillDifference(line, quantityDifference, totalDifference));

    // invoice (current invoice)
    invoiceDifference.getDifferences()
        .put(DifferenceType.INVOICE, buildCurrentInvoiceDifference(line, index, invoiceItemCodeField));

    // Difference
    invoiceDifference.getDifferences()
        .put(DifferenceType.DIFFERENCE, buildDifferenceLineOfDifferences(quantityDifference, netUnitPriceDifference, totalDifference,
            invoiceDifference.isIntruderProduct()));

    // Tolerance
    invoiceDifference.getDifferences()
        .put(DifferenceType.TOLERANCE, buildToleranceDifferences(quantityDifference, netUnitPriceDifference, totalDifference));
    return invoiceDifference;
  }

  private static DifferenceDetail buildToleranceDifferences(Difference quantityDifference, Difference netUnitPriceDifference,
      Difference totalDifference) {
    DifferenceDetail toleranceDetail = new DifferenceDetail();
    toleranceDetail.setQuantity(quantityDifference.getAuthorizedAmountGap());
    toleranceDetail.setQuantityPercentage(quantityDifference.getAuthorizedPercentageGap());

    toleranceDetail.setUnitPrice(netUnitPriceDifference.getAuthorizedAmountGap());
    toleranceDetail.setUnitPricePercentage(netUnitPriceDifference.getAuthorizedPercentageGap());

    toleranceDetail.setAmount(totalDifference.getAuthorizedAmountGap());
    toleranceDetail.setAmountPercentage(totalDifference.getAuthorizedPercentageGap());

    return toleranceDetail;
  }

  private static DifferenceDetail buildDifferenceLineOfDifferences(Difference quantityDifference, Difference netUnitPriceDifference,
      Difference totalDifference, boolean isIntruderProduct) {
    DifferenceDetail differenceDetail = new DifferenceDetail();
    if (quantityDifference.getReceptionValue() != null && (!quantityDifference.getReceptionValue()
        .equals("0") || isIntruderProduct)) {
      differenceDetail.setQuantity(calculateDifferenceValue(quantityDifference.getInvoiceValue(), quantityDifference.getReceptionValue()));
      differenceDetail.setQuantityPercentage(
          calculateDifferencePercentage(quantityDifference.getInvoiceValue(), quantityDifference.getReceptionValue()));
    }
    if (quantityDifference.getOrderValue() != null && (!quantityDifference.getOrderValue()
        .equals("0") || isIntruderProduct)) {
      differenceDetail.setQuantity(calculateDifferenceValue(quantityDifference.getInvoiceValue(), quantityDifference.getOrderValue()));
      differenceDetail
          .setQuantityPercentage(calculateDifferencePercentage(quantityDifference.getInvoiceValue(), quantityDifference.getOrderValue()));
    }
    differenceDetail
        .setUnitPrice(calculateDifferenceValue(netUnitPriceDifference.getInvoiceValue(), netUnitPriceDifference.getOrderValue()));
    differenceDetail.setUnitPricePercentage(
        calculateDifferencePercentage(netUnitPriceDifference.getInvoiceValue(), netUnitPriceDifference.getOrderValue()));

    differenceDetail.setAmount(calculateDifferenceValue(totalDifference.getInvoiceValue(), totalDifference.getOrderValue()));
    differenceDetail.setAmountPercentage(calculateDifferencePercentage(totalDifference.getInvoiceValue(), totalDifference.getOrderValue()));
    return differenceDetail;
  }

  public static BigDecimal calculateDifferenceValue(String invoicedAsString, String orderedAsString) {
    if (StringUtils.isEmpty(invoicedAsString) || StringUtils.isEmpty(orderedAsString))
      return null;
    BigDecimal invoiced = new BigDecimal(invoicedAsString);
    BigDecimal ordered = new BigDecimal(orderedAsString);
    return invoiced.subtract(ordered);
  }

  public static BigDecimal calculateDifferencePercentage(String invoicedAsString, String orderedAsString) {
    if (StringUtils.isEmpty(invoicedAsString) || StringUtils.isEmpty(orderedAsString))
      return null;
    BigDecimal invoiced = new BigDecimal(invoicedAsString);
    BigDecimal ordered = new BigDecimal(orderedAsString);
    if (BigDecimal.ZERO.compareTo(ordered) == 0)
      return BigDecimal.ZERO;
    return ((invoiced.subtract(ordered)).multiply(new BigDecimal(100))).divide(ordered, 2,
        RoundingMode.HALF_UP);
  }

  private static DifferenceDetail buildBillDifference(InvoiceDetailIndex line, Difference quantityDifference, Difference totalDifference) {
    DifferenceDetail billDetail = new DifferenceDetail();
    billDetail
        .setQuantity(quantityDifference.getInvoiceValue() != null ? new BigDecimal(quantityDifference.getInvoiceValue()) : null);
    billDetail.setAmount(totalDifference.getInvoiceValue() != null ? new BigDecimal(totalDifference.getInvoiceValue()) : null);
    return billDetail;
  }

  private static DifferenceDetail buildCurrentInvoiceDifference(InvoiceDetailIndex line, InvoiceIndex index, String invoiceItemCodeField) {
    DifferenceDetail invDetail = new DifferenceDetail();
    BigDecimal lineQuantity = Optional.ofNullable(line)
        .map(InvoiceDetailIndex::getItemInvoicedQuantityValue)
        .orElse(BigDecimal.ZERO);
    BigDecimal lineUnitPrice = Optional.ofNullable(line)
        .map(InvoiceDetailIndex::getItemNetUnitPrice)
        .orElse(BigDecimal.ZERO);
    BigDecimal lineAmount = Optional.ofNullable(line)
        .map(InvoiceDetailIndex::getItemTotal)
        .orElse(BigDecimal.ZERO);
    for (InvoiceDetailIndex detail : index.getDetails()) {
      if (StringUtils.isNotEmpty(invoiceItemCodeField) &&
          Objects.equals(getItemCodeValue(detail, invoiceItemCodeField), getItemCodeValue(line, invoiceItemCodeField)) &&
          !detail.equals(line)) {
        lineQuantity = lineQuantity.add(Optional.ofNullable(detail)
            .map(InvoiceDetailIndex::getItemInvoicedQuantityValue)
            .orElse(BigDecimal.ZERO));
        lineUnitPrice = lineUnitPrice.add(Optional.ofNullable(detail)
            .map(InvoiceDetailIndex::getItemNetUnitPrice)
            .orElse(BigDecimal.ZERO));
        lineAmount = lineAmount.add(Optional.ofNullable(detail)
            .map(InvoiceDetailIndex::getItemTotal)
            .orElse(BigDecimal.ZERO));
      }
    }
    invDetail.setQuantity(lineQuantity);
    invDetail.setUnitPrice(lineUnitPrice);
    invDetail.setAmount(lineAmount);
    return invDetail;
  }

  public static String getItemCodeValue(Object obj, String itemCodeField) {
    try {
      if (itemCodeField != null)
        return (String) obj.getClass()
            .getDeclaredMethod("get" + capitalize(itemCodeField))
            .invoke(obj);
    }
    catch (Exception e) {
      MessageHelper.error("Error when searching the matching between invoice and order or reception", e.getMessage());
    }
    return EMPTY;
  }

  private static DifferenceDetail buildOrderedDifference(Difference quantityDifference, Difference netUnitPriceDifference,
      Difference totalDifference) {
    DifferenceDetail orderedDetail = new DifferenceDetail();
    orderedDetail.setQuantity(quantityDifference.getOrderValue());
    orderedDetail.setUnitPrice(netUnitPriceDifference.getOrderValue());
    orderedDetail.setAmount(totalDifference.getOrderValue());
    return orderedDetail;
  }

  private static DifferenceDetail buildReceptionDifference(Difference quantityDifference) {
    DifferenceDetail receptionDetail = new DifferenceDetail();
    receptionDetail.setQuantity(quantityDifference.getReceptionValue());
    return receptionDetail;
  }

  private static Difference getDifference(Line differenceLine, String fieldName) {
    if (CollectionUtils.isEmpty(differenceLine.getDifference()))
      return new Difference();
    return differenceLine.getDifference()
        .stream()
        .filter(d -> fieldName.equals(d.getField()))
        .findFirst()
        .orElse(new Difference());

  }

  private static InvoiceDifferenceLine buildInvoiceLineWhenNoDifference(InvoiceDifferenceLine invoiceDifference, InvoiceDetailIndex line) {
    invoiceDifference.setQuantity(line.getItemInvoicedQuantityValue());
    invoiceDifference.setUnitPrice(line.getItemNetUnitPrice());
    invoiceDifference.setAmount(line.getItemTotal());
    return invoiceDifference;
  }

  private static Line searchDifferenceLine(int itemLineNumber, ReconciliationDifferences reconciliationDiferences,
      InvoiceDetailIndex line) {
    List<Line> lines = ofNullable(reconciliationDiferences).map(ReconciliationDifferences::getLines)
        .map(ReconciliationDifferences.Lines::getLine)
        .orElse(null);
    if (lines == null)
      return null;
    return lines
        .stream()
        .filter(l -> String.valueOf(itemLineNumber)
            .equals(l.getLineNumber()) && l.getLineItemIdentifier() != null &&
            (Objects.equals(line.getItemBuyerPartNumber(), l.getLineItemIdentifier()) ||
                Objects.equals(line.getItemSellerPartNumber(), l.getLineItemIdentifier()) ||
                Objects.equals(line.getItemProductIdentifier(), l.getLineItemIdentifier())))
        .findFirst()
        .orElse(null);
  }

  public static DocumentFile getReconcilationDocumentFile(Document document) {
    return document.getFiles()
        .stream()
        .filter(f -> FileType.XML.equals(f.getType()) &&
            "gap".equals(f.getActionName()))
        .findFirst()
        .orElse(null);
  }

  public static String formatBigDecimal(BigDecimal value, Locale locale) {
    if (value == null || locale == null)
      return "";
    value = value.setScale(2, BigDecimal.ROUND_HALF_UP);
    DecimalFormatSymbols dfs = new DecimalFormatSymbols(locale);
    dfs.setDecimalSeparator('.');
    dfs.setGroupingSeparator(' ');
    DecimalFormat df = new DecimalFormat("#,##0.00", dfs);
    df.setGroupingSize(3);
    return df.format(value);
  }

  public static Invoice createInvoiceBasedOnXcbl(Document document)
      throws FileNotFoundException, IOException, JAXBException, XMLStreamException {
    File invoiceFile = null;
    if (document.getFiles()
        .stream()
        .filter(PREDICATE_INVOICE_XCBL)
        .count() > 1) {
      MessageHelper.error("Document invalid : official index is not unique");
    }
    else {
      invoiceFile = getXcblInvoiceFile(document);
    }
    return JAXBHelper.unmarshal(Invoice.class, invoiceFile, new InvalidValueAdapter());
  }

  public static List<DocumentFile> getTaxOriginalFiles(Document document) {
    List<DocumentFile> files = document.getFiles()
        .stream()
        .filter(dof -> ResultingFileAction.of(dof.getActionName()) == ResultingFileAction.TAX_ORIGINAL)
        .collect(toCollection(ArrayList::new));

    return files;
  }

  public static String getFileSize(DocumentFile dof) {
    if (dof == null || dof.getFile() == null) {
      return "0 Byte";
    }
    return FileUtils.byteCountToDisplaySize(dof.getFile()
        .length());
  }

  public static Map<String, List<DocumentFile>> getFilesByType(Document document) {
    Map<String, List<DocumentFile>> filesByType = new HashMap<>();
    filesByType.put("attachedFiles", new ArrayList<>());
    filesByType.put("taxOriginalFiles", new ArrayList<>());
    filesByType.put("reportedDataFiles", new ArrayList<>());
    filesByType.put("relatedFiles", new ArrayList<>());
    List<DocumentFile> documentFiles = DocumentPdfHelper.getAllDocumentFiles(document);
    documentFiles.stream()
        .flatMap(dof -> {
          if ((ResultingFileAction.of(dof.getActionName()) == ResultingFileAction.ADD_FILE_TO_INVOICE ||
              ResultingFileAction.of(dof.getActionName()) == ResultingFileAction.ADD_FILE_TO_INVOICE_FROM_WORKFLOW) &&
              !SubType.FR_Report_Flux1.getName()
                  .equals(dof.getSubtype()) &&
              !SubType.FR_Report_Flux10.getName()
                  .equals(dof.getSubtype())) {
            return Stream.of(new AbstractMap.SimpleEntry<>("attachedFiles", dof));
          }
          else if (ResultingFileAction.of(dof.getActionName()) == ResultingFileAction.TAX_ORIGINAL) {
            return Stream.of(
                new AbstractMap.SimpleEntry<>("taxOriginalFiles", dof),
                new AbstractMap.SimpleEntry<>("relatedFiles", dof)
            );
          }
          else if (SubType.FR_Report_Flux1.getName()
              .equals(dof.getSubtype()) ||
              SubType.FR_Report_Flux10.getName()
                  .equals(dof.getSubtype())) {
            return Stream.of(new AbstractMap.SimpleEntry<>("reportedDataFiles", dof));
          }
          else if (ResultingFileAction.of(dof.getActionName()) != ResultingFileAction.ADD_FILE_TO_INVOICE &&
              ResultingFileAction.of(dof.getActionName()) != ResultingFileAction.READABLE &&
              SubType.of(dof.getSubtype()) != SubType.FR_REPORT_FLUX) {
            return Stream.of(new AbstractMap.SimpleEntry<>("relatedFiles", dof));
          }
          else {
            return Stream.empty();
          }
        })
        .forEach(entry -> filesByType.get(entry.getKey())
            .add(entry.getValue()));

    return filesByType;
  }

  public static List<DocumentFile> getReportedData(Document document) {
    List<DocumentFile> files = document.getFiles()
        .stream()
        .filter(dof -> (SubType.FR_Report_Flux1.getName()
            .equals(dof.getSubtype()) ||
            SubType.FR_Report_Flux10.getName()
                .equals(dof.getSubtype())))
        .collect(toCollection(ArrayList::new));

    return files;
  }

  public static List<DocumentFile> getRelatedFiles(Document document, boolean fromLifeCycle) {
    List<DocumentFile> files = DocumentPdfHelper.getDocumentFiles(document)
        .stream()
        .filter(dof -> (!fromLifeCycle || StageEnum.fromValue(dof.getSubtype()) != null) &&
            ResultingFileAction.of(dof.getActionName()) != ResultingFileAction.ADD_FILE_TO_INVOICE &&
            ResultingFileAction.of(dof.getActionName()) != ResultingFileAction.READABLE &&
            SubType.of(dof.getSubtype()) != SubType.FR_REPORT_FLUX)
        .collect(Collectors.toCollection(ArrayList::new));

    return files;
  }

  public static List<DocumentFile> getAttachedFiles(Document document) {
    List<DocumentFile> files = document.getFiles()
        .stream()
        .filter(dof -> ((ResultingFileAction.of(dof.getActionName()) == ResultingFileAction.ADD_FILE_TO_INVOICE ||
            ResultingFileAction.of(dof.getActionName()) == ResultingFileAction.ADD_FILE_TO_INVOICE_FROM_WORKFLOW) &&
            (!SubType.FR_Report_Flux1.getName()
                .equals(dof.getSubtype()) &&
                !SubType.FR_Report_Flux10.getName()
                    .equals(dof.getSubtype()))))
        .collect(toCollection(ArrayList::new));

    return files;
  }

  public static List<DocumentFile> getErrorsFiles(Document document) {
    List<DocumentFile> files = document.getFiles()
        .stream()
        .filter(dof -> !ResultingFileAction.ADD_FILE_TO_INVOICE.equals(ResultingFileAction.of(dof.getActionName())) &&
            !ResultingFileAction.READABLE.equals(ResultingFileAction.of(dof.getActionName())) &&
            !SubType.FR_REPORT_FLUX.equals(SubType.of(dof.getSubtype())) && dof.getType() == ERROR)
        .collect(toCollection(ArrayList::new));
    return files;
  }

  public static void copyWorkflowInfoToInvoice(Invoice modifiedInvoice, InvoiceIndex originalInvoiceIndex) {
    String wkfName = originalInvoiceIndex.getWkfName();
    if (wkfName == null) {
      return;
    }
    modifiedInvoice.setWkfName(originalInvoiceIndex.getWkfName());
    modifiedInvoice.setWkfVersion(originalInvoiceIndex.getWkfVersion());
    modifiedInvoice.setWkfStep(originalInvoiceIndex.getWkfStep());
    modifiedInvoice.setWkfNumberOfSteps(originalInvoiceIndex.getWkfNumberOfSteps());
    modifiedInvoice.setWkfCompletionPercentage(originalInvoiceIndex.getWkfCompletionPercentage());
    modifiedInvoice.setWkfLastModificationDate(originalInvoiceIndex.getWkfLastModificationDate());
    modifiedInvoice.setWkfLockedBy(originalInvoiceIndex.getWkfLockedBy());
    modifiedInvoice.setWkfLockedById(originalInvoiceIndex.getWkfLockedById());
    modifiedInvoice.setWkfStepActions(originalInvoiceIndex.getWkfStepActions());
    modifiedInvoice.setWkfStepActors(originalInvoiceIndex.getWkfStepActors());
    modifiedInvoice.setWkfStepActorsList(originalInvoiceIndex.getWkfStepActorsList());
  }

  public static void copyWorkflowInfo(Document invoice, Workflow workflow, WorkflowDocumentStatus workflowDocumentStatus,
      SecurityService securityService, DocumentService documentService) {
    InvoiceIndex invoiceIndex = invoice.getIndexValue();

    invoiceIndex.setWkfName(workflow.getName());
    invoiceIndex.setWkfVersion(workflow.getVersion());

    invoiceIndex.setWkfLastModificationDate(workflowDocumentStatus.getModificationDate());
    String userThatHasLockedInvoice = workflowDocumentStatus.getUserId();
    if (StringUtils.isNotBlank(userThatHasLockedInvoice)) {
      invoiceIndex.setWkfLockedById(userThatHasLockedInvoice);

      String userFullName = null;
      User user = securityService.getUser(userThatHasLockedInvoice);
      if (user != null) {
        userFullName = user.getFullname();
      }
      invoiceIndex.setWkfLockedBy(userFullName);
    }
    else {
      invoiceIndex.setWkfLockedById(null);
      invoiceIndex.setWkfLockedBy(null);
    }
    invoiceIndex.setWkfStep(workflowDocumentStatus.getNumStep());
    // Workflow steps, we search current step
    List<WorkflowStep> workflowStepsList = workflow.getWorkflowSteps();
    invoiceIndex.setWkfNumberOfSteps(workflowStepsList.size());

    if (workflowStepsList.size() == 0) {
      // zero steps in workflow, it is possible
      invoiceIndex.setWkfCompletionPercentage(0.0);
      return;
    }
    invoiceIndex.setWkfCompletionPercentage(Double.valueOf(
        ToolsHelper.WKF_PERCENTAGE_FORMAT.format((double) (invoiceIndex.getWkfStep() - 1) * 100.0 / (double) workflow.getWorkflowSteps()
            .size())));

    // Current step
    WorkflowStep currentStep = workflowStepsList.get(invoiceIndex.getWkfStep() - 1);
    if (currentStep != null) {
      ToolsHelper.fillIndexForActionsAndActors(null, invoiceIndex, currentStep, securityService, documentService, workflowDocumentStatus);
    }
  }

}
