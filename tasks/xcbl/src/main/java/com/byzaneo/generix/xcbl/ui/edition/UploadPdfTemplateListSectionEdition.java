package com.byzaneo.generix.xcbl.ui.edition;

import static com.byzaneo.commons.ui.util.JSFHelper.getLabel;
import static com.byzaneo.commons.ui.util.JSFHelper.getManagedBean;
import static com.byzaneo.commons.ui.util.MessageHelper.error;
import static com.byzaneo.commons.ui.util.MessageHelper.info;
import static com.byzaneo.generix.xcbl.portal.task.AbstractUploadPdfEditionTask.VARIABLE_CUSTOMER;
import static com.byzaneo.generix.xcbl.portal.task.AbstractUploadPdfEditionTask.VARIABLE_PLACEHOLDER;
import static com.byzaneo.generix.xcbl.portal.task.AbstractUploadPdfEditionTask.VARIABLE_SELECTED_TEMPLATE;
import static com.byzaneo.generix.xcbl.portal.task.AbstractUploadPdfEditionTask.VARIABLE_SELECT_CURRENT_PARTNER;
import static java.util.Collections.emptyList;
import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;
import static org.springframework.data.domain.Sort.Direction.ASC;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.io.FilenameUtils;
import org.primefaces.event.FileUploadEvent;
import org.springframework.data.domain.PageRequest;

import com.byzaneo.commons.service.ServiceException;
import com.byzaneo.generix.bean.*;
import com.byzaneo.generix.gmi.bean.SmartPdfTemplate;
import com.byzaneo.generix.service.TransformService;
import com.byzaneo.generix.ui.ApplicationHandler;
import com.byzaneo.generix.ui.instance.PortalISHandler;
import com.byzaneo.generix.xcbl.portal.task.UploadPdfClientEditionTask;
import com.byzaneo.security.bean.*;

public class UploadPdfTemplateListSectionEdition extends DocumentSectionEdition {

  // EDIT
  private String placeholder;

  private String customer;

  // VIEW
  protected transient SmartPdfTemplate selectedTemplate;

  protected transient List<SmartPdfTemplate> templates;

  protected transient Partner selectedPartner;

  protected transient boolean isSelectCurrentPartner = false;

  private String partnerTemplateLabel;

  @Override
  protected void prepare() {
    /* retrieve the EDIT variables if not already set on bean */
    customer = customer != null ? customer : context.getVariable(VARIABLE_CUSTOMER, null);
    placeholder = placeholder != null ? placeholder : context.getVariable(VARIABLE_PLACEHOLDER, null);
    isSelectCurrentPartner = context.getVariable(VARIABLE_SELECT_CURRENT_PARTNER, false);
  }

  @Override
  protected boolean validate() {
    if (selectedTemplate == null) {
      error(UploadPdfClientEditionTask.LABEL_FAMILY + ".error_required_selected_template");
      return false;
    }
    return true;
  }

  @Override
  protected boolean validateConfiguration() {
    return true;
  }

  @Override
  protected void reset() {
    this.selectedTemplate = null;
    this.selectedPartner = null;
    this.templates = null;
  }

  @Override
  protected void populate() {
    context.setVariable(VARIABLE_SELECTED_TEMPLATE, selectedTemplate);
    if (this.selectedPartner != null) {
      context.setPartnerCode(this.selectedPartner.getCode());
    }
  }

  @Override
  protected String[] getRequiredFields() {
    return new String[0];// no requried fields in standard mode
  }

  @Override
  public String getLabelFamily() {
    return null;
  }

  public List<Partner> onCompletePartner(final String query) {
    if (query == null)
      return new ArrayList<>();

    Company company = this.context.getCompany();
    return this.context.getSecurityService()
        .search(
            Partner.class,
            (root, q, cb) -> cb.and(
                cb.equal(root.get(Partner_.parent), company),
                cb.or(
                    cb.like(cb.lower(root.get(Partner_.fullname)), "%" + query.toLowerCase() + "%"),
                    cb.like(cb.lower(root.get(Partner_.name)), "%" + query.toLowerCase() + "%"),
                    cb.like(cb.lower(root.get(Partner_.code)), "%" + query.toLowerCase() + "%"))),
            PageRequest.of(0, 10, ASC, Partner_.fullname.getName()))
        .getContent()
        .stream()
        .filter(g -> !company.equals(g))
        .map(g -> (Partner) g)
        .collect(Collectors.toList());
  }

  public void onSelectPartner() {
    templates = null;
  }

  public void onFileUploadTemplate(FileUploadEvent event) {
    if (isBlank(this.customer)) {
      error("Missing final customer configuration (See your administrator to configure this portlet)"); // I18N
      return;
    }
    if (this.selectedPartner == null) {
      info("Please, select a partner before uploading a template"); // I18N
      return;
    }
    try {
      // uploads the template
      List<Template> templates = this.context.getTransformService()
          .addTemplates(
              ofNullable(getManagedBean(PortalISHandler.class, PortalISHandler.MANAGED_BEAN_NAME))
                  .map(PortalISHandler::getInstance)
                  .map(Instance::getCode)
                  .orElse(null),
              TransformService.Transform.SmartPDF,
              FilenameUtils.getName(event.getFile()
                  .getFileName()),
              event.getFile()
                  .getInputstream());
      if (templates.isEmpty())
        return;

      // stores the SmartPDF template
      this.context.getSmartPdfService()
          .storeTemplate(new SmartPdfTemplate(
              this.customer,
              this.selectedPartner.getCode(),
              templates.get(0)
                  .getUri(),
              templates.get(0)
                  .getName()));
      this.templates = null;

    }
    catch (ServiceException | IllegalArgumentException e) {
      error(e, "Error uploading template (%s)", e.getMessage()); // I18N
    }
    catch (Exception e) {
      error(e, "Error uploading template (%s)", getRootCauseMessage(e)); // I18N
    }
  }

  public void onRemoveTemplate(SmartPdfTemplate template) {
    if (template != null)
      this.context.getSmartPdfService()
          .removeTemplate(template);
    this.templates = null;
  }

  public void onChangeTemplateName(SmartPdfTemplate template) {
    if (template != null)
      this.context.getSmartPdfService()
          .storeTemplate(template);
  }

  public List<SmartPdfTemplate> getTemplates() {
    if (templates == null) {
      templates = searchTemplates(this.customer, this.selectedPartner, this.isSelectCurrentPartner);
    }
    return templates;

  }

  public Partner getSelectedPartner() {
    return selectedPartner;
  }

  public void setSelectedPartner(Partner selectedPartner) {
    if (this.selectedPartner == null || selectedPartner == null || !this.selectedPartner.getCode()
        .equals(selectedPartner.getCode())) {
      this.templates = null;
    }
    this.selectedPartner = selectedPartner;
  }

  public String getPartnerTemplateLabel() {
    return partnerTemplateLabel = partnerTemplateLabel != null ? partnerTemplateLabel
        : getPartnerTemplateLabel(this.placeholder, getLabel("gnxspdfadinvpdflbls", "autocomplete_partner", "", null));
  }

  /**
   * Searches all the smartPdfTemplates for the given customer and partner or if isSelectCurrentPartner all the smartPdfTemplates for the
   * given customer and the current partner
   * 
   * @param customer
   * @param selectedPartner
   * @param isSelectCurrentPartner
   * @return
   */
  private List<SmartPdfTemplate> searchTemplates(String customer, Partner selectedPartner, boolean isSelectCurrentPartner) {
    List<SmartPdfTemplate> templates = null;
    Partner partner = this.context.getPartner();
    if (partner != null && isSelectCurrentPartner) {
      templates = this.context.getSmartPdfService()
          .getTemplates(customer, partner.getCode());
    }
    else {
      templates = selectedPartner != null
          ? this.context.getSmartPdfService()
              .getTemplates(customer, selectedPartner.getCode())
          : emptyList();

      if (templates.size() == 1) {
        setSelectedTemplate(templates.get(0));
      }
    }
    return templates;
  }

  /**
   * Obtains the label for the given id
   * 
   * @param value
   * @param defaultLabel
   * @return
   */
  private String getPartnerTemplateLabel(String value, String defaultLabel) {
    return getManagedBean(ApplicationHandler.class, ApplicationHandler.MANAGED_BEAN_NAME)
        .label(value, null, null, defaultLabel);
  }

  public String getPlaceholder() {
    return this.placeholder;
  }

  public void setPlaceholder(String placeholder) {
    this.placeholder = placeholder;
  }

  public String getCustomer() {
    return this.customer;
  }

  public void setCustomer(String customer) {
    this.customer = customer;
  }

  public SmartPdfTemplate getSelectedTemplate() {
    return selectedTemplate;
  }

  public void setSelectedTemplate(SmartPdfTemplate selectedTemplate) {
    this.selectedTemplate = selectedTemplate;
  }

  public void setTemplates(List<SmartPdfTemplate> templates) {
    this.templates = templates;
  }
}
