package com.byzaneo.generix.xcbl.portal.models;

import java.util.*;

import org.springframework.data.domain.*;

import com.byzaneo.generix.ui.model.AbstractDataModel;
import com.byzaneo.query.Query;
import com.byzaneo.xtrade.bean.*;
import com.byzaneo.xtrade.service.*;

import lombok.*;

public class WorkflowFunctionDataModel extends AbstractDataModel<WorkflowFunction> {

  private static final long serialVersionUID = -7850991862185845929L;

  private final transient WorkflowFunctionService workflowFunctionService;
  
  @Getter
  @Setter
  private transient Query query;

  public WorkflowFunctionDataModel(WorkflowFunctionService workflowFunctionService, Query query) {
    this.workflowFunctionService = workflowFunctionService;
    this.query = query;
  }
  
  @Override
  protected Page<WorkflowFunction> load(Pageable pageable, Map<String, Object> filters) {
    return this.workflowFunctionService.getAllWorkflowFunctions(query, pageable);
  }

}
