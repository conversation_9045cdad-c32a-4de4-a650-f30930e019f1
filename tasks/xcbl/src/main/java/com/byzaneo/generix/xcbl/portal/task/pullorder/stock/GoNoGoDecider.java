package com.byzaneo.generix.xcbl.portal.task.pullorder.stock;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.List;

import org.springframework.batch.core.*;
import org.springframework.batch.core.job.flow.*;
import org.springframework.beans.factory.annotation.Autowired;

import com.byzaneo.generix.edocument.service.OrderService;
import com.byzaneo.query.Query;
import com.byzaneo.query.builder.Clauses;
import com.byzaneo.query.clause.AndClause;
import com.byzaneo.xtrade.xcbl.bean.*;
import com.byzaneo.xtrade.xcbl.dao.OrderIndexDAO;
import com.byzaneo.xtrade.xcbl.util.OrderHelper;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.NameValueSetType;
import com.byzaneo.xtrade.xcbl.v4_0.ordermanagement.v1_0.ordermanagement.ItemDetailType;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

public class GoNoGoDecider implements JobExecutionDecider {

  @Autowired
  private OrderIndexDAO orderDAO;

  @Autowired
  private OrderService orderService;

  @Override
  public FlowExecutionStatus decide(JobExecution jobExecution, StepExecution stepExecution) {
    String orders = jobExecution.getJobParameters()
        .getString("orders");
    // forceUpdate indicate that we are one the same page.
    // the caller is the pooling system.
    String forceUpdate = jobExecution.getJobParameters()
        .getString("forceUpdate");
    List<String> ordersId;
    try {
      ordersId = getIdsTransmitFromParameters(orders);
    }
    catch (IOException e) {
      return FlowExecutionStatus.FAILED;
    }
    if (ordersId.isEmpty()) {
      return new FlowExecutionStatus("NOGO");
    }
    // get orders by their id
    List<Order> ordersEntity = getOrdersById(ordersId);
    if (Boolean.TRUE.toString()
        .equals(forceUpdate)) {
      // in this case, the job is called from the same view by the polling.
      // => we want to retrieve webservice data only if all processing are done
      // => NOGO if one has UpdateStatus defined to PROCESSING
      if (ordersEntity.stream()
          .anyMatch(o ->
      {
            List<ItemDetailType> itemsDetailType = o.getOrderDetail()
                .getListOfItemDetail()
                .getItemDetail();
            if (itemsDetailType.size() == 0) {
              // should never be here
              return false;
            }
            if (itemsDetailType.get(0)
                .getListOfNameValueSet() == null) {
              return false;
            }
            NameValueSetType nameValueSetType = OrderHelper.getOrCreateNameValueSetType(itemsDetailType
                .get(0)
                .getListOfNameValueSet(), OrderHelper.LINE_STATUS);
            return nameValueSetType.getListOfNameValuePair()
                .getNameValuePair()
                .stream()
                .anyMatch(n -> "UpdateStatus".equals(n.getName()) &&
                    StockProcessingStatus.PROCESSING.name()
                        .equals(n.getValue()));
          })) {
        // Order are being processed by an another Job. I don't relaunch them.
        return new FlowExecutionStatus("NOGO");
      }
      // Every order are in DONE status so I relaunch them
    }
    return new FlowExecutionStatus("GO");
  }

  public List<String> getIdsTransmitFromParameters(String orders) throws IOException {
    Type listType = new TypeToken<List<String>>() {
    }.getType();
    return new Gson().fromJson(orders, listType);
  }

  private List<Order> getOrdersById(List<String> ordersId) {
    List<OrderIndex> orderIndexs = orderDAO.search(
        new Query(new AndClause(Clauses.equal("orderType", "BlanketOrder"), Clauses.in("_id", ordersId))));
    return orderService.convertToOrderType(orderIndexs, false);
  }

}
