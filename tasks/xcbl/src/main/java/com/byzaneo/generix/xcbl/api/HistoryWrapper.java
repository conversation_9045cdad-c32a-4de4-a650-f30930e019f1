package com.byzaneo.generix.xcbl.api;

import java.io.IOException;
import java.util.Arrays;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.byzaneo.commons.api.BeanDescriptorWrapper;
import com.byzaneo.commons.bean.BeanDescriptor;
import com.byzaneo.commons.service.BeanService;
import com.byzaneo.generix.xcbl.portal.task.bean.History;

@Component("gnxHistory")
public class HistoryWrapper extends BeanDescriptorWrapper<History> {

  @Autowired
  protected BeanService beanService;

  @Override
  public Iterable<History> getBeans(Object owner) {
    History history = (History) owner;

    return Arrays.asList(history);
  }

  @Override
  protected BeanDescriptor createBeanDescriptor() {
    try {
      return beanService.fromClasspath("descriptors/historyDescriptor.xml");
    }
    catch (IOException ex) {
      throw new RuntimeException(ex);
    }
  }

  @Override
  public History getBean() {
    return new History();
  }

  @Override
  public boolean persist(History bean) {
    return false;
  }
}
