package com.byzaneo.generix.xcbl.portal.task.bean.planningschedule;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.ReferenceType;

public class PlanningScheduleItemReferenceDTO extends ReferenceType {

  private double quantity;

  public double getQuantity() {
    return quantity;
  }

  public void setQuantity(double quantity) {
    this.quantity = quantity;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    if (!super.equals(o)) return false;

    PlanningScheduleItemReferenceDTO that = (PlanningScheduleItemReferenceDTO) o;

    return Double.compare(that.quantity, quantity) == 0;
  }

  @Override
  public int hashCode() {
    int result = super.hashCode();
    long temp;
    temp = Double.doubleToLongBits(quantity);
    result = 31 * result + (int) (temp ^ (temp >>> 32));
    return result;
  }
}
