package com.byzaneo.generix.xcbl.portal.task.bean.planningschedule;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 */
public class PlanningScheduleDatesDialogDTO implements Serializable {

  private static final long serialVersionUID = 4999107251278414003L;

  private int lineNumber;

  private Date issueDate;;

  private String reference;

  private String firstUnitType;

  private double firstQuantity;

  private String secondUnitType;

  private double secondQuantity;

  private List<DateQuantityCodedType> dateQuantityList;

  private double totalQuantity;

  public Date getIssueDate() {
    return issueDate;
  }

  public void setIssueDate(Date issueDate) {
    this.issueDate = issueDate;
  }

  public int getLineNumber() {
    return lineNumber;
  }

  public void setLineNumber(int lineNumber) {
    this.lineNumber = lineNumber;
  }

  public String getReference() {
    return reference;
  }

  public void setReference(String reference) {
    this.reference = reference;
  }

  public String getFirstUnitType() {
    return firstUnitType;
  }

  public void setFirstUnitType(String firstUnitType) {
    this.firstUnitType = firstUnitType;
  }

  public double getFirstQuantity() {
    return firstQuantity;
  }

  public void setFirstQuantity(double firstQuantity) {
    this.firstQuantity = firstQuantity;
  }

  public String getSecondUnitType() {
    return secondUnitType;
  }

  public void setSecondUnitType(String secondUnitType) {
    this.secondUnitType = secondUnitType;
  }

  public double getSecondQuantity() {
    return secondQuantity;
  }

  public void setSecondQuantity(double secondQuantity) {
    this.secondQuantity = secondQuantity;
  }

  public List<DateQuantityCodedType> getDateQuantityList() {
    return dateQuantityList;
  }

  public void setDateQuantityList(List<DateQuantityCodedType> dateQuantityList) {
    this.dateQuantityList = dateQuantityList;
  }

  public double getTotalQuantity() {
    return totalQuantity;
  }

  public void setTotalQuantity(double totalQuantity) {
    this.totalQuantity = totalQuantity;
  }

}
