package com.byzaneo.generix.xcbl.portal.task.bean.planningschedule;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class PlanningScheduleHeaderDatesDTO implements Serializable {

  private List<PlanningScheduleWeekHeaderDTO> weekHeaders = new ArrayList<PlanningScheduleWeekHeaderDTO>(4);

  private List<PlanningScheduleMonthHeaderDTO> monthHeaders = new ArrayList<PlanningScheduleMonthHeaderDTO>(4);

  public List<PlanningScheduleWeekHeaderDTO> getWeekHeaders() {
    return weekHeaders;
  }

  public void setWeekHeaders(List<PlanningScheduleWeekHeaderDTO> weekHeaders) {
    this.weekHeaders = weekHeaders;
  }

  public List<PlanningScheduleMonthHeaderDTO> getMonthHeaders() {
    return monthHeaders;
  }

  public void setMonthHeaders(List<PlanningScheduleMonthHeaderDTO> monthHeaders) {
    this.monthHeaders = monthHeaders;
  }

  public void addWeekHeader(PlanningScheduleWeekHeaderDTO weekHeader) {
    weekHeaders.add(weekHeader);
  }

  public void addMonthHeader(PlanningScheduleMonthHeaderDTO monthHeader) {
    monthHeaders.add(monthHeader);
  }

  // private int fistWeek;
  // private Date fistWeekDate;
  //
  // private int secondWeek;
  // private Date secondWeekDate;
  //
  // private int thirdWeek;
  // private Date thirdWeekDate;
  //
  // private int fourthWeek;
  // private Date fourthWeekDate;
  //
  // private Date fistMothStartDate;
  // private Date fistMothEndDate;
  //
  // private Date secondMothStartDate;
  // private Date secondMothEndDate;
  //
  // private Date thirdMothStartDate;
  // private Date thirdMothEndDate;
  //
  // private Date fourthMothStartDate;
  // private Date fourthMothEndDate;

}
