package com.byzaneo.generix.xcbl.portal.task.converter;

import java.util.List;
import java.util.Optional;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import com.byzaneo.xtrade.xcbl.v4_0.core.core.ContactType;

@FacesConverter(ContactConverter.CONVERTER_ID)
public class ContactConverter implements Converter {

  public static final String CONVERTER_ID = "gnxContactConverter";

  private final List<ContactType> contactTypeList;

  public ContactConverter(List<ContactType> listContact) {
    this.contactTypeList = listContact;
  }

  @Override
  public Object getAsObject(FacesContext context, UIComponent component, String value) {
    Optional<ContactType> first = contactTypeList.stream()
        .filter(p -> value != null && value.equals(p.getContactName()))
        .findFirst();
    return first.isPresent() ? first.get() : new ContactType();
  }

  @Override
  public String getAsString(FacesContext context, UIComponent component, Object value) {
    ContactType contact = (ContactType) value;
    return contact != null ? contact.getContactName() : "";
  }
}
