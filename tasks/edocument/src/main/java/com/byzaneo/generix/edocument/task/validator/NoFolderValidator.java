package com.byzaneo.generix.edocument.task.validator;

import static com.byzaneo.commons.ui.util.MessageHelper.getMessage;
import static org.slf4j.LoggerFactory.getLogger;

import java.util.Locale;

import javax.faces.application.FacesMessage;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.validator.FacesValidator;
import javax.faces.validator.Validator;
import javax.faces.validator.ValidatorException;

import org.slf4j.Logger;

import com.byzaneo.xtrade.ipm.util.ArchivingHelper;

@FacesValidator("gnxNoFolderValidator")
public class NoFolderValidator implements Validator {

  protected static final Logger log = getLogger(NoFolderValidator.class);

  public NoFolderValidator() {
    super();
  }

  @Override
  public void validate(FacesContext context, UIComponent component, Object value) throws ValidatorException {
    Locale selectedLanguage = (Locale) component.getAttributes()
        .get("selectedLanguage");

    if (ArchivingHelper.hasQueryFolderCause(value.toString())) {
      throw new ValidatorException(new FacesMessage(FacesMessage.SEVERITY_ERROR,
          getMessage("idxtsklbls.folder_type_in_query_forbidden", "idxtsklbls.folder_type_in_query_forbidden",
              selectedLanguage),
          "taskPurge"));
    }

  }

}
