<?xml version="1.0" encoding="UTF-8"?>
<ui:component xmlns="http://www.w3.org/1999/xhtml"
              xmlns:f="http://xmlns.jcp.org/jsf/core"
              xmlns:cc="http://xmlns.jcp.org/jsf/composite" xmlns:p="http://primefaces.org/ui"
              xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
              xmlns:b="http://byzaneo.com/ui"
              xmlns:h="http://xmlns.jcp.org/jsf/html"
              xmlns:g="http://xmlns.jcp.org/jsf/composite/components/gnx">

    <cc:interface name="indexDataEdit">
        <cc:attribute name="value" type="com.byzaneo.generix.edocument.task.portal.IndexDataTask" required="true" />
    </cc:interface>

    <cc:implementation>
        <style type="text/css">
            .statusTbl, .statusTbl * {
                overflow: initial !important;
            }
        </style>
        <!-- * COLLECTION * -->
        <p:fieldset legend="#{idxtsklbls.data}" toggleable="true">
            <p:outputPanel styleClass="form-group">
                <p:outputLabel styleClass="col-sm-2 control-label" for="idatacol" value="#{idxtsklbls.collection}" />
                <p:outputPanel styleClass="col-sm-10">
					<p:autoComplete id="idatacol"
						value="#{cc.attrs.value.collection}"
						required="true"
						completeMethod="#{gnxEDocumentAppHandler.onCompleteCollections}"
						var="col" itemLabel="#{col}" itemValue="#{col}"
						groupBy="#{gnxEDocumentAppHandler.getCollectionGroup(col)}"
						dropdown="true" cache="true" forceSelection="false" inputStyle="width:305px;"
						scrollHeight="250" />
                </p:outputPanel>
            </p:outputPanel>
        </p:fieldset>
        
         <p:spacer height="50px" width="100%"/>
        
        <!-- * COLUMNS * -->
        <p:fieldset id="beanDescriptorFst" legend="#{dmttsklbls.im_columns}" toggleable="true" styleClass="psBeanDescriptorFst">
            <g:editColumns value="#{cc.attrs.value}" locales="#{cc.attrs.locales}" defaultLocale="#{cc.attrs.defaultLocale}" localeSelected="#{cc.attrs.locale}" renderAdd="true" renderRemove="true" renderClickable="true" />
        </p:fieldset>
        
        <p:spacer height="50px" width="100%"/>
       
		<!--  * VISUALIZATION * -->
        <p:fieldset legend="#{idxtsklbls.visualization}" toggleable="true">
            <!-- birt -->
            <p:outputPanel styleClass="form-group">
                <p:outputLabel styleClass="col-sm-2 control-label" style="padding-top: 0" for="vwbirt" value="#{idxtsklbls.birt}" />
                <p:outputPanel styleClass="col-sm-4">
                    <p:selectManyCheckbox id="vwbirt" value="#{cc.attrs.value.viewBirtTypes}">
                        <p:ajax event="change" process="@this" update="vwname" />
                        <f:selectItems value="#{gnxTransformAppHandler.birtOutputs}" var="bt" itemValue="#{bt}" itemLabel="#{bt.name()}" />
                    </p:selectManyCheckbox>
                </p:outputPanel>
            </p:outputPanel>
            <!-- files -->
            <p:outputPanel styleClass="form-group">
                <p:outputLabel styleClass="col-sm-2 control-label" style="padding-top: 0" for="vwfiles" value="#{idxtsklbls.files}" />
                <p:outputPanel styleClass="col-sm-2">
                    <p:inputSwitch id="vwfiles" value="#{cc.attrs.value.viewFiles}"
                                   onLabel="#{comtsklbls.switcherOnLabel_Yes}"
                                   offLabel="#{comtsklbls.switcherOffLabel_No}"
                                   styleClass="form-control">
                        <p:ajax event="change" process="@this" update="vwfquery vwname" />
                    </p:inputSwitch>
                </p:outputPanel>
            </p:outputPanel>
            <!-- file filter -->
            <p:outputPanel styleClass="form-group">
                <p:outputLabel styleClass="col-sm-2 control-label" for="vwfquery" value="#{idxtsklbls.files_filter}" />
                <p:outputPanel styleClass="col-sm-10" style="margin-left: -5px">
                    <b:query id="vwfquery" value="#{cc.attrs.value.viewFileFilter}"
                             model="#{gnxIntegrationAppHandler.documentFileQueryModel}"
                             completeMethod="#{gnxIntegrationAppHandler.onCompleteDocumentFileQuery}"
                             disabled="#{not cc.attrs.value.viewFiles}" />
                </p:outputPanel>
            </p:outputPanel>
            <!-- title/name -->
            <p:outputPanel styleClass="form-group">
                <p:outputLabel styleClass="col-sm-2 control-label" for="vwname" value="#{labels.name}" />
                <p:outputPanel styleClass="col-sm-10">
                    <p:selectOneMenu id="vwname" value="#{cc.attrs.value.viewNameProperty}" style="width: 305px"
                                     disabled="#{not cc.attrs.value.viewBirt and not cc.attrs.value.viewFiles}">
                        <f:selectItem itemValue="#{null}" itemLabel="#{labels.default}" />
                        <f:selectItems value="#{cc.attrs.value.getDescriptor(cc.attrs.owner).getProperties()}" var="prop"
                                       itemValue="#{prop.name}"
                                       itemLabel="#{gnxHandler.label(prop.label, gnxSessionHandler.locale, cc.attrs.defaultLocale)}" />
                    </p:selectOneMenu>
                </p:outputPanel>
            </p:outputPanel>
        </p:fieldset>
        
         <p:spacer height="50px" width="100%"/>
        
        <!-- * FILTER * -->
        <p:fieldset legend="#{comtsklbls.criteria_title}" toggleable="true">
            <b:query id="query" value="#{cc.attrs.value.bqlFilter}" model="#{cc.attrs.value.getQueryModel(cc.attrs.owner)}"
                completeMethod="#{cc.attrs.value.onCompleteQuery}" widgetVar="wBqlQuery" helpRendered="fase">
                <f:validator validatorId="bqlValidator"/>
            </b:query>
        </p:fieldset>
        
        <p:spacer height="50px" width="100%"/>
        
        <!-- SEARCH -->
        <p:fieldset legend="#{gnxxcblcomlbls.search_fieldset}" toggleable="true">
			<!-- ADVANCED SEARCH -->
			<g:advancedSearchEdition gnxxcblcomlbls="#{gnxxcblcomlbls}" value="#{cc.attrs.value}" showSearchConfig="true"/>
	        <p:spacer height="50px" width="100%"/>
	        <!-- QUICK SEARCH -->
	        <g:quickSearchEdition gnxxcblcomlbls="#{gnxxcblcomlbls}" value="#{cc.attrs.value}" showFieldsNumber="false" />
        </p:fieldset>
        
        <p:spacer height="50px" width="100%"/>
        
        <!-- EXPORT LIST -->
        <p:fieldset legend="#{comtsklbls.export_list}" toggleable="true">
            <p:outputPanel>
                <g:editAsynchronousListExport value="#{cc.attrs.value}" max="20000"/>
            </p:outputPanel>
            <p:outputPanel>
                <g:editListExportMax value="#{cc.attrs.value}" max="20000"/>
            </p:outputPanel>
            <p:spacer height="10px" width="100%"/>
        </p:fieldset>
        
        <p:spacer height="50px" width="100%"/>
        
         <!-- EXPORT DOCUMENTS -->
        <p:fieldset legend="#{comtsklbls.export_document}" toggleable="true">
            <p:outputPanel>
                <g:editAsynchronousDocumentExport value="#{cc.attrs.value}" max="20000"/>
            </p:outputPanel>
            <p:outputPanel>
                <g:editDocumentExportMax value="#{cc.attrs.value}" max="500"/>
            </p:outputPanel>
            <p:spacer height="10px" width="100%"/>
        </p:fieldset>
    </cc:implementation>
</ui:component>