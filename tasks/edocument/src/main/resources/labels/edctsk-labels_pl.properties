cancel = Anulowane
pt_recap_documentNumber = number
ConsignmentOrder = Consignment order
RemittanceAdvice = Remittance advice
owners = Owner
invoiceHeader_invoiceDates_invoiceDueDate = Invoice due date
pt_recap_documentPreparationDate = preparation
DebitNoteGoodsAndServices = Debit note
ReplaceModeOfShipment = Order change
ReceivingDockAdvice = Receipt
eat_documentFileCheckPolicy = Document file check policy
invoiceHeader_invoiceParty_sellerParty_nameAddress_name1 = Seller
ChangeOrderResponse = Change order response
CreditAdvice = Credit advice
pt_recap_messageUri = message URI
DelcredereInvoice = Delcredere invoice
error_loading_document = Error loading the document {0}, Please contact your administrator.
eat_processingWay = Processing way
error_msg_confirm_document = Error, one or more documents selection have been already treated. Please retry.
ChangeInHeadingSection = Order change
LineItemsNumbersChanged = Order change
actions = Actions
CrossDockingDespatchAdvice = Cross docking despatch advice
INDEXER = INDEXER
PartialInvoice = Partial invoice
invoiceHeader_invoiceParty_billToParty_partyTaxInformation_taxIdentifier_ident = Invoicee VAT code
pt_recap_status = status
PurchaseOrderResponse = Purchase order response
invoiceHeader_invoiceParty_sellerParty_partyID_ident = Seller code
DeliveryNote = Delivery note
business_executablePDF = Name of executable NAMED PDF
DetourBilling = Detour billing
AcknowledgementOfOrder = Acknowledgement of order
DeliveryForecast = Delivery forecast
RESOURCE = Resource
rte = RTE
document_update_successfully = The documents have been updated successfully
refused = Odrzucone
pt_recap_force_legal_entity = Force legal entity selection
OrderResponse = Order response
Planned = Planned despatch advice
ReversalOfACredit = Order change
unauthorized_access = Unauthorized access, your role does not allow you to access the validation of documents <br/>Please contact your administrator
ConsolidatedCreditNoteGoodsAndServices = Consolidated credit note
stage = Legal status
BlanketOrderEstimatedQuantities = Estimated blanket order
to = To
to_validate = Aby potwierdzi\u0107
DebitNoteFinancialAdjustment = Financial debit note
ConsignmentInvoice = Consignment invoice
TaxInvoice = Tax invoice
ReversalOfADebit = Order change
EmergencyOrder = Emergency order
invoiceHeader_invoiceNumber = Invoice number
PickUpNotice = Pick up notice
CommercialInvoice = Invoice
source = Validation source
successfully_removed = Successfully removed
PrepaymentInvoice = Prepayment invoice
succesfull_import = Successful import {0}
invoiceHeader_invoiceType_invoiceTypeCoded = Invoice type
DebitNoteRelatedToGoodsOrServices = Debit note
pt_recap_legal_entity = Legal entity
ThirdPartyConsolidatedInvoice = Third party consolidated invoice
CallOffDelivery = Call off delivery
DespatchAdvice = Despatch advice
CargoManifest = Cargo manifest
query = Contextual documents query
invoiceHeader_invoiceParty_billToParty_nameAddress_name1 = Invoicee
Reorder = Reorder
Archive_archiveOwner = Archive owner
ChangeOfDateTerms = Order change
ConsolidatedInvoice = Consolidated invoice
pt_recap_currencyCode = currency
schemas = Schemas
ReplaceAllDates = Order change
CreditNoteFinancialAdjustment = Financial credit note
Bordereau = Bordereau
CorrectedInvoice = Corrected invoice
NewOrder = New order
failed_to_delete = Failed to delete
pending = Invoicee
invoiceHeader_invoiceParty_billToParty_partyID_ident = Invoicee code
invoiceHeader_invoiceParty_buyerParty_partyTaxInformation_taxIdentifier_ident = Buyer VAT code
search_criteria = Search criteria
Archive_signeReport = Signe the report
CorrectionOfError = Order correction
ReversalForCancellation = Order change for reversal
InventoryMovementAdvice = Inventory movement
InventoryStatusAdvice = Inventory status
pt_recap_documentDate = date
pt_recap_errors = errors
CreditMemo = Credit memo
PopulateRecapList_recapListSavingRule = Documents saving rule
pt_recap_reference = reference
DebitAdvice = Debit advice
pt_recap_legalEntityCode = legal entity code
issueDate = Issue date
pt_recap_softwareVersion = version
sent_partially = Cz\u0119\u015Bciowo wys\u0142ane
AcceptanceCertificate = Acceptance certificate
pt_recap_creationDate = creation
ReplaceHeadingSectionOnly = Order change
ChangeInDetailSection = Order change
SupplyOrServiceOrder = Supply or service order
CreditNoteGoodsAndServices = Credit note
DispositionAdvice = Receipt
VALIDATE = Validate
update_document = Validate documents
CrossDockingOrder = Cross docking order
BillOfLadingCopy = Copy of a bill of lading
MeteredGasVolumeAdvice = Receipt
business_executableD96A = Name of executable D96A (default:edoc_validation_gs1_d96a)
ChangesToItemLevelAllowanceAndCharges = Order change
DebitNote = Debit note
Reschedule = Order change
InvoicingDataSheet = Invoicing data sheet
PreviousPaymentDecisionReversed = Order change
none = Nieprzeczytane
type = Type
DeliveryNoticeGoods = Delivery notice
number = Number
BillOfLading = Bill of lading
NaturalGasSellersAllocationAdvice = Receipt
name_executablePDF = Name of executable NAMED PDF (default:edoc_invoic_filename2xcbl40.exe)
ProformaInvoice = Proforma invoice
options = Options
Archive_certificate = certificate for signing
PostReceiptAdvice = Receipt
pt_recap_owners = owner
name_executableD93A = Name of executable D93A
pt_recap_atiAmount = amount
DelcredereCreditNote = Delcredere credit note
name_executableD01B = Name of executable D01B
indexer = INDEX Schemas
DebitNoteRelatedToFinancialAdjustments = Financial debit note
AuthorizedRepresentativeInReceipt = Receipt
QuantityIncrease = Order change
Actual = Despatch advice
att_attachment_error_joining_filealreadyexists = Error attaching to document : the file already exists
Contract = Contract
gum = MIG
status = Status
name_executableD96A = Name of executable D96A (default:edoc_invoic_d96a2xcbl40.exe)
Cancel = Order cancellation
FactoredCreditNote = Factored credit note
ExceptionalOrder = Exceptional order
IntermediatePointReceiptAdvice = Receipt
SellerInitiatedChange = Order change
msg_confirm_document = You are trying to update {0} document(s) <br/> Click \"{1}\" or \"Cancel\" to cancel the change
MeteredServicesInvoice = Metered services invoice
PriceChange = Order change
AcknowledgeProcess = Acknowledge
ackcheck_timeout = Timeout (days)
CallOffOrder = Call off order
pt_recap_taxableAmount = Podstawa opodatkowania
legislation_type = Legislation
DebitInvoice = Debit Invoice
duplicate_gum = Duplicate GUM
CommissionNote = Commission note
accepted_with_amendment = Acknowledged with modification(s)
AirWaybill = Air way bill
PurchaseOrder = Purchase order
invoiceHeader_invoiceParty_buyerParty_nameAddress_name1 = Buyer
CreditNote = Credit note
pt_recap_messageType = message type
PurchaseOrderChangeRequest = Order change request
pt_recap_type = type
reference = Reference
ChangesToTotalLevelAllowanceAndCharges = Order change
documents_update_successfully = The documents have been updated successfully
from = From
CreditNoteRelatedToFinancialAdjustments = Financial credit note
SwapOrder = Swap order
BlanketPurchaseAgreement = Blanket purchase order
Order = Order
RepairOrder = Repair order
read = Odczytane
RenewalOrder = Renewal order
resource = Resource
accepted = Zatwierdzone
AddAdditionalItems = Order change
creationDate = Creation date
CreditInvoice = Credit invoice
sent = Wys\u0142ane
BookingConfirmation = Booking confirmation
PipelineAllocationAdvice = Receipt
ExciseCertificate = Excise certificate
SpecialOrder = Special order
SCHEMAS = Schemas
ChangesToLineItems = Order change
ChangeToPurchaseOrder = Change to purchase order
InventoryReport = Inventory report
Waybill = Waybill
REFUSE = Refuse
Acknowledgement = Acknowledgement
QuantityDecrease = Order change
validated = Validated
subtype = Category
Archive_timestamp = Timestamp archive
FreightInvoice = Freight invoice
BlanketOrder = Blanket order
AuthorizedRepresentativeNotInReceipt = Receipt
ReplaceItemDetailAndSummaryOnly = Order change
CreditNoteRelatedToGoodsOrServices = Credit note
business_executableD93A = Name of executable D93A
StandAloneOrder = Stand alone order
InventoryAdjustmentStatusReport = Inventory adjustment
FactoredInvoice = Factored invoice
business_executableD01B = Name of executable D01B
invoiceHeader_invoiceParty_buyerParty_partyID_ident = Buyer code
ChangeOfDates = Order change
FacilityOperatorsAllocationAdvice = Receipt
TermsChangedForNewTerms = Order change
BillOfLadingOriginal = Original bill of lading
ReplaceAllValues = Order change
successfully_added = Successfully added
modificationDate = Data modyfikacji
invoiceHeader_invoiceIssueDate = Invoice issue date
SelfBilledInvoice = Self-billed invoice
DebitMemo = Debit memo
invoiceHeader_invoiceParty_sellerParty_partyTaxInformation_taxIdentifier_ident = Seller VAT code
id_dematPartner_registration=
id_dematPartner_code=
id_dematPartner_duns=
id_dematPartner_vat=
id_dematPartner_location_address_streetName=Adres
id_dematPartner_location_address_postalCode=
id_dematPartner_location_address_city=Miasto
id_dematPartner_location_address_country=Kraj

id_direction=
id_kind=

legal_entity=
demat_partner=
start=
end=

legalEntity_addressComplement = Uzupe\u0142nienie adresu
legalEntity_postal_country = Kraj
legalEntity_fullname = Nazwa
archiveStatus = Stan archiwizacji
legalEntity_postal_city = Miasto
legalEntity_address = Adres
id_dematPartner_location_address_addressComplement = Uzupe\u0142nienie adresu
id_dematPartner_fullname = Nazwa
error = B\u0142\u0105d