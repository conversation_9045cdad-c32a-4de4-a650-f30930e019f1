# en
_from=From
_to=To
_date=Creation Date
_ref=Reference
_owner=Owner
_state=State
_type=Type
_id=ID

# LEGAL CONTROLS TASK
legislation_type= Legislation

#Task ConverterInvoiceXcbl40
name_executableD96A=Name of executable D96A (default:edoc_invoic_d96a2xcbl40.exe)
name_executableD93A=Name of executable D93A
name_executableD01B=Name of executable D01B
name_executablePDF=Name of executable NAMED PDF (default:edoc_invoic_filename2xcbl40.exe)

#Task BusinessValidator
business_executableD96A=Executable D96A (default:edoc_validation_gs1_d96a)
business_executableD93A=Executable D93A
business_executableD01B=Executable D01B
business_executablePDF=Executable NAMED PDF

# - RecapListTask OLD-
pt_recap_legal_entity=Legal entity
pt_recap_force_legal_entity=Force legal entity selection
# Item's fields
pt_recap_atiAmount=amount
pt_recap_creationDate=creation
pt_recap_currencyCode=currency
pt_recap_documentDate=date
pt_recap_documentNumber=number
pt_recap_documentPreparationDate=preparation
pt_recap_errors=erreurs
pt_recap_legalEntity=legalEntity
pt_recap_legalEntityAddress=legalEntity - address
pt_recap_legalEntityCity=legalEntity - city
pt_recap_legalEntityCountry=legalEntity - country
pt_recap_legalEntityName=legalEntity - name
pt_recap_legalEntityPostalCode=legalEntity - zip
pt_recap_legalEntityReference=legalEntity - reference
pt_recap_legalEntitySiren=legalEntity - SIREN
pt_recap_legalEntityVATNumber=legalEntity - VAT
pt_recap_messageType=message type
pt_recap_messageUri=message URI
pt_recap_legalEntityCode=code entit\u00E9 juridique
pt_recap_owners=owner
pt_recap_reference=reference
pt_recap_softwareVersion=version
pt_recap_status=status
pt_recap_taxableAmount=taxable amount
pt_recap_dematPartner=dematPartner
pt_recap_dematPartnerAddress=dematPartner - address
pt_recap_dematPartnerCity=dematPartner - city
pt_recap_dematPartnerCode=dematPartner - code
pt_recap_dematPartnerCountry=dematPartner - country
pt_recap_dematPartnerName=dematPartner - name
pt_recap_dematPartnerPostalCode=dematPartner - zip
pt_recap_dematPartnerReference=dematPartner reference
pt_recap_dematPartnerSiren=dematPartner - SIREN
pt_recap_dematPartnerVATNumber=dematPartner - VAT
pt_recap_type=type
pt_recap_monetaryAmount= VAT Amount
# - RecapListTask NEW-
list_items = RecapListItems
legal_entity= Legal Entity
force_legal_entity= Force selection of legal entity
creationDate= Validation date
norm= Norme
type= Type
documentNumber= Document number
documentDate= Document date
documentPreparationDate=Date of constitution of the document
taxableAmount= Taxable amount
atiAmount= Total amount
currencyCode= Currency
dematPartner= Demat partner
dematPartnerCode= Partner code
dematPartnerName= Partner name
dematPartnerReference= Partner reference
dematPartnerSiren= Partner SIREN
dematPartnerVATNumber= VAT
dematPartnerAddress= Partner address
dematPartnerPostalCode= Partner zip
dematPartnerCity= Partner city
dematPartnerCountry= Partner country
dematPartnerQuality=Partner Quality
softwareVersion = Version
errors= Errors
messageType= Message type
messageUri= message uri
legalEntityCode= Legal Entity Code
legalEntity_code= Code
legalEntity_fullname=Name
legalEntity_siren=SIREN
legalEntity_duns=Reference
legalEntity_vat= Intra-Community VAT
legalEntity_address=Address
legalEntity_addressComplement=Address complement
legalEntity_postal_code=Postal Code
legalEntity_postal_city=City
legalEntity_postal_country=Country
RECIPIENT=Recipient
SENDER=Sender
monetaryAmount= VAT Amount
# COMPLETE EDITION TASK
invoiceHeader_invoiceParty_buyerParty_partyID_ident=Buyer code
invoiceHeader_invoiceParty_buyerParty_nameAddress_name1=Buyer
invoiceHeader_invoiceParty_buyerParty_partyTaxInformation_taxIdentifier_ident=Buyer VAT code
invoiceHeader_invoiceParty_sellerParty_partyID_ident=Seller code
invoiceHeader_invoiceParty_sellerParty_nameAddress_name1=Seller
invoiceHeader_invoiceParty_sellerParty_partyTaxInformation_taxIdentifier_ident=Seller VAT code
invoiceHeader_invoiceParty_billToParty_partyID_ident=Invoicee code
invoiceHeader_invoiceParty_billToParty_nameAddress_name1=Invoicee
invoiceHeader_invoiceParty_billToParty_partyTaxInformation_taxIdentifier_ident=Invoicee VAT code
invoiceHeader_invoiceNumber=Invoice number
invoiceHeader_invoiceIssueDate=Invoice issue date
invoiceHeader_invoiceDates_invoiceDueDate=Invoice due date
invoiceHeader_invoiceType_invoiceTypeCoded=Invoice type
more_than_one_document_found=More than one document found, please try again
no_criteria_selected=At least one field should be filled to start the search
no_document_found=No document found, please try again

#ExchangeAllowedTask
eat_documentFileCheckPolicy=Document file check policy
eat_processingWay=Processing way

gum=MIG
rte=RTE
successfully_removed=Successfully removed
duplicate_gum=Duplicate GUM
successfully_added=Successfully added
failed_to_delete=Failed to delete
succesfull_import=Successful import {0}
search_criteria=Search criteria

#ArchiveTask
archiveOwner=Archive owner
preservation_manager=Preservation manager
enable_preservation_manager=Add preservation manager
certificate=Certificate for signature
timestamp=Timestamp archive
archiving_policy=Determine archiving policy using document type
ste=STE
signeReport=Sign the report
template=BIRT report
archiveAuditTrail=audit trail
report_subject=Archive process report
report_body=Hell {0},<br/><br/> {1} document has been archived. <br/>Please find attached the report of the archiving process.
context_document_filter=Context document filter

#PopulateRecapListTask
PopulateRecapList_recapListSavingRule=Documents saving rule

# Xml2Index
Xml2Index_query=Document Files Filter
Xml2Index_collection=Collection
Xml2Index_documentKind=Documents Kind
Xml2Index_autoArray=Detect arrays
Xml2Index_autoPrimitive=Detect primitive
Xml2Index_namespaceDeclarations=Namespace declaration
Xml2Index_namespacePrefix=Namespace prefix
Xml2Index_namespaceSeparator=Namespace separator
Xml2Index_omitRoot=Omit root element
Xml2Index_omitRootAttributes=Omit root's attributes
Xml2Index_attributePrefix=Attribute prefix
Xml2Index_valueProperty=Value property name
Xml2Index_datePatterns=Date patterns
Xml2Index_timeZone=Time zone of the dates

#EDocumentTask
actions=Actions
update_document=Validate documents
unauthorized_access=Unauthorized access, your role does not allow you to access the validation of documents <br/>Please contact your administrator
documents_update_successfully=The documents have been updated successfully
document_update_successfully=The documents have been updated successfully
options=Options
msg_confirm_document=You are trying to update {0} document(s) <br/> Click \"{1}\" or \"Cancel\" to cancel the change
validated=Validated
refused=Refused
VALIDATE=Validate
REFUSE=Refuse
error_loading_document=Error loading the document {0}, Please contact your administrator.
error_msg_confirm_document=Error, one or more documents selection have been already treated. Please retry.

#Document status
none=Unread
read=Read
accepted=Acknowledged
accepted_with_amendment=Acknowledged with alteration
cancel=Cancel
refused=Refused
sent=Shipped
sent_partially=Partially shipped
pending=Invoicee
to_validate=To validate

#Document legal status
undefined=Undefined
correct=Correct
error=Error

from=From
to=To
status=Status
stage=Legal status
type=Type
owners=Owner
modificationDate=Modification date
reference=Reference
subtype=Category
issueDate=Issue date
number=Number
archiveStatus=Archive status

#AcknowledgmentCheckTask
ackcheck_timeout=Timeout (days)

#XML Validation task
source=Validation source
query=Contextual documents query
indexer=Type of document
schemas=Schemas
resource=Resource
INDEXER=INDEXER
SCHEMAS=Schemas
RESOURCE=Resource

#AuditTrailTask
att_attachment_error_joining_filealreadyexists=Error attaching to document : the file already exists

# XCBL Message type
AcceptanceCertificate=Acceptance certificate
Acknowledgement=Acknowledgement
AcknowledgementMessage=Acknowledgement message
AcknowledgementOfOrder=Acknowledgement of order
Actual=Despatch advice
AddAdditionalItems=Order change
AirWaybill=Air way bill
AuthorizedRepresentativeInReceipt=Receipt
AuthorizedRepresentativeNotInReceipt=Receipt
BillOfLading=Bill of lading
BillOfLadingCopy=Copy of a bill of lading
BillOfLadingOriginal =Original bill of lading
BlanketOrder=Blanket order
BlanketOrderEstimatedQuantities=Estimated blanket order
BlanketPurchaseAgreement =Blanket purchase order
BookingConfirmation=Booking confirmation
Bordereau=Bordereau
CallOffDelivery=Call off delivery
CallOffOrder=Call off order
Cancel=Order cancellation
CargoManifest=Cargo manifest
ChangeInDetailSection=Order change
ChangeInHeadingSection=Order change
ChangeOfDates=Order change
ChangeOfDateTerms=Order change
ChangeOrderResponse=Change order response
ChangesToItemLevelAllowanceAndCharges=Order change
ChangesToLineItems=Order change
ChangesToTotalLevelAllowanceAndCharges=Order change
ChangeToPurchaseOrder=Change to purchase order
CommercialInvoice=Invoice
CommissionNote=Commission note
ConsignmentInvoice=Consignment invoice
ConsignmentOrder=Consignment order
ConsolidatedCreditNoteGoodsAndServices=Consolidated credit note
ConsolidatedInvoice=Consolidated invoice
Contract=Contract
CorrectedInvoice =Corrected invoice
CorrectionOfError=Order correction
CreditAdvice =Credit advice
CreditInvoice=Credit invoice
CreditMemo=Credit memo
CreditNote=Credit note
CreditNoteFinancialAdjustment=Financial credit note
CreditNoteGoodsAndServices=Credit note
CreditNoteRelatedToFinancialAdjustments=Credit note for financial adjustment
CreditNoteRelatedToGoodsOrServices=Credit note
CrossDockingDespatchAdvice=Cross docking despatch advice
CrossDockingOrder=Cross docking order
DebitAdvice=Debit advice
DebitInvoice=Debit Invoice
DebitMemo=Debit memo
DebitNote=Debit note
DebitNoteFinancialAdjustment=Debit note for financial adjustment
DebitNoteGoodsAndServices=Debit note
DebitNoteRelatedToFinancialAdjustments=Debit note for financial adjustment
DebitNoteRelatedToGoodsOrServices=Debit note
DelcredereCreditNote=Delcredere credit note
DelcredereInvoice=Delcredere invoice
DeliveryForecast=Delivery forecast
DeliveryNote=Delivery note
DeliveryNoticeGoods=Delivery notice
DespatchAdvice=Despatch advice
DetourBilling=Detour billing
DispositionAdvice=Receipt
EmergencyOrder=Emergency order
ExceptionalOrder=Exceptional order
ExciseCertificate=Excise certificate
FacilityOperatorsAllocationAdvice=Receipt
FactoredCreditNote=Factored credit note
FactoredInvoice=Factored invoice
FreightInvoice=Freight invoice
IntermediatePointReceiptAdvice=Receipt
InventoryAdjustmentStatusReport=Inventory adjustment
InventoryMovementAdvice=Inventory movement
InventoryReport=Inventory report
InventoryStatusAdvice=Inventory status
InvoicingDataSheet=Invoicing data sheet
LineItemsNumbersChanged=Order change
MeteredGasVolumeAdvice=Receipt
MeteredServicesInvoice=Metered services invoice
NaturalGasSellersAllocationAdvice=Receipt
NewOrder=New order
Order=Order
OrderResponse=Order response
PartialInvoice=Partial invoice
PickUpNotice=Pick up notice
PipelineAllocationAdvice=Receipt
Planned=Planned despatch advice
PostReceiptAdvice=Receipt
PrepaymentInvoice=Prepayment invoice
PreviousPaymentDecisionReversed=Order change
PriceChange=Order change
ProformaInvoice=Pro forma invoice
PurchaseOrder=Purchase order
PurchaseOrderChangeRequest=Order change request
PurchaseOrderResponse=Purchase order response
QuantityDecrease=Order change
QuantityIncrease=Order change
ReceivingDockAdvice=Receipt
RemittanceAdvice =Remittance advice
RenewalOrder=Renewal order
Reorder=Reorder
RepairOrder=Repair order
ReplaceAllDates=Order change
ReplaceAllValues=Order change
ReplaceHeadingSectionOnly=Order change
ReplaceItemDetailAndSummaryOnly=Order change
ReplaceModeOfShipment=Order change
Reschedule=Order change
ReversalForCancellation=Order change for reversal
ReversalOfACredit=Order change
ReversalOfADebit=Order change
SelfBilledInvoice=Self-billed invoice
SellerInitiatedChange=Order change
SpecialOrder =Special order
StandAloneOrder=Stand alone order
SupplyOrServiceOrder=Supply or service order
SwapOrder=Swap order
TaxInvoice=Tax invoice
TermsChangedForNewTerms=Order change
ThirdPartyConsolidatedInvoice=Third party consolidated invoice
Waybill=Waybill
AcknowledgeProcess=Acknowledge

Archive_archiveOwner = Archive owner
Archive_signeReport = Sign the report
Archive_certificate = Certificate for signature
Archive_timestamp = Timestamp archive

id_dematPartner_fullname=Name
id_dematPartner_registration=Registration
id_dematPartner_code=Code
id_dematPartner_duns=Reference
id_dematPartner_vat=VAT
id_dematPartner_location_address_streetName=Address
id_dematPartner_location_address_addressComplement=Address Complement
id_dematPartner_location_address_postalCode=Postal code
id_dematPartner_location_address_city=City
id_dematPartner_location_address_country=Country
id_dematPartner_creation=Demat partner creation date
id_legalEntity_creation=Legal entity creation date
id_legalEntity_fullname=Legal entity name
id_legalEntity_registration=Legal entity registration
id_legalEntity_code=Legal entity ID
id_legalEntity_duns=Legal entity reference
id_legalEntity_vat=Legal entity VAT code
id_legalEntity_location_address_streetName=Legal entity address
id_legalEntity_location_address_addressComplement=Legal entity address complement
id_legalEntity_location_address_postalCode=Legal entity zip code
id_legalEntity_location_address_city=Legal entity city
id_legalEntity_location_address_country=Legal entity country
id_direction=Direction
id_kind=Kind

collapseLegalEntityPnl=Show legal entity panel
showLegalEntityLabel=Print legal entity label
legal_entity=Legal Entity
demat_partner=Demat Partner
recipient=Receiver
sender=Sender

end = End
start = Start

recap_list_empty_print=No item to print

Links_query = Filter
Links_indexClassName = xCBL Document
gum_d93a = MIG D93A
gum_d01b = MIG D01B
archived_documents = Archived documents
gum_d96a = MIG D96A
Links_documentClassName = Document Class
Links_repository = Repository