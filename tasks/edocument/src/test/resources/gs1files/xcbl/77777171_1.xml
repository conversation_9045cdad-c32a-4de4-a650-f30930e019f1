<Invoice xmlns="rrn:org.xcbl:schemas/xcbl/v4_0/financial/v1_0/financial.xsd" xmlns:core="rrn:org.xcbl:schemas/xcbl/v4_0/core/core.xsd">
    <InvoiceHeader>
        <InvoiceNumber>77777171</InvoiceNumber>
        <InvoiceIssueDate>2008-01-30T00:00:00</InvoiceIssueDate>
        <InvoiceReferences>
            <PurchaseOrderReference>
                <core:BuyerOrderNumber>35352900</core:BuyerOrderNumber>
                <core:PurchaseOrderDate>2008-01-02T00:00:00</core:PurchaseOrderDate>
            </PurchaseOrderReference>
            <OtherInvoiceReferences>
                <core:ReferenceCoded>
                    <core:ReferenceTypeCoded>DespatchAdviceNumber</core:ReferenceTypeCoded>
                    <core:PrimaryReference>
                        <core:RefNum>83399193</core:RefNum>
                        <core:RefDate>2008-01-14T00:00:00</core:RefDate>
                    </core:PrimaryReference>
                </core:ReferenceCoded>
                <core:ReferenceCoded>
                    <core:ReferenceTypeCoded>PriceListNumber</core:ReferenceTypeCoded>
                    <core:PrimaryReference>
                        <core:RefNum>********</core:RefNum>
                    </core:PrimaryReference>
                </core:ReferenceCoded>
                <core:ReferenceCoded>
                    <core:ReferenceTypeCoded>AccountNumber</core:ReferenceTypeCoded>
                    <core:PrimaryReference>
                        <core:RefNum>********</core:RefNum>
                    </core:PrimaryReference>
                </core:ReferenceCoded>
            </OtherInvoiceReferences>
        </InvoiceReferences>
        <InvoicePurpose>
            <InvoicePurposeCoded>Original</InvoicePurposeCoded>
        </InvoicePurpose>
        <InvoiceType>
            <InvoiceTypeCoded>CommercialInvoice</InvoiceTypeCoded>
        </InvoiceType>
        <InvoiceCurrency>
            <core:CurrencyCoded>EUR</core:CurrencyCoded>
        </InvoiceCurrency>
        <InvoiceLanguage>
            <core:LanguageCoded>en</core:LanguageCoded>
        </InvoiceLanguage>
        <TaxReference>
            <core:TaxTypeCoded>ValueAddedTax</core:TaxTypeCoded>
            <core:TaxFunctionQualifierCoded>TaxRelatedInformation</core:TaxFunctionQualifierCoded>
            <core:TaxCategoryCoded>Other</core:TaxCategoryCoded>
            <core:TaxCategoryCodedOther>NotApplicable</core:TaxCategoryCodedOther>
            <core:TaxTreatmentCoded>Other</core:TaxTreatmentCoded>
            <core:TaxTreatmentCodedOther>DEB</core:TaxTreatmentCodedOther>
        </TaxReference>
        <InvoiceDates>
            <InvoiceDueDate>2008-02-28T00:00:00</InvoiceDueDate>
            <ListOfOtherInvoiceDates>
                <core:DateCoded>
                    <core:Date>2017-12-01T11:18:26</core:Date>
                    <core:DateQualifier>
                        <core:DateQualifierCoded>DocumentReceivedDateTime</core:DateQualifierCoded>
                    </core:DateQualifier>
                </core:DateCoded>
                <core:DateCoded>
                    <core:Date>2008-01-30T12:32:00</core:Date>
                    <core:DateQualifier>
                        <core:DateQualifierCoded>PreparationDateTimeOfDocument</core:DateQualifierCoded>
                    </core:DateQualifier>
                </core:DateCoded>
                <core:DateCoded>
                    <core:Date>2008-01-15T00:00:00</core:Date>
                    <core:DateQualifier>
                        <core:DateQualifierCoded>Collection</core:DateQualifierCoded>
                    </core:DateQualifier>
                </core:DateCoded>
            </ListOfOtherInvoiceDates>
        </InvoiceDates>
        <InvoiceParty>
            <BuyerParty>
                <core:PartyID>
                    <core:Ident>3026990099715</core:Ident>
                </core:PartyID>
            </BuyerParty>
            <SellerParty>
                <core:PartyID>
                    <core:Ident>3014531200171</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>AssignedByNationalTradeAgency</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>BusinessLegalStructureType</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>SOCIETE ANONYME</core:Ident>
                    </core:Identifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>LegalCapital</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>250000 EUR</core:Ident>
                    </core:Identifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>439787360</core:Ident>
                    </core:Identifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>Other</core:AgencyCoded>
                            <core:AgencyCodedOther>RCS-RCM</core:AgencyCodedOther>
                            <core:AgencyDescription>French Trade and Companies Register</core:AgencyDescription>
                        </core:Agency>
                        <core:Ident>403501596 RCS NANCY</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:NameAddress>
                    <core:Name1>CEREAL</core:Name1>
                    <core:Street>2 RUE DES ARBRES</core:Street>
                    <core:PostalCode>54000</core:PostalCode>
                    <core:City>NANCY</core:City>
                    <core:Country>
                        <core:CountryCoded>FR</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>FR2543978730</core:Ident>
                    </core:TaxIdentifier>
                    <core:RegisteredName>PEINTOU</core:RegisteredName>
                </core:PartyTaxInformation>
            </SellerParty>
            <ShipToParty>
                <core:PartyID>
                    <core:Ident>3026990099807</core:Ident>
                </core:PartyID>
            </ShipToParty>
            <BillToParty>
                <core:PartyID>
                    <core:Ident>3026990099739</core:Ident>
                </core:PartyID>
                <core:ListOfIdentifier>
                    <core:Identifier>
                        <core:Agency>
                            <core:AgencyCoded>FR-INSEE</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>Other</core:CodeListIdentifierCoded>
                            <core:CodeListIdentifierCodedOther>SIREN</core:CodeListIdentifierCodedOther>
                        </core:Agency>
                        <core:Ident>*********</core:Ident>
                    </core:Identifier>
                </core:ListOfIdentifier>
                <core:NameAddress>
                    <core:Name1>DISTRIBUTOUT</core:Name1>
                    <core:Street>8 RUE CAUMARTIN</core:Street>
                    <core:PostalCode>75009</core:PostalCode>
                    <core:City>PARIS</core:City>
                    <core:Country>
                        <core:CountryCoded>FR</core:CountryCoded>
                    </core:Country>
                </core:NameAddress>
                <core:PartyTaxInformation>
                    <core:TaxIdentifier>
                        <core:Agency>
                            <core:AgencyCoded>CEC</core:AgencyCoded>
                            <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                        </core:Agency>
                        <core:Ident>FR36*********</core:Ident>
                    </core:TaxIdentifier>
                </core:PartyTaxInformation>
            </BillToParty>
            <ShipFromParty>
                <core:PartyID>
                    <core:Ident>3014531200249</core:Ident>
                </core:PartyID>
                <core:OtherContacts>
                    <core:Contact>
                        <core:ContactName>M MARTIN</core:ContactName>
                        <core:ContactFunction>
                            <core:ContactFunctionCoded>DeliveryContact</core:ContactFunctionCoded>
                        </core:ContactFunction>
                        <core:ListOfContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue><EMAIL></core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>EmailAddress</core:ContactNumberTypeCoded>
                            </core:ContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue>0112345678</core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>FaxNumber</core:ContactNumberTypeCoded>
                            </core:ContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue>0112345687</core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>TelephoneNumber</core:ContactNumberTypeCoded>
                            </core:ContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue>8247527894</core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>TelexNumber</core:ContactNumberTypeCoded>
                            </core:ContactNumber>
                            <core:ContactNumber>
                                <core:ContactNumberValue>/C=FR/A=ATLAS/P=SERES/O=ALLEGRO/OU1=EAN/S=3027800006206/I=LF</core:ContactNumberValue>
                                <core:ContactNumberTypeCoded>Other</core:ContactNumberTypeCoded>
                                <core:ContactNumberTypeCodedOther>X400</core:ContactNumberTypeCodedOther>
                            </core:ContactNumber>
                        </core:ListOfContactNumber>
                    </core:Contact>
                </core:OtherContacts>
            </ShipFromParty>
            <ListOfPartyCoded>
                <core:PartyCoded>
                    <core:PartyID>
                        <core:Ident>3014531200188</core:Ident>
                    </core:PartyID>
                    <core:PartyRoleCoded>PartytoReceiveInvoiceFOrGoodsOrServices</core:PartyRoleCoded>
                </core:PartyCoded>
                <core:PartyCoded>
                    <core:PartyID>
                        <core:Ident>3014531200300</core:Ident>
                    </core:PartyID>
                    <core:NameAddress>
                        <core:Name1>PEINTOU SIEGE</core:Name1>
                        <core:Street>8 RUE DE LA CONCORDE</core:Street>
                        <core:PostalCode>37000</core:PostalCode>
                        <core:City>TOURS</core:City>
                        <core:Country>
                            <core:CountryCoded>FR</core:CountryCoded>
                        </core:Country>
                    </core:NameAddress>
                    <core:PartyRoleCoded>HeadOffice</core:PartyRoleCoded>
                </core:PartyCoded>
                <core:PartyCoded>
                    <core:PartyID>
                        <core:Ident>3014531200294</core:Ident>
                    </core:PartyID>
                    <core:NameAddress>
                        <core:Name1>CEREAL DECLARANT TVA</core:Name1>
                        <core:Street>99 RUE DU TANGO</core:Street>
                        <core:PostalCode>37000</core:PostalCode>
                        <core:City>TOURS</core:City>
                        <core:Country>
                            <core:CountryCoded>FR</core:CountryCoded>
                        </core:Country>
                    </core:NameAddress>
                    <core:PartyTaxInformation>
                        <core:TaxIdentifier>
                            <core:Agency>
                                <core:AgencyCoded>CEC</core:AgencyCoded>
                                <core:CodeListIdentifierCoded>ValueAddedTaxIdentification</core:CodeListIdentifierCoded>
                            </core:Agency>
                            <core:Ident>FR33322444988</core:Ident>
                        </core:TaxIdentifier>
                    </core:PartyTaxInformation>
                    <core:PartyRoleCoded>DeclarantsAgentOrRepresentative</core:PartyRoleCoded>
                </core:PartyCoded>
            </ListOfPartyCoded>
        </InvoiceParty>
        <InvoiceTermsOfDelivery>
            <core:TermsOfDeliveryFunctionCoded>DeliveryCondition</core:TermsOfDeliveryFunctionCoded>
            <core:TransportTermsCoded>Carriage-InsurancePaidTo</core:TransportTermsCoded>
            <core:ShipmentMethodOfPaymentCoded>NotSpecified</core:ShipmentMethodOfPaymentCoded>
        </InvoiceTermsOfDelivery>
        <InvoicePaymentInstructions>
            <core:PaymentTerms>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>LatePayment</core:PaymentTermCoded>
                    <core:PaymentTermDescription>Penalites de 40 euros en cas de retard de paiement</core:PaymentTermDescription>
                </core:PaymentTerm>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>Discount</core:PaymentTermCoded>
                    <core:PaymentTermDescription>1.00</core:PaymentTermDescription>
                    <core:DiscountInformation>
                        <core:DiscountDueDate>2008-02-15T00:00:00</core:DiscountDueDate>
                    </core:DiscountInformation>
                </core:PaymentTerm>
                <core:PaymentTerm>
                    <core:PaymentTermCoded>PenaltyTerms</core:PaymentTermCoded>
                    <core:PaymentTermDescription>1.50</core:PaymentTermDescription>
                </core:PaymentTerm>
            </core:PaymentTerms>
            <core:PaymentMethod>
                <core:PaymentMeanCoded>BillDrawnByTheCreditorOnTheDebtor</core:PaymentMeanCoded>
            </core:PaymentMethod>
        </InvoicePaymentInstructions>
        <InvoiceAllowancesOrCharges>
            <core:AllowOrCharge>
                <core:IndicatorCoded>Allowance</core:IndicatorCoded>
                <core:BasisCoded>MonetaryAmount</core:BasisCoded>
                <core:MethodOfHandlingCoded>AllowanceToBeIssuedByVendor</core:MethodOfHandlingCoded>
                <core:AllowOrChargeTreatment>
                    <core:AllowOrChargeTreatmentCoded>Other</core:AllowOrChargeTreatmentCoded>
                    <core:AllowOrChargeTreatmentCodedOther>FirstStepOfCalculation</core:AllowOrChargeTreatmentCodedOther>
                </core:AllowOrChargeTreatment>
                <core:AllowanceOrChargeDescription>
                    <core:ListOfDescription>REMISE PUB (Remise pour publicit&#x00e9;)</core:ListOfDescription>
                    <core:ServiceCoded>AdvertisingAllowance</core:ServiceCoded>
                </core:AllowanceOrChargeDescription>
                <core:BasisMonetaryRange>
                    <core:MonetaryLimit>
                        <core:MonetaryLimitValue>4228.93</core:MonetaryLimitValue>
                    </core:MonetaryLimit>
                </core:BasisMonetaryRange>
                <core:TypeOfAllowanceOrCharge>
                    <core:PercentageAllowanceOrCharge>
                        <core:PercentQualifier>
                            <core:PercentQualifierCoded>Other</core:PercentQualifierCoded>
                            <core:PercentQualifierCodedOther>HeaderAllowanceorCharge</core:PercentQualifierCodedOther>
                        </core:PercentQualifier>
                        <core:Percent>1</core:Percent>
                    </core:PercentageAllowanceOrCharge>
                </core:TypeOfAllowanceOrCharge>
                <core:Tax>
                    <core:TaxTypeCoded>ValueAddedTax</core:TaxTypeCoded>
                    <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
                    <core:TaxCategoryCoded>Other</core:TaxCategoryCoded>
                    <core:TaxCategoryCodedOther>5.5</core:TaxCategoryCodedOther>
                    <core:TaxPercent>5.5</core:TaxPercent>
                    <core:TaxAmount>0</core:TaxAmount>
                </core:Tax>
            </core:AllowOrCharge>
            <core:AllowOrCharge>
                <core:IndicatorCoded>Charge</core:IndicatorCoded>
                <core:MethodOfHandlingCoded>ChargeToBePaidByCustomer</core:MethodOfHandlingCoded>
                <core:AllowOrChargeTreatment>
                    <core:AllowOrChargeTreatmentCoded>Other</core:AllowOrChargeTreatmentCoded>
                    <core:AllowOrChargeTreatmentCodedOther>FirstStepOfCalculation</core:AllowOrChargeTreatmentCodedOther>
                </core:AllowOrChargeTreatment>
                <core:AllowanceOrChargeDescription>
                    <core:ListOfDescription>FRAIS MARCHANDISES (Frais de marchandises retourn&#x00e9;es)</core:ListOfDescription>
                    <core:ServiceCoded>ReturnedLoad</core:ServiceCoded>
                </core:AllowanceOrChargeDescription>
                <core:TypeOfAllowanceOrCharge>
                    <core:MonetaryValue>
                        <core:MonetaryAmount>10.00</core:MonetaryAmount>
                    </core:MonetaryValue>
                </core:TypeOfAllowanceOrCharge>
                <core:Tax>
                    <core:TaxTypeCoded>ValueAddedTax</core:TaxTypeCoded>
                    <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
                    <core:TaxCategoryCoded>Other</core:TaxCategoryCoded>
                    <core:TaxCategoryCodedOther>5.5</core:TaxCategoryCodedOther>
                    <core:TaxPercent>5.5</core:TaxPercent>
                    <core:TaxAmount>0</core:TaxAmount>
                </core:Tax>
            </core:AllowOrCharge>
        </InvoiceAllowancesOrCharges>
        <ListOfStructuredNote>
            <core:StructuredNote>
                <core:GeneralNote>APPLICATION DU TARIF K</core:GeneralNote>
                <core:TextTypeCoded>Other</core:TextTypeCoded>
                <core:TextTypeCodedOther>SupplierRemarks</core:TextTypeCodedOther>
            </core:StructuredNote>
            <core:StructuredNote>
                <core:GeneralNote>GoodsRelatedInvoice</core:GeneralNote>
                <core:NoteID>01</core:NoteID>
                <core:Agency>
                    <core:AgencyCoded>Other</core:AgencyCoded>
                    <core:AgencyCodedOther>GS1France</core:AgencyCodedOther>
                </core:Agency>
                <core:TextTypeCoded>Other</core:TextTypeCoded>
                <core:TextTypeCodedOther>GeneralInformation</core:TextTypeCodedOther>
            </core:StructuredNote>
        </ListOfStructuredNote>
    </InvoiceHeader>
    <InvoiceDetail>
        <ListOfInvoiceItemDetail>
            <InvoiceItemDetail>
                <InvoiceBaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>1</core:BuyerLineItemNum>
                        <core:SellerLineItemNum>1</core:SellerLineItemNum>
                    </LineItemNum>
                    <LineItemType>
                        <core:LineItemTypeCoded>Item</core:LineItemTypeCoded>
                    </LineItemType>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:SellerPartNumber>
                                <core:PartID>7492857810</core:PartID>
                            </core:SellerPartNumber>
                            <core:BuyerPartNumber>
                                <core:PartID>1234557810</core:PartID>
                            </core:BuyerPartNumber>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>GTIN</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifier>3083630026319</core:ProductIdentifier>
                            </core:StandardPartNumber>
                            <core:OtherItemIdentifiers>
                                <core:ProductIdentifierCoded>
                                    <core:ProductIdentifierQualifierCoded>PromotionalVariantNumber</core:ProductIdentifierQualifierCoded>
                                    <core:ProductIdentifier>00</core:ProductIdentifier>
                                </core:ProductIdentifierCoded>
                                <core:ProductIdentifierCoded>
                                    <core:ProductIdentifierQualifierCoded>SerialNumber</core:ProductIdentifierQualifierCoded>
                                    <core:ProductIdentifier>4R7TG85X7428</core:ProductIdentifier>
                                </core:ProductIdentifierCoded>
                            </core:OtherItemIdentifiers>
                        </core:PartNumbers>
                        <core:ItemDescription>BOITE DE PETITS POIS 1 KG</core:ItemDescription>
                    </ItemIdentifiers>
                    <ListOfDimension>
                        <core:Dimension>
                            <core:Measurement>
                                <core:MeasurementValue>1</core:MeasurementValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>KGM</core:UOMCoded>
                                </core:UnitOfMeasurement>
                            </core:Measurement>
                            <core:DimensionCoded>TotalNetWeight</core:DimensionCoded>
                        </core:Dimension>
                    </ListOfDimension>
                    <InvoicedQuantity>
                        <core:QuantityValue>2050</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>Other</core:UOMCoded>
                            <core:UOMCodedOther>PCE</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </InvoicedQuantity>
                    <ListOfQuantityCoded>
                        <core:QuantityCoded>
                            <core:QuantityValue>6</core:QuantityValue>
                            <core:UnitOfMeasurement>
                                <core:UOMCoded>Other</core:UOMCoded>
                                <core:UOMCodedOther>PCE</core:UOMCodedOther>
                            </core:UnitOfMeasurement>
                            <core:QuantityQualifierCoded>NumberOfUnits</core:QuantityQualifierCoded>
                        </core:QuantityCoded>
                    </ListOfQuantityCoded>
                </InvoiceBaseItemDetail>
                <InvoicePricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationNet</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1.54704</core:UnitPriceValue>
                            </core:UnitPrice>
                            <core:PriceBasisQuantity>
                                <core:QuantityValue>1</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>Other</core:UOMCoded>
                                    <core:UOMCodedOther>PCE</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:PriceBasisQuantity>
                        </core:Price>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationGross</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>1.548</core:UnitPriceValue>
                            </core:UnitPrice>
                            <core:PriceBasisQuantity>
                                <core:QuantityValue>1</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>Other</core:UOMCoded>
                                    <core:UOMCodedOther>PCE</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:PriceBasisQuantity>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:Tax>
                        <core:TaxTypeCoded>ValueAddedTax</core:TaxTypeCoded>
                        <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
                        <core:TaxCategoryCoded>StandardRate</core:TaxCategoryCoded>
                        <core:TaxPercent>5.50</core:TaxPercent>
                        <core:TaxAmount>0</core:TaxAmount>
                    </core:Tax>
                    <core:ItemAllowancesOrCharges>
                        <core:AllowOrCharge>
                            <core:IndicatorCoded>LineItemAllowance</core:IndicatorCoded>
                            <core:BasisCoded>MonetaryAmount</core:BasisCoded>
                            <core:MethodOfHandlingCoded>AllowanceToBeIssuedByVendor</core:MethodOfHandlingCoded>
                            <core:AllowOrChargeTreatment>
                                <core:AllowOrChargeTreatmentCoded>Other</core:AllowOrChargeTreatmentCoded>
                                <core:AllowOrChargeTreatmentCodedOther>FirstStepOfCalculation</core:AllowOrChargeTreatmentCodedOther>
                            </core:AllowOrChargeTreatment>
                            <core:AllowanceOrChargeDescription>
                                <core:ListOfDescription>REMISE CAMION (Remise pour camion complet)</core:ListOfDescription>
                                <core:ServiceCoded>FullTruckloadAllowance</core:ServiceCoded>
                            </core:AllowanceOrChargeDescription>
                            <core:BasisMonetaryRange>
                                <core:MonetaryLimit>
                                    <core:MonetaryLimitValue>1.548</core:MonetaryLimitValue>
                                </core:MonetaryLimit>
                            </core:BasisMonetaryRange>
                            <core:TypeOfAllowanceOrCharge>
                                <core:PercentageAllowanceOrCharge>
                                    <core:PercentQualifier>
                                        <core:PercentQualifierCoded>BasePriceAmount</core:PercentQualifierCoded>
                                    </core:PercentQualifier>
                                    <core:Percent>2</core:Percent>
                                </core:PercentageAllowanceOrCharge>
                            </core:TypeOfAllowanceOrCharge>
                        </core:AllowOrCharge>
                        <core:AllowOrCharge>
                            <core:IndicatorCoded>LineItemCharge</core:IndicatorCoded>
                            <core:MethodOfHandlingCoded>ChargeToBePaidByCustomer</core:MethodOfHandlingCoded>
                            <core:AllowOrChargeTreatment>
                                <core:AllowOrChargeTreatmentCoded>Other</core:AllowOrChargeTreatmentCoded>
                                <core:AllowOrChargeTreatmentCodedOther>FirstStepOfCalculation</core:AllowOrChargeTreatmentCodedOther>
                            </core:AllowOrChargeTreatment>
                            <core:AllowanceOrChargeDescription>
                                <core:ListOfDescription>EMBALLAGE CARTON (Suppl&#x00e9;ment emballage)</core:ListOfDescription>
                                <core:ServiceCoded>Packaging</core:ServiceCoded>
                            </core:AllowanceOrChargeDescription>
                            <core:TypeOfAllowanceOrCharge>
                                <core:MonetaryValue>
                                    <core:MonetaryAmount>0.03</core:MonetaryAmount>
                                </core:MonetaryValue>
                            </core:TypeOfAllowanceOrCharge>
                        </core:AllowOrCharge>
                    </core:ItemAllowancesOrCharges>
                    <core:LineItemTotal>
                        <core:MonetaryAmount>3171.43</core:MonetaryAmount>
                    </core:LineItemTotal>
                </InvoicePricingDetail>
                <LineItemDates>
                    <InvoiceDueDate>2008-02-28T00:00:00</InvoiceDueDate>
                    <ActualDeliveryDate>2008-01-16T00:00:00</ActualDeliveryDate>
                </LineItemDates>
            </InvoiceItemDetail>
            <InvoiceItemDetail>
                <InvoiceBaseItemDetail>
                    <LineItemNum>
                        <core:BuyerLineItemNum>2</core:BuyerLineItemNum>
                        <core:SellerLineItemNum>2</core:SellerLineItemNum>
                    </LineItemNum>
                    <LineItemType>
                        <core:LineItemTypeCoded>Other</core:LineItemTypeCoded>
                        <core:LineItemTypeCodedOther>VariableQuantityProduct</core:LineItemTypeCodedOther>
                    </LineItemType>
                    <ItemIdentifiers>
                        <core:PartNumbers>
                            <core:StandardPartNumber>
                                <core:ProductIdentifierQualifierCoded>GTIN</core:ProductIdentifierQualifierCoded>
                                <core:ProductIdentifier>3083630026333</core:ProductIdentifier>
                            </core:StandardPartNumber>
                        </core:PartNumbers>
                        <core:ItemDescription>CHOUX DE BRUXELLES</core:ItemDescription>
                    </ItemIdentifiers>
                    <InvoicedQuantity>
                        <core:QuantityValue>470</core:QuantityValue>
                        <core:UnitOfMeasurement>
                            <core:UOMCoded>Other</core:UOMCoded>
                            <core:UOMCodedOther>PCE</core:UOMCodedOther>
                        </core:UnitOfMeasurement>
                    </InvoicedQuantity>
                    <ListOfQuantityCoded>
                        <core:QuantityCoded>
                            <core:QuantityValue>3</core:QuantityValue>
                            <core:UnitOfMeasurement>
                                <core:UOMCoded>Other</core:UOMCoded>
                                <core:UOMCodedOther>PCE</core:UOMCodedOther>
                            </core:UnitOfMeasurement>
                            <core:QuantityQualifierCoded>NumberOfUnits</core:QuantityQualifierCoded>
                        </core:QuantityCoded>
                    </ListOfQuantityCoded>
                </InvoiceBaseItemDetail>
                <InvoicePricingDetail>
                    <core:ListOfPrice>
                        <core:Price>
                            <core:PricingType>
                                <core:PriceTypeCoded>CalculationNet</core:PriceTypeCoded>
                            </core:PricingType>
                            <core:UnitPrice>
                                <core:UnitPriceValue>2.250</core:UnitPriceValue>
                            </core:UnitPrice>
                            <core:PriceBasisQuantity>
                                <core:QuantityValue>1</core:QuantityValue>
                                <core:UnitOfMeasurement>
                                    <core:UOMCoded>Other</core:UOMCoded>
                                    <core:UOMCodedOther>PCE</core:UOMCodedOther>
                                </core:UnitOfMeasurement>
                            </core:PriceBasisQuantity>
                        </core:Price>
                    </core:ListOfPrice>
                    <core:Tax>
                        <core:TaxTypeCoded>ValueAddedTax</core:TaxTypeCoded>
                        <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
                        <core:TaxCategoryCoded>StandardRate</core:TaxCategoryCoded>
                        <core:TaxPercent>5.50</core:TaxPercent>
                        <core:TaxAmount>0</core:TaxAmount>
                    </core:Tax>
                    <core:LineItemTotal>
                        <core:MonetaryAmount>1057.50</core:MonetaryAmount>
                    </core:LineItemTotal>
                </InvoicePricingDetail>
                <LineItemDates>
                    <InvoiceDueDate>2008-02-28T00:00:00</InvoiceDueDate>
                    <ActualShipDate>2008-01-14T00:00:00</ActualShipDate>
                </LineItemDates>
            </InvoiceItemDetail>
        </ListOfInvoiceItemDetail>
    </InvoiceDetail>
    <InvoiceSummary>
        <InvoiceTotals>
            <InvoiceTotal>
                <core:MonetaryAmount>4429.78</core:MonetaryAmount>
            </InvoiceTotal>
            <InvoiceSubTotal>
                <core:MonetaryAmount>4429.78</core:MonetaryAmount>
            </InvoiceSubTotal>
            <TaxableValue>
                <core:MonetaryAmount>4196.64</core:MonetaryAmount>
            </TaxableValue>
            <TotalTaxAmount>
                <core:MonetaryAmount>233.14</core:MonetaryAmount>
            </TotalTaxAmount>
        </InvoiceTotals>
        <ListOfTaxSummary>
            <core:TaxSummary>
                <core:TaxTypeCoded>Total</core:TaxTypeCoded>
                <core:TaxFunctionQualifierCoded>Tax</core:TaxFunctionQualifierCoded>
                <core:TaxCategoryCoded>Other</core:TaxCategoryCoded>
                <core:TaxCategoryCodedOther>5.50</core:TaxCategoryCodedOther>
                <core:TaxableAmount>4196.64</core:TaxableAmount>
                <core:TaxAmount>233.14</core:TaxAmount>
                <core:TaxAmountInTaxAccountingCurrency>233.14</core:TaxAmountInTaxAccountingCurrency>
            </core:TaxSummary>
        </ListOfTaxSummary>
    </InvoiceSummary>
</Invoice>
