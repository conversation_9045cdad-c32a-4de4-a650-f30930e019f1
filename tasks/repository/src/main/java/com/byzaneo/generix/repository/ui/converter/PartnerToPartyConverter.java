package com.byzaneo.generix.repository.ui.converter;

import static java.util.Optional.of;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.*;

import com.byzaneo.security.bean.Partner;
import com.byzaneo.xtrade.xcbl.v4_0.core.core.*;

@FacesConverter(PartnerToPartyConverter.CONVERTER_ID)
public class PartnerToPartyConverter implements Converter {
  public static final String CONVERTER_ID = "gnxPartnerToPartyConverter";

  public PartnerToPartyConverter() {
  }

  @Override
  public Object getAsObject(FacesContext context, UIComponent component, String value) {
    return value;
  }

  @Override
  public String getAsString(FacesContext context, UIComponent component, Object value) {
    if (value == null)
      return null;

    if (value instanceof String)
      return (String) value;

    if (value instanceof Partner)
      return of(value)
          .map(Partner.class::cast)
          .map(Partner::getCode)
          .orElse(null);

    if (value instanceof PartyCodedType)
      return of(value)
          .map(PartyCodedType.class::cast)
          .map(PartyCodedType::getPartyID)
          .map(IdentifierType::getIdent)
          .orElse(null);

    return null;
  }

}
