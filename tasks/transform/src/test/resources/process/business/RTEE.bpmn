<?xml version='1.0' encoding='UTF-8'?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.activiti.org/test">
  <process id="RTEE" name="RTEE" isExecutable="true">
    <extensionElements>
      <activiti:executionListener event="start" class="com.byzaneo.xtrade.process.listener.ReportListener">
        <activiti:field name="enabled">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="loggerLevel">
          <activiti:string><![CDATA[INFO]]></activiti:string>
        </activiti:field>
        <activiti:field name="addVariables">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
        <activiti:field name="addDocuments">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="addDeads">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="addLogs">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="persist">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="persistReport">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="persistDeads">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
        <activiti:field name="writeEmptyReport">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
        <activiti:field name="writeAsync">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="writeTimeoutInMinutes">
          <activiti:string><![CDATA[120]]></activiti:string>
        </activiti:field>
        <activiti:field name="notifyOnlyIfProblem">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="acknowledged">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
        <activiti:field name="acknowledgementDirectory">
          <activiti:expression><![CDATA[${process_work_dir_string}/ack]]></activiti:expression>
        </activiti:field>
        <activiti:field name="acknowledgementProducer">
          <activiti:string><![CDATA[com.byzaneo.xtrade.process.listener.ReportListener$DefaultAckProducer]]></activiti:string>
        </activiti:field>
      </activiti:executionListener>
      <activiti:executionListener event="end" class="com.byzaneo.xtrade.process.listener.ReportListener">
        <activiti:field name="enabled">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="addVariables">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
        <activiti:field name="addDocuments">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="addDeads">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="addLogs">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="persist">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
        <activiti:field name="persistReport">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="persistDeads">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
        <activiti:field name="writeEmptyReport">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
        <activiti:field name="writeAsync">
          <activiti:string><![CDATA[true]]></activiti:string>
        </activiti:field>
        <activiti:field name="writeTimeoutInMinutes">
          <activiti:string><![CDATA[120]]></activiti:string>
        </activiti:field>
        <activiti:field name="notifyOnlyIfProblem">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
        <activiti:field name="acknowledged">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
        <activiti:field name="acknowledgementDirectory">
          <activiti:expression><![CDATA[${process_work_dir_string}/ack]]></activiti:expression>
        </activiti:field>
        <activiti:field name="acknowledgementProducer">
          <activiti:string><![CDATA[com.byzaneo.xtrade.process.listener.ReportListener$DefaultAckProducer]]></activiti:string>
        </activiti:field>
      </activiti:executionListener>
    </extensionElements>
    <sequenceFlow id="Link8562" sourceRef="Start1" targetRef="RteSource642"/>
    <startEvent id="Start1" name="Start"/>
    <sequenceFlow id="Link8828" sourceRef="RteSource642" targetRef="End2"/>
    <serviceTask id="RteSource642" name="RTEE" activiti:class="com.byzaneo.generix.transform.task.business.RteSourceTask">
        <extensionElements>
        <activiti:field name="timeout">
          <activiti:string><![CDATA[600]]></activiti:string>
        </activiti:field>
        <activiti:field name="source">
          <activiti:string><![CDATA[/GNX/RTE_SOURCE/AIO-10100/AIO-10100]]></activiti:string>
        </activiti:field>
        <activiti:field name="input">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
        <activiti:field name="outputDirectory">
          <activiti:expression><![CDATA[${process_output_dir_string}]]></activiti:expression>
        </activiti:field>
        <activiti:field name="stdout">
          <activiti:string><![CDATA[Logs]]></activiti:string>
        </activiti:field>
        <activiti:field name="stdoutLogsLevel">
          <activiti:string><![CDATA[Info]]></activiti:string>
        </activiti:field>
        <activiti:field name="documentClassName">
          <activiti:string><![CDATA[com.byzaneo.xtrade.bean.Document]]></activiti:string>
        </activiti:field>
        <activiti:field name="fileType">
          <activiti:string><![CDATA[UNKNOWN]]></activiti:string>
        </activiti:field>
        <activiti:field name="stderr">
          <activiti:string><![CDATA[Logs]]></activiti:string>
        </activiti:field>
        <activiti:field name="stderrLogsLevel">
          <activiti:string><![CDATA[Error]]></activiti:string>
        </activiti:field>
        <activiti:field name="disabled">
          <activiti:string><![CDATA[false]]></activiti:string>
        </activiti:field>
      </extensionElements>
    </serviceTask>
    <endEvent id="End2" name="End"/>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_ID1978ad94-d7bc-481c-9eb2-8a6a799090bb">
    <bpmndi:BPMNPlane bpmnElement="ID1978ad94-d7bc-481c-9eb2-8a6a799090bb" id="BPMNPlane_ID1978ad94-d7bc-481c-9eb2-8a6a799090bb">
      <bpmndi:BPMNShape bpmnElement="Start1" id="BPMNShape_Start1">
        <omgdc:Bounds height="33.0" width="33.0" x="-652.0" y="-121.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="RteSource642" id="BPMNShape_RteSource642">
        <omgdc:Bounds height="55.0" width="105.0" x="-240.0" y="-48.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="End2" id="BPMNShape_End2">
        <omgdc:Bounds height="33.0" width="33.0" x="105.0" y="-159.0"/>
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>