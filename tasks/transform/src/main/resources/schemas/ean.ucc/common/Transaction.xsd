<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:eanucc="urn:ean.ucc:2" attributeFormDefault="unqualified" elementFormDefault="unqualified" targetNamespace="urn:ean.ucc:2" version="2.5">
    <xsd:annotation>
        <xsd:documentation>
            
                        ---------------------------
                        © Copyright GS1, 2008

                        GS1 is providing this XML Schema Definition file and resultant XML file as a service to interested industries.
                        This XML Schema Definition file and resultant XML file were developed through a consensus process of interested parties.

                        Although efforts have been made to ensure that the XML Schema Definition file and resultant XML file are correct, reliable, and technically
                        accurate,  GS1 makes NO WARRANTY, EXPRESS OR IMPLIED, THAT THIS XML Schema Definition file and resultant XML file ARE
                        CORRECT, WILL NOT REQUIRE MODIFICATION AS EXPERIENCE AND TECHNOLOGICAL ADVANCES DICTATE, OR WILL BE SUITABLE FOR
                        ANY PURPOSE OR WORKABLE IN ANY APPLICATION, OR OTHERWISE.  Use of the XML Schema Definition file and resultant XML
                        file are with the understanding that GS1 has no liability for any claim to the contrary, or for any damage or loss of any kind or nature.

                        Version Information:
                        Version Number: 2.5
                        Date of creation: August 2008

                        The schema and subsequent updates will be provided on the GS1 websites.
                        ---------------------------
                            
            </xsd:documentation>
    </xsd:annotation>
    <xsd:include schemaLocation="Command.xsd"/>
    <xsd:include schemaLocation="EntityIdentification.xsd"/>
    <xsd:complexType name="TransactionType">
        <xsd:sequence>
            <xsd:element name="entityIdentification" type="eanucc:EntityIdentificationType"/>
            <xsd:element name="command" type="eanucc:CommandType" maxOccurs="unbounded"/>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:element name="transaction" type="eanucc:TransactionType"/>
</xsd:schema>