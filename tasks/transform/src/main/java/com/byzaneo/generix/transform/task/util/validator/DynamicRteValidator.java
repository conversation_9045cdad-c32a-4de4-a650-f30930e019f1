package com.byzaneo.generix.transform.task.util.validator;

import com.byzaneo.xtrade.util.DocumentHelper;

import javax.faces.application.Application;
import javax.faces.application.FacesMessage;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.validator.FacesValidator;
import javax.faces.validator.Validator;
import javax.faces.validator.ValidatorException;
import java.text.MessageFormat;
import java.util.ResourceBundle;
import java.util.regex.Pattern;

/*
  Example valid values:
  ${variable1}
  ${Variable1}+"quoted String"
  ${Variable1} + "quotedString_" + ${variable2}
  "QuotedString" + "quotedstring" + ${Variable1}+"quotedstring2"+${Variable2}

  Example invalid values:
  $${{{variable}
  ${variable}}}} + """quotedstring"
  ${variable}++++"quotedString"
 */
@FacesValidator("dynamicRteValidator")
public class DynamicRteValidator implements Validator {
  @Override
  public void validate(FacesContext context, UIComponent component, Object value) throws ValidatorException {
    String data = (value == null) ? "" : value.toString();
    if (!Pattern.matches(DocumentHelper.STRING_AND_VARIABLE_REGEX, data)) {
      Application app = context.getApplication();
      ResourceBundle bundle = app.getResourceBundle(context, "trftsklbls");
      String message = MessageFormat.format(bundle.getString("RteSource_error_dynamic_rte"), data);
      FacesMessage fMessage = new FacesMessage(message);
      fMessage.setSeverity(FacesMessage.SEVERITY_ERROR);
      throw new ValidatorException(fMessage);
    }
  }
}
