package com.byzaneo.generix.commons.task.service;

import java.util.function.Supplier;

import org.springframework.stereotype.Service;

import com.byzaneo.generix.api.AbstractConfigurableService;
import com.byzaneo.generix.commons.task.bean.CarouselConfiguration;

@Service(CarouselConfigurationService.SERVICE_NAME)
public class CarouselConfigurationServiceImpl extends AbstractConfigurableService<CarouselConfiguration>
    implements CarouselConfigurationService {

  private static final long serialVersionUID = -3687541987884376596L;

  @Override
  public void init() throws Exception {
  }

  @Override
  protected String getVariableName() {
    return CarouselConfiguration.class.getSimpleName();
  }

  @Override
  protected String getServiceName() {
    return SERVICE_NAME;
  }

  @Override
  protected Supplier<CarouselConfiguration> getConfigurationSupplier() {
    return CarouselConfiguration::new;
  }

}
