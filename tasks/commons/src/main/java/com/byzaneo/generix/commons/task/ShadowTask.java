package com.byzaneo.generix.commons.task;

import static com.byzaneo.commons.ui.util.JSFHelper.getLocale;
import static com.byzaneo.faces.model.query.QueryModelBuilder.createQueryModel;
import static com.byzaneo.portal.util.PortalHelper.findFirstPageByPortletContentId;
import static com.byzaneo.query.util.QueryHelper.toBql;
import static com.byzaneo.query.util.QueryHelper.toQuery;
import static com.byzaneo.task.util.TaskHelper.getDefinition;
import static com.byzaneo.xtrade.ipm.ui.QueryHandler.completeDocumentQuery;
import static java.lang.String.valueOf;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.slf4j.LoggerFactory.getLogger;

import java.util.*;

import javax.faces.event.AjaxBehaviorEvent;

import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.*;

import com.byzaneo.commons.bean.BeanDescriptor;
import com.byzaneo.faces.event.CompleteEvent;
import com.byzaneo.faces.model.query.QueryModel;
import com.byzaneo.generix.bean.Instance;
import com.byzaneo.generix.service.TaskService;
import com.byzaneo.generix.xtrade.task.AbstractPortalTask;
import com.byzaneo.portal.bean.Portal;
import com.byzaneo.query.Query;
import com.byzaneo.task.annotation.*;
import com.byzaneo.task.api.TaskType;
import com.byzaneo.task.shadow.*;
import com.byzaneo.task.util.TaskHelper;

@Task(name = ShadowTask.SHADOW_TASK, library = "portal/commons", version = "1.0.0")
@TaskViewModel(labelFamily = ShadowTask.LABEL_FAMILY)
public class ShadowTask extends AbstractPortalTask implements ShadowTaskInterface {

  private static final long serialVersionUID = 1032478267604567293L;

  public static final String LABEL_FAMILY = "gnxshadowlbls";

  private static final Logger log = getLogger(ShadowTask.class);

  @Autowired
  @Qualifier(TaskService.SERVICE_NAME)
  private transient TaskService taskService;

  private transient Shadowable sourceTask;

  private transient List<Shadowable> allTasks;

  protected transient QueryModel queryModel;

  protected transient Long taskDefinitionId;
  // PROPERTIES
  protected Long sourceTaskId;

  protected Query query;

  protected String currentPageParam;


  @Override
  public Long getSourceTaskId() {
    return sourceTaskId;
  }

  @Override
  public Query getQuery() {
    return query;
  }

  public void setSourceTaskId(Long sourceTaskId) {
    this.sourceTaskId = sourceTaskId;
    if (allTasks != null) {
      allTasks.stream()
          .filter(task -> TaskHelper.getTaskDefinitionId(task)
              .equals(sourceTaskId))
          .findFirst()
          .ifPresent(task -> sourceTask = task);
    }

  }

  public void onSelectSourceTask(final AjaxBehaviorEvent event) {
    queryModel = null;
    query = null;
  }

  public List<Shadowable> getShadowableTasks(Instance instance) {
    if (allTasks == null) {
      allTasks = loadShadowableTasks(instance);
    }
    return allTasks;
  }

  public String getBqlFilter() {
    return this.query == null ? null : toBql(query);
  }

  public void setBqlFilter(String bql) {
    try {
      this.query = isBlank(bql) ? null : toQuery(bql);
    }
    catch (Exception e) {
      log.error("Wrong BQL: " + bql, e);
    }
  }

  public QueryModel getQueryModel(Instance instance) {
    if (queryModel == null) {
      queryModel = createQueryModel(getSourceTaskDescriptor(sourceTask), null, null, null, getLocale(),
          instance != null ? instance.getConfiguration()
              .getDefaultLanguage() : null,
          true, true, "type",
          "status");
    }
    return queryModel;
  }

  @Override
  public String getCurrentPageParam(Object portal) {
    if (currentPageParam == null) {
      // the method is defined in a project where I don't have dependency to the project containing the class Portal
      com.byzaneo.portal.bean.Page currentPage = findFirstPageByPortletContentId((Portal) portal, valueOf(getDefinition(this).getId()));
      currentPageParam = currentPage.getId();
    }
    return currentPageParam;
  }

  public Collection<String> onCompleteQuery(final CompleteEvent event) {
    return completeDocumentQuery(event);
  }

  private List<Shadowable> loadShadowableTasks(Instance instance) {
    List<TaskType> taskTypes = taskService.getTypes();
    return taskTypes.stream()
        .filter(taskType -> Shadowable.class
            .isAssignableFrom(taskType.getType()))
        .map(taskType -> taskService.getTasks(instance, taskType))
        .filter(Objects::nonNull)
        .flatMap(List::stream)
        .map(Shadowable.class::cast)
        .collect(toList());
  }

  private BeanDescriptor getSourceTaskDescriptor(Shadowable sourceTask) {
    return sourceTask != null ? sourceTask.getDescriptor() : null;
  }

  public Shadowable getSourceTask() {
    return sourceTask;
  }

  public void setSourceTask(Shadowable sourceTask) {
    this.sourceTask = sourceTask;
  }

  @Override
  public Long getTaskDefinitionId() {
    return taskDefinitionId == null ? taskDefinitionId = com.byzaneo.task.util.TaskHelper.getTaskDefinitionId(this) : taskDefinitionId;
  }

}
