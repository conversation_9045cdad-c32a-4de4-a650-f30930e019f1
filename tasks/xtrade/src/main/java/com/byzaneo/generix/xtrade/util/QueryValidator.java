package com.byzaneo.generix.xtrade.util;

import static com.byzaneo.commons.ui.util.MessageHelper.toFacesMessage;
import static com.byzaneo.query.util.QueryHelper.toQuery;
import static javax.faces.application.FacesMessage.SEVERITY_ERROR;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

import javax.faces.application.FacesMessage;
import javax.faces.component.UIComponent;
import javax.faces.component.UIInput;
import javax.faces.context.FacesContext;
import javax.faces.validator.FacesValidator;
import javax.faces.validator.Validator;
import javax.faces.validator.ValidatorException;

/**
 * <AUTHOR> <PERSON>AO<PERSON> <<EMAIL>>
 * @company Generix
 * @date Mar 13, 2015
 */
@FacesValidator("bqlValidator")
public class QueryValidator implements Validator {

  /**
   * @see javax.faces.validator.Validator#validate(javax.faces.context.FacesContext, javax.faces.component.UIComponent, java.lang.Object)
   */
  @Override
  public void validate(FacesContext context, UIComponent component, Object value) throws ValidatorException {
    if (value == null)
      return;
    String bql = value.toString();
    try {
      if (isNotBlank(bql)) {
        toQuery(bql);
      }
    }
    catch (Exception e) {
      String validatorMessage = null;
      if (component instanceof UIInput) {
        validatorMessage = ((UIInput) component).getValidatorMessage();
      }

      FacesMessage msg;
      if (validatorMessage != null) {
        msg = new FacesMessage(SEVERITY_ERROR, validatorMessage, validatorMessage);
      }
      else {
        msg = toFacesMessage("xtdtsklbls.error_wrong_bql", SEVERITY_ERROR, bql);
      }

      throw new ValidatorException(msg);
    }
  }
}