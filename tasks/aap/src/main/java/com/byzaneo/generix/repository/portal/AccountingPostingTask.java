/*
 * Copyright (c) 2024.
 * Created by Mansour SRIDI
 */

package com.byzaneo.generix.repository.portal;

import com.byzaneo.commons.bean.BeanDescriptor;
import com.byzaneo.commons.ui.util.MessageHelper;
import com.byzaneo.generix.service.repository.bean.aap.AccountingPosting;
import com.byzaneo.generix.xtrade.task.AbstractIndexableTask;
import com.byzaneo.security.bean.User;
import com.byzaneo.task.annotation.Task;
import com.byzaneo.task.annotation.TaskViewModel;
import com.byzaneo.task.bean.AngularPortlet;
import com.byzaneo.xtrade.api.ArchiveStatus;
import com.byzaneo.xtrade.api.DocumentConsultStatus;
import com.byzaneo.xtrade.api.DocumentStage;
import com.byzaneo.xtrade.api.PaymentStatus;
import com.byzaneo.xtrade.bean.Document;
import com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceTypeCodeType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import java.io.IOException;
import java.util.*;
import java.util.stream.Stream;

import static com.byzaneo.generix.xtrade.util.IndexableTaskHelper.toStageItems;
import static com.byzaneo.task.util.TaskHelper.error;
import static com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceTypeCodeType.COMMERCIAL_INVOICE;
import static com.byzaneo.xtrade.xcbl.v4_0.financial.v1_0.financial.InvoiceTypeCodeType.CREDIT_NOTE_GOODS_AND_SERVICES;
import static java.util.Arrays.asList;
import static java.util.Arrays.stream;
import static java.util.Collections.emptyMap;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toCollection;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.lang3.exception.ExceptionUtils.getRootCauseMessage;

@Task(name = "AAPManagement", library = "portal/accountingPosting", version = "1.0.0")
@TaskViewModel(labelFamily = AccountingPostingTask.LABEL_FAMILY)
public class AccountingPostingTask extends AbstractIndexableTask implements AngularPortlet {

    private static final long serialVersionUID = 1L;

    private final static Logger LOGGER = LoggerFactory.getLogger(AccountingPostingTask.class);

    public static final String LABEL_FAMILY = "gnxaccountingpostinglbls";


    @PrePersist
    @PreUpdate
    public void prePersist() {
    }

    @Override
    public Class<AccountingPosting> getIndexableType() {
        return AccountingPosting.class;
    }

    @Override
    protected BeanDescriptor createDescriptorStatus() {
        BeanDescriptor bean = new BeanDescriptor();
        return bean;
    }

    @Override
    protected BeanDescriptor createDescriptor() {
        if (getPathDescriptor() != null) {
            try {
                return beanService.fromClasspath(getPathDescriptor());
            } catch (IOException ex) {
                error(this, ex, getRootCauseMessage(ex));
                LOGGER.error("Failed to load bean descriptor {}", ex);
                return null;
            }
        }
        return null;
    }

    public BeanDescriptor createDescriptorScenario() {
        if (getPathDescriptor() != null) {
            try {
                return beanService.fromClasspath("descriptors/aapmanagement-scenario-columns.xml");
            } catch (IOException ex) {
                error(this, ex, getRootCauseMessage(ex));
                LOGGER.error("Failed to load bean descriptor {}", ex);
                return null;
            }
        }
        return null;
    }


    @Override
    public String getPathDescriptor() {
        return "descriptors/aapmanagement-columns.xml";
    }

    @Override
    public void setPathDescriptor(String pathDescriptor) {
        this.pathDescriptor = pathDescriptor;
    }

    @Override
    public Map<Object, String> getValues(String label, User user, String instanceCode, Locale locale, Locale selectedLanguage) {
        List<String> list;
        if (isOriginalStatusProperty(label)) {
            list = sortLabelOfStatusEntity(documentStatusService.getDocumentsStatuses(instanceCode));
            return getStatusesAsMapOfCodeObject(list, instanceCode, locale);
        } else if (isOriginalStageProperty(label)) {
            return toStageItems(sortStageLabel(asList(DocumentStage.values()), locale), selectedLanguage);
        } else if (isOriginalConsultStatusProperty(label)) {
            list = sortConsultStatusLabel(asList(DocumentConsultStatus.values()), locale);
        } else if (isOriginalPaymentStatusProperty(label)) {
            list = sortPaymentStatusLabel(asList(PaymentStatus.values()), locale);
        } else if ("acquisition".equals(label)) {
            list = sortAcquisitionLabel(asList(Document.AcquisitionType.values()), locale);
        } else if (isArchiveStatusProperty(label)) {
            list = sortArchiveStatusLabel(asList(ArchiveStatus.values()), locale);
        } else if ("invoiceTypeCoded".equals(label)) {
            List<InvoiceTypeCodeType> invoiceTypeCodesType = stream(InvoiceTypeCodeType.values())
                    .filter(type -> type != COMMERCIAL_INVOICE && type != CREDIT_NOTE_GOODS_AND_SERVICES)
                    .collect(toCollection(ArrayList::new));
            return Stream.of(asList(COMMERCIAL_INVOICE.name(), CREDIT_NOTE_GOODS_AND_SERVICES.name()),
                            sortInvoiceTypeCodeTypeByLabel(invoiceTypeCodesType, locale))
                    .flatMap(List::stream)
                    .collect(toMap(identity(), key -> label(key, locale), (o1, o2) -> o1, LinkedHashMap::new));
        } else if ("processingWay".equals(label)) {
            list = sortProcessingWay(asList(Document.ProcessingWay.values()), locale);
        } else {
            return emptyMap();
        }
        return list.stream()
                .collect(toMap(o -> o,
                        o -> getMessage(LABEL_CORE + "." + o, o, selectedLanguage)));
    }

    public String getMessage(final String messageKey, final String defaultMessage, final Locale locale, final Object... args) {
        return MessageHelper.getMessage(messageKey, defaultMessage, locale, args);
    }

}
