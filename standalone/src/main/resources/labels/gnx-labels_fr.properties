NONE_icon=fa fa-minus-circle
NONE_color=#d4d4d4
NONE=Non lu
PENDING=Brouillon
APPROVED=Approuv\u00E9
ACCEPTED=Confirm\u00E9
ACCEPTED_WITH_AMENDMENT=Confirm\u00E9 avec modifications
REFUSED=Refus\u00E9
SENT=Exp\u00E9di\u00E9
ACQUITTED=Acquitt\u00E9
ARCHIVED=Archiv\u00E9
UNARCHIVABLE=Non archivable
IN_SUBMISSION=En cours de versement
SUBMITTED=Vers\u00E9
REMOVED=Supprim\u00E9
ERROR=Erreur
WARNING=Warning
DUPLICATE=Dupliqu\u00E9
TO_VALIDATE=A valider
UPDATED=Mis \u00E0 jour
READ=Lu
CANCEL=Annul\u00E9
FATAL=Erreur syst\u00E8me
SENT_PARTIALLY=Exp\u00E9di\u00E9 partiellement
INVOICED=Factur\u00E9
PARTIALLY_ACCEPTED=Confirm\u00E9 partiellement
PARTIALLY_ACCEPTED_STYLE=status_partially_accepted
PARTIALLY_ACCEPTED_WITH_AMENDMENTS=Confirm\u00E9 partiellement avec modifications
PARTIALLY_ACCEPTED_WITH_AMENDMENTS_STYLE=status_deep_blue
PARTIALLY_SHIPPED = Envoy\u00E9 partiellement
PARTIALLY_SHIPPED_STYLE=status_yellow
ANSWERED=R\u00E9pondu
TIMEOUT=Expir\u00E9
TO_REMOVE=A supprimer
CLOSED=Ferm\u00E9
DELIVERED=Livr\u00E9
NONE_STYLE=status_yellow
PENDING_STYLE=status_yellow
APPROVED_STYLE=info
ACCEPTED_STYLE=info
ACCEPTED_WITH_AMENDMENT_ICON=fa fa-check-circle-o
ACCEPTED_WITH_AMENDMENT_STYLE=info
REFUSED_ICON=fa fa-exclamation-triangle
REFUSED_STYLE=status_red
SENT_ICON=fa fa-paper-plane-o
SENT_STYLE=info
ACQUITTED_STYLE=status_green
REMOVED_STYLE=default
ERROR_ICON=fa fa-remove
ERROR_STYLE=status_red
WARNING_STYLE=status_yellow
DUPLICATE_STYLE=status_yellow
TO_VALIDATE_STYLE=status_yellow
UPDATED_STYLE=info
READ_ICON=fa fa-eye
READ_STYLE=info
CANCEL_ICON=fa fa-times
CANCEL_STYLE=default
FATAL_STYLE=status_red
SENT_PARTIALLY_STYLE=status_yellow
INVOICED_STYLE=status_green
ANSWERED_STYLE=info
TIMEOUT_ICON=fa fa-remove fa-fw
TIMEOUT_STYLE=status_red
TO_REMOVE_STYLE=status_yellow
CLOSED_STYLE=default
DELIVERED_STYLE=status_green
SYNTAX_ERR=Err. Syntaxe
SYNTAX_ERR_STYLE=status_red
DEMAT_ERR=Err. D\u00E9mat
DEMAT_ERR_STYLE=status_red
TO_CORRECT=A corriger
TO_CORRECT_STYLE=status_yellow
OK=Ok
OK_STYLE=status_green
RESOLVED=R\u00E9solu
RESOLVED_STYLE=status_green
SHIPPED=Envoy\u00E9
SHIPPED_STYLE=status_green
ERR_XLEG=Erreur Ex. L\u00E9g.
ERR_XLEG_STYLE=status_red
REFUSED_MANUALLY=Refus\u00E9 manuellement
REFUSED_MANUALLY_STYLE=default
FORCED=Forc\u00E9
FORCED_STYLE=default
INTEGRATED=Int\u00E9gr\u00E9
INTEGRATED_STYLE=status_green
IN_DISPUTE=En litige
IN_DISPUTE_STYLE=status_yellow
BLOCKED=Bloqu\u00E9
BLOCKED_STYLE=status_orange
TO_PAY=A payer
TO_PAY_STYLE=status_green
PAID=Pay\u00E9
PAID_STYLE=status_green
NO_ROUTE=Route absente
NO_ROUTE_STYLE=status_yellow
IMPORT_CORRECTION=Correction import
IMPORT_CORRECTION_STYLE=info
UNKNOWN=Inconnu
UNKNOWN_STYLE=status_yellow
UNKNOWN_ICON=fa fa-remove
IN_PREPARATION=En cours de pr\u00E9paration
IN_PREPARATION_STYLE=status_orderlist_yellow
IN_DELIVERY=En cours de livraison
IN_DELIVERY_STYLE=status_orderlist_yellow
VALIDATED=Valid\u00E9
VALIDATED_STYLE=status_orderlist_green
IN_VALIDATION=En cours de validation
IN_VALIDATION_STYLE=status_orderlist_blue
REFERENTIAL_OK=R\u00E9f\u00E9rentiel ok
REFERENTIAL_OK_STYLE=status_green
REFERENTIAL_KO=R\u00E9f\u00E9rentiel ko
REFERENTIAL_KO_STYLE=status_red
CONTROL_OK=Contr\u00F4les ok
CONTROL_OK_STYLE=status_green
METADATA_KO=M\u00E9tadonn\u00E9es ko
METADATA_KO_STYLE=status_red
SIGNATURE_OK=Signature ok
SIGNATURE_OK_STYLE=status_green
SIGNATURE_KO=Signature ko
SIGNATURE_KO_STYLE=status_red
BEING_SENT_CLIENT=En cours envoi client
BEING_SENT_CLIENT_STYLE=status_orange
AWAITING_VALIDATION=Attente validation
AWAITING_VALIDATION_STYLE=status_orange
SMTP_ERROR=Erreur smtp
SMTP_ERROR_STYLE=status_red
CONTROL_TOTAL_KO=Contr\u00F4les montant ko
CONTROL_TOTAL_KO_STYLE=status_red
END_PROCESS=Fin traitement
END_PROCESS_STYLE=status_green
RESENT=Renvoy\u00E9
RESENT_STYLE=status_green
UNDEFINED=Non d\u00E9mat\u00E9rialis\u00E9e
CORRECT=D\u00E9mat\u00E9rialis\u00E9e
UNDEFINED_STYLE_STAGE=default
CORRECT_STYLE_STAGE=success
REFUSED_STYLE_STAGE=warning
ERROR_STYLE_STAGE=danger
UNKNOWN_STYLE_STAGE=warning
UNDEFINED_STYLE=default
ARCHIVED_STYLE=status_green
SUBMITTED_STYLE=status_green
IN_SUBMISSION_STYLE=status_yellow
UNARCHIVABLE_STYLE = status_red
SYSTEMATIC=G\u00E9n\u00E9ration syst\u00E9matique
REPLACE_IF_EXISTS=G\u00E9n\u00E9ration avec remplacement
NO_GENERATION_IF_EXISTS=Pas de g\u00E9n\u00E9ration si le fichier existe
Development=D\u00E9veloppement
Acceptance=Recette
Preproduction=Pr\u00E9-production
Production=Production
Unknown=INCONNU !
DocChart=DocChart

cancel = Annuler
selection_or_enter = Choisir ou saisir une valeur
user_lastname = Nom
channel_ftp_download = T\u00E9l\u00E9charger
partner_user_send_pass_yes = Oui je veux envoyer
info_portlet_removed = Le contenu de la portlet a \u00E9t\u00E9 supprim\u00E9.
portlet_invoice_refused_manually = INVOICE : Refuser manuellement
users_search_placeholder = Rechercher un utilisateur par utilisateur, pr\u00E9nom ou nom
portlet_asn_ship = ASN : Exp\u00E9dier
organization_address_addressComplement = Compl\u00E9ment d'adresse
customer_partner_create = Ajouter un ${client}
ANSWERED_ICON = fa fa-undo fa-fw fa-rotate-90
client_type_EDI = partenaire
AtLeastOneUser = Au moins un utilisateur d'un partenaire
instance_type_SPECIFIC = Sp\u00E9cifique
channel_adressX121 = X121 address
ks_comment = Description
docs_list = Liste des documents
configuration_page = Configuration de la page
organization_autoGenerationOfSSCC = SSCC g\u00E9n\u00E9r\u00E9
size = Taille
ks_hasprivatekey = Avec cl\u00E9 priv\u00E9e
left = Gauche
channel_Emc2VnxEndpointConfiguration = EMC\u00B2 VNX
error_removing_client = Erreur lors de la suppression du client ({0}).
doc_select = S\u00E9lectionner
domain_modif = Modifier un domaine
gcnSubscriberIdentification_error = L'identifiant de facturation Generix doit contenir au maximum 64 caract\u00E8res alphanum\u00E9riques
role = R\u00F4le
infinite = Infinie
organization_email = _email
doc_update = Mettre \u00E0 jour
result = Resultats
R = R\u00E9cepteur
S = Emetteur
agreement_file_not_found = Le CGU {0} est introuvable pour la langue {1}
login_validator = Login doit contenir au moins 3 caract\u00E8res alphanumeriques, '_', '-', '@' ou '.'.
perimeter_edit = \u00C9diter p\u00E9rim\u00E8tre pour
campaigns_new = Nouvelle campagne
SMARTPDF = SUPPLIER PORTALS
connect = Connecter
channel_arkhineo_sectionId_req = Identifiant de section
portlet_referentiel_taxes = R\u00E9f\u00E9rentiel/Taxes
validator_password = Mot de passse incorrect (minimum 3 caract\u00E8res).
report_partners_completion = STATUTS DE LA CAMPAGNE
exception_no_backoffice_access = {0} n''appartient ni \u00E0 un partenaire ni \u00E0 un client et n''a pas d''acc\u00E8s au back-office.
check_site_conditions = Je valide les conditions d'utilisation du site
partner_import_error = Erreur lors de l'import de partenaires\u00A0: {0}
doc_upload_error = Erreur d''import du document\u00A0: {0} ({1})
information = Information
ui_validator = Donn\u00E9e invalide
channel_fax_only_one_endpoint_per_organization = Un seul canal fax est autoris\u00E9
correct = Corriger
Noname = PAS DE NOM !
error_saving_partner = Erreur lors de la sauvegarde du partenaire ({0}).
portlet_invoice_join = INVOICE : Joindre
self_register_login = Enregistrez-vous
channel_ssl = SSL
doc_deleted = Document supprim\u00E9
good = bien
error_uploding_logo = Chargement du logo {0} en \u00E9chec.
deploy = D\u00E9ployer
update_user_parameters = Mettre \u00E0 jour mes param\u00E8tres
entry_add_title = Ajouter un titre
portlet_files = FILES
organization_duns = RCS-RCM
export = Exporter
channel_save_ok = Canal sauvegard\u00E9.
cart_checkout = VALIDER
exception_id_duplication = L''identifiant existe d\u00E9j\u00E0: {0}
menu_user_information = Mon compte
a_day_past = moins d'un jour
filterSuccessfullySaved = Le filtre a \u00E9t\u00E9 enregistr\u00E9
campaign_documents = Documents
users_email = Utilisateurs / email
partner_field = Champs de saisie
channel_initial = Initial
channel_subject = Sujet
channel_mdn_type = Type
lang_english = Anglais
mail_sent = Envoy\u00E9
contextual_validation_user_scope_partner_id = Le num\u00E9ro d'identification du partenaire {0} et du p\u00E9rim\u00E8tre {1} doivent \u00EAtre diff\u00E9rents pour l'utilisateur {2}
create_notifications = Cr\u00E9er une annonce
organization_end = Date de fin
remove = Supprimer
channel_HttpEndpointConfiguration = HTTP/S
user_blocked = Votre compte a \u00E9t\u00E9 d\u00E9sactiv\u00E9. Veuillez contacter votre administrateur.
portlet_deadpool = Deadpool
configuration_portal = Configuration du portail
cart_view = VOIR LE PANIER
root = Racine
channel_ftp_compression = Niveau de compression
entry_title = Titre
ks_config_require_name = Le nom du KeyStore est obligatoire.
partner_import_already_existing_users = utilisateur(s) d\u00E9j\u00E0 existant(s)
menu_security_user = Ma s\u00E9curit\u00E9
no_records_found = Pas d'enregistrement trouv\u00E9
clients_type_EDI = partenaires
menu_process_manage = Gestion
STOPPING_icon = fa fa-exclamation-circle
bank_account_bic = BIC
info_role_removed = R\u00F4le {0} supprim\u00E9
OK_label = OK
template_revision = Importer un template de mail
info_user_role_saved = R\u00F4le de l'utilisateur sauvegard\u00E9
partner_user_send_new_pass_header = R\u00E9initialiser le mot de passe pour l'utilisateur
channel = Canal
add_above = Ajouter au-dessus
menu_process_execution = Processus: Ex\u00E9cutions
from_address_validator = L'adresse mail d'\u00E9metteur n'est pas valide. Une seule adresse peut \u00EAtre remplie.
organization_phone = _phone
menu_company_information = Mes informations soci\u00E9t\u00E9
report_not_completed = Non compl\u00E9t\u00E9
order = N\u00B0 de commande
organization_vat = N\u00B0 TVA intracommunautaire
WARN_icon = fa fa-exclamation-circle
period = P\u00E9riode
trf_generic = format g\u00E9n\u00E9rique
channel_organization = Organisation
info_user_new_password = Un lien de renouvellement de mot de passe a \u00E9t\u00E9 envoy\u00E9 \u00E0 {0}.
channel_debug = Debug
organization_address_country_iSO3Country = Code ISO-3 du pays
ks_no_file_selected = Un certificat doit \u00EAtre s\u00E9lectionn\u00E9.
demat_partner_dialog_title = Historique des modifications
task_adv_search_avancee = Avanc\u00E9e
analyze = Analyser
integration = Processus
smtp_enableStartTLS = enableStartTLS
channel_X400EndpointConfiguration = X400
organization_logoLarge = _logoLarge
save = Enregistrer
exception_user_associated = {0} utilisateur(s) li\u00E9(s)
organization_address_country = Pays
channel_duplicate_error = Erreur lors de la copie du canal
angular_url = URL Angular
imenu_repository_documents = Documents
campaign_date_start = Date d\u00E9but
document_type = Type de document
regenerate_all = Reg\u00E9n\u00E9rer tous
channel_test = Tester
related_process = Chercher les processus li\u00E9s
reminder = Rappel
STARTED_color = #bebfbb
channel_used_keystore = Certificat utilis\u00E9
menu_partners = Partenaires
disable = D\u00E9sactiver
error_saving_instance = Erreur lors de la sauvegarde de l''environnement ({0}).
entry_add_question = Ajouter une question
completion_bounded = Avancement D\u00E9l\u00E9mit\u00E9
organization_freeLongText01 = _freeLongText01
organization_freeLongText02 = _freeLongText02
recent_mapping = Mapping r\u00E9cents
clear_query = Effacer
menu_admin = Administration
info_client_saved = Client {0} sauvegard\u00E9
date_last_authentication_no_date = Date de derni\u00E8re Connexion : Information Indisponible
entry_radio = Radio
regenerate_all_password = Reg\u00E9n\u00E9rer tous les mots de passe
instance_type_INVOICE = Facture
portlet_invoice_forced = INVOICE : Forcer
angular_template = Template Angular
error_password_link_generation = Une erreur est survenue lors de la g\u00E9n\u00E9ration du lien de renouvellement de mot de passe.
channel_generate_file = G\u00E9n\u00E9ration fichier automatique
collapse_all = Tout r\u00E9duire
cart_totalAmount = Total :
partner_delete_error_children_exist = Impossible de supprimer un partenaire tant qu''il a des sous-partenaires\u00A0: {0}.
rte_input_file_must_be_unique = Le fichier d'entr\u00E9e doit \u00EAtre unique
error_exporting_client = Erreur lors de l''export des clients ({0}).
freetext = Champs libres
organization_address_country_country = Code ISO-2 du pays
aws_batch = Batch AWS
environment_error_max_characters_allowed = Maximum 64 caract\u00E8res sont autoris\u00E9s
error_export_not_empty_portlet = Vous ne pouvez pas exporter : le portlet n'est pas vide.
ui_converter = Probl\u00E8 de conversion
channel_deleted = Canal supprim\u00E9
channel_encoding = Encodage
error_removing_portlet = Erreur lors de la suppression du contenu de la portlet ({0}).
info_client_removed = Client {0} supprim\u00E9
cart_numberOfArticles = article(s) dans le panier
exception_instance_not_found = L''utilisateur {0} n''a pas d''environnement configur\u00E9 pour le domaine {1}
ks_error_could_not_write_certificate_file = Impossible d'\u00E9crire de fichier associ\u00E9 pour ce certificat.
order_history_title = HISTORIQUE DE MA COMMANDE N\u00B0 :
entry_answer = R\u00E9ponse
permission_required = S\u00E9lection obligatoire
rte_status_not_deployed = Le script RTE n'est pas d\u00E9ploy\u00E9
CarrefourInvoice = CarrefourInvoice
organization_address_country_displayCountry = Pays
partner_import_file = Import d'un fichier de partenaires (*.xls, *.xlsx)
account_creation_confirm = Votre compte a \u00E9t\u00E9 cr\u00E9\u00E9 avec succ\u00E8s. Vous allez recevoir sous peu un email de confirmation \u00E0 l'adresse\u00A0:
fileName = Nom du fichier
imenu_integration = Processus
mark_as_read = Marquer comme lu
error_importing_page = Erreur lors de l'import de la page
export_extension_file = Exporter le fichier
STARTED_icon = fa fa-times-circle-o
exception_message = Une erreur est survenue. \n Veuillez r\u00E9essayer plus tard ou contacter votre administrateur.\n ({0})
organization_freeText09 = _freeText09
creation_mail_subject = Cr\u00E9ation de votre compte utilisateur pour le site de {0}
error_removing_page = Erreur lors de la suppression de la page ({0}).
organization_freeText07 = _freeText07
organization_subscriberId = Identifiant de facturation Generix
organization_freeText08 = _freeText08
organization_freeText05 = _freeText05
readonly = Lecture seule
doc_uploaded = Document import\u00E9\u00A0: {0}
organization_freeText06 = _freeText06
identical_new_passoword = Le nouveau mot de passe est identique \u00E0 l\u2019ancien mot de passe
organization_freeText03 = _freeText03
organization_freeText04 = _freeText04
organization_freeText01 = _freeText01
organization_freeText02 = _freeText02
state = \u00C9tat
exception_user_not_partner = L''utilisateur {0} du groupe {1} n''est ni un partenaire ni un client.
portlet_invoice_remove = INVOICE : Supprimer
events = D\u00E9clencheurs
tva = TVA
exception_import_portal_not_empty = Le portail n''est pas vide pour l'environnement {0}.
channel_hashing_algorithm = Algorithme de hachage
user_creation_date = Date de cr\u00E9ation
move_top = D\u00E9placer tout en haut
labels = Libell\u00E9s
menu_campaigns = Onboarding
rendered = Afficher
logistic_gs1_error_format_message = Le code national unifi\u00E9 Fournisseur (GS1) doit \u00EAtre compos\u00E9 de 7 \u00E0 10 chiffres
actions = Actions
edit_notifications = \u00C9diter l'annonce
action_line = Action sur la ligne
cut = Couper
no_extensions_found = Pas de fichier d'extension trouv\u00E9 pour cette base de donn\u00E9es
other_variable = Variable libre
document = Document
upload_file_limit = Nombre maximum de fichiers atteints
label_search = Pr\u00E9ciser votre recherche
info_user_removed = Utilisateur {0} supprim\u00E9
channel_common_name = Common name
reminder_number = Nombre de rappels
upload_with_conflict_conflict_message = Les fichiers suivants vont \u00EAtre \u00E9cras\u00E9s :
exception_invalid_gson = Fichier Gson invalide
report_partners_status = STATUS DES PARTENAIRES
channel_ftp_soTimeout = SocketOptions.SO_TIMEOUT (ms)
notification_new_contact = Email notification nouveau contact
creation = Date de cr\u00E9ation
ui_invalid_size = Taille du fichier incorrecte
menu_process = Processus
validate = Valider
FAILED_color = #cc1e00
template_import = Importer template
customer_clientUsers = Mes utilisateurs
languages = Langues
placeholder_database_name = Le nom de la base de donn\u00E9es de l'instance
refresh = Rafraichir
partner_save_error = Erreur de la sauvegarde du partenaire\u00A0: {0}.
channel_server = Nom du serveur
ks_unrecognized = Les donn\u00E9es import\u00E9es ne peuvent \u00EAtre charg\u00E9es comme un KeyStore PKCS11, PKCS12 ou JKS.\n - V\u00E9rifiez le mot de passe.\n - V\u00E9rifiez le type du fichier. \n - Essayer de changer les fichiers \"Unlimited Strength Jurisdiction Policy Files\" de votre JRE.
STARTED_label = COMMENC\u00C9
channel_create_error = Erreur lors de la cr\u00E9ation du canal.
contact_recipients = Destinataires de l'email de contact
channel_proxy_used = Utilisation d'un serveur proxy
generate_gcn_subscriber = G\u00E9n\u00E9rer l'identifiant
organization_gcnSubscriber = Client Generix
to_validate = En cours de validation
campaign_name_short = Nom court
channel_delay = D\u00E9lai entre chaque tentative
FAILED_label = \u00C9CHOU\u00C9
accounting = Contact Client
secure = S\u00E9curis\u00E9 (HTTP/S)
ks_entry_notafter = Valide jusqu'\u00E0
APPROVED_color = #87b87f
portlet_referentiel_produits_other = R\u00E9f\u00E9rentiel/Produits/Autres
imenu_portal_einvoice = e-Invoice
error_removing_template = Erreur lors de la suppression du template ({0}).
version_technical = R\u00E9f\u00E9rence technique
template_invalid_pathname = L'archive contient des fichiers dont le nom est malform\u00E9
channel_modify = Modifier un canal
portlet_documentation = DOCUMENTATION
error_user_without_primary_group = Aucun groupe principal trouv\u00E9 pour l'utilisateur.
APPROVED_label = OK
info_portlet_saved = Le contenu de la portlet a \u00E9t\u00E9 sauvegard\u00E9.
channel_ftp_execProt = execProt
imenu_repository_templates = Templates
not_valid = Pas valid
switch_off = Connect\u00E9 en tant que
logistic_gs1_company_prefix = Code Entreprise GS1 ( CNUF )
partner_client_name = Nom ${client}
page = Page
error_export_empty_page = Vous ne pouvez pas exporter une page vide;
channel_gcn_only_one_endpoint_per_organization = Un seul canal GCN est autoris\u00E9
configuration_portlet = Configuration du portlet
channel_ftp_sendNoop = Envoyer noop
error_duplicate_template = Le template {0} existe d\u00E9j\u00E0.
no_records_found_loreal_order = Aucun article ne correspond \u00E0 votre recherche. <br/> Nous vous invitons \u00E0 modifier celle-ci.
channel_mime = Type mime
complementary_info = Informations Compl\u00E9mentaires
channel_unit4 = Organisation unit4
select_instance = S\u00E9lectionner un environnement
rte_status_deployed = Le script RTE a bien \u00E9t\u00E9 d\u00E9ploy\u00E9
channel_unit1 = Organisation unit1
ks_error_could_not_find_nor_create_parent_for_friendly_name = Impossible de trouver ou cr\u00E9er un parent pour le nom d''usage {0}.
channel_unit3 = Organisation unit3
channel_unit2 = Organisation unit2
hours = Heures
warn_app_template_deleting = Le template {0} ne peut pas \u00EAtre supprim\u00E9.
yes = Oui
TO_VALIDATE_ICON = fa fa fa-pause fa-fw
start = D\u00E9marrer
GCN = COLLABORATIVE NETWORK
OK_color = #87b87f
partner_code_validator = Le code doit \u00EAtre form\u00E9 du code client (repr\u00E9sent\u00E9 de trois lettres en majuscule) suivi par \"-\" et son identification.
customer_partner_edit = ${client}
fullname = Nom Complet
channel_encryption_algorithm = Algorithme de cryptage
info_import_processes = Les processus ont \u00E9t\u00E9 import\u00E9s
document_statuses_added = {0} nouveau(x) statut(s) a\\ont \u00E9t\u00E9 ajout\u00E9(s)
confirmation_mail_message_text = Hello {0},\n\nYour account has been successfully created. Please cut and paste the following address in your favorite browser to confirm your account creation: {1}.\n\nSee you soon!=======
logistic_serial_reference_required = Le Num\u00E9ro S\u00E9quentiel est obligatoire car la g\u00E9n\u00E9ration automatique de SSCC est activ\u00E9e.
ks_cannot_load_keystore = Keystore non chargeable. V\u00E9rifiez les param\u00E8tres. Consultez un administrateur.
organization_orderContact = Contact
required = Obligatoire
doc_name = Nom du document
info_partner_saved_detail = Partenaire {0} sauvegard\u00E9\u00A0: {1}
keystore = Keystore
switch_user = Usurper identit\u00E9
channel_asynchronous = Asynchrone
ks_no_slot_index_selected = Un slot index doit \u00EAtre s\u00E9lectionn\u00E9.
customer_partner_export = Exporter un fichier ${client}
error_switching_user = Erreur lors de l''usurpation d''identit\u00E9 ({0}).
rte_base_not_supported = Les collections RTE sont exclues du domaine de test (pour l'instant)
client_type_SPECIFIC = partenaire
user_password_dialog_header = Ajouter un mot de passe
portlet_order = Commande
organization_name = Code
introduction_placeholder = Texte d'introduction Ex : \n La documentation disponible est class\u00E9e par cat\u00E9gorie. N'h\u00E9sitez pas \u00E0 cliquer sur une cat\u00E9gorie pour d\u00E9couvrir les documents que nous mettons \u00E0 votre disposition. Si un document est manquant vous pouvez nous le signaler via la page de contact.
self_register_placeholder_2_placeholder = Champ N\u00B0 unique de connexion de la page d'auto-enregistrement
general_edi_service = Generix EDI Services
menu_security_order_issuers = Donneurs d'Ordre
total_carts = Total des paniers :
exception_file_upload_unknown_request = Le param\u00E8tre de la requ\u00EAte est inconnu\u00A0: {0}
placeholder_host_name = Le nom d'h\u00F4te ou l'adresse IP du syst\u00E8me o\u00F9 fonctionne l'instance
menu_system = Syst\u00E8me
menu_process_trigger = Processus: D\u00E9clencheurs
confirmOverrideFilter = Le filtre existe d\u00E9j\u00E0. Voulez-vous l'\u00E9craser ?
add_new_status = Ajouter un statut pour les documents
customer_partner_edit_button = Mettre \u00E0 jour le ${client}
channel_compressed_message = Message compress\u00E9
logistic_gs1_company_not_provided = Merci de renseigner le Code National Unifi\u00E9 Fournisseur.
channel_WSStockEndpoint_stock_password = Mot de passe
ks_provide_friendly_name = Nom d'usage doit \u00EAtre renseign\u00E9.
channel_arkhineo_depositCert_req = Certificat de d\u00E9p\u00F4t
roadmap = Roadmap
contact_us = CONTACT
channel_AliasEndpointConfiguration = Alias
contextual_validation_user_scope_user_cpy = L''utilisateur {0} n''appartient pas \u00E0 {1}
exception_portlet_cloning = Probl\u00E8me lors du clonage de portlet {0}.
name_executable = Nom de l'ex\u00E9cutable
customer_partner_add_user = Ajouter un utilisateur
exception_duplicate_perimeter = P\u00E9rim\u00E8tre dupliqu\u00E9\u00A0: {0}.
user_message_delete = L'utilisateur sera supprim\u00E9 d\u00E9finitivement.
exception_duplicate_role = R\u00F4le dupliqu\u00E9\u00A0: {0}.
enabled_linked_document_import = Activer l'import de document attach\u00E9
theme = Th\u00E8me
channel_ftp_serverAliveCountMax = SFTP serverAliveCountMax
start_date = Date de d\u00E9but
editor = \u00C9diteur
ks_alias_not_found = Alias non trouv\u00E9\u00A0: {0}. Alias trouv\u00E9s, avec s\u00E9parateur ;\u00A0: {1}
indexing = R\u00E9indexation
warn_portlet_content_not_found = Le contenu de la portlet ayant l''id {0} n''a pas \u00E9t\u00E9 trouv\u00E9.
entry_answers = R\u00E9ponses
exception_partner_associated = {0} partenaire(s) li\u00E9(s)
channel_as2to = AS2 to
organization_address_address = _address.address
identification_validator = Identification doit contenir des caract\u00E8res alphanum\u00E9riques ou '_'.
partner_saved = Sauvegarde du partenaire\u00A0: {0}.
last_modification = Derni\u00E8re modification
chat = Chat
instance_code_validator = Le code doit \u00EAtre form\u00E9 de trois chiffres ou lettres en majuscule.
channel_number_of_retry = Nombre d'essais
menu_user_parameters = Mes param\u00E8tres
document_statuses_updated = {0} statut(s) a\\ont \u00E9t\u00E9 mis \u00E0 jour
replace = Remplacer
menu_process_deployment = Processus: D\u00E9ploiement
allow_user_managing_tab = Permettre aux utilisateurs de g\u00E9rer cet onglet
template_edit = \u00C9dition du template
organization_orderPhone = T\u00E9l\u00E9phone
day = Jour
from_address = \u00C9metteur
ks_error_could_not_extract_certificate = Impossible d'extraire le ou les certificats contenus dans le fichier.
client_number = N\u00B0 ${client}
channel_proxy_name = Nom Proxy
ks_remove_timestamp_server_integrity = Op\u00E9ration bloqu\u00E9e\u00A0: certificat encore r\u00E9f\u00E9renc\u00E9 dans un serveur d'horodatage.
minutes = Minutes
error_saving_host = Erreur lors de la sauvegarde du domaine ({0}).
format = Format
recent_business = Business r\u00E9cents
archive = Archive
history = Historique
upload_invalid_size = Taille de fichier non valide
channel_ftp_serverAliveInterval = SFTP serverAliveInterval
portlet_referentiel_global_allowances_charges = R\u00E9f\u00E9rentiel/Remises et charges globales
reminder_content = Corps du rappel
FATAL_label = CRITIQUE
deploy_process = D\u00E9ployer le processus
imenu_repository = R\u00E9f\u00E9rentiel
displayed_extension_name = Nom affich\u00E9 pour l'extension
ui_file_limit = Nombre maximum de fichiers atteint
numeric = Num\u00E9rique
to_prepare = En cours de pr\u00E9paration
info_no_portlet_defined = La portlet n'a pas \u00E9t\u00E9 d\u00E9finie pour cette page.
banking = Bancaire
error_exporting_instance = Erreur lors de l''export de l''environnement\u00A0: {0}.
campaign_name = Nom
general = G\u00E9n\u00E9ral
rte_status_deployed_but_changed = Le script RTE a \u00E9t\u00E9 modifi\u00E9 depuis son d\u00E9ploiement
channel_ftp_separator = S\u00E9parateur de path
channel_SmtpEndpointConfiguration = SMTP
test_send_mail = S'envoyer l'email
move_up = D\u00E9placer vers le haut
error_saving_perimeter = Erreur lors de la sauvegarde du p\u00E9rim\u00E8tre ({0}).
error_saving = Erreur lors de la sauvegarde ({0})
keyword_$company = Nom de l'entreprise lan\u00E7ant la campagne
portlet_invoice_actions_10 = INVOICE : Action 10
general_invoices_service = Generix Invoices Services
agreement_default_required = Il est obligatoire d''associer des CGU \u00E0 la langue par d\u00E9faut {0} (onglet CGU)
clear = Vider
partner_user_roles_save = Modifier le r\u00F4le de l'utilisateur
upload_with_conflict_title = Importer une archive
import_cert_pem_cer_crt = PEM/CER/CRT
DocumentPieChart = PieChart
organization_orderFax = Fax
error_saving_organization = Erreur: l''\u00E9l\u00E9ment existe d\u00E9j\u00E0.
move_down = D\u00E9placer vers le bas
account = Mon compte
gcnSubscriberIdentification = Identifiant de facturation Generix
host_name = Domaine
WARNING_label = AVERTISSEMENT
estimated_delivery_date = Date de livraison estim\u00E9e :
siret = SIREN
SENT_PARTIALLY_ICON = fa fa-paper-plane
EDOCUMENT = INVOICING SERVICES
STOPPED_color = #cc1e00
info_category = Pour ordonner les cat\u00E9gories, il est n\u00E9cessaire de faire un gliss\u00E9 / d\u00E9pos\u00E9 en remontant la cat\u00E9gorie du bas vers le haut
entry_add = Ajouter
error_password_generation = Une erreur est survenue lors de la g\u00E9n\u00E9ration du mot de passe.
profile = Profil
exception_gcn_subscriber_id_duplication = L''identifiant de facturation Generix existe d\u00E9j\u00E0: {0}
reminder_delay = Intervalle de temps
role_edit = \u00C9diter le r\u00F4le
menu_security_users = Utilisateurs Back Office
confirm_instance_enable = Tous les services de l'environnement seront activ\u00E9s (int\u00E9gration, onboarding...).\\n\\n\u00CAtes-vous s\u00FBr de vouloir activer cet environnement ?
tasks_campaign = Workflow de t\u00E2ches
domain = Domaine
channel_toggle_linked_chanel = Erreur lors de la modification du canal. Il existe encore des partenaires avec un alias sur ce canal :<br/>{0}
self_register_placeholder_1 = Auto-enregistrement Placeholder 1
self_register_placeholder_2 = Auto-enregistrement Placeholder 2
Category.name = Cat\u00E9gorie
clients_type_INVOICE = partenaires
preview_not_available = Aper\u00E7u non disponible
organization_bankAccount_iban = IBAN
OrderLine = OrderLineDeprecated
bank_account_name_required = Le nom est obligatoire
document_status = Statuts des documents
user_number_connexion = Connexion(s)
error_saving_file = Erreur lors de la sauvegarde du fichier ({0}).
channel_ftp_disableSecureDataChannelDefaults = D\u00E9sactiver les valeurs par d\u00E9faut de s\u00E9curit\u00E9
java_lang_Integer = Entier
warn_existing_resource = La ressource existe d\u00E9j\u00E0
organization_bankAccount_bic = BIC
channel_ftp_receiveBufferSize = Taille du buffer de t\u00E9l\u00E9chargement
perimeters = P\u00E9rim\u00E8tres
import_cert_jks_pkcs12 = JKS/PKCS#12...
logistic_missing_mandatories_message = Veuillez renseigner l'ensemble des informations requises car la g\u00E9n\u00E9ration automatique de SSCC est activ\u00E9e
portlet_freetext = FreeText
confirmation_mail_message_html = Hello {0},<br/><br/>Your account has been successfully created. Please confirm your email by clicking on the following link: <a href=\"{1}\">Email confirmation</a>.<br/></br>Alternatively, you can cut and paste the following address in your favorite browser: {1}.<br/><br/>See you soon!
channel_ftp_privateKeyUri = URI du fichier de cl\u00E9 priv\u00E9e
error_removing_role = Erreur lors de la suppression du r\u00F4le ({0}).
menu_config = Configuration
confirmationTitle = Confirmation
info_logo_uploaded = Logo charg\u00E9
organization_comment = Description
styleClass = Classes de style
channel_ftp_connectTimeout = Timeout de connexion (ms)
add_right = Ajouter \u00E0 droite
ks_hasRSApublickey = RSA
task_adv_search = Recherche par d\u00E9faut
WARNING_icon = fa fa-exclamation-circle
imenu_campaigns = Onboarding
decimal_separator = S\u00E9parateur d\u00E9cimal
warn_locked_folder = Dossier verrouill\u00E9
docs_available = Documents disponibles
ignored = Ignor\u00E9s
user_email = Adresse email
entry_delete_question = Supprimer une question
generate_new_password = G\u00E9n\u00E9rer un email de r\u00E9initialisation du mot de passe
authentication_mail_template = Email de notification nouveau contact
logistic_gs1_company_required = Le code GS1 est obligatoire car la g\u00E9n\u00E9ration automatique de SSCC est activ\u00E9e.
count = Total
bql_filter_details = Requ\u00EAte BQL filtrage portlet Invoice
undefine = Non d\u00E9mat\u00E9rialis\u00E9e
channel_signed_mdn = MDN sign\u00E9
month = Mois
portlet_order_actions_10 = ORDER : Action 10
bank_account_currency = Devise
back = Retour
title = Titre
customer_partner_delete_yes = Oui je le supprime
channel_ftp_knownHostsFile = Fichier known_hosts
duration = Dur\u00E9e
doc_upload_disabled = Vous ne pouvez pas effectuer cette action car la t\u00E2che est v\u00E9rouill\u00E9e (une campagne est peut-\u00EAtre en cours).
portlet_order_confirm_with_modification = ORDER: Confirmer avec modification
alpha = Alpha
user_phone = T\u00E9l\u00E9phone
company = Client
agreement_instance_required = Environnement du CGU obligatoire
info_portal_saved = Le portail a \u00E9t\u00E9 sauvegard\u00E9.
error_invalid_date = {0} : le contenu entr\u00E9e n''est pas une date : {1}
customer_partner_import_button = Importer le fichier
warm_change_into_empty_when_other_exists = Vous ne pouvez pas changer cette disposition en une disposition vide tant qu'il existe d'autres dispositions
WARNING_color = #ffb752
portlet_invoice_export_list = INVOICE : Exporter liste
ks_entry_alias = Alias
length = Longueur
validator_email = Email incorrect.
home = Accueil
schedule = Fr\u00E9quence
customer_partner_add_user_dialog_header = Ajouter un utilisateur au client
print = Imprimer
warn_delete_fail = La suppression a \u00E9chou\u00E9
agreement_lang_required = La langue du CGU est obligatoire
ks_morethanoneentry = Votre keystore contient plus d'un certificat. Il n'est pas pr\u00E9vu de g\u00E9rer ce type de keystore.
logistic_serial_reference = Num\u00E9ro S\u00E9quentiel
exception_instance_associated = {0} environnement(s) li\u00E9(s)
ks_error_no_friendly_name_found = Le nom d'usage n'est pas trouv\u00E9 dans les param\u00E8tres et ne peut \u00EAtre d\u00E9termin\u00E9.
error_removing_portlet_content = Erreur lors de la suppression du contenu de la portlet ({0}).
portlet_indexdata_export = INDEXDATA : Exporter
partner_imported = Partenaires import\u00E9s\u00A0: {0}.
channel_dda3 = DDA3
user_login_validator = L'identifiant de l'utilisateur doit \u00EAtre une adresse email.
channel_dda4 = DDA4
add_child_page = Ajouter une page fille
channel_dda1 = DDA1
channel_dda2 = DDA2
channel_delete_linked_chanel = Erreur lors de la suppression du canal. Il existe encore des partenaires avec un alias sur ce canal :<br/>{0}
organization_fax = _fax
ks_password = Mot de passe
channel_port = Port
value_default = Valeur par d\u00E9faut
action = Action
info_file_saved = Fichier sauvegard\u00E9
text = Texte
channel_ftp_stepWise = Parcours de r\u00E9pertoires un par un
cookie = Cookie
ks_config_require_instance = L'environnement du KeyStore est obligatoire.
partner_user_add = Ajouter un contact
templates = Templates
permission_edit = \u00C9diter permissions pour
no_processed = Non trait\u00E9
field = un champ
messages = Emails
warm_add_empty_when_other_exits = Vous ne pouvez pas ajouter une disposition vide si une disposition existe d\u00E9j\u00E0
doc_cancel = D\u00E9s\u00E9lectionner
instance_type = Type de portail
ks_upload_error = Erreur d''import du fichier\u00A0: {0}
status = Statut
portlet_invoice_actions_3 = INVOICE : Action 3
portlet_invoice_actions_4 = INVOICE : Action 4
portlet_invoice_actions_1 = INVOICE : Action 1
portlet_invoice_actions_2 = INVOICE : Action 2
portlet_invoice_actions_7 = INVOICE : Action 7
portlet_invoice_actions_8 = INVOICE : Action 8
portlet_invoice_actions_5 = INVOICE : Action 5
triggername_ACTION_10 = Action 10
portlet_invoice_actions_6 = INVOICE : Action 6
channel_uaid = Uaid
organization_web = _web
notify = Notifier
file = Fichier
java_lang_String = Texte
portlet = Portlet
warn_creation_fail = La cr\u00E9ation a \u00E9chou\u00E9
Monitoring = MonitoringDeprecated
channel_authentification_http = Authentification HTTP
menu_company_information_client = Mes informations ${client}
export_list = Exporter la liste
user_firstname_placeholder = Votre pr\u00E9nom
linked_document_import = Importer pi\u00E8ce jointe
menu = Menu droit
url = Url
keyword_$campaigncontactname = Nom du contact pour la campagne
warn_instance_code_already_used = Le code de l'environnement est d\u00E9j\u00E0 utilis\u00E9.
channel_alernative_relation = Relation alternative
portlet_invoice_actions_9 = INVOICE : Action 9
channel_encoding_message = Encoding message
doc_remove_error = Erreur lors de la suppression du document
portlet_referentiel_produits_remise_charge = R\u00E9f\u00E9rentiel/Produits/Remise et Charge
return = Retour
exception_import_export_null = Export est null.
is_locked = est v\u00E9rrouill\u00E9.
edit_library = Editer une biblioth\u00E8que
instance = Environnement
doc_rename_error = Document d\u00E9j\u00E0 existant
subject = Sujet
ks_use_start_date = Utilisable depuis
channel_ftp_useList = Utiliser FTP LIST
ks_provide_library = La librairie \u00EAtre renseign\u00E9e.
partner_user_delete = Supprimer l'utilisateur
find = Rechercher
host = H\u00F4te
menu_search = Rechercher
url_tracking = Activer le suivi des URL pour chaque utilisateur connect\u00E9
workflow = Workflow
my_bank_accounts = Mes comptes bancaire
partner_user_message_send_new_pass_msg = Un email va \u00EAtre envoy\u00E9 \u00E0 l'utilisateur avec un lien qui lui permettra de changer son mot de passe. Le lien a une dur\u00E9e de vie de 1h.
warn_profile_already_exists = Ce profil existe d\u00E9j\u00E0.
customer_partner_import_header = Importer un fichier ${client} (*.xls, *.xlsx)
task = T\u00E2che
library_created_success = La documentation a \u00E9t\u00E9 cr\u00E9\u00E9e
portlet_orderresponse_import = ORDER RESPONSE: Importer
company_edit = \u00C9diter client {1} ({0})
channel_ftp_hostname = Hostname du serveur
emitter = \u00C9metteur
PlanningSchedule = PlanningScheduleDeprecated
java_lang_Class = Classe
extension_name = Nom de l'extension
user_edit = \u00C9diter l'utilisateur
delete = Supprimer
channel_ftp_passiveMode = Mode passive
contextual_validation_partner_role = Le r\u00F4le doit exister
hour = Heure
ks_use_end_date = Utilisable jusqu'\u00E0
error_save_user_no_partner = La s\u00E9lection du partenaire est obligatoire.
channel_ftp_jschLoggingLevel = Niveau de log JSCH
clientUsers = Mes utilisateurs
account_creation = Cr\u00E9er mon compte
ks_keystoredetailstab = D\u00E9tails du KeyStore
exception_unknown_layout_type = Type du layout inconnu
show_triggers = Visualiser les d\u00E9clencheurs
processed = Trait\u00E9
company_number = ${client} n\u00B0
rigth = Droite
channel_signed_message = Message sign\u00E9
writer = R\u00E9dacteur
document_status_updated = Le statut du document {0} a \u00E9t\u00E9 mis \u00E0 jour
entry_text = Texte
channel_ArkhineoEndpointConfiguration = Arkhineo
about = A propos de TradeXpress Evolution
History = RoquetteHistory
edit_mail = \u00C9diter le mail
rte_test_properties_placeholder = Propri\u00E9t\u00E9s:
exception_failing_ACE_instanciation = Une erreur est survenue lors de l''instanciation des ACE pour\u00A0: {0}
in_delivery = En cours de livraison
rte_forbidden_value = init.tst: la propri\u00E9t\u00E9 {0} n''est pas correctement renseign\u00E9e
channel_ftp_streamDownload = T\u00E9l\u00E9charger un flux
customer_partner_create_button = Ajouter le ${client}
error_editing_default_host = Le domaine par d\u00E9faut n'est pas modifiable
execute = Ex\u00E9cuter
info_host_removed = Domaine {0} supprim\u00E9
regex = Regex
channel_ftp_ftpClientKeyStoreId = Keystore client
WARN_color = #ffb752
processing = Statut
exception_gcn_id_duplication = L''identifiant GCN existe d\u00E9j\u00E0: {0}
end_date = Date de fin
channel_ftp_flatten = Ecraser le path des fichiers
portlet_invoice_add = INVOICE : Ajouter
task_edit = \u00C9diter une t\u00E2che
com_byzaneo_xtrade_api_DocumentStage = Etat
bql_filter_title = Filtres
menu_security_partners = Partenaires
import_cert_pkcs11 = PKCS#11 (RGS-2*)...
ks_use_period = P\u00E9riode d'utilisation
uploaded_since = Mise en ligne depuis
indexClassName = Classe de l'index
error_localizations_import_bad_structure = La structure du fichier n'est pas conforme au format attendu
customer_partner_message_delete = Le ${client} sera supprim\u00E9 d\u00E9finitivement.
creation_mail_message_text = Bonjour {0},\n\nVeuillez trouver ci-dessous vos informations de connexion au site de {1} ({2})\u00A0: \n\n- Utilisateur\u00A0: {3}\n- Lien de renouvellement de mot de passe\u00A0: {4}\n\nA bient\u00F4t !
variables = Variables
error_export_portlet_rte_collection = Vous ne pouvez pas exporter la portlet Collection.
database_name = Nom de la base
warn_select_role = Veuillez s\u00E9lectionner un r\u00F4le.
Calendar = CalendarDeprecated
channel_coded_message = Message crypt\u00E9
doc_upload_duplicate = Dupliqu\u00E9\u00A0: {0}
to_edit = \u00C9diter
number_connexion = Nombre de connexions
trf_required_template_uri = L'URI du template est obligatoire.
partner_user_search_placeholder = Rechercher un utilisateur par n\u00B0, nom ${client}, utilisateur, pr\u00E9nom ou nom
portlet_invoice_export = INVOICE : Exporter
channel_auth_userDnPattern = User DN pattern
info_import_portal = Le portail a \u00E9t\u00E9 import\u00E9
progress = En cours..
gcn_subscriber_active = Client Generix
keyword_$url = URL de l'application
ks_entry_version = Version
edit_status = Editer un statut pour les documents
export_extension_all_files = Exporter les fichiers
organization_freeDouble02 = _freeDouble02
organization_freeDouble01 = _freeDouble01
scope_customer = P\u00E9rim\u00E8tre restreint
doc_edit = \u00C9diter un document
exception_duplicate_login = Login d\u00E9j\u00E0 utilis\u00E9
portlet_referentiel_produits = R\u00E9f\u00E9rentiel/Produits
eDocument = eDocument
channel_ftp_reconnectDelay = D\u00E9lai de reconnexion (ms)
channel_content_ident = Identification contenu
confirmation_mail_subject = Account creation confirmation.
error_file_not_found = Fichier non trouv\u00E9\u00A0: {0}.
user_logged = D\u00E9j\u00E0 connect\u00E9
no_data = Pas de donn\u00E9es
active_carts = panier(s) actif(s)
modification = Date de modification
portlet_asn_import = ASN : Importer
channels = Canaux
entry_remove = Supprimer
partner_field_configuration = Champs de saisie libre partenaires
campaign = Campagne
error_exporting_partner = Erreur lors de l''export des partenaires ({0}).
doc_status_code = Code statut
partner_address = Adresse postale
general_customers_service = Generix Customers Services
category_field_empty = Le champs cat\u00E9gorie doit \u00EAtre renseign\u00E9.
partner_company = Soci\u00E9t\u00E9
channel_ftp_strictHostKeyChecking = Test strict de cl\u00E9 h\u00F4te
info_user_role_duplicated = Le r\u00F4le {0} \u00E9t\u00E9  dupliqu\u00E9
managed_by_order_issuer = G\u00E9r\u00E9 par DO
purge = Purge
customer_partner_userNumber = Nombre d'utilisateurs
task_create = Cr\u00E9er une t\u00E2che
no_processes_were_found = Le RTE n'est appel\u00E9 dans aucun processus
organization_bankAccount_partner = Partenaire
info_perimeter_saved = P\u00E9rim\u00E8tre {0} sauvegard\u00E9
organization_collaborativeId = GCN Id (gid)
com_byzaneo_xtrade_api_DocumentStatus = Statut
warn_locked_file = Fichier verrouill\u00E9
rte_status_unknown = ?EAtre d\u00E9termin\u00E9
status_import_file = Import d'un fichier statuts des documents (*.xls, *.xlsx)
layout = Disposition
exception_json_export_error = Erreur lors de l''export JSON\u00A0: {0}
import_cert_pkcs7 = PKCS#7
save_register = Je valide pour m'enregistrer
disagree = Refuser
error_saving_client = Erreur lors de la sauvegarde du client ({0}).
channel_GcnEndpointConfiguration = GCN
comment = Commentaire
clientPartners = Mes ${clients}
channel_add_type = Type du canal \u00E0 ajouter
RECIPIENT = R\u00E9cepteur
doc_upload = Charger
user_new = Cr\u00E9er l'utilisateur
client_order = N\u00B0 commande
contact_subject = Sujet de l'email de contact
enable = Activer
permissions = Permissions
remindpassword = Rappel Mot de Passe
user_email_placeholder = Adresse email qui servira \u00E0 la connexion
logistic_serial_reference_and_cnuf_error_format_message = La longueur du CNUF et du num\u00E9ro s\u00E9quentiel doit \u00EAtre de 16 caract\u00E8res.
channel_ftp_ignoreFileNotFoundOrPermissionError = Ignorer les erreurs FileNotFound ou Permission
java_math_BigDecimal = D\u00E9cimal
portlet_faq = FAQ
STOPPED_label = ARR\u00CAT\u00C9
partner_comment = Commentaire
agree = Accepter
exception_task_import_parents = Probl\u00E8me d''import taskParent pour {0}.
ks_config_require_usage = L'usage du KeyStore est obligatoire.
ACCEPTED_ICON = fa fa-check
Survey = SurveyDeprecated
display_extension = Afficher extension
exception_task_import = Probl\u00E8me d''import des t\u00E2ches pour {0}.
Contract = RoquetteContract
organization_extension = Caract\u00E8re d'extension
customer_clientPartners = Mes ${clients}
add_description = Ajouter une description
info_file_removed = Fichier supprim\u00E9
permissions_dialog_campaign_title = Ajouter des partenaires \u00E0 la campagne
error_importing_partner = Erreur lors de l''import des partenaires ({0}).
channel_ftp_preferredAuthentications = Authentifications pr\u00E9f\u00E9r\u00E9es
triggername_ACTION_2 = Action 2
triggername_ACTION_1 = Action 1
triggername_ACTION_4 = Action 4
triggername_ACTION_3 = Action 3
triggername_ACTION_6 = Action 6
triggername_ACTION_5 = Action 5
triggername_ACTION_8 = Action 8
triggername_ACTION_7 = Action 7
triggername_ACTION_9 = Action 9
CAMPAIGN = ONBOARDING
warn_host_not_removed = {0} n''a PAS \u00E9t\u00E9 supprim\u00E9 ({1} environnement(s) associ\u00E9(s)\u00A0: CODE= {2}).
menu_rights_clients = Clients
task_types = Type de T\u00E2ches
description_short = Courte description
menu_dashboard = Tableau de Bord
info_addressSearch = Il n'est pas possible de rechercher par champ de pays
entry_edit_question = \u00C9diter une question
imenu.portal = Collaboratif
in_validation = En cours de validation
upload_file = Chargement du fichier
channel_ftp_port = Port
portlet_library = Biblioth\u00E8que
organization_address_streetName = Adresse
ks_label = Label
user_company = Utilisateurs client
channel_duplicate_ok = Canal dupliqu\u00E9
contextual_validation_user_scope_role = L''utilisateur {0} n''a pas le r\u00F4le {1}
error_importing_Languages = Une erreur est survenu lors de l'import du portail localisation fichier
move_rigth = D\u00E9placer vers la droite
channel_WSStockEndpoint_stock_login = Login
bank_account_iban_error_exist = D\u00E9sol\u00E9 un IBAN est d\u00E9ja associ\u00E9 \u00E0 cette devise, merci de supprimer l'IBAN avant d'en saisir un autre
channel_surname = Surname
rte_collection = Collection
invalid_password = Mot de passe non valide. Merci de saisir un nouveau mot de passe.
document_children_policy = Gestion des liens parents-enfants
configuration = Configuration
exception_sending_mail_partner = Une erreur est survenue lors de l''envoi de mail au(x) partenaire(s)\u00A0: {0}.
site_optimized_for_ie9 = Ce site est optimis\u00E9 pour Internet Explorer 9.
error = Erreur
smtp_mailHost = H\u00F4te
delete_select_single = Souhaitez-vous supprimer d\u00E9finitivement l'\u00E9l\u00E9ment s\u00E9lectionn\u00E9\u00A0?
validated = Valid\u00E9e
customer_partner_delete_no = Non je vais r\u00E9fl\u00E9chir
parameter = Param\u00E8tre
STARTING_icon = fa fa-times-circle-o
ks_entry_notbefore = Valide depuis
campaign_date_end = Date fin
value = Valeur
PENDING_icon = fa fa-cog fa-spin
channel_AuthenticationEndpointConfiguration = Authentication
extension_label = Enregistrement n\u00B0
partner_user_roles = R\u00F4le de l'utilisateur
import_portal = Importer portail
backoffice = Back Office
channel_ftp_account = Account
invoice = N\u00B0 facture
scope_partner = P\u00E9rim\u00E8tre \u00E9tendu
organization_address_addressLine = Adresse
year = Ann\u00E9e
ABANDONED_label = ABANDONN\u00C9
menu_message = Email
channel_ftp_binary = Binaire
organization_location = _location
java_lang_Boolean = Bool\u00E9en
channel_arkhineo_digitalVaultId_req = Identifiant du coffre-fort
jsf_template = Template Jsf
themes = Th\u00E8mes
template_invalid_name = Le nom du template doit contenir entre 3 et 18 lettres minuscules et/ou chiffres.
exception_import_page_null = La page d''export est nulle.
company_view = Client {1} ({0})
portlet_referentiel_produits_general_description = R\u00E9f\u00E9rentiel/Produits/G\u00E9n\u00E9ral/Description
invalid_file_type = Type de fichier invalide
move_left = D\u00E9placer vers la gauche
partner_user_send_new_pass = R\u00E9initialiser le mot de passe
alphanumeric = Alphanumerique
agreement = Conditions g\u00E9n\u00E9rales d'utilisation
DocumentCounter = Counter
task_adv_search_simple = Simple
autoGenerationOfSSCC = SSCC g\u00E9n\u00E9r\u00E9
version = Version
order_issuer = Donneur d'Ordres
extensions = Extensions
bank_account_bic_error_required = Le BIC code est obligatoire
folder = Dossier
selection = S\u00E9lectionner
stop = Arr\u00EAter
logistic_sscc = SSCC
channel_priority = Priorit\u00E9
users_number_exceeded = Vous d\u00E9passez le nombre d'utilisateur pour un ${client}. Merci de supprimer les utilisateurs inactifs ou de contacter le support
client_type_SUPPLIER = fournisseur
error_no_company_specified = Aucun client sp\u00E9cifi\u00E9 pour l''\u00E9dition du partenaire {0}.
requested_delivery_date = Date de livraison souhait\u00E9e :
portlet_uploadpdfclient = UploadPdfClient
contextual_validation_user_scope_user = L''utilisateur {0} n''existe pas
archiving = Archivage
length_min = Longueur minimale
occurence = Nombre d'occurences
refuse = Refuser
reminder_subject = Objet du rappel
rte_test_results_header = R\u00E9sultats du Test Rte
STOPPED_icon = fa fa-exclamation-circle
close = Fermer
linked_document_import_selection = S\u00E9lectionner et importer un ou plusieurs fichiers
preferences = Pr\u00E9f\u00E9rences
channel_timeout = Timeout
channel_url = URL
ui_required = Donn\u00E9e obligatoire
user_email_address = Email
error_save_user_no_env = La s\u00E9lection de l'environnement est obligatoire pour pouvoir notifier l'utilisateur.
organization_fullname = Nom
period_to = au
rte_test_fail_message = Rte test ex\u00E9cut\u00E9 avec \u00E9chec
keyword_$currentDate = Date du jour
error_removing_partner = Erreur lors de la suppression du partenaire ({0}).
EDocuments = EdocumentsDeprecated
locale_not_defined = La langue de votre fiche partenaire n'est pas d\u00E9finie, veuillez le remplir
gcn_identification_group = Generix Collaborative Network
add_below = Ajouter en-dessous
channel_ftp_privateKeyFile = Fichier de cl\u00E9 priv\u00E9e
portlet_invoice_view_history = INVOICE : Auditer
UNKNOWN_color = #d4d4d4
doc_new_name = Nouveau nom
info_host_saved = Domaine {0} sauvegard\u00E9
add = Ajouter
imenu_integration_recent_business = Process r\u00E9cents
ks_friendlyname = Nom d'usage
generate_new_password_confirm = Confirmez-vous la g\u00E9n\u00E9ration d\\'un email de r\u00E9initialisation du mot de passe ?
menu_messages = Emails
contact_user = Contact
java_util_Date = Date
warn_select_partner_notification = Veuillez s\u00E9lectionner les partenaires \u00E0 notifier.
channel_ftp_disconnect = D\u00E9connecter
encoding = Encodage
users = Utilisateurs
port = Port
channel_ftp_localWorkDirectory = R\u00E9pertoire de travail local
organization_gs1 = Code Entreprise GS1
clients_type_SPECIFIC = partenaires
ks_remove_error = Erreur lors de la suppression du fichier\u00A0: {0}.
info_company_saved = Client {0} sauvegard\u00E9
partner_import_no_user = Le partenaire {0} n''a pas d''utilisateur
order_number = Commande n\u00B0
roles = R\u00F4les
channel_ftp_fastExistsCheck = Test rapide d'existence
channel_proxy_url = URL Proxy
choose = Choisir
paste = Coller
partner_delete_error = Erreur de suppression de partenaire\u00A0: {0}.
page_new = Nouvelle Page
ks_filename = Nom du certificat
organization_freeDate01 = _freeDate01
partner_postal_code = Code Postal
error_removing_user = Erreur lors de la suppression de l''utilisateur ({0}).
scope = P\u00E9rim\u00E8tre
info_perimeter_removed = P\u00E9rim\u00E8tre {0} supprim\u00E9
end = Fin
sort_order = Sens du tri
organization_freeDate02 = _freeDate02
warn_deleting_own_user = Il n''est pas possible de supprimer l''utilisateur {0}. Il s''agit de votre propre utilisateur.
customer_partner_number = ${client} n\u00B0
info_instance_toggled = L''environnement {0} a \u00E9t\u00E9 commut\u00E9.
label = Libell\u00E9
keyword_$password = Mot de passe de l'utilisateur
message = Email
portlet_pack = Pack
modify = Modifier
organization_start = Date de d\u00E9but
imenu_security = Partenaires
ks_unexpected_error_determing_alias = Erreur inattendue dans la d\u00E9termination de l'alias.
company_society = Soci\u00E9t\u00E9
generate_password = G\u00E9n\u00E9rer le lien de renouvellement de mot de passe
family = Famille
channel_ftp_maximumReconnectAttempts = Maximum d'essais de reconnexion
backup = Sauvegarder
portal_general = G\u00E9n\u00E9ral
partner_contacts = Contacts
APPROVED_icon = fa fa-check-circle-o
channel_other = Autre
contextual_validation_user_scope_partner = Le partenaire {0} n''appartient pas \u00E0 {1}
menu_audit = Audit
logistic_sscc_auto = SSCC automatique
DOC = KPI
invalid_file_accent_char = Le fichier ne peut contenir de caract\u00E8res accentu\u00E9s
TO_REMOVE_ICON = fa fa-archive
customer_partner_connectionCode = N\u00B0 unique de connexion
upload_invalid_file = Format de fichier non valide
warn_host_already_used = Le domaine est d\u00E9j\u00E0 utilis\u00E9.
channel_ftp_execPbsz = execPbsz
exception_backoffice_user = {0} est un utilisateur du back-office.
self_register_placeholder_1_placeholder = Champ N\u00B0 ${client} de la page d'auto-enregistrement
comment_upload_file = Ajouter un commentaire \u00E0 votre fichier
info_parameters_saved = Param\u00E8tres utilisateur sauvegard\u00E9s
WARN_label = AVERTISSEMENT
keyword_$login = Login de l'utilisateur
ABANDONED_color = #cc1e00
exit = Quitter
partner_deleted = Suppression des partenaires\u00A0: {0}.
profile_title = Mon compte
quick_search = Recherche rapide
connection_code = N\u00B0 unique de connexion
portlet_taxes_allowances = Remises et Taxes para-fiscale
organization_role = R\u00F4le
info_partner_file_import = {0} import\u00E9
other = Autre
property_required = est requis.
menurights = Menu droit
login = Login
organization_serialReference = Num\u00E9ro S\u00E9quentiel
error_saving_role = Erreur lors de la sauvegarde du r\u00F4le ({0}).
DocumentBirt = Birt
ERROR_color = #d15b47
add_user_mail_button = Ajouter l'utilisateur + Email notification
entry_import_faq = Importer une FAQ
boolean_true = Vrai
deployment = D\u00E9ploiement
ks_entry_serialnumber = Num\u00E9ro de s\u00E9rie
banking_partner = Partenaire Bancaire
exception_partner_subgroups = Le partenaire {0} a {1} sous-groupe(s).
permissions_dialog_page_title = D\u00E9terminer les droits d'acc\u00E8s \u00E0 la page
channel_add_error_duplicatename = Un canal portant ce nom existe d\u00E9j\u00E0. Ce canal ne peut \u00EAtre cr\u00E9\u00E9.
exception_perimeter_has_users = Le p\u00E9rim\u00E8tre {0} a {1} utilisateur(s) associ\u00E9(s).
removed = Supprim\u00E9
unread_message = message(s) non lu(s)
doc_downloads = Nombre de t\u00E9l\u00E9chargements
instance_type_EDI = EDI
create_mail = Cr\u00E9er un courrier
report_partners_status_by_month = \u00C9VOLUTION DU STATUT DES PARTENAIRES
invalid_generix_billing_id = L'identifiant de facturation Generix saisi n'est pas valide
add_sso_role_mapping = Ajouter un mapping de r\u00F4le
pages = Pages
sort_order_descending = D\u00E9croissant
Factor = FactorDeprecated
all = Tous
new = Nouveau
modif_contact_partner = Modification contact partenaire
process_msg = Traiter
logistic_extension_required = Le Caract\u00E8re d'extension est obligatoire car la g\u00E9n\u00E9ration automatique de SSCC est activ\u00E9e.
DocumentCalendar = DocumentCalendarDeprecated
upload_files = Chargement des fichiers
error_saving_company = Erreur lors de la sauvegarde du client.
customer_partner_import = Importer un fichier ${client}
report_informations = Informations
length_max = Longueur maximale
exception_constraint_violation = {0}\u00A0: {1}
channel_coded_mdn = MDN crypt\u00E9
self_register = Auto-enregistrement
template_saved = Template sauvegard\u00E9
sort_by = Tri par
deny_message = Veuillez motiver votre refus.
creation_mail_message_html = Bonjour {0},<br/><br/>Veuillez trouver ci-dessous vos informations de connexion au site de {1} ({2})\u00A0: <br/><br/> <li>Utilisateur: {3} </li><li> Lien de renouvellement de mot de passe\u00A0: {4}</li><br/><br/>A bient\u00F4t !
folder_in = Dossier d'entr\u00E9e
error_invalid_row_length = [{0},{1}] Nombre de champs incorrects attendus ({2}), trouv\u00E9s ({3})
localisation = Localisation portail
button_edit = \u00C9diter
select_one = Choisissez une option
doc_update_information = Mettre \u00E0 jour mes informations
milestone = Date pr\u00E9visionnelle de r\u00E9alisation
error_invalid_number = {0} : Le contenu entr\u00E9e n''est pas un nombre : {1}
bank_accounts = Comptes bancaire
quantityValue_validator = La valeur de la quantit\u00E9 n'est pas valide
exception_task_type_not_found = Impossible de trouver le type de t\u00E2che {0}
info_partner_role_saved = R\u00F4le du partenaire sauvegard\u00E9
channel_authentification_proxy = Authentification proxy
ks_publickeytab = Cl\u00E9 publique certificat
partner_name = Nom Partenaire
exception_duplicate_email = Cette adresse email a d\u00E9j\u00E0 \u00E9t\u00E9 cr\u00E9\u00E9e
freetext_details = Champs libres/D\u00E9tails
channel_ftp_ftpClientTrustStoreId = Truststore client
error_input_content_not_valid = Le contenu entr\u00E9e n''est pas valide : {0}
warn_language_mandatory = La configuration des langues est obligatoire.
companies = Clients
delete_select = Souhaitez-vous supprimer d\u00E9finitivement les \u00E9l\u00E9ments s\u00E9lectionn\u00E9s\u00A0?
CUSTOMERS = PORTAILS CLIENTS
identification = Identification
ks_entry_alias_optional = Optionnel sauf si l'alias ne peut \u00EAtre d\u00E9termin\u00E9 automatiquement.
partner_view = Partenaire {1} ({0})
FATAL_icon = fa fa-bomb
hosts = H\u00F4tes
UNKNOWN_icon = fa fa-exclamation-circle
bad_account_or_password = Nom d'utilisateur inconnu ou mot de passe incorrect.
error_getting_portlet_content = Portlet {0}, Contenu {1}\u00A0: {2}
monitoring = Activit\u00E9
channel_filename_required = Masque du nom de fichier automatique
dictionary = Dictionnaires m\u00E9tier
menu_notifications = Annonces
recipients = Destinataires
boolean_false = Faux
duns = RCS-RCM
date_last_authentication = Derni\u00E8re connexion
companyinformation = Mes informations soci\u00E9t\u00E9
channel_auth_userSearchBase = User search base
error_export_empty_portlet = Vous ne pouvez pas exporter un portail vide;
contact_admin = Veuillez contacter votre administrateur.
expand_all = Tout d\u00E9ployer
lang_french = Fran\u00E7ais
ERROR_label = ERREUR
menu_security_companies = Clients
bank_account_delete_confirm = Etes-vous s\u00FBr de vouloir supprimer l'IBAN pour la devise
ks_remove_linked_partner_integrity = Op\u00E9ration bloqu\u00E9e\u00A0: certificat encore li\u00E9 \u00E0 ces groupes\u00A0: {0}.
default = d\u00E9faut
organization_profile = Profil
disabled = D\u00E9sactiv\u00E9
warn_only_parent_empty = Seule une page parent peut \u00EAtre vide.
arn = ARN du r\u00F4le IAM
organization_freeBoolean01 = _freeBoolean01
organization_freeBoolean02 = _freeBoolean02
rte = RTE
ks_modulus_length = Longueur du modulo (bits)
organization_address_postalCode = Code Postal
tout = Tout
about_contact_email = Email du contact
account_confirmation = Confirmation de cr\u00E9ation de compte
warn_no_type_selected = Veuillez s\u00E9lectionner le type de la t\u00E2che.
organization_client = _client
is_expired = est expir\u00E9.
ks_upload_select = Keystore (*.jks, *.p12, *.pfx)
partners = Partenaires
entry_select_excel = Choisir un fichier Excel (*.xls, *.xlsx)
Detach = D\u00E9tacher les documents enfants
xPath = xPath
documents = Documents
administration = Administration
partner_address_1 = Adresse 1
channel_ftp_timeout = Timeout data (ms)
organization_parent = _parent
client_type_CUSTOMER = client
ks_upload = Importer un keystore
menu_security = S\u00E9curit\u00E9
view = Voir
domain_edit = Modifier un domaine
partner_address_2 = Adresse 2
test_upload = Chargement d'un fichier de test.
edit_record = \u00C9diter
keyword_$campaignvariable = Variable libre
PARTIALLY_SHIPPED_ICON = fa fa-paper-plane
FeedReader = FeedReaderDeprecated
channel_FaxEndpointConfiguration = Fax
menu_statistics = Reporting
sso_role_mapping = Mapping des r\u00F4les
channel_ftp_privateKeyPassphrase = Passphrase du fichier de cl\u00E9 priv\u00E9e
new_file = Nouveau fichier
smtp_mailPort = Port
date_to = \u00E0
duplicate = Dupliquer
channel_ftp_knownHostsUri = URI du fichier known_hosts
canNotSaveFilter = Vous ne pouvez pas sauvegarder ce filtre car l\u2019emplacement de stockage est plein
user_search = Affiner votre recherche en tapant le nom de l'auteur des modifications.
portlet_collection = Collection
add_left = Ajouter \u00E0 gauche
warn_template_with_associated_host = Certains domaines sont associ\u00E9s \u00E0 un template.
partner_add = Ajouter Partenaire
name = Nom
installation_detail = Logiciels install\u00E9s
parameters = Param\u00E8tres
task_types_revision = Visualisation d\u00E9finition t\u00E2che
import = Importer
channel_filename = NomDuFichier-<NUM>-<DATE>.edi
run_rte = Ex\u00E9cuter
description = Description
edition = Edition
campaign_description = Description
rte_file_or_folder_invalid_name = Le nom ne doit contenir que des caract\u00E8res alphanum\u00E9riques sans espace. Les caract\u00E8res '.', '_' et '-' sont admis.
exception_multiple_principal_groups_user = L''utilisateur {0} a plus d''un groupe principal ({1}).
gcnIdentification_error = Le GCN Id doit contenir au maximum 64 caract\u00E8res alphanum\u00E9riques
customer_partner_address_city = Ville
factor = Factor
general_supplier_service = Generix Suppliers Services
Cascade = Purger les documents enfants
day_of_week = Jours de la semaine
channel_given_name = Given name
severity = S\u00E9v\u00E9rit\u00E9
exception_removed_instance = L''environnement {0} a \u00E9t\u00E9 supprim\u00E9.
info_partner_removed = Partenaire {0} supprim\u00E9
quicksight = QuickSight
portlet_switch_user = Usurper identit\u00E9
channel_directory_name = Nom du r\u00E9pertoire
file_name = Nom du fichier :
Penalty = FranprixPenalty
logistic = Logistique
ks_cannot_determine_alias = Ne peut d\u00E9terminer l''alias \u00E0 utiliser. Alias trouv\u00E9s, avec s\u00E9parateur ;\u00A0: {0}
confirmScopeSelection = Vous allez perdre votre s\u00E9lection de p\u00E9rim\u00E8tre partenaire
channel_FtpEndpointConfiguration = FTP
exception_user_no_associated_company = L''utilisateur {0} du partenaire {1} n''est li\u00E9e \u00E0 aucun client.
ks_library = Librairie
client_type_INVOICE = partenaire
usage = Usage
organization_address_city = Ville
ks_entry_subject = Livr\u00E9 \u00E0
organization_freeViewProfile = Profil
active_partners = PARTENAIRES ACTIFS
docs_selected = Documents selectionn\u00E9s
logout = D\u00E9connexion
user_mobile = Mobile
user_login = Utilisateur
javax_xml_datatype_XMLGregorianCalendar = Date XML
fax = Fax
partner_city = Ville
dashboard = Tableau de bord
error_exporting_page = Erreur lors de l'export de la page.
entry_new = Nouvelle entr\u00E9e
channel_ftp_siteCommand = Site commands
role_min = r\u00F4le
linked_certificates = Certificats li\u00E9s
phone = T\u00E9l\u00E9phone
contact_validator = L'adresse mail de contact n'est pas valide. Plusieurs adresses peuvent \u00EAtre remplies, s\u00E9par\u00E9es par le caract\u00E8re ';'.
reset = R\u00E9initialiser
style = Style
channel_add = Ajouter un canal
menu_basket = Panier
placeholder_tcp = Le port TCP sur lequel \u00E9coute l'instance
portlet_orderresponse_print = ORDER RESPONSE: Imprimer
confirm_file_delete = Voulez-vous vraiment supprimer {0}
document_status_removed = Le statut du document {0} a \u00E9t\u00E9 supprim\u00E9
enabled = Activ\u00E9
bank_account_iban_error_notValid = L'IBAN n'est pas valide
channel_ftp_username = Username
error_saving_permission = Erreur lors de la sauvegarde de la permission ({0}).
channel_name = Nom du canal
report_partners_completion_campaigns = ONBOARDING
menu_instances = Environnements
display = Affichage
export_portal = Exporter portail
customer_partner_name_for_user = Nom ${client}
info_instance_saved = L''environnement {0} a \u00E9t\u00E9 sauvegard\u00E9.
field_missing = Le champ \"{0}\" est manquant.
channel_alias_target = Cible
exception_unavailable_instance = Le site {0} est temporairement indisponible.
portlet_referentiel_produits_general_net_price = R\u00E9f\u00E9rentiel/Produits/G\u00E9n\u00E9ral/Prix Net
task_to_complete = T\u00E2che \u00E0 effectuer
cron_expression = Expression Cron
exception_import_error_during_cloning = Erreur lors de la duplication du portail
no_message_display = Ne plus afficher ce message
AllUsers = Tous les utilisateurs d'un partenaire
error_creating_template_archive = Erreur lors de la cr\u00E9ation du template ({0}).
statistics = Statistiques
Ignore = Ignorer les documents parents
ABANDONED_icon = fa fa-exclamation-circle
keyword_$contact = Contact
channel_AS2EndpointConfiguration = AS2
partner_send_mail = Envoyer les informations de connexion
channel_others = Autres
notification = Notification
add_library = Ajouter une biblioth\u00E8que
ks_uploaddate = Date de cr\u00E9ation
FATAL_color = #cc1e00
ks_slotIndex = Slot index
tasks_noselection = Aucune T\u00E2che s\u00E9lectionn\u00E9e
menu_domain = Domaines
home_message = Acc\u00E9l\u00E9rez vos d\u00E9ploiements EDI et B2B tout en r\u00E9duisant vos co\u00FBts avec GCI Community Management
partner_new = Nouveau partenaire
error_profile_name_mandatory = Le nom du profil est obligatoire.
partner_user_create = Ajouter un utilisateur
permission_missing = Vous n'avez pas les permissions suffisantes pour acc\u00E9der \u00E0 cette fonctionnalit\u00E9
about_contact = Contactez-nous
bank_account_bic_error_notValid = Le BIC code est constitu\u00E9 de 8 ou 11 caract\u00E8res
imenu_general = G\u00E9n\u00E9ral
insert = Ins\u00E9rer
channel_report_timer = Delivery report timer
entry_new_answer = Option
channel_type_select = Type de canal...
authentication = Authentification
invalid_file_special_char = Le fichier ne peut contenir de caract\u00E8res sp\u00E9ciaux
organization_bankAccount_date = Date cr\u00E9ation
document_status_added = Le statut du document {0} a \u00E9t\u00E9 ajout\u00E9
organization_freeViewConfiguration = Champs libres
portlet_invoice_open = INVOICE : Ouvir
info_user_saved = Utilisateur {0} sauvegard\u00E9
channel_arkhineo_consultCert_req = Certificat de consultation
library_document_introduction = La documentation disponible est class\u00E9e par cat\u00E9gorie. N'h\u00E9sitez pas \u00E0 cliquer sur une cat\u00E9gorie pour d\u00E9couvrir les documents que nous vous mettons \u00E0 disposition.
organization_userGroupAssociations = _userGroupAssociations
warn_creation_succes = La ressource a \u00E9t\u00E9 cr\u00E9\u00E9e avec succ\u00E8s
organization_logoSmall = _logoSmall
duplicate_mapping = Ce mappage existe d\u00E9j\u00E0.
user = Utilisateur
document_status_exists = Le statut du document {0} existe d\u00E9j\u00E0
channel_MDN = MDN
ks_password_pkcs11 = Mot de passe (code PIN)
clients_type_CUSTOMER = clients
importInstance = Importation (*.json)
campaigns = Onboarding
organization_creation = _creation
portlet_carousel = Carrousel
partner_country = Pays
warn_partner_profile_deleted = Le profil d'un partenaire ne doit pas \u00EAtre supprim\u00E9.
doc_purge_children_policy = Liens parents-enfants
DISABLED_label = D\u00C9SACTIV\u00C9
FAILED_icon = fa fa-exclamation-circle
warn_kpi_invalid = La service KPI ne peut pas \u00EAtre d\u00E9marr\u00E9. Veuillez r\u00E9essayer plus tard ou contacter votre administrateur
ks_error_no_keystore_folder = Error - le dossier keystore n'a pas pu \u00EAtre d\u00E9termin\u00E9.
entry_type = Type
max_size_of_linked_document = Taille maximum des fichiers attach\u00E9s
schema = Schema
strong = fort
exception_role_has_sso = Le r\u00F4le {0} a des sso mappages de r\u00F4les.
organization_logoMedium = _logoMedium
managed_by = G\u00E9r\u00E9 par
menu_security_groups = Groupes
channel_message_body = Corps de message
organization_bankAccount_currency = Devise
ERROR_icon = fa fa-times-circle-o
organization_code = Identifiant
search = Rechercher
customer_partner_delete = Supprimer le ${client}
portlet_invoice_diagnostic = INVOICE : Diagnostiquer
channel_ftp_isImplicit = isImplicit
menu_notification = Annonce
partner_parent = Parent
PENDING_color = #bebfbb
user_last_authentication = Derni\u00E8re connexion
integrationnotification = Recevoir rapport d'int\u00E9gration processus
completion = Avancement
mail_content = Corps
portlet_invoice_import = INVOICE : Importer
keyword_$campaigncontactphone = Num\u00E9ro de t\u00E9l\u00E9phone du contact pour la campagne
error_removing_perimeter = Erreur lors de la suppression du portlet ({0}).
ks_uploaded = keystore import\u00E9\u00A0: {0}
confirm_task_result_reset = Les r\u00E9sultats de cette t\u00E2che seront supprim\u00E9s. Souhaitez-vous continuer\u00A0?
company_add = Ajouter client
partner = Partenaire
ks_entry_signaturealgoname = Nom de l'algorithme de signature
channel_relation = Relation
partner_import_already_existing_partners = partenaire(s) d\u00E9j\u00E0 existant(s)
instance_type_CUSTOMER = Client
language = Langue
OK_icon = fa fa-check-circle-o
error_uploding_template = Chargement du template en \u00E9chec ({0}).
ks_entry_fingerprintsha1 = Empreinte num\u00E9rique (SHA-1)
contact = Contact
SafranInvoice = SafranInvoiceDeprecated
exception_import_instance_code_null = Le code de l'environnement est null.
error_sending_new_password = Une erreur est survenue lors de l''envoi du lien de renouvellement de mot de passe \u00E0 {0} ({1}).
generate_reset_link_expired = Le lien de renouvellement de mot de passe a expir\u00E9.
user_firstname = Pr\u00E9nom
Documentation = DocumentationDeprecated
imenu_messaging = Messagerie
error_saving_host_not_unique = Un domaine du m\u00EAme nom existe d\u00E9j\u00E0
error_no_user_found = Aucun utilisateur trouv\u00E9 avec l''identifiant\u00A0: {0}.
one_hour_past = moins d'une heure
channel_as2_only_one_endpoint_per_organization = Un seul canal AS2 est autoris\u00E9
channel_ftp_keyPair = Java KeyPair
organization_id = _id
control_ean = Contr\u00F4le longueur champ code EAN
archiveimv3 = Archive IMV3
organization_registerName = Raison sociale
channel_indicator = Indicateur test
sources = Sources
content = Contenu
timeout = Timeout
channel_admin_domain = Administration management domain
entry_question = Question
smtp_ssl = SSL
exception_export_null = Pas d''export trouv\u00E9 dans le fichier
quick_search_loreal_order = Recherche rapide sur Marque, article, code enseigne, EAN
client = Client
new_message = Nouveau message
clear_portlet_confirm = \u00CAtes-vous s\u00FBr de vouloir vider le contenu de cette portlet\u00A0?
default_test_rte = Test par d\u00E9faut
logistic_sscc_fieldset = Num\u00E9ro SSCC
import_role_header = Import d'un fichier de r\u00F4les (*.xls, *.xlsx)
info_perimeter_duplicated = Le p\u00E9rim\u00E8tre {0} \u00E9t\u00E9  dupliqu\u00E9
channel_ftp_securityProtocol = Protocole de s\u00E9curit\u00E9
ks_error_during_file_reading = Une erreur s'est produite lors de la lecture du fichier.
channel_not_selected = Pas de canal s\u00E9lectionn\u00E9
generate_reset_link_invalid = Lien de renouvellement de mot de passe non valide. Veuillez contacter votre administrateur.
prompt = Entrez le mot de passe
error_duplicate_configuration = La configuration existe d\u00E9j\u00E0
imenu_portal_o2c = Order to cash
select = S\u00E9lection
shipment_date = Date d'exp\u00E9dition
channel_ftp_ciphers = Chiffrements
ks_entrydetailstab = Certificat
doc_status_style = Style statut
partner_user_add_role = Attribuer un r\u00F4le
channel_ftp_charset = Encodage des fichiers
PENDING_label = RAPPORT EN COURS...
exception_exchange_associated = {0} \u00E9change(s) autoris\u00E9(s) li\u00E9(s)
portal = Portail
DISABLED_icon = fa fa-minus-circle
tasks = T\u00E2ches
io = DO
self_register_ok = L'enregistrement a \u00E9t\u00E9 fait avec succ\u00E8s!<br/>Un lien de renouvellement de mot de passe vous a \u00E9t\u00E9 envoy\u00E9 par email.
exception_code_duplication = Le code existe d\u00E9j\u00E0: {0}.
DISABLED_color = #d4d4d4
clients_type_SUPPLIER = fournisseurs
messaging = Messagerie
STOPPING_label = ARR\u00CAT
info_company_removed = Client {0} supprim\u00E9
campaign_date_creation = Date cr\u00E9ation
begin = D\u00E9but
template = Template
organization_orderEmail = Email
channel_options = Options
alphanumeric_underscore = Seuls les caract\u00E8res alphanum\u00E9riques et l'underscore sont autoris\u00E9s
partner_user = Utilisateur
error_importing_instance = Erreur lors de l''import de l''environnement\u00A0: {0}.
channel_terminal_id = Terminal id
instance_type_SUPPLIER = Fournisseur
DocumentBarChart = BarChart
ks_unexpected_multiple_keys = Plusieurs cl\u00E9s dans le keystore, cas inattendu.
library = Librairie
folder_out = Dossier de sortie
exception_role_has_users = Le r\u00F4le {0} a {1} utilisateur(s) associ\u00E9(s).
classificationPlan = Plan de classement
organization_children = _children
organization_registration = SIREN
add_user_button = Ajouter l'utilisateur
role_add = Ajouter un
channel_private_domain = Private management domain
customer_partner_name = Nom
doc_uploaded_success = Document(s) import\u00E9(s)\u00A0: {0}/{1}
tcp_port = Port TCP
exception_user_more_companies = L''utilisateur {0} du partenaire {1} a plus d''un client li\u00E9e ({2}).
template_new = Cr\u00E9er un template
menu_monitoring = Activit\u00E9
logistic_missing_sscc_message = Veuillez renseigner l'ensemble des informations requises
day_of_month = Jours du mois
error_saving_user = Erreur lors de la sauvegarde de l''utilisateur ({0}).
channel_retry = Essai
download = T\u00E9l\u00E9charger
clear_page_confirm = \u00CAtes-vous s\u00FBr de vouloir supprimer le contenu de cette page\u00A0?
info_role_saved = R\u00F4le {0} sauvegard\u00E9
line_comment = Commenter la ligne
portlet_referentiel_carrier = R\u00E9f\u00E9rentiel/Transporteur
communication = Communication
boolean_select = --S\u00E9lectionner--
exception_import_instance_null = L'environnement du fichier d'export est null.
user_password_confirm = Confirmer le mot de passe
organization_shareCapital = Capital social
author = Auteur
error_changing_layout = Erreur lors du changement de layout ({0}).
self_register_ko = Nous d\u00E9tections un probl\u00E8me sur l'enregistrement de votre compte, merci d'essayer ult\u00E9rieurement
channel_ftp_ftp_variant = Mode FTP
warn_portlet_localization_lang_not_supported = Langue {0} non support\u00E9e par l'environnement
perimeter = P\u00E9rim\u00E8tre
NONE_ICON = fa fa-envelope-o
channel_WSStockEndpoint_stock_retrieval = Endpoint r\u00E9cup\u00E9ration du Stock
channel_fax = Num\u00E9ro Fax
info_no_portlet_content_defined = La portlet a \u00E9t\u00E9 d\u00E9finie sans contenu pour cette page.
error_select_correct_user = Veuillez s\u00E9lectionner un utilisateur valide.
no = Non
code = Code
site_conditions_link = Conditions d'utilisation du site
ui_invalid_file = Type du fichier incorrect
organization_modification = _modification
user_lastname_placeholder = Votre nom
logistic_extension = Caract\u00E8re d'extension
move_bottom = D\u00E9placer tout en bas
subtype = Sous-type
individual_tests_rte = Test individuel
ok = OK
channel_auth_groupSearchBase = Group search base
ks_entry_fingerprintmd5 = Empreinte num\u00E9rique (MD5)
imenu_repository_dictionary = Dictionnaire m\u00E9tier
control = Contr\u00F4ler
new_folder = Nouveau dossier
partner_with_code_missing = Le num\u00E9ro ${client} et/ou le num\u00E9ro d'enregistrement n'existent pas
ks_usage_mismatch = L''usage {0} doit correspondre \u00E0 l''usage parent {1}
error_saving_portal = Erreur lors de la sauvegarde du portail ({0}).
info_partner_saved = Partenaire {0} sauvegard\u00E9
bql = BQL
report_no_data = PAS DE DONN\u00C9ES
about_short = A propos
information_system = Information Syst\u00E8me
error_removing_row = Erreur lors de la suppression de la ligne ({0}).
bank_account_iban = IBAN
channel_auth_groupSearchFilter = Group search filter
library_edited_succes = La documentation a \u00E9t\u00E9 modifi\u00E9e
number_reviewed_messages = Nombre de messages revus
info_portlet_localization_import = L''import du portail localisation termine\u00E9.{0} message(s) ajout\u00E9(s) et {1} modifi\u00E9(s)
menu_rights_users = Utilisateurs
deny = Refuser
edit = \u00C9dition
entry_checkbox = Case \u00E0 cocher
channel_generation_qualifier = Generation qualifier
accept = Accepter
exception_more_than_one_instance = L''utilisateur {0} associ\u00E9 au client {1} a plus d''un environnement configur\u00E9 pour le domaine {2} ({3}).
STOPPING_color = #cc1e00
invalid_file_csv_type = Le fichier doit \u00EAtre de type CSV
rte_property_unknown = init.tst: la propri\u00E9t\u00E9 {0} est inconnue
RTE = SERVICES EDI
contact_mode = M\u00E9dia
database_kpi = Base de donn\u00E9es KPI
view_all = Tout voir
bank_accounts_dlg_header = Ajouter des coordonn\u00E9es bancaires
portlet_contact_us = Contactez-nous
CarrefourInvoiceEdition = CarrefourInvoiceDeprecated
property_not_integer = doit \u00EAtre un nombre entier.
exception_perimeter_has_partners = Le p\u00E9rim\u00E8tre {0} a {1} partenaire(s) associ\u00E9(s).
ks_type = Type
portlet_invoice_print = INVOICE : Imprimer
user_empty_client = Choisir un ${client}...
email = E-mail
started = D\u00E9marrage
channel_ftp_chmod = Appliquer le chmod
confirm = Confirmer
trf_required_document_type = Le type de document ou sa collection est obligatoire.
ks_error_no_certificate_found = Aucun certificat trouv\u00E9 dans le fichier.
contacts = Contacts
country = Pays
partner_edit = \u00C9diter Partenaire {1} ({0})
by_date = le {0}
channel_type_mandatory = Vous devez choisir un type de canal avant de cliquer sur Ajouter.
channel_receipt_report = Receipt report
UNKNOWN_label = INCONNU
imenu_integration_recent_mapping = Mapping r\u00E9cents
details = D\u00E9tails
empty_page = Page Vide
channel_arkhineo_clientId_req = Identifiant client
bank_account_deleting_error = Erreur lors de la suppression du compte bancaire {0}
channel_auth_userSearchFilter = User search filter
query = Filtrer
exception_user_not_specified = L''utilisateur n''est pas sp\u00E9cifi\u00E9.\nexception_backoffice_user={0} est un utilisateur back-office.
date_read = Date Lecture
preview = Aper\u00E7u
domain_create = Cr\u00E9er un domaine
keystores = Keystores
sooner_shipment_date = Date d'exp\u00E9dition au plus t\u00F4t
error_editing_portlet = Erreur lors de l''\u00E9dition du contenu de la portlet ({0}).
organization_legalStructure = Structure juridique
bank_account_iban_error_required = L'IBAN est obligatoire
save_success = L'ensemble de la biblioth\u00E8que a \u00E9t\u00E9 sauvegard\u00E9
imenu_portal_p2p = Purchase to pay
edocument = E-Document
gcnIdentification = GCN Id (gid)
warn_import_pt_collection = Si le portail utilise des portlets \"collection\", une configuration de ces portlets sera n\u00E9cessaire
company_code_validator = Le code doit \u00EAtre form\u00E9 de trois lettres en majuscule.
keyword_$campaigncontactemail = Email du contact pour la campagne
confirm_instance_disable = Tous les services de l'environnement seront d\u00E9sactiv\u00E9s (int\u00E9gration, onboarding...).\\n\\n\u00CAtes-vous s\u00FBr de vouloir d\u00E9sactiver cet environnement ?
customer_partner_show_users = Utilisateurs associ\u00E9s au client
exception_task_change_parent = Erreur dans l'algorithme changeParent.
portlet_orderresponse_export = ORDER RESPONSE: Exporter
exception_access_denied = Acc\u00E8s refus\u00E9
validity_date = Date d'expiration:
COMPLETED_icon = fa fa-check-circle-o
none = Aucun
repository = R\u00E9f\u00E9rentiel
type = Type
seconds = Secondes
action_global = Action globale
organization_bankAccount_user = Cr\u00E9e par
imenu_general_instance = Environnement
sort_order_ascending = Croissant
portlet_order_mark_as_unread = ORDER: Marquer comme non lu
channel_save_error_null = Erreur lors de la sauvegarde du canal (le canal est null).
later_shipment_date = Date d'exp\u00E9dition au plus tard
return_to = Retour \u00E0
portlet_invoice_view_attachment = INVOICE : Visualiser pi\u00E8ce jointe
imenu_portal = Collaboratif
error_removing_host = Erreur lors de la suppression du domaine ({0}).
customer_partner_search_placeholder = Rechercher un ${client} par n\u00B0, nom ou ville
logistic_serial_reference_error_format_message = Le num\u00E9ro s\u00E9quentiel doit \u00EAtre compos\u00E9 de 6 \u00E0 9 chiffres
ks_entry_issuer = Livr\u00E9 par
notifications = Notifications
portlet_order_actions_9 = ORDER : Action 9
portlet_order_actions_8 = ORDER : Action 8
error_removing_company = Erreur lors de la suppression du client ({0}).
ks_certificationpath = Chemin d'acc\u00E8s de certification
menu_rights_roles = R\u00F4les
NONE_label = AUCUN
ks_error_multiple_parent_for_friendly_name = \u00C9tat incoh\u00E9rent\u00A0: plusieurs parents trouv\u00E9s pour le nom d''usage {0}.
imenu_report = Activit\u00E9
weak = faible
channel_WSStockEndpointConfiguration = WS/Stock
channel_delivery_report = Delivery report
invalid_file_size = Taille du fichier invalide
advSearch = Recherche avanc\u00E9e
copy = Copier
channel_desc = Description
filter = Filtre
channel_synchronous = Synchrone
help = Aide
organization_description = _description
rename = Renommer
iframe_error_message = Impossible d'afficher le site car il ne le permet pas.
error_removing_file = Erreur lors de la suppression du fichier ({0}).
doc_uploaded_update = Document mis \u00E0 jour
date = Date
exception_admin_url_portal = Veuillez saisir un autre nom d'utilisateur ou contactez votre administrateur pour obtenir l'URL d'acc\u00E8s \u00E0 votre portail.
warn_user_missing = L'utilisateur est introuvable (v\u00E9rifier l'uuid du param\u00E9tre).
unprocesses_message = message(s) non trait\u00E9(s)
error_saving_portal_portlet_missing = La configuration d'une portlet est manquante.
partner_identification = Identification
menu_process_reports = Rapports
page_noselection = Pas de page selectionn\u00E9e
create = Cr\u00E9er
DocumentLineChart = LineChart
campaign_general = G\u00E9n\u00E9ral
warn_localizations_import_portlet_not_found = Portlet {0} non trouv\u00E9 dans la d\u00E9finition du portail
agreement_version_required = La version du CGU est obligatoire
STARTING_label = D\u00C9PART
SENDER = Emetteur
task_last_completed = Derni\u00E8re t\u00E2che achev\u00E9e
portlet_order_actions_1 = ORDER : Action 1
add_certificate = Ajouter un certificat
portlet_order_actions_3 = ORDER : Action 3
portlet_order_actions_2 = ORDER : Action 2
portlet_order_actions_5 = ORDER : Action 5
portlet_order_confirm = ORDER: Confirmer
report_completed = Compl\u00E9t\u00E9
STARTING_color = #bebfbb
portlet_order_actions_4 = ORDER : Action 4
send = Envoyer
portlet_order_actions_7 = ORDER : Action 7
portlet_order_actions_6 = ORDER : Action 6
exception_role_has_pages = Le r\u00F4le {0} a {1} page(s) associ\u00E9e(s).
COMPLETED_color = #87b87f
week = Semaine
instances = Environnements
exception_task_properties_not_found = Import de la t\u00E2che {0} impossible, une mont\u00E9e de version est n\u00E9cessaire
edit_user_button = Mettre \u00E0 jour l'utilisateur
security = S\u00E9curit\u00E9
self_register_bottom_msg = Une fois l'enregistrement termin\u00E9, vous allez recevoir un email contenant votre mot de passe temporaire. Ce mot de passe devra \u00EAtre modifi\u00E9 \u00E0 la premi\u00E8re connexion.
PENDING_ICON = fa fa-eur
channel_type = Type
library_deleted_success = La documentation a \u00E9t\u00E9 supprim\u00E9e
rte_no_init_test_file = Aucun fichier init.tst par d\u00E9faut n'a \u00E9t\u00E9 trouv\u00E9
COMPLETED_label = TERMIN\u00C9
rte_test_success_message = Rte test ex\u00E9cut\u00E9 avec succ\u00E8s
info_company_created = Client {0} cr\u00E9\u00E9
warn_partner_missing = Le partenaire est introuvable (v\u00E9rifier le pcode du param\u00E9tre).
partner_user_send_pass_no = Non je vais r\u00E9fl\u00E9chir
category = Cat\u00E9gorie
ks_no_key_found = Pas de cl\u00E9 trouv\u00E9e dans le KeyStore.