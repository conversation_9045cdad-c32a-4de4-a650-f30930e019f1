pipeline {
    agent any
    options {
        skipDefaultCheckout()
    }
    tools {
        maven 'Maven3.8.8'
        jdk 'AdoptOpenJDK 17.0.13'

    }
    stages{
        stage('Checkout repos'){
            steps{
                script {

                    def baseDir = "${WORKSPACE}/repos"  
                    sh "mkdir -p ${baseDir}"  

                    def gitlabCredentialsId = '1ff6869d-4818-45ff-bbab-1c94a304cf3a'  

                    def GNXbranchName = params.GNX_BRANCH
                    def BYZbranchName= params.BYZ_BRANCH
                    if (env.JOB_NAME == "release-aio-minor-jdk17") {
                        GNXbranchName = "master"
                        BYZbranchName= "master"
                    }

                    dir("${baseDir}/chassagnemaven") {
                        checkout([
                            $class: 'GitSCM',
                            branches: [[name: GNXbranchName]],
                            userRemoteConfigs: [[url: 'http://git.chassagne-scm.generixgroup.com/generix/chassagne-maven.git', credentialsId: gitlabCredentialsId]]
                        ])
                    }

                    dir("${baseDir}/byzaneo") {
                        checkout([
                            $class: 'GitSCM',
                            branches: [[name: BYZbranchName]],
                            userRemoteConfigs: [[url: 'ssh://**********************************:10022/generix/byzaneo.git', credentialsId: gitlabCredentialsId]]
                        ])
                    }

                    dir("${baseDir}/chassagne") {
                        checkout([
                            $class: 'GitSCM',
                            branches: [[name: GNXbranchName]],
                            userRemoteConfigs: [[url: 'ssh://**********************************:10022/generix/chassagne.git', credentialsId: gitlabCredentialsId]]
                        ])
                    }
                }
            }

        }
        stage('compil-tests') {
            when {
                expression { env.JOB_NAME == 'release-aio-micro-jdk17' }
            }
            steps {
                script {

                    def job1 = build job: 'compil-chassagnemaven-nexus',
                        parameters: [
                            string(name: 'chassagneMavenBranch', value: params.GNX_BRANCH),
                            string(name: 'DEPLOY', value: ""),
                            string(name: 'PROFILE', value: "")
                        ]

                    def job2 = build job: 'compil-byzaneo-nexus',
                        parameters: [
                            string(name: 'byzaneoBranch', value: params.BYZ_BRANCH),
                            string(name: 'DEPLOY', value: ""),
                            string(name: 'PROFILE', value: "")
                        ]

                    def job3 = build job: 'compil-chassagne-nexus',
                        parameters: [
                            string(name: 'chassagneBranch', value: params.GNX_BRANCH),
                            string(name: 'DEPLOY', value: ""),
                            string(name: 'PROFILE', value: "")
                        ]

                }
            }
        }
        stage('Extract GNX_VERSION') {
            steps {
                script {
                    // Execute a shell script to extract the gnx-version from pom.xml
                    def gnxVersion = sh(script: '''
                        #!/bin/bash
                        # Extract the gnx-version from pom.xml
                        gnxVersion=$(grep -oPm1 "(?<=<gnx-version>)[^<]+" repos/chassagnemaven/pom.xml)
                        # Remove '-SNAPSHOT' if present
                        gnxVersion=${gnxVersion%-SNAPSHOT}
                        echo $gnxVersion
                    ''', returnStdout: true).trim()

                    echo "Extracted GNX_VERSION: ${gnxVersion}"

                    binding.setVariable('GNX_VERSION', gnxVersion)
                }
            }
        }
        stage("Update pom.xml Minor version") {
            when {
                expression { env.JOB_NAME == 'release-aio-minor-jdk17' }
            }
            steps {
                script {
                    withCredentials([usernamePassword(credentialsId: '1ff6869d-4818-45ff-bbab-1c94a304cf3a', usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
                    sh '''
                    	#chassagnemaven
			            cd ${WORKSPACE}/repos/chassagnemaven
                        git remote set-url origin http://$USERNAME:$<EMAIL>/generix/chassagne-maven
                        git checkout master
                        git branch --set-upstream-to=origin/master master
                        git pull --rebase

                        find -name "pom.xml" -exec sed -i "s/6.0.0-SNAPSHOT/${GNX_VERSION}/g" '{}' ';'
                        find -name "pom.xml" -exec sed -i "s/10.0.0-SNAPSHOT/${BYZ_VERSION}/g" '{}' ';'

                        git config --global user.name "jenkins"
                        git config --global user.email "<EMAIL>"

                        find -name "pom.xml" -exec git add '{}' '+'
                        if git diff-index --quiet HEAD --; then
                            echo "No changes to commit."
                        else
                            git commit -m "@Version ${GNX_VERSION}"
                            git push origin master
                        fi
                        git tag -a GNX-${GNX_VERSION} -m "Version ${GNX_VERSION}"
                        git push origin GNX-${GNX_VERSION}

                        sleep 5
			
                        #byzaneo
                        cd ${WORKSPACE}/repos/byzaneo
                
                        git remote set-url origin ssh://**********************************:10022/generix/byzaneo.git
                        git checkout master
                        git pull --rebase
                        # Update pom.xml versions
                        find . -name "pom.xml" -exec sed -i "s/10.0.0-SNAPSHOT/${BYZ_VERSION}/g" '{}' ';'
                        find . -name "pom.xml" -exec sed -i "s/6.0.0-SNAPSHOT/${GNX_VERSION}/g" '{}' ';'
                        # Configure Git
                        git config --global user.name "jenkins"
                        git config --global user.email "<EMAIL>"
                        # Commit and push changes
                        find . -name "pom.xml" -exec git add '{}' '+'
                        if git diff-index --quiet HEAD --; then
                            echo "No changes to commit."
                        else
                            git commit -m "@Version ${BYZ_VERSION}" 
                            git push origin master
                        fi
                        # Create and push tag
                        git tag -a BYZ-${BYZ_VERSION} -m "Version ${BYZ_VERSION}"
                        git push origin BYZ-${BYZ_VERSION}
                        sleep 5
			
                        #chassagne
                        cd ${WORKSPACE}/repos/chassagne
                        git remote set-url origin ssh://**********************************:10022/generix/chassagne.git
                        git checkout  master
                        git stash
                        git pull --rebase
                        # Update pom.xml versions
                        find . -name "pom.xml" -exec sed -i "s/6.0.0-SNAPSHOT/${GNX_VERSION}/g" '{}' ';'
                        # Configure Git
                        git config --global user.name "jenkins"
                        git config --global user.email "<EMAIL>"
                        
                        # Commit and push changes
                        find . -name "pom.xml" -exec git add '{}' '+'
                         if git diff-index --quiet HEAD --; then
                            echo "No changes to commit."
                        else
                            git commit -m "@Version ${GNX_VERSION}" 
                            git push origin  master
                        fi
                        # Create and push tag
                        git tag -a GNX-${GNX_VERSION} -m "Version ${GNX_VERSION}"
                        git push origin GNX-${GNX_VERSION}
                        sleep 5


                        #######Revert update pom.xml######
                        #chassagnemaven
                        cd ${WORKSPACE}/repos/chassagnemaven
                        git remote set-url origin http://$USERNAME:$<EMAIL>/generix/chassagne-maven
                        git checkout master
                        git pull --rebase
                        # Revert pom.xml versions
                        find . -name "pom.xml" -exec sed -i "s/${GNX_VERSION}/6.0.0-SNAPSHOT/g" '{}' ';'
                        find . -name "pom.xml" -exec sed -i "s/${BYZ_VERSION}/10.0.0-SNAPSHOT/g" '{}' ';'
                        # Configure Git
                        git config --global user.name "jenkins"
                        git config --global user.email "<EMAIL>"
                        # Commit and push changes
                        find . -name "pom.xml" -exec git add '{}' '+'
                        if git diff-index --quiet HEAD --; then
                            echo "No changes to commit."
                        else
                            git commit -m "@PostVersion ${GNX_VERSION} (6.0.0-SNAPSHOT)" 
                            git push origin master
                        fi
                        sleep 5

                        #byzaneo
                        cd ${WORKSPACE}/repos/byzaneo
                        git remote set-url origin ssh://**********************************:10022/generix/byzaneo.git
                        git checkout master
                        git pull --rebase
                        # Revert pom.xml versions
                        find . -name "pom.xml" -exec sed -i "s/${BYZ_VERSION}/10.0.0-SNAPSHOT/g" '{}' ';'
                        find . -name "pom.xml" -exec sed -i "s/${GNX_VERSION}/6.0.0-SNAPSHOT/g" '{}' ';'
                        # Configure Git
                        git config --global user.name "jenkins"
                        git config --global user.email "<EMAIL>"
                        # Commit and push changes
                        find . -name "pom.xml" -exec git add '{}' '+'
                        if git diff-index --quiet HEAD --; then
                            echo "No changes to commit."
                        else
                            git commit -m "@PostVersion ${BYZ_VERSION} (10.0.0-SNAPSHOT)" 
                            git push origin master
                        fi
                        sleep 5

                        #chassagne
                        git remote set-url origin ssh://**********************************:10022/generix/chassagne.git
                        cd ${WORKSPACE}/repos/chassagne
                        git checkout master
                        git pull --rebase
                        # Revert pom.xml version
                        find . -name "pom.xml" -exec sed -i "s/${GNX_VERSION}/6.0.0-SNAPSHOT/g" '{}' ';'
                        # Configure Git
                        git config --global user.name "jenkins"
                        git config --global user.email "<EMAIL>"
                        
                        find . -name "pom.xml" -exec git add '{}' '+'
                        if git diff-index --quiet HEAD --; then
                            echo "No changes to commit."
                        else
                            git commit -m "@PostVersion ${GNX_VERSION} (6.0.0-SNAPSHOT)" 
                            git push origin master
                        fi
                    '''


                    }
                }
            }
        }
        stage("Update pom.xml Micro version") {
            when {
                expression { env.JOB_NAME == 'release-aio-micro-jdk17' }
            }
            steps {
                script {
                    withCredentials([usernamePassword(credentialsId: '1ff6869d-4818-45ff-bbab-1c94a304cf3a', usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
                        sh '''
                        cd ${WORKSPACE}/repos/chassagnemaven

                        SNAPSHOT="-SNAPSHOT"
                        
                        GNX_SNAPSHOT=$(grep '<gnx-version>' pom.xml | sed -e 's/<gnx-version>//g' -e 's/<\\/gnx-version>//g' -e 's/ //g' -e 's/\\t//g' -e 's/\\r//g')
                        export GNX_VERSION=$(echo ${GNX_SNAPSHOT} | sed -e "s/${SNAPSHOT}//g")
                        GNX_MAJOR=$(echo ${GNX_VERSION} | cut -d. -f1)
                        GNX_MINOR=$(echo ${GNX_VERSION} | cut -d. -f2)
                        GNX_MICRO=$(echo ${GNX_VERSION} | cut -d. -f3)
                        GNX_NEW_MICRO=$(( GNX_MICRO + 1 ))
                        GNX_NEW_SNAPSHOT="${GNX_MAJOR}.${GNX_MINOR}.${GNX_NEW_MICRO}${SNAPSHOT}"
                        echo "New GNX Snapshot Version: ${GNX_NEW_SNAPSHOT}"

                        BYZ_SNAPSHOT=$(grep '<byzaneo-version>' pom.xml | sed -e 's/<byzaneo-version>//g' -e 's/<\\/byzaneo-version>//g' -e 's/ //g' -e 's/\\t//g' -e 's/\\r//g')
                        export BYZ_VERSION=$(echo ${BYZ_SNAPSHOT} | sed -e "s/${SNAPSHOT}//g")
                        BYZ_MAJOR=$(echo ${BYZ_VERSION} | cut -d. -f1)
                        BYZ_MINOR=$(echo ${BYZ_VERSION} | cut -d. -f2)
                        BYZ_MICRO=$(echo ${BYZ_VERSION} | cut -d. -f3)
                        BYZ_NEW_MICRO=$(( BYZ_MICRO + 1 ))
                        BYZ_NEW_SNAPSHOT="${BYZ_MAJOR}.${BYZ_MINOR}.${BYZ_NEW_MICRO}${SNAPSHOT}"
                        echo "New BYZ Snapshot Version: ${BYZ_NEW_SNAPSHOT}"

                        # Configure Git with credentials
                        git remote set-url origin http://$USERNAME:$<EMAIL>/generix/chassagne-maven

                        # Chassagne Maven Version Update
                        echo "Generating $GNX_VERSION for chassagne-maven"
                        cd ${WORKSPACE}/repos/chassagnemaven
                        git checkout ${GNX_BRANCH}
                        git branch --set-upstream-to=origin/${GNX_BRANCH} ${GNX_BRANCH}
                        git pull --rebase
                        find . -name "pom.xml" -exec sed -i "s/${GNX_SNAPSHOT}/${GNX_VERSION}/g" {} +
                        find . -name "pom.xml" -exec sed -i "s/${BYZ_SNAPSHOT}/${BYZ_VERSION}/g" {} +
                        git config --global user.name "jenkins"
                        git config --global user.email "<EMAIL>"
                        find . -name "pom.xml" -exec git add {} +
                        # Check for uncommitted changes
                        if git diff-index --quiet HEAD --; then
                            echo "No changes to commit."
                        else
                            git commit -m "@Version ${GNX_VERSION}"
                            git push origin ${GNX_BRANCH}
                        fi
                        if git rev-parse "GNX-${GNX_VERSION}" >/dev/null 2>&1; then
                            echo "Tag 'GNX-${GNX_VERSION}' already exists ."
                        else
                            # Tag and push the new version
                            git tag -a GNX-${GNX_VERSION} -m "Version ${GNX_VERSION}"
                            git push origin GNX-${GNX_VERSION}
                        fi

                        # Byzaneo Version Update
                        echo "Generating $BYZ_VERSION for Byzaneo"
                        git remote set-url origin ssh://**********************************:10022/generix/byzaneo.git
                        cd ${WORKSPACE}/repos/byzaneo
                        git checkout ${BYZ_BRANCH}
                        git branch --set-upstream-to=origin/${BYZ_BRANCH} ${BYZ_BRANCH}
                        git pull --rebase
                        find . -name "pom.xml" -exec sed -i "s/${BYZ_SNAPSHOT}/${BYZ_VERSION}/g" {} +
                        find . -name "pom.xml" -exec sed -i "s/${GNX_SNAPSHOT}/${GNX_VERSION}/g" {} +
                        git config --global user.name "jenkins"
                        git config --global user.email "<EMAIL>"
                        find . -name "pom.xml" -exec git add {} +
                        if git diff-index --quiet HEAD --; then
                            echo "No changes to commit."
                        else
                            git commit -m "@Version ${BYZ_VERSION}"
                            git push origin ${BYZ_BRANCH}
                        fi
                        if git rev-parse "BYZ-${BYZ_VERSION}" >/dev/null 2>&1; then
                            echo "Tag 'BYZ-${BYZ_VERSION}' already exists ."
                        else
                            # Tag and push the new version
                            git tag -a "BYZ-${BYZ_VERSION}" -m "Version ${BYZ_VERSION}"
                            git push origin "BYZ-${BYZ_VERSION}"
                        fi

                        # Chassagne Version Update
                        git remote set-url origin ssh://**********************************:10022/generix/chassagne.git
                        echo "Generating $GNX_VERSION for Chassagne"
                        cd ${WORKSPACE}/repos/chassagne
                        git checkout ${GNX_BRANCH}
                        git branch --set-upstream-to=origin/${GNX_BRANCH} ${GNX_BRANCH}
                        git pull --rebase
                        find . -name "pom.xml" -exec sed -i "s/${GNX_SNAPSHOT}/${GNX_VERSION}/g" {} +
                        git config --global user.name "jenkins"
                        git config --global user.email "<EMAIL>"
                        find . -name "pom.xml" -exec git add {} +
                        if git diff-index --quiet HEAD --; then
                            echo "No changes to commit."
                        else
                            git commit -m "@Version ${GNX_VERSION}"
                            git push origin ${GNX_BRANCH}
                        fi
                        if git rev-parse "GNX-${GNX_VERSION}" >/dev/null 2>&1; then
                            echo "Tag 'GNX-${GNX_VERSION}' already exists ."
                        else
                            # Tag and push the new version
                            git tag -a "GNX-${GNX_VERSION}" -m "Version ${GNX_VERSION}"
                            git push origin "GNX-${GNX_VERSION}"
                        fi


                        # Revert update pom.xml with the new snapshot version
                        echo "Updating chassagne-maven with ${GNX_NEW_SNAPSHOT} version"
                        cd ${WORKSPACE}/repos/chassagnemaven
                        git remote set-url origin http://$USERNAME:$<EMAIL>/generix/chassagne-maven
                        git checkout ${GNX_BRANCH}
                        #git pull --rebase
                        find . -name "pom.xml" -exec sed -i "s/${GNX_VERSION}/${GNX_NEW_SNAPSHOT}/g" {} +
                        find . -name "pom.xml" -exec sed -i "s/${BYZ_VERSION}/${BYZ_NEW_SNAPSHOT}/g" {} +
                        find . -name "pom.xml" -exec git add {} +
                        if git diff-index --quiet HEAD --; then
                            echo "No changes to commit."
                        else
                            git commit -m "@PostVersion ${GNX_VERSION} (${GNX_NEW_SNAPSHOT})"
                            git push origin ${GNX_BRANCH}
                        fi

                        # Byzaneo Version Update
                        echo "Updating Byzaneo with version ${BYZ_NEW_SNAPSHOT}"
                        cd ${WORKSPACE}/repos/byzaneo
                        git remote set-url origin ssh://**********************************:10022/generix/byzaneo.git
                        git checkout ${BYZ_BRANCH}
                        git branch --set-upstream-to=origin/${BYZ_BRANCH} ${BYZ_BRANCH}
                        #git pull --rebase
                        find . -name "pom.xml" -exec sed -i "s/${BYZ_VERSION}/${BYZ_NEW_SNAPSHOT}/g" {} +
                        find . -name "pom.xml" -exec sed -i "s/${GNX_VERSION}/${GNX_NEW_SNAPSHOT}/g" {} +
                        git config --global user.name "jenkins"
                        git config --global user.email "<EMAIL>"
                        find . -name "pom.xml" -exec git add {} +
                        if git diff-index --quiet HEAD --; then
                            echo "No changes to commit."
                        else
                            git commit -m "@PostVersion ${BYZ_VERSION} (${BYZ_NEW_SNAPSHOT})" || true
                            git push origin ${BYZ_BRANCH}
                        fi

                        # Chassagne Version Update
                        echo "Updating Chassagne with version ${GNX_NEW_SNAPSHOT}"
                        cd ${WORKSPACE}/repos/chassagne
                        git remote set-url origin ssh://**********************************:10022/generix/chassagne.git
                        git checkout ${GNX_BRANCH}
                        git branch --set-upstream-to=origin/${GNX_BRANCH} ${GNX_BRANCH}
                        #git pull --rebase
                        find . -name "pom.xml" -exec sed -i "s/${GNX_VERSION}/${GNX_NEW_SNAPSHOT}/g" {} +
                        git config --global user.name "jenkins"
                        git config --global user.email "<EMAIL>"
                        find . -name "pom.xml" -exec git add {} +
                        if git diff-index --quiet HEAD --; then
                            echo "No changes to commit."
                        else
                            git commit -m "@PostVersion ${GNX_VERSION} (${GNX_NEW_SNAPSHOT})" || true
                            git push origin ${GNX_BRANCH}
                        fi
                        '''
                    }

                }
            }
        }
        stage('deploy to nexus') {
            steps {
                script {
                    def GNXbranchName = params.GNX_BRANCH
                    def BYZbranchName= params.BYZ_BRANCH
                    def PROFILE = ""
                    if (env.JOB_NAME == "release-aio-minor-jdk17") {
                        GNXbranchName = "master"
                        BYZbranchName= "master"
                        PROFILE = "-P ci"
                    }
                    def job1 = build job: 'compil-chassagnemaven-nexus',
                        parameters: [
                            string(name: 'chassagneMavenBranch', value: GNXbranchName),
                            string(name: 'DEPLOY', value: "deploy"),
                            string(name: 'PROFILE', value: "")
                        ]

                    def job2 = build job: 'compil-byzaneo-nexus',
                        parameters: [
                            string(name: 'byzaneoBranch', value: BYZbranchName),
                            string(name: 'DEPLOY', value: "deploy"),
                            string(name: 'PROFILE', value: PROFILE)
                        ]

                    def job3 = build job: 'compil-chassagne-nexus',
                        parameters: [
                            string(name: 'chassagneBranch', value: GNXbranchName),
                            string(name: 'DEPLOY', value: "deploy"),
                            string(name: 'PROFILE', value: PROFILE)
                        ]

                }
            }
        }
        stage('docker chassagne build') {
            steps {
                script {
                    def branchName = params.GNX_BRANCH
                    if (env.JOB_NAME == "release-aio-minor-jdk17") {
                        branchName = "master"
                    }
                    def gnxVersion = binding.getVariable('GNX_VERSION')
                    def job1 = build job: 'docker-chassagne-only-jdk17',
                        parameters: [
                            string(name: 'BRANCH', value: branchName),
                            string(name: 'DOCKER_TAG', value: "GNX-${gnxVersion}")
                        ]

                }
            }
        }
        stage('Trigger tools-patch-migration') {
            steps {
                script {
                    def gnxVersion = binding.getVariable('GNX_VERSION')
                    def job1 = build job: 'tools-patch-migration-jdk17',
                        parameters: [
                            string(name: 'BRANCH', value: "GNX-${gnxVersion}"),
                            string(name: 'DEPLOY', value: "deploy:deploy")
                        ]

                }
            }
        }
        stage('create branch') {
            when {
                expression { env.JOB_NAME == 'release-aio-minor-jdk17' && params.CREATE_QUALIF_BRANCHES }
            }
            steps {
                script {
                    def gnxVersion = binding.getVariable('GNX_VERSION')
                    def job1 = build job: 'release-aio-jdk17-create-branch',
                        parameters: [
                            string(name: 'GNX-VERSION', value: params.GNX_VERSION),
                            string(name: 'BYZ-VERSION', value: params.BYZ_VERSION)
                        ]

                }
            }
        }

    }
            
    post {
        always {
            cleanWs()
        }
    }
}
